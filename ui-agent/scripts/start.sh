#!/bin/bash

# UI Agent 启动脚本 (Mac/Linux)
# 一键启动前端和后端服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

echo -e "${BLUE}🚀 UI Agent 启动脚本${NC}"
echo -e "${BLUE}======================================${NC}"

# 检查Python环境
check_python() {
    echo -e "${YELLOW}📋 检查Python环境...${NC}"
    
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3 未安装，请先安装Python 3.8+${NC}"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    echo -e "${GREEN}✅ Python版本: $PYTHON_VERSION${NC}"
}

# 检查Node.js环境
check_node() {
    echo -e "${YELLOW}📋 检查Node.js环境...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装Node.js 16+${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js版本: $NODE_VERSION${NC}"
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm版本: $NPM_VERSION${NC}"
}

# 安装后端依赖
install_backend_deps() {
    echo -e "${YELLOW}📦 安装后端依赖...${NC}"
    cd "$BACKEND_DIR"
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}🔧 创建Python虚拟环境...${NC}"
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    pip install -r requirements.txt
    
    echo -e "${GREEN}✅ 后端依赖安装完成${NC}"
}

# 安装前端依赖
install_frontend_deps() {
    echo -e "${YELLOW}📦 安装前端依赖...${NC}"
    cd "$FRONTEND_DIR"
    
    if [ ! -d "node_modules" ]; then
        npm install
    else
        echo -e "${GREEN}✅ 前端依赖已存在${NC}"
    fi
}

# 启动后端服务
start_backend() {
    echo -e "${YELLOW}🔧 启动后端服务...${NC}"
    cd "$BACKEND_DIR"
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 启动服务
    python main.py &
    BACKEND_PID=$!
    
    echo -e "${GREEN}✅ 后端服务已启动 (PID: $BACKEND_PID)${NC}"
    echo -e "${GREEN}📍 后端地址: http://localhost:8000${NC}"
    
    # 等待后端启动
    sleep 3
}

# 启动前端服务
start_frontend() {
    echo -e "${YELLOW}🔧 启动前端服务...${NC}"
    cd "$FRONTEND_DIR"
    
    # 启动开发服务器
    npm run dev &
    FRONTEND_PID=$!
    
    echo -e "${GREEN}✅ 前端服务已启动 (PID: $FRONTEND_PID)${NC}"
    echo -e "${GREEN}📍 前端地址: http://localhost:3000${NC}"
}

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🛑 正在停止服务...${NC}"
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo -e "${GREEN}✅ 后端服务已停止${NC}"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo -e "${GREEN}✅ 前端服务已停止${NC}"
    fi
    
    echo -e "${BLUE}👋 再见！${NC}"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    echo -e "${BLUE}开始启动 UI Agent...${NC}\n"
    
    # 环境检查
    check_python
    check_node
    
    echo ""
    
    # 安装依赖
    install_backend_deps
    install_frontend_deps
    
    echo ""
    
    # 启动服务
    start_backend
    start_frontend
    
    echo ""
    echo -e "${GREEN}🎉 UI Agent 启动完成！${NC}"
    echo -e "${GREEN}📱 前端地址: http://localhost:3000${NC}"
    echo -e "${GREEN}🔧 后端地址: http://localhost:8000${NC}"
    echo -e "${GREEN}📚 API文档: http://localhost:8000/docs${NC}"
    echo ""
    echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
    
    # 等待用户中断
    wait
}

# 运行主函数
main
