@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM UI Agent 启动脚本 (Windows)
REM 一键启动前端和后端服务

echo 🚀 UI Agent 启动脚本
echo ======================================

REM 获取项目根目录
set "PROJECT_ROOT=%~dp0.."
set "BACKEND_DIR=%PROJECT_ROOT%\backend"
set "FRONTEND_DIR=%PROJECT_ROOT%\frontend"

REM 检查Python环境
echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装Python 3.8+
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

REM 检查Node.js环境
echo 📋 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装Node.js 16+
    pause
    exit /b 1
)

for /f %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

for /f %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

echo.

REM 安装后端依赖
echo 📦 安装后端依赖...
cd /d "%BACKEND_DIR%"

REM 检查虚拟环境
if not exist "venv" (
    echo 🔧 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 升级pip
python -m pip install --upgrade pip

REM 安装依赖
pip install -r requirements.txt

echo ✅ 后端依赖安装完成

REM 安装前端依赖
echo 📦 安装前端依赖...
cd /d "%FRONTEND_DIR%"

if not exist "node_modules" (
    npm install
) else (
    echo ✅ 前端依赖已存在
)

echo.

REM 启动后端服务
echo 🔧 启动后端服务...
cd /d "%BACKEND_DIR%"

REM 激活虚拟环境并启动后端
start "UI Agent Backend" cmd /k "venv\Scripts\activate.bat && python main.py"

echo ✅ 后端服务已启动
echo 📍 后端地址: http://localhost:8000

REM 等待后端启动
timeout /t 3 /nobreak >nul

REM 启动前端服务
echo 🔧 启动前端服务...
cd /d "%FRONTEND_DIR%"

start "UI Agent Frontend" cmd /k "npm run dev"

echo ✅ 前端服务已启动
echo 📍 前端地址: http://localhost:3000

echo.
echo 🎉 UI Agent 启动完成！
echo 📱 前端地址: http://localhost:3000
echo 🔧 后端地址: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo.
echo 服务已在新窗口中启动，关闭对应窗口即可停止服务
echo 按任意键退出此窗口...
pause >nul
