#!/usr/bin/env python3
"""
UI Agent Backend Server
智能自动化测试平台后端服务
"""

import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from app.core.database import init_db
from app.main import create_app

def main():
    """启动后端服务"""
    print("🚀 启动 UI Agent 后端服务...")
    print(f"📍 服务地址: http://{settings.HOST}:{settings.PORT}")
    print(f"📚 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔧 环境模式: {settings.ENVIRONMENT}")
    
    # 初始化数据库
    init_db()
    
    # 创建应用
    app = create_app()
    
    # 启动服务
    uvicorn.run(
        app,
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning"
    )

if __name__ == "__main__":
    main()
