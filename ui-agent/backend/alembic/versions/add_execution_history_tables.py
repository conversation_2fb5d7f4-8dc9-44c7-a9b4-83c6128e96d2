"""Add execution history tables for real-time execution

Revision ID: add_execution_history
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_execution_history'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create task_history table
    op.create_table('task_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('automation_type', sa.String(length=20), nullable=False, comment='自动化类型: android/web'),
        sa.Column('input_type', sa.String(length=20), nullable=True, comment='输入类型: text/voice'),
        sa.Column('input_content', sa.Text(), nullable=True, comment='输入内容'),
        sa.Column('ai_model', sa.String(length=100), nullable=True, comment='AI模型名称'),
        sa.Column('api_base', sa.String(length=200), nullable=True, comment='API基础URL'),
        sa.Column('temperature', sa.Float(), nullable=True, comment='温度参数'),
        sa.Column('status', sa.String(length=20), nullable=True, comment='执行状态: running/completed/failed/stopped'),
        sa.Column('result', sa.JSON(), nullable=True, comment='执行结果JSON'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
        sa.Column('execution_time', sa.Float(), nullable=True, comment='执行时间（秒）'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True, comment='完成时间'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_task_history_id'), 'task_history', ['id'], unique=False)
    op.create_index('ix_task_history_automation_type', 'task_history', ['automation_type'], unique=False)
    op.create_index('ix_task_history_status', 'task_history', ['status'], unique=False)
    op.create_index('ix_task_history_created_at', 'task_history', ['created_at'], unique=False)

    # Create execution_steps table
    op.create_table('execution_steps',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('history_id', sa.Integer(), nullable=False, comment='历史记录ID'),
        sa.Column('step_index', sa.Integer(), nullable=False, comment='步骤索引'),
        sa.Column('action_type', sa.String(length=50), nullable=True, comment='动作类型'),
        sa.Column('description', sa.Text(), nullable=True, comment='步骤描述'),
        sa.Column('screenshot_path', sa.String(length=500), nullable=True, comment='截图路径'),
        sa.Column('element_info', sa.JSON(), nullable=True, comment='元素信息'),
        sa.Column('ai_response', sa.JSON(), nullable=True, comment='AI响应信息'),
        sa.Column('success', sa.Boolean(), nullable=True, comment='是否成功'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['history_id'], ['task_history.id'], ondelete='CASCADE')
    )
    op.create_index(op.f('ix_execution_steps_id'), 'execution_steps', ['id'], unique=False)
    op.create_index('ix_execution_steps_history_id', 'execution_steps', ['history_id'], unique=False)
    op.create_index('ix_execution_steps_step_index', 'execution_steps', ['step_index'], unique=False)


def downgrade() -> None:
    # Drop execution_steps table
    op.drop_index('ix_execution_steps_step_index', table_name='execution_steps')
    op.drop_index('ix_execution_steps_history_id', table_name='execution_steps')
    op.drop_index(op.f('ix_execution_steps_id'), table_name='execution_steps')
    op.drop_table('execution_steps')
    
    # Drop task_history table
    op.drop_index('ix_task_history_created_at', table_name='task_history')
    op.drop_index('ix_task_history_status', table_name='task_history')
    op.drop_index('ix_task_history_automation_type', table_name='task_history')
    op.drop_index(op.f('ix_task_history_id'), table_name='task_history')
    op.drop_table('task_history')
