"""
Android端API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.android_service import AndroidService

router = APIRouter()

class DeviceInfo(BaseModel):
    """设备信息模型"""
    device_id: str
    device_name: str
    brand: Optional[str] = None
    model: Optional[str] = None
    android_version: Optional[str] = None
    screen_resolution: Optional[str] = None
    is_connected: bool = False
    is_active: bool = False

class InstallApkRequest(BaseModel):
    """安装APK请求模型"""
    download_url: str
    device_id: Optional[str] = None

class AdbCommandRequest(BaseModel):
    """ADB命令请求模型"""
    command: str
    device_id: Optional[str] = None

@router.get("/devices", response_model=List[DeviceInfo])
async def get_devices(db: Session = Depends(get_db)):
    """
    获取Android设备列表
    """
    try:
        android_service = AndroidService(db)
        devices = await android_service.get_devices()
        return devices
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取设备列表失败: {str(e)}")

@router.post("/devices/{device_id}/select")
async def select_device(device_id: str, db: Session = Depends(get_db)):
    """
    选择当前活动设备
    """
    try:
        android_service = AndroidService(db)
        result = await android_service.select_device(device_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"选择设备失败: {str(e)}")

@router.post("/devices/refresh")
async def refresh_devices(db: Session = Depends(get_db)):
    """
    刷新设备列表
    """
    try:
        android_service = AndroidService(db)
        devices = await android_service.refresh_devices()
        return {
            "success": True,
            "message": "设备列表已刷新",
            "devices": devices
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新设备列表失败: {str(e)}")

@router.post("/install_apk")
async def install_apk(request: InstallApkRequest, db: Session = Depends(get_db)):
    """
    下载并安装APK
    """
    try:
        android_service = AndroidService(db)
        result = await android_service.install_apk(
            download_url=request.download_url,
            device_id=request.device_id
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"安装APK失败: {str(e)}")

@router.post("/adb_command")
async def execute_adb_command(request: AdbCommandRequest, db: Session = Depends(get_db)):
    """
    执行ADB命令
    """
    try:
        android_service = AndroidService(db)
        result = await android_service.execute_adb_command(
            command=request.command,
            device_id=request.device_id
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行ADB命令失败: {str(e)}")

@router.get("/devices/{device_id}/screenshot")
async def take_screenshot(device_id: str, db: Session = Depends(get_db)):
    """
    获取设备截图
    """
    try:
        android_service = AndroidService(db)
        result = await android_service.take_screenshot(device_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取截图失败: {str(e)}")

@router.get("/devices/{device_id}/status")
async def get_device_status(device_id: str, db: Session = Depends(get_db)):
    """
    获取设备状态
    """
    try:
        android_service = AndroidService(db)
        status = await android_service.get_device_status(device_id)
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取设备状态失败: {str(e)}")
