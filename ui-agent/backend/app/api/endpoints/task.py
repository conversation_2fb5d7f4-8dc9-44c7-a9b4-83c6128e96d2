"""
实时任务执行API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.task_service import TaskService

router = APIRouter()

class DirectExecuteRequest(BaseModel):
    """直接执行任务请求模型"""
    automation_type: str  # android/web
    input_type: str  # text/voice
    input_content: str
    ai_model: Optional[str] = None
    api_base: Optional[str] = None
    temperature: Optional[float] = 0.7
    max_scroll_times: Optional[int] = 3

class TaskHistoryResponse(BaseModel):
    """任务历史响应模型"""
    id: int
    automation_type: str
    input_content: str
    status: str
    result: Optional[Dict[str, Any]] = None
    created_at: str
    execution_time: Optional[float] = None

class ExecutionStepResponse(BaseModel):
    """执行步骤响应模型"""
    step_index: int
    action_type: str
    description: str
    screenshot_path: Optional[str] = None
    element_info: Optional[Dict[str, Any]] = None
    ai_response: Optional[Dict[str, Any]] = None
    success: bool
    error_message: Optional[str] = None
    timestamp: str

@router.post("/execute")
async def execute_task_directly(request: DirectExecuteRequest, db: Session = Depends(get_db)):
    """
    直接执行任务（实时执行模式）
    """
    try:
        task_service = TaskService(db)
        result = await task_service.execute_task_directly(
            automation_type=request.automation_type,
            input_type=request.input_type,
            input_content=request.input_content,
            ai_model=request.ai_model,
            api_base=request.api_base,
            temperature=request.temperature,
            max_scroll_times=request.max_scroll_times
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行任务失败: {str(e)}")

@router.get("/history", response_model=List[TaskHistoryResponse])
async def get_task_history(
    skip: int = 0,
    limit: int = 50,
    automation_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取任务执行历史
    """
    try:
        task_service = TaskService(db)
        history = await task_service.get_task_history(
            skip=skip,
            limit=limit,
            automation_type=automation_type
        )
        return history
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务历史失败: {str(e)}")

@router.get("/history/{history_id}/steps", response_model=List[ExecutionStepResponse])
async def get_execution_steps(history_id: int, db: Session = Depends(get_db)):
    """
    获取任务执行步骤详情
    """
    try:
        task_service = TaskService(db)
        steps = await task_service.get_execution_steps(history_id)
        return steps
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取执行步骤失败: {str(e)}")

@router.delete("/history/{history_id}")
async def delete_task_history(history_id: int, db: Session = Depends(get_db)):
    """
    删除任务历史记录
    """
    try:
        task_service = TaskService(db)
        result = await task_service.delete_task_history(history_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除任务历史失败: {str(e)}")

@router.post("/stop")
async def stop_current_execution(db: Session = Depends(get_db)):
    """
    停止当前正在执行的任务
    """
    try:
        task_service = TaskService(db)
        result = await task_service.stop_current_execution()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"停止任务失败: {str(e)}")

@router.get("/status")
async def get_execution_status(db: Session = Depends(get_db)):
    """
    获取当前执行状态
    """
    try:
        task_service = TaskService(db)
        status = await task_service.get_execution_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取执行状态失败: {str(e)}")
