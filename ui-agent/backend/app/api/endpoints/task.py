"""
任务管理API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.task_service import TaskService

router = APIRouter()

class CreateTaskRequest(BaseModel):
    """创建任务请求模型"""
    title: str
    description: str
    automation_type: str  # android/web
    input_type: str  # text/voice
    input_content: str
    ai_model: Optional[str] = None
    api_base: Optional[str] = None
    temperature: Optional[float] = 0.7

class TaskResponse(BaseModel):
    """任务响应模型"""
    id: int
    title: str
    description: str
    automation_type: str
    status: str
    progress: float
    created_at: str
    updated_at: Optional[str] = None

class ExecuteTaskRequest(BaseModel):
    """执行任务请求模型"""
    task_id: int
    max_scroll_times: Optional[int] = 3

@router.post("/create", response_model=TaskResponse)
async def create_task(request: CreateTaskRequest, db: Session = Depends(get_db)):
    """
    创建新任务
    """
    try:
        task_service = TaskService(db)
        task = await task_service.create_task(
            title=request.title,
            description=request.description,
            automation_type=request.automation_type,
            input_type=request.input_type,
            input_content=request.input_content,
            ai_model=request.ai_model,
            api_base=request.api_base,
            temperature=request.temperature
        )
        return task
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@router.post("/execute")
async def execute_task(request: ExecuteTaskRequest, db: Session = Depends(get_db)):
    """
    执行任务
    """
    try:
        task_service = TaskService(db)
        result = await task_service.execute_task(
            task_id=request.task_id,
            max_scroll_times=request.max_scroll_times
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行任务失败: {str(e)}")

@router.get("/list", response_model=List[TaskResponse])
async def get_tasks(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    automation_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取任务列表
    """
    try:
        task_service = TaskService(db)
        tasks = await task_service.get_tasks(
            skip=skip,
            limit=limit,
            status=status,
            automation_type=automation_type
        )
        return tasks
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(task_id: int, db: Session = Depends(get_db)):
    """
    获取任务详情
    """
    try:
        task_service = TaskService(db)
        task = await task_service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        return task
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务详情失败: {str(e)}")

@router.delete("/{task_id}")
async def delete_task(task_id: int, db: Session = Depends(get_db)):
    """
    删除任务
    """
    try:
        task_service = TaskService(db)
        result = await task_service.delete_task(task_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")

@router.get("/{task_id}/steps")
async def get_task_steps(task_id: int, db: Session = Depends(get_db)):
    """
    获取任务步骤
    """
    try:
        task_service = TaskService(db)
        steps = await task_service.get_task_steps(task_id)
        return steps
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务步骤失败: {str(e)}")

@router.get("/{task_id}/logs")
async def get_task_logs(task_id: int, db: Session = Depends(get_db)):
    """
    获取任务日志
    """
    try:
        task_service = TaskService(db)
        logs = await task_service.get_task_logs(task_id)
        return logs
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务日志失败: {str(e)}")

@router.post("/{task_id}/stop")
async def stop_task(task_id: int, db: Session = Depends(get_db)):
    """
    停止任务执行
    """
    try:
        task_service = TaskService(db)
        result = await task_service.stop_task(task_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"停止任务失败: {str(e)}")
