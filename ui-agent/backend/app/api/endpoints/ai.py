"""
AI执行API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.ai_service import AIService

router = APIRouter()

class ProcessComplexAIExecRequest(BaseModel):
    """AI执行请求模型"""
    automation_type: str  # android/web
    bdd_script: str
    last_result: Optional[str] = ""
    last_step_result: Optional[str] = ""
    max_scroll_times: Optional[int] = 3

class AIConfigRequest(BaseModel):
    """AI配置请求模型"""
    model_name: str
    api_base: str
    temperature: float = 0.7

@router.post("/process_complex_ai_exec")
async def process_complex_ai_exec(
    request: ProcessComplexAIExecRequest,
    db: Session = Depends(get_db)
):
    """
    执行AI智能规划任务
    这是核心的AI执行接口，根据需求文档中的流程实现
    """
    try:
        ai_service = AIService(db)
        
        # 根据自动化类型调用不同的处理方法
        if request.automation_type == "web":
            result = await ai_service.process_complex_ai_exec_web(
                desc=request.bdd_script,
                last_result=request.last_result,
                last_step_result=request.last_step_result,
                max_scroll_times=request.max_scroll_times
            )
        elif request.automation_type == "android":
            result = await ai_service.process_complex_ai_exec_android(
                desc=request.bdd_script,
                last_result=request.last_result,
                last_step_result=request.last_step_result,
                max_scroll_times=request.max_scroll_times
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的自动化类型: {request.automation_type}"
            )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI执行失败: {str(e)}")

@router.post("/config")
async def set_ai_config(request: AIConfigRequest, db: Session = Depends(get_db)):
    """
    设置AI配置
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.set_config(
            model_name=request.model_name,
            api_base=request.api_base,
            temperature=request.temperature
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置AI配置失败: {str(e)}")

@router.get("/config")
async def get_ai_config(db: Session = Depends(get_db)):
    """
    获取AI配置
    """
    try:
        ai_service = AIService(db)
        config = await ai_service.get_config()
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取AI配置失败: {str(e)}")

@router.post("/test_connection")
async def test_ai_connection(request: AIConfigRequest, db: Session = Depends(get_db)):
    """
    测试AI连接
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.test_connection(
            model_name=request.model_name,
            api_base=request.api_base
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试AI连接失败: {str(e)}")

@router.get("/models")
async def get_available_models():
    """
    获取可用的AI模型列表
    """
    try:
        models = [
            {
                "name": "gpt-4-vision-preview",
                "description": "GPT-4 Vision Preview - 支持图像分析",
                "provider": "OpenAI"
            },
            {
                "name": "gpt-4",
                "description": "GPT-4 - 高性能文本模型",
                "provider": "OpenAI"
            },
            {
                "name": "gpt-3.5-turbo",
                "description": "GPT-3.5 Turbo - 快速响应模型",
                "provider": "OpenAI"
            },
            {
                "name": "claude-3-opus",
                "description": "Claude 3 Opus - Anthropic最强模型",
                "provider": "Anthropic"
            },
            {
                "name": "claude-3-sonnet",
                "description": "Claude 3 Sonnet - 平衡性能模型",
                "provider": "Anthropic"
            }
        ]
        return {
            "success": True,
            "models": models
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")
