"""
Web端API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.web_service import WebService

router = APIRouter()

class NavigateRequest(BaseModel):
    """导航请求模型"""
    url: str
    cookies: Optional[List[Dict[str, Any]]] = None
    headers: Optional[Dict[str, str]] = None
    is_h5_mode: bool = False

class SetCookiesRequest(BaseModel):
    """设置Cookie请求模型"""
    cookies: List[Dict[str, Any]]

class SetHeadersRequest(BaseModel):
    """设置Header请求模型"""
    headers: Dict[str, str]

class ExecuteJsRequest(BaseModel):
    """执行JavaScript请求模型"""
    code: str

@router.post("/navigate")
async def navigate_to_url(request: NavigateRequest, db: Session = Depends(get_db)):
    """
    导航到指定URL
    """
    try:
        web_service = WebService(db)
        result = await web_service.navigate(
            url=request.url,
            cookies=request.cookies,
            headers=request.headers,
            is_h5_mode=request.is_h5_mode
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导航失败: {str(e)}")

@router.post("/cookies")
async def set_cookies(request: SetCookiesRequest, db: Session = Depends(get_db)):
    """
    设置Cookie
    """
    try:
        web_service = WebService(db)
        result = await web_service.set_cookies(request.cookies)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置Cookie失败: {str(e)}")

@router.post("/headers")
async def set_headers(request: SetHeadersRequest, db: Session = Depends(get_db)):
    """
    设置Header
    """
    try:
        web_service = WebService(db)
        result = await web_service.set_headers(request.headers)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置Header失败: {str(e)}")

@router.post("/execute_js")
async def execute_javascript(request: ExecuteJsRequest, db: Session = Depends(get_db)):
    """
    执行JavaScript代码
    """
    try:
        web_service = WebService(db)
        result = await web_service.execute_javascript(request.code)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行JavaScript失败: {str(e)}")

@router.post("/reset")
async def reset_browser(db: Session = Depends(get_db)):
    """
    重置浏览器实例
    """
    try:
        web_service = WebService(db)
        result = await web_service.reset_browser()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置浏览器失败: {str(e)}")

@router.get("/screenshot")
async def take_screenshot(db: Session = Depends(get_db)):
    """
    获取页面截图
    """
    try:
        web_service = WebService(db)
        result = await web_service.take_screenshot()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取截图失败: {str(e)}")

@router.get("/status")
async def get_browser_status(db: Session = Depends(get_db)):
    """
    获取浏览器状态
    """
    try:
        web_service = WebService(db)
        status = await web_service.get_browser_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取浏览器状态失败: {str(e)}")

@router.get("/current_url")
async def get_current_url(db: Session = Depends(get_db)):
    """
    获取当前页面URL
    """
    try:
        web_service = WebService(db)
        result = await web_service.get_current_url()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取当前URL失败: {str(e)}")
