"""
报告管理API
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.report_service import ReportService

router = APIRouter()

class GenerateReportRequest(BaseModel):
    """生成报告请求模型"""
    task_id: int
    report_name: Optional[str] = None

class ReportResponse(BaseModel):
    """报告响应模型"""
    id: int
    task_id: int
    report_name: str
    total_steps: int
    success_steps: int
    failed_steps: int
    execution_time: Optional[float]
    html_file_path: str
    created_at: str

@router.post("/generate", response_model=ReportResponse)
async def generate_report(request: GenerateReportRequest, db: Session = Depends(get_db)):
    """
    生成任务执行报告
    """
    try:
        report_service = ReportService(db)
        report = await report_service.generate_report(
            task_id=request.task_id,
            report_name=request.report_name
        )
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")

@router.get("/list", response_model=List[ReportResponse])
async def get_reports(
    skip: int = 0,
    limit: int = 100,
    task_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """
    获取报告列表
    """
    try:
        report_service = ReportService(db)
        reports = await report_service.get_reports(
            skip=skip,
            limit=limit,
            task_id=task_id
        )
        return reports
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取报告列表失败: {str(e)}")

@router.get("/{report_id}", response_model=ReportResponse)
async def get_report(report_id: int, db: Session = Depends(get_db)):
    """
    获取报告详情
    """
    try:
        report_service = ReportService(db)
        report = await report_service.get_report(report_id)
        if not report:
            raise HTTPException(status_code=404, detail="报告不存在")
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取报告详情失败: {str(e)}")

@router.get("/{report_id}/download")
async def download_report(report_id: int, db: Session = Depends(get_db)):
    """
    下载HTML报告文件
    """
    try:
        report_service = ReportService(db)
        report = await report_service.get_report(report_id)
        if not report:
            raise HTTPException(status_code=404, detail="报告不存在")
        
        # 返回HTML文件
        return FileResponse(
            path=report.html_file_path,
            filename=f"report_{report_id}.html",
            media_type="text/html"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载报告失败: {str(e)}")

@router.get("/{report_id}/data")
async def get_report_data(report_id: int, db: Session = Depends(get_db)):
    """
    获取报告详细数据
    """
    try:
        report_service = ReportService(db)
        data = await report_service.get_report_data(report_id)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取报告数据失败: {str(e)}")

@router.delete("/{report_id}")
async def delete_report(report_id: int, db: Session = Depends(get_db)):
    """
    删除报告
    """
    try:
        report_service = ReportService(db)
        result = await report_service.delete_report(report_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除报告失败: {str(e)}")

@router.get("/task/{task_id}/screenshots")
async def get_task_screenshots(task_id: int, db: Session = Depends(get_db)):
    """
    获取任务的所有截图
    """
    try:
        report_service = ReportService(db)
        screenshots = await report_service.get_task_screenshots(task_id)
        return screenshots
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务截图失败: {str(e)}")

@router.get("/task/{task_id}/dom_snapshots")
async def get_task_dom_snapshots(task_id: int, db: Session = Depends(get_db)):
    """
    获取任务的DOM快照
    """
    try:
        report_service = ReportService(db)
        snapshots = await report_service.get_task_dom_snapshots(task_id)
        return snapshots
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取DOM快照失败: {str(e)}")
