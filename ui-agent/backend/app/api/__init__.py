"""
API路由模块
"""

from fastapi import APIRouter

from .endpoints import (
    connection,
    android,
    web,
    task,
    ai,
    report
)

# 创建主路由
api_router = APIRouter()

# 注册子路由
api_router.include_router(connection.router, prefix="/connection", tags=["连接管理"])
api_router.include_router(android.router, prefix="/android", tags=["Android端"])
api_router.include_router(web.router, prefix="/web", tags=["Web端"])
api_router.include_router(task.router, prefix="/task", tags=["任务管理"])
api_router.include_router(ai.router, prefix="/ai", tags=["AI执行"])
api_router.include_router(report.router, prefix="/report", tags=["报告管理"])
