"""
FastAPI应用主模块
"""

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse

from app.core.config import settings
from app.api import api_router

def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.VERSION,
        description=settings.DESCRIPTION,
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册API路由
    app.include_router(api_router, prefix="/api")
    
    # 静态文件服务
    app.mount("/output", StaticFiles(directory=str(settings.OUTPUT_DIR)), name="output")
    
    @app.get("/", response_class=HTMLResponse)
    async def root():
        """根路径"""
        return """
        <html>
            <head>
                <title>UI Agent Backend</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .header { text-align: center; margin-bottom: 40px; }
                    .status { background: #e8f5e8; padding: 20px; border-radius: 8px; }
                    .links { margin-top: 30px; }
                    .links a { display: inline-block; margin: 10px; padding: 10px 20px; 
                              background: #007bff; color: white; text-decoration: none; 
                              border-radius: 5px; }
                    .links a:hover { background: #0056b3; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🤖 UI Agent Backend</h1>
                        <p>智能自动化测试平台后端服务</p>
                    </div>
                    <div class="status">
                        <h3>✅ 服务运行正常</h3>
                        <p><strong>版本:</strong> """ + settings.VERSION + """</p>
                        <p><strong>环境:</strong> """ + settings.ENVIRONMENT + """</p>
                        <p><strong>端口:</strong> """ + str(settings.PORT) + """</p>
                    </div>
                    <div class="links">
                        <a href="/docs">📚 API文档</a>
                        <a href="/redoc">📖 ReDoc文档</a>
                        <a href="/output">📁 输出文件</a>
                    </div>
                </div>
            </body>
        </html>
        """
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "app_name": settings.APP_NAME,
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT
        }
    
    return app

# 创建应用实例
app = create_app()
