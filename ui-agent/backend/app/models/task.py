"""
任务相关数据模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSON
from sqlalchemy.sql import func

from app.core.database import Base

class Task(Base):
    """任务模型"""
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, comment="任务标题")
    description = Column(Text, comment="任务描述")
    automation_type = Column(String(20), nullable=False, comment="自动化类型: android/web")
    
    # 任务状态
    status = Column(String(20), default="pending", comment="任务状态: pending/running/completed/failed")
    progress = Column(Float, default=0.0, comment="任务进度 0-100")
    
    # 输入方式
    input_type = Column(String(20), comment="输入类型: text/voice")
    input_content = Column(Text, comment="输入内容")
    
    # AI配置
    ai_model = Column(String(100), comment="AI模型名称")
    api_base = Column(String(200), comment="API基础URL")
    temperature = Column(Float, default=0.7, comment="温度参数")
    
    # 执行结果
    result = Column(JSON, comment="执行结果JSON")
    error_message = Column(Text, comment="错误信息")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True), comment="开始执行时间")
    completed_at = Column(DateTime(timezone=True), comment="完成时间")

class TaskStep(Base):
    """任务步骤模型"""
    __tablename__ = "task_steps"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, nullable=False, comment="任务ID")
    step_index = Column(Integer, nullable=False, comment="步骤索引")
    
    # 步骤信息
    action = Column(String(200), comment="执行动作")
    element_info = Column(JSON, comment="元素信息")
    code_info = Column(JSON, comment="代码信息")
    
    # 执行结果
    status = Column(String(20), default="pending", comment="步骤状态")
    result = Column(JSON, comment="执行结果")
    screenshot_path = Column(String(500), comment="截图路径")
    observations = Column(Text, comment="观察结果")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    executed_at = Column(DateTime(timezone=True), comment="执行时间")

class TaskLog(Base):
    """任务日志模型"""
    __tablename__ = "task_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, nullable=False, comment="任务ID")
    step_id = Column(Integer, comment="步骤ID")
    
    # 日志信息
    level = Column(String(20), default="info", comment="日志级别")
    message = Column(Text, comment="日志消息")
    details = Column(JSON, comment="详细信息")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
