"""
报告相关数据模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, Float
from sqlalchemy.sql import func

from app.core.database import Base

class ExecutionReport(Base):
    """执行报告模型"""
    __tablename__ = "execution_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, nullable=False, comment="任务ID")
    report_name = Column(String(200), comment="报告名称")
    
    # 报告内容
    summary = Column(JSON, comment="执行摘要")
    steps_data = Column(JSON, comment="步骤数据")
    screenshots = Column(JSON, comment="截图列表")
    dom_data = Column(JSON, comment="DOM数据")
    
    # 统计信息
    total_steps = Column(Integer, default=0, comment="总步骤数")
    success_steps = Column(Integer, default=0, comment="成功步骤数")
    failed_steps = Column(Integer, default=0, comment="失败步骤数")
    execution_time = Column(Float, comment="执行时间(秒)")
    
    # 文件路径
    html_file_path = Column(String(500), comment="HTML报告文件路径")
    json_file_path = Column(String(500), comment="JSON数据文件路径")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Screenshot(Base):
    """截图模型"""
    __tablename__ = "screenshots"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, nullable=False, comment="任务ID")
    step_id = Column(Integer, comment="步骤ID")
    
    # 截图信息
    file_path = Column(String(500), nullable=False, comment="文件路径")
    file_name = Column(String(200), comment="文件名")
    file_size = Column(Integer, comment="文件大小")
    
    # 截图类型
    screenshot_type = Column(String(50), comment="截图类型: before/after/error")
    automation_type = Column(String(20), comment="自动化类型: android/web")
    
    # 元素标记信息
    has_annotations = Column(Boolean, default=False, comment="是否有元素标记")
    annotations_data = Column(JSON, comment="标记数据")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class DomSnapshot(Base):
    """DOM快照模型"""
    __tablename__ = "dom_snapshots"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, nullable=False, comment="任务ID")
    step_id = Column(Integer, comment="步骤ID")
    screenshot_id = Column(Integer, comment="关联截图ID")
    
    # DOM数据
    dom_tree = Column(JSON, comment="DOM树结构")
    viewport_info = Column(JSON, comment="视口信息")
    selected_element = Column(JSON, comment="选中的元素信息")
    
    # 元数据
    automation_type = Column(String(20), comment="自动化类型: android/web")
    page_url = Column(String(500), comment="页面URL(Web)")
    app_package = Column(String(200), comment="应用包名(Android)")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
