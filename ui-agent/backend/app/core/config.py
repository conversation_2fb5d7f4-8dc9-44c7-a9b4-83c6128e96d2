"""
应用配置模块
"""

import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "UI Agent"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "智能自动化测试平台"
    
    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./ui_agent.db"
    
    # AI配置
    DEFAULT_AI_MODEL: str = "gpt-4-vision-preview"
    DEFAULT_API_BASE: str = "https://api.openai.com/v1"
    DEFAULT_TEMPERATURE: float = 0.7
    
    # 文件路径配置
    PROJECT_ROOT: Path = Path(__file__).parent.parent.parent.parent
    OUTPUT_DIR: Path = PROJECT_ROOT / "output"
    SCREENSHOTS_DIR: Path = OUTPUT_DIR / "screenshots"
    REPORTS_DIR: Path = OUTPUT_DIR / "reports"
    LOGS_DIR: Path = OUTPUT_DIR / "logs"
    
    # 自动化配置
    MAX_SCROLL_TIMES: int = 3
    MAX_EXEC_TIMES: int = 20
    SCREENSHOT_TIMEOUT: int = 30
    
    # CORS配置
    CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173"
    ]
    
    class Config:
        env_file = ".env"
        case_sensitive = True

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保输出目录存在
        self.OUTPUT_DIR.mkdir(exist_ok=True)
        self.SCREENSHOTS_DIR.mkdir(exist_ok=True)
        self.REPORTS_DIR.mkdir(exist_ok=True)
        self.LOGS_DIR.mkdir(exist_ok=True)

# 创建全局配置实例
settings = Settings()
