"""
报告管理服务
"""

import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from pathlib import Path
from jinja2 import Template

from app.models.task import Task, TaskStep, TaskLog
from app.models.report import ExecutionReport, Screenshot, DomSnapshot
from app.core.config import settings

class ReportService:
    """报告管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def generate_report(
        self,
        task_id: int,
        report_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成任务执行报告"""
        try:
            # 获取任务信息
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise Exception("任务不存在")
            
            # 获取任务步骤
            steps = self.db.query(TaskStep).filter(
                TaskStep.task_id == task_id
            ).order_by(TaskStep.step_index).all()
            
            # 获取任务日志
            logs = self.db.query(TaskLog).filter(
                TaskLog.task_id == task_id
            ).order_by(TaskLog.created_at).all()
            
            # 获取截图
            screenshots = self.db.query(Screenshot).filter(
                Screenshot.task_id == task_id
            ).order_by(Screenshot.created_at).all()
            
            # 生成报告名称
            if not report_name:
                report_name = f"Task_{task_id}_Report_{int(time.time())}"
            
            # 统计信息
            total_steps = len(steps)
            success_steps = len([s for s in steps if s.status == "success"])
            failed_steps = len([s for s in steps if s.status == "failed"])
            
            # 计算执行时间
            execution_time = None
            if task.started_at and task.completed_at:
                execution_time = (task.completed_at - task.started_at).total_seconds()
            
            # 准备报告数据
            report_data = {
                "task": {
                    "id": task.id,
                    "title": task.title,
                    "description": task.description,
                    "automation_type": task.automation_type,
                    "input_type": task.input_type,
                    "input_content": task.input_content,
                    "status": task.status,
                    "progress": task.progress,
                    "ai_model": task.ai_model,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None
                },
                "steps": [
                    {
                        "id": step.id,
                        "step_index": step.step_index,
                        "action": step.action,
                        "status": step.status,
                        "element_info": step.element_info,
                        "code_info": step.code_info,
                        "result": step.result,
                        "observations": step.observations,
                        "screenshot_path": step.screenshot_path,
                        "executed_at": step.executed_at.isoformat() if step.executed_at else None
                    }
                    for step in steps
                ],
                "logs": [
                    {
                        "id": log.id,
                        "level": log.level,
                        "message": log.message,
                        "details": log.details,
                        "created_at": log.created_at.isoformat() if log.created_at else None
                    }
                    for log in logs
                ],
                "screenshots": [
                    {
                        "id": screenshot.id,
                        "file_path": screenshot.file_path,
                        "file_name": screenshot.file_name,
                        "screenshot_type": screenshot.screenshot_type,
                        "has_annotations": screenshot.has_annotations,
                        "created_at": screenshot.created_at.isoformat() if screenshot.created_at else None
                    }
                    for screenshot in screenshots
                ],
                "statistics": {
                    "total_steps": total_steps,
                    "success_steps": success_steps,
                    "failed_steps": failed_steps,
                    "success_rate": (success_steps / total_steps * 100) if total_steps > 0 else 0,
                    "execution_time": execution_time
                }
            }
            
            # 生成HTML报告
            html_content = self._generate_html_report(report_data)
            
            # 保存文件
            timestamp = int(time.time())
            html_filename = f"report_{task_id}_{timestamp}.html"
            json_filename = f"report_{task_id}_{timestamp}.json"
            
            html_file_path = settings.REPORTS_DIR / html_filename
            json_file_path = settings.REPORTS_DIR / json_filename
            
            # 写入HTML文件
            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # 写入JSON文件
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            # 保存到数据库
            report = ExecutionReport(
                task_id=task_id,
                report_name=report_name,
                summary=report_data["statistics"],
                steps_data=report_data["steps"],
                screenshots=[s["file_path"] for s in report_data["screenshots"]],
                total_steps=total_steps,
                success_steps=success_steps,
                failed_steps=failed_steps,
                execution_time=execution_time,
                html_file_path=str(html_file_path),
                json_file_path=str(json_file_path)
            )
            
            self.db.add(report)
            self.db.commit()
            self.db.refresh(report)
            
            return {
                "id": report.id,
                "task_id": report.task_id,
                "report_name": report.report_name,
                "total_steps": report.total_steps,
                "success_steps": report.success_steps,
                "failed_steps": report.failed_steps,
                "execution_time": report.execution_time,
                "html_file_path": report.html_file_path,
                "created_at": report.created_at.isoformat() if report.created_at else None
            }
            
        except Exception as e:
            raise Exception(f"生成报告失败: {str(e)}")
    
    def _generate_html_report(self, report_data: Dict[str, Any]) -> str:
        """生成HTML报告"""
        template_str = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ task.title }} - 执行报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
        .title { color: #333; margin: 0 0 10px 0; }
        .subtitle { color: #666; margin: 0; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 6px; text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        .section { margin-bottom: 30px; }
        .section-title { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; margin-bottom: 20px; }
        .step { background: #f8f9fa; margin-bottom: 15px; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .step.failed { border-left-color: #dc3545; }
        .step.success { border-left-color: #28a745; }
        .step-header { font-weight: bold; margin-bottom: 10px; }
        .step-content { color: #666; }
        .log { padding: 10px; margin-bottom: 10px; border-radius: 4px; font-family: monospace; font-size: 14px; }
        .log.info { background: #d1ecf1; color: #0c5460; }
        .log.warning { background: #fff3cd; color: #856404; }
        .log.error { background: #f8d7da; color: #721c24; }
        .screenshot { margin: 10px 0; text-align: center; }
        .screenshot img { max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">{{ task.title }}</h1>
            <p class="subtitle">任务执行报告 - {{ task.automation_type|upper }}端</p>
            <p class="subtitle">生成时间: {{ current_time }}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">{{ statistics.total_steps }}</div>
                <div class="stat-label">总步骤数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ statistics.success_steps }}</div>
                <div class="stat-label">成功步骤</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ statistics.failed_steps }}</div>
                <div class="stat-label">失败步骤</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ "%.1f"|format(statistics.success_rate) }}%</div>
                <div class="stat-label">成功率</div>
            </div>
            {% if statistics.execution_time %}
            <div class="stat-card">
                <div class="stat-value">{{ "%.1f"|format(statistics.execution_time) }}s</div>
                <div class="stat-label">执行时间</div>
            </div>
            {% endif %}
        </div>
        
        <div class="section">
            <h2 class="section-title">任务信息</h2>
            <p><strong>描述:</strong> {{ task.description }}</p>
            <p><strong>输入类型:</strong> {{ task.input_type }}</p>
            <p><strong>输入内容:</strong> {{ task.input_content }}</p>
            <p><strong>AI模型:</strong> {{ task.ai_model }}</p>
            <p><strong>状态:</strong> {{ task.status }}</p>
        </div>
        
        <div class="section">
            <h2 class="section-title">执行步骤</h2>
            {% for step in steps %}
            <div class="step {{ step.status }}">
                <div class="step-header">
                    步骤 {{ step.step_index }}: {{ step.action }}
                    <span style="float: right; color: {{ 'green' if step.status == 'success' else 'red' if step.status == 'failed' else 'orange' }};">
                        {{ step.status|upper }}
                    </span>
                </div>
                <div class="step-content">
                    {% if step.observations %}
                    <p><strong>观察结果:</strong> {{ step.observations }}</p>
                    {% endif %}
                    {% if step.screenshot_path %}
                    <div class="screenshot">
                        <img src="{{ step.screenshot_path }}" alt="步骤截图" />
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="section">
            <h2 class="section-title">执行日志</h2>
            {% for log in logs %}
            <div class="log {{ log.level }}">
                <strong>[{{ log.created_at }}]</strong> {{ log.message }}
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>
        """
        
        template = Template(template_str)
        return template.render(
            **report_data,
            current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
    
    async def get_reports(
        self,
        skip: int = 0,
        limit: int = 100,
        task_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """获取报告列表"""
        try:
            query = self.db.query(ExecutionReport)
            
            if task_id:
                query = query.filter(ExecutionReport.task_id == task_id)
            
            reports = query.offset(skip).limit(limit).all()
            
            return [
                {
                    "id": report.id,
                    "task_id": report.task_id,
                    "report_name": report.report_name,
                    "total_steps": report.total_steps,
                    "success_steps": report.success_steps,
                    "failed_steps": report.failed_steps,
                    "execution_time": report.execution_time,
                    "html_file_path": report.html_file_path,
                    "created_at": report.created_at.isoformat() if report.created_at else None
                }
                for report in reports
            ]
            
        except Exception as e:
            raise Exception(f"获取报告列表失败: {str(e)}")
    
    async def get_report(self, report_id: int) -> Optional[Dict[str, Any]]:
        """获取报告详情"""
        try:
            report = self.db.query(ExecutionReport).filter(
                ExecutionReport.id == report_id
            ).first()
            
            if not report:
                return None
            
            return {
                "id": report.id,
                "task_id": report.task_id,
                "report_name": report.report_name,
                "summary": report.summary,
                "steps_data": report.steps_data,
                "screenshots": report.screenshots,
                "total_steps": report.total_steps,
                "success_steps": report.success_steps,
                "failed_steps": report.failed_steps,
                "execution_time": report.execution_time,
                "html_file_path": report.html_file_path,
                "json_file_path": report.json_file_path,
                "created_at": report.created_at.isoformat() if report.created_at else None
            }
            
        except Exception as e:
            raise Exception(f"获取报告详情失败: {str(e)}")
    
    async def delete_report(self, report_id: int) -> Dict[str, Any]:
        """删除报告"""
        try:
            report = self.db.query(ExecutionReport).filter(
                ExecutionReport.id == report_id
            ).first()
            
            if not report:
                raise Exception("报告不存在")
            
            # 删除文件
            try:
                if report.html_file_path and Path(report.html_file_path).exists():
                    Path(report.html_file_path).unlink()
                if report.json_file_path and Path(report.json_file_path).exists():
                    Path(report.json_file_path).unlink()
            except Exception as e:
                print(f"删除报告文件失败: {e}")
            
            # 删除数据库记录
            self.db.delete(report)
            self.db.commit()
            
            return {
                "success": True,
                "message": f"报告 {report.report_name} 已删除"
            }
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"删除报告失败: {str(e)}")
    
    async def get_report_data(self, report_id: int) -> Dict[str, Any]:
        """获取报告详细数据"""
        try:
            report = await self.get_report(report_id)
            if not report:
                raise Exception("报告不存在")
            
            # 如果有JSON文件，读取详细数据
            if report["json_file_path"] and Path(report["json_file_path"]).exists():
                with open(report["json_file_path"], 'r', encoding='utf-8') as f:
                    detailed_data = json.load(f)
                return detailed_data
            else:
                return report
                
        except Exception as e:
            raise Exception(f"获取报告数据失败: {str(e)}")
    
    async def get_task_screenshots(self, task_id: int) -> List[Dict[str, Any]]:
        """获取任务的所有截图"""
        try:
            screenshots = self.db.query(Screenshot).filter(
                Screenshot.task_id == task_id
            ).order_by(Screenshot.created_at).all()
            
            return [
                {
                    "id": screenshot.id,
                    "task_id": screenshot.task_id,
                    "step_id": screenshot.step_id,
                    "file_path": screenshot.file_path,
                    "file_name": screenshot.file_name,
                    "screenshot_type": screenshot.screenshot_type,
                    "automation_type": screenshot.automation_type,
                    "has_annotations": screenshot.has_annotations,
                    "created_at": screenshot.created_at.isoformat() if screenshot.created_at else None
                }
                for screenshot in screenshots
            ]
            
        except Exception as e:
            raise Exception(f"获取任务截图失败: {str(e)}")
    
    async def get_task_dom_snapshots(self, task_id: int) -> List[Dict[str, Any]]:
        """获取任务的DOM快照"""
        try:
            snapshots = self.db.query(DomSnapshot).filter(
                DomSnapshot.task_id == task_id
            ).order_by(DomSnapshot.created_at).all()
            
            return [
                {
                    "id": snapshot.id,
                    "task_id": snapshot.task_id,
                    "step_id": snapshot.step_id,
                    "screenshot_id": snapshot.screenshot_id,
                    "dom_tree": snapshot.dom_tree,
                    "viewport_info": snapshot.viewport_info,
                    "selected_element": snapshot.selected_element,
                    "automation_type": snapshot.automation_type,
                    "page_url": snapshot.page_url,
                    "app_package": snapshot.app_package,
                    "created_at": snapshot.created_at.isoformat() if snapshot.created_at else None
                }
                for snapshot in snapshots
            ]
            
        except Exception as e:
            raise Exception(f"获取DOM快照失败: {str(e)}")
