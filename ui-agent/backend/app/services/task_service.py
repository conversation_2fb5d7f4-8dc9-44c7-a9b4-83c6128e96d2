"""
任务管理服务
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.models.task import Task, TaskStep, TaskLog
from app.core.config import settings

class TaskService:
    """任务管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_task(
        self,
        title: str,
        description: str,
        automation_type: str,
        input_type: str,
        input_content: str,
        ai_model: Optional[str] = None,
        api_base: Optional[str] = None,
        temperature: Optional[float] = 0.7
    ) -> Dict[str, Any]:
        """创建新任务"""
        try:
            task = Task(
                title=title,
                description=description,
                automation_type=automation_type,
                input_type=input_type,
                input_content=input_content,
                ai_model=ai_model or settings.DEFAULT_AI_MODEL,
                api_base=api_base or settings.DEFAULT_API_BASE,
                temperature=temperature,
                status="pending"
            )
            
            self.db.add(task)
            self.db.commit()
            self.db.refresh(task)
            
            # 记录日志
            await self._add_task_log(
                task.id,
                "info",
                f"任务创建成功: {title}",
                {"automation_type": automation_type, "input_type": input_type}
            )
            
            return {
                "id": task.id,
                "title": task.title,
                "description": task.description,
                "automation_type": task.automation_type,
                "status": task.status,
                "progress": task.progress,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None
            }
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"创建任务失败: {str(e)}")
    
    async def execute_task(self, task_id: int, max_scroll_times: int = 3) -> Dict[str, Any]:
        """执行任务"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise Exception("任务不存在")
            
            # 更新任务状态
            task.status = "running"
            task.started_at = datetime.utcnow()
            task.progress = 0.0
            self.db.commit()
            
            # 记录开始执行日志
            await self._add_task_log(
                task_id,
                "info",
                "开始执行任务",
                {"max_scroll_times": max_scroll_times}
            )
            
            # 这里应该调用AI服务执行任务
            # 为了演示，暂时返回成功状态
            
            # 模拟执行过程
            import asyncio
            await asyncio.sleep(1)  # 模拟执行时间
            
            # 更新任务状态
            task.status = "completed"
            task.completed_at = datetime.utcnow()
            task.progress = 100.0
            task.result = {
                "success": True,
                "message": "任务执行完成",
                "steps_completed": 5,
                "execution_time": 30.5
            }
            self.db.commit()
            
            # 记录完成日志
            await self._add_task_log(
                task_id,
                "info",
                "任务执行完成",
                task.result
            )
            
            return {
                "success": True,
                "message": "任务执行完成",
                "task_id": task_id,
                "status": task.status,
                "progress": task.progress,
                "result": task.result
            }
            
        except Exception as e:
            # 更新任务状态为失败
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if task:
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = datetime.utcnow()
                self.db.commit()
                
                # 记录错误日志
                await self._add_task_log(
                    task_id,
                    "error",
                    f"任务执行失败: {str(e)}",
                    {"error": str(e)}
                )
            
            return {
                "success": False,
                "message": f"任务执行失败: {str(e)}",
                "task_id": task_id
            }
    
    async def get_tasks(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        automation_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取任务列表"""
        try:
            query = self.db.query(Task)
            
            if status:
                query = query.filter(Task.status == status)
            
            if automation_type:
                query = query.filter(Task.automation_type == automation_type)
            
            tasks = query.offset(skip).limit(limit).all()
            
            return [
                {
                    "id": task.id,
                    "title": task.title,
                    "description": task.description,
                    "automation_type": task.automation_type,
                    "status": task.status,
                    "progress": task.progress,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None
                }
                for task in tasks
            ]
            
        except Exception as e:
            raise Exception(f"获取任务列表失败: {str(e)}")
    
    async def get_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取任务详情"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return None
            
            return {
                "id": task.id,
                "title": task.title,
                "description": task.description,
                "automation_type": task.automation_type,
                "input_type": task.input_type,
                "input_content": task.input_content,
                "ai_model": task.ai_model,
                "api_base": task.api_base,
                "temperature": task.temperature,
                "status": task.status,
                "progress": task.progress,
                "result": task.result,
                "error_message": task.error_message,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None
            }
            
        except Exception as e:
            raise Exception(f"获取任务详情失败: {str(e)}")
    
    async def delete_task(self, task_id: int) -> Dict[str, Any]:
        """删除任务"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise Exception("任务不存在")
            
            # 删除相关的步骤和日志
            self.db.query(TaskStep).filter(TaskStep.task_id == task_id).delete()
            self.db.query(TaskLog).filter(TaskLog.task_id == task_id).delete()
            
            # 删除任务
            self.db.delete(task)
            self.db.commit()
            
            return {
                "success": True,
                "message": f"任务 {task.title} 已删除"
            }
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"删除任务失败: {str(e)}")
    
    async def get_task_steps(self, task_id: int) -> List[Dict[str, Any]]:
        """获取任务步骤"""
        try:
            steps = self.db.query(TaskStep).filter(
                TaskStep.task_id == task_id
            ).order_by(TaskStep.step_index).all()
            
            return [
                {
                    "id": step.id,
                    "task_id": step.task_id,
                    "step_index": step.step_index,
                    "action": step.action,
                    "element_info": step.element_info,
                    "code_info": step.code_info,
                    "status": step.status,
                    "result": step.result,
                    "screenshot_path": step.screenshot_path,
                    "observations": step.observations,
                    "created_at": step.created_at.isoformat() if step.created_at else None,
                    "executed_at": step.executed_at.isoformat() if step.executed_at else None
                }
                for step in steps
            ]
            
        except Exception as e:
            raise Exception(f"获取任务步骤失败: {str(e)}")
    
    async def get_task_logs(self, task_id: int) -> List[Dict[str, Any]]:
        """获取任务日志"""
        try:
            logs = self.db.query(TaskLog).filter(
                TaskLog.task_id == task_id
            ).order_by(TaskLog.created_at.desc()).all()
            
            return [
                {
                    "id": log.id,
                    "task_id": log.task_id,
                    "step_id": log.step_id,
                    "level": log.level,
                    "message": log.message,
                    "details": log.details,
                    "created_at": log.created_at.isoformat() if log.created_at else None
                }
                for log in logs
            ]
            
        except Exception as e:
            raise Exception(f"获取任务日志失败: {str(e)}")
    
    async def stop_task(self, task_id: int) -> Dict[str, Any]:
        """停止任务执行"""
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise Exception("任务不存在")
            
            if task.status != "running":
                return {
                    "success": False,
                    "message": "任务未在运行中"
                }
            
            # 更新任务状态
            task.status = "stopped"
            task.completed_at = datetime.utcnow()
            self.db.commit()
            
            # 记录停止日志
            await self._add_task_log(
                task_id,
                "warning",
                "任务被手动停止",
                {"stopped_by": "user"}
            )
            
            return {
                "success": True,
                "message": "任务已停止"
            }
            
        except Exception as e:
            raise Exception(f"停止任务失败: {str(e)}")
    
    async def _add_task_log(
        self,
        task_id: int,
        level: str,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        step_id: Optional[int] = None
    ):
        """添加任务日志"""
        try:
            log = TaskLog(
                task_id=task_id,
                step_id=step_id,
                level=level,
                message=message,
                details=details
            )
            
            self.db.add(log)
            self.db.commit()
            
        except Exception as e:
            print(f"添加任务日志失败: {e}")
            self.db.rollback()
