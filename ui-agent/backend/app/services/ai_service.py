"""
AI执行服务
"""

import json
import asyncio
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from pathlib import Path

from app.core.config import settings

class AIService:
    """AI执行服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.config = {
            "model_name": settings.DEFAULT_AI_MODEL,
            "api_base": settings.DEFAULT_API_BASE,
            "temperature": settings.DEFAULT_TEMPERATURE
        }
    
    async def process_complex_ai_exec_web(
        self,
        desc: str,
        last_result: str = "",
        last_step_result: str = "",
        max_scroll_times: int = 3
    ) -> Dict[str, Any]:
        """Web端AI执行"""
        try:
            print(f"Web端AI执行: {desc}")

            # 导入Web服务
            from app.services.web_service import WebService
            web_service = WebService(self.db)

            # 获取DOM树和页面截图
            dom_data = await web_service.get_dom_tree_and_page_screenshot(ai_exec=True)
            if "error" in dom_data:
                return json.dumps({
                    "error": f"获取页面信息失败: {dom_data['error']}"
                }, ensure_ascii=False)

            # 加载提示词模板
            prompt_template = self._load_prompt_template("web")

            # 格式化提示词
            formatted_prompt = self._format_prompt(
                template=prompt_template,
                desc=desc,
                viewport=f"{dom_data['viewport']['width']}x{dom_data['viewport']['height']}",
                is_scrollable=dom_data['is_scrollable'],
                max_scroll_times=max_scroll_times,
                last_result=last_result,
                last_step_result=last_step_result
            )

            # 调用大模型进行分析
            ai_response = await self._call_ai_model(
                prompt=formatted_prompt,
                image_base64=dom_data['base64_image'],
                dom_tree=dom_data['dom_tree']
            )

            if "error" in ai_response:
                return json.dumps(ai_response, ensure_ascii=False)

            # 解析AI响应并执行操作
            execution_result = await self._execute_ai_action_web(ai_response, web_service)

            return json.dumps(execution_result, ensure_ascii=False)

        except Exception as e:
            return json.dumps({
                "error": f"Web端AI执行失败: {str(e)}"
            }, ensure_ascii=False)
    
    async def process_complex_ai_exec_android(
        self,
        desc: str,
        last_result: str = "",
        last_step_result: str = "",
        max_scroll_times: int = 3
    ) -> Dict[str, Any]:
        """Android端AI执行"""
        try:
            print(f"Android端AI执行: {desc}")

            # 导入Android服务
            from app.services.android_service import AndroidService
            android_service = AndroidService(self.db)

            # 获取DOM树和页面截图
            dom_data = await android_service.get_dom_tree_and_page_screenshot()
            if "error" in dom_data:
                return json.dumps({
                    "error": f"获取设备信息失败: {dom_data['error']}"
                }, ensure_ascii=False)

            # 加载提示词模板
            prompt_template = self._load_prompt_template("android")

            # 格式化提示词
            formatted_prompt = self._format_prompt(
                template=prompt_template,
                desc=desc,
                viewport=f"{dom_data['viewport']['width']}x{dom_data['viewport']['height']}",
                is_scrollable=dom_data['is_scrollable'],
                max_scroll_times=max_scroll_times,
                last_result=last_result,
                last_step_result=last_step_result
            )

            # 调用大模型进行分析
            ai_response = await self._call_ai_model(
                prompt=formatted_prompt,
                image_base64=dom_data['base64_image'],
                dom_tree=dom_data['dom_tree']
            )

            if "error" in ai_response:
                return json.dumps(ai_response, ensure_ascii=False)

            # 解析AI响应并执行操作
            execution_result = await self._execute_ai_action_android(ai_response, android_service)

            return json.dumps(execution_result, ensure_ascii=False)

        except Exception as e:
            return json.dumps({
                "error": f"Android端AI执行失败: {str(e)}"
            }, ensure_ascii=False)
    
    async def set_config(
        self,
        model_name: str,
        api_base: str,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """设置AI配置"""
        try:
            self.config.update({
                "model_name": model_name,
                "api_base": api_base,
                "temperature": temperature
            })
            
            return {
                "success": True,
                "message": "AI配置设置成功",
                "config": self.config
            }
            
        except Exception as e:
            return {"success": False, "message": f"设置AI配置失败: {str(e)}"}
    
    async def get_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return {
            "success": True,
            "config": self.config
        }
    
    async def test_connection(
        self,
        model_name: str,
        api_base: str
    ) -> Dict[str, Any]:
        """测试AI连接"""
        try:
            # 这里应该实现真实的连接测试
            # 暂时模拟测试结果
            
            await asyncio.sleep(1)  # 模拟网络请求
            
            return {
                "success": True,
                "message": "AI连接测试成功",
                "model_name": model_name,
                "api_base": api_base,
                "response_time": "1.2s"
            }
            
        except Exception as e:
            return {"success": False, "message": f"AI连接测试失败: {str(e)}"}
    
    def _load_prompt_template(self, automation_type: str) -> str:
        """加载提示词模板"""
        try:
            prompt_file = settings.PROJECT_ROOT / "prompt" / f"complex_ai_exec_{automation_type}.md"
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return "默认提示词模板"
        except Exception as e:
            print(f"加载提示词模板失败: {e}")
            return "默认提示词模板"
    
    def _format_prompt(
        self,
        template: str,
        desc: str,
        viewport: str,
        is_scrollable: bool,
        max_scroll_times: int,
        last_result: str,
        last_step_result: str
    ) -> str:
        """格式化提示词"""
        try:
            # 替换模板中的占位符
            formatted_prompt = template.replace("{{desc}}", desc)
            formatted_prompt = formatted_prompt.replace("{{viewport}}", viewport)
            formatted_prompt = formatted_prompt.replace("{{is_scrollable}}", str(is_scrollable))
            formatted_prompt = formatted_prompt.replace("{{max_scroll_times}}", str(max_scroll_times))
            formatted_prompt = formatted_prompt.replace("{{last_result}}", last_result)
            formatted_prompt = formatted_prompt.replace("{{last_step_result}}", last_step_result)
            
            return formatted_prompt

        except Exception as e:
            print(f"格式化提示词失败: {e}")
            return template

    async def _call_ai_model(self, prompt: str, image_base64: str, dom_tree: Dict[str, Any]) -> Dict[str, Any]:
        """调用AI模型进行分析"""
        try:
            # 这里实现真实的AI模型调用
            # 目前先返回模拟结果

            # 模拟AI分析结果
            result = {
                "content": prompt[:100] + "...",  # 截取部分提示词作为内容
                "step_list": [
                    {
                        "step_index": 1,
                        "action": "分析页面元素"
                    },
                    {
                        "step_index": 2,
                        "action": "执行用户指令"
                    }
                ],
                "next_executed_step": {
                    "step_index": 1,
                    "code_info": {
                        "type": "Action",
                        "type_thought": "需要分析当前页面的可交互元素",
                        "code_thought": "通过DOM树分析找到合适的操作目标",
                        "code_generate": "self.clickByCoordinate(100, 200)"
                    },
                    "element_info": {
                        "find_status": 1,
                        "thought": "在页面中找到了目标元素",
                        "seq_index": 5,
                        "seq_index_list": [5, 6, 7, 8]
                    },
                    "observations": "页面分析完成，准备执行操作"
                },
                "test_progress": {
                    "completed_steps": [],
                    "remaining_steps": ["分析页面元素", "执行用户指令"]
                },
                "result": -1  # 表示需要继续执行
            }

            return result

        except Exception as e:
            return {"error": f"AI模型调用失败: {str(e)}"}

    async def _execute_ai_action_web(self, ai_response: Dict[str, Any], web_service) -> Dict[str, Any]:
        """执行Web端AI操作"""
        try:
            next_step = ai_response.get("next_executed_step", {})
            code_info = next_step.get("code_info", {})
            element_info = next_step.get("element_info", {})

            # 解析代码生成的操作
            code_generate = code_info.get("code_generate", "")

            if "clickByCoordinate" in code_generate:
                # 提取坐标或seq_index
                if "(" in code_generate and ")" in code_generate:
                    params = code_generate.split("(")[1].split(")")[0]
                    if "," in params:
                        # 坐标点击
                        x, y = map(int, params.split(","))
                        result = await web_service.click_by_coordinate(x, y)
                    else:
                        # seq_index点击
                        seq_index = int(params)
                        result = await web_service.click_by_seq_index(seq_index)

                    # 更新AI响应中的观察结果
                    if result["success"]:
                        ai_response["next_executed_step"]["observations"] = f"成功执行点击操作: {result['message']}"
                    else:
                        ai_response["next_executed_step"]["observations"] = f"点击操作失败: {result['message']}"

            elif "inputText" in code_generate:
                # 文本输入操作
                # 这里需要解析输入的文本和目标元素
                seq_index = element_info.get("seq_index")
                if seq_index:
                    # 这里应该从AI响应中提取要输入的文本
                    text = "示例文本"  # 实际应该从AI响应中解析
                    result = await web_service.input_text(text, seq_index=seq_index)

                    if result["success"]:
                        ai_response["next_executed_step"]["observations"] = f"成功输入文本: {text}"
                    else:
                        ai_response["next_executed_step"]["observations"] = f"输入文本失败: {result['message']}"

            elif "scroll" in code_generate.lower():
                # 滚动操作
                direction = "down"  # 默认向下滚动
                if "up" in code_generate.lower():
                    direction = "up"
                elif "left" in code_generate.lower():
                    direction = "left"
                elif "right" in code_generate.lower():
                    direction = "right"

                result = await web_service.scroll_page(direction=direction)

                if result["success"]:
                    ai_response["next_executed_step"]["observations"] = f"成功执行滚动操作: {direction}"
                else:
                    ai_response["next_executed_step"]["observations"] = f"滚动操作失败: {result['message']}"

            # 更新执行状态
            ai_response["result"] = 1 if "成功" in ai_response["next_executed_step"]["observations"] else 0

            return ai_response

        except Exception as e:
            ai_response["next_executed_step"]["observations"] = f"执行操作失败: {str(e)}"
            ai_response["result"] = 0
            return ai_response

    async def _execute_ai_action_android(self, ai_response: Dict[str, Any], android_service) -> Dict[str, Any]:
        """执行Android端AI操作"""
        try:
            next_step = ai_response.get("next_executed_step", {})
            code_info = next_step.get("code_info", {})
            element_info = next_step.get("element_info", {})

            # 解析代码生成的操作
            code_generate = code_info.get("code_generate", "")

            if "clickByCoordinate" in code_generate:
                # 提取坐标或seq_index
                if "(" in code_generate and ")" in code_generate:
                    params = code_generate.split("(")[1].split(")")[0]
                    if "," in params:
                        # 坐标点击
                        x, y = map(int, params.split(","))
                        result = await android_service.click_by_coordinate(x, y)
                    else:
                        # seq_index点击
                        seq_index = int(params)
                        result = await android_service.click_by_seq_index(seq_index)

                    # 更新AI响应中的观察结果
                    if result["success"]:
                        ai_response["next_executed_step"]["observations"] = f"成功执行点击操作: {result['message']}"
                    else:
                        ai_response["next_executed_step"]["observations"] = f"点击操作失败: {result['message']}"

            elif "inputText" in code_generate:
                # 文本输入操作
                seq_index = element_info.get("seq_index")
                if seq_index:
                    text = "示例文本"  # 实际应该从AI响应中解析
                    result = await android_service.input_text(text, seq_index=seq_index)

                    if result["success"]:
                        ai_response["next_executed_step"]["observations"] = f"成功输入文本: {text}"
                    else:
                        ai_response["next_executed_step"]["observations"] = f"输入文本失败: {result['message']}"

            elif "scroll" in code_generate.lower():
                # 滚动操作
                direction = "down"
                if "up" in code_generate.lower():
                    direction = "up"
                elif "left" in code_generate.lower():
                    direction = "left"
                elif "right" in code_generate.lower():
                    direction = "right"

                result = await android_service.scroll_page(direction=direction)

                if result["success"]:
                    ai_response["next_executed_step"]["observations"] = f"成功执行滚动操作: {direction}"
                else:
                    ai_response["next_executed_step"]["observations"] = f"滚动操作失败: {result['message']}"

            # 更新执行状态
            ai_response["result"] = 1 if "成功" in ai_response["next_executed_step"]["observations"] else 0

            return ai_response

        except Exception as e:
            ai_response["next_executed_step"]["observations"] = f"执行操作失败: {str(e)}"
            ai_response["result"] = 0
            return ai_response
