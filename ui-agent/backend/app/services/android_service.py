"""
Android端服务
"""

import os
import subprocess
import asyncio
import requests
import json
import time
import base64
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import xml.etree.ElementTree as ET

from app.models.device import AndroidDevice, AppPackage
from app.core.config import settings

class AndroidService:
    """Android端服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.current_device = None
        
    async def get_devices(self) -> List[Dict[str, Any]]:
        """获取Android设备列表"""
        try:
            # 执行adb devices命令
            result = subprocess.run(
                ["adb", "devices"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            devices = []
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行标题
                for line in lines:
                    if line.strip() and '\t' in line:
                        device_id, status = line.split('\t')
                        if status == 'device':
                            device_info = await self._get_device_info(device_id)
                            devices.append(device_info)
            
            # 更新数据库中的设备信息
            await self._update_devices_in_db(devices)
            
            return devices
            
        except Exception as e:
            print(f"获取设备列表失败: {e}")
            return []
    
    async def _get_device_info(self, device_id: str) -> Dict[str, Any]:
        """获取设备详细信息"""
        try:
            # 获取设备属性
            props = {}
            prop_commands = {
                'brand': 'ro.product.brand',
                'model': 'ro.product.model',
                'android_version': 'ro.build.version.release'
            }
            
            for key, prop in prop_commands.items():
                result = subprocess.run(
                    ["adb", "-s", device_id, "shell", "getprop", prop],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    props[key] = result.stdout.strip()
            
            # 获取屏幕分辨率
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "wm", "size"],
                capture_output=True,
                text=True,
                timeout=5
            )
            screen_resolution = "unknown"
            if result.returncode == 0:
                output = result.stdout.strip()
                if "Physical size:" in output:
                    screen_resolution = output.split("Physical size: ")[1]
            
            return {
                "device_id": device_id,
                "device_name": f"{props.get('brand', 'Unknown')} {props.get('model', 'Device')}",
                "brand": props.get('brand'),
                "model": props.get('model'),
                "android_version": props.get('android_version'),
                "screen_resolution": screen_resolution,
                "is_connected": True,
                "is_active": False
            }
            
        except Exception as e:
            print(f"获取设备信息失败: {e}")
            return {
                "device_id": device_id,
                "device_name": f"Device {device_id}",
                "is_connected": True,
                "is_active": False
            }
    
    async def _update_devices_in_db(self, devices: List[Dict[str, Any]]):
        """更新数据库中的设备信息"""
        try:
            # 获取当前数据库中的设备
            db_devices = self.db.query(AndroidDevice).all()
            db_device_ids = {device.device_id for device in db_devices}
            
            # 更新或创建设备记录
            for device_info in devices:
                device_id = device_info["device_id"]
                
                if device_id in db_device_ids:
                    # 更新现有设备
                    device = self.db.query(AndroidDevice).filter(
                        AndroidDevice.device_id == device_id
                    ).first()
                    if device:
                        device.device_name = device_info.get("device_name")
                        device.brand = device_info.get("brand")
                        device.model = device_info.get("model")
                        device.android_version = device_info.get("android_version")
                        device.screen_resolution = device_info.get("screen_resolution")
                        device.is_connected = True
                else:
                    # 创建新设备
                    device = AndroidDevice(
                        device_id=device_id,
                        device_name=device_info.get("device_name"),
                        brand=device_info.get("brand"),
                        model=device_info.get("model"),
                        android_version=device_info.get("android_version"),
                        screen_resolution=device_info.get("screen_resolution"),
                        is_connected=True
                    )
                    self.db.add(device)
            
            # 标记未连接的设备
            current_device_ids = {device["device_id"] for device in devices}
            for device in db_devices:
                if device.device_id not in current_device_ids:
                    device.is_connected = False
            
            self.db.commit()
            
        except Exception as e:
            print(f"更新设备数据库失败: {e}")
            self.db.rollback()
    
    async def select_device(self, device_id: str) -> Dict[str, Any]:
        """选择当前活动设备"""
        try:
            # 取消所有设备的活动状态
            self.db.query(AndroidDevice).update({"is_active": False})
            
            # 设置指定设备为活动状态
            device = self.db.query(AndroidDevice).filter(
                AndroidDevice.device_id == device_id
            ).first()
            
            if not device:
                return {"success": False, "message": "设备不存在"}
            
            if not device.is_connected:
                return {"success": False, "message": "设备未连接"}
            
            device.is_active = True
            self.db.commit()
            
            self.current_device = device
            
            return {
                "success": True,
                "message": f"已选择设备: {device.device_name}",
                "device": {
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "brand": device.brand,
                    "model": device.model
                }
            }
            
        except Exception as e:
            self.db.rollback()
            return {"success": False, "message": f"选择设备失败: {str(e)}"}
    
    async def refresh_devices(self) -> List[Dict[str, Any]]:
        """刷新设备列表"""
        return await self.get_devices()
    
    async def install_apk(self, download_url: str, device_id: Optional[str] = None) -> Dict[str, Any]:
        """下载并安装APK"""
        try:
            # 如果没有指定设备，使用当前活动设备
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id
            
            # 下载APK文件
            download_dir = settings.OUTPUT_DIR / "downloads"
            download_dir.mkdir(exist_ok=True)
            
            filename = download_url.split('/')[-1]
            if not filename.endswith('.apk'):
                filename += '.apk'
            
            file_path = download_dir / filename
            
            # 下载文件
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 安装APK
            result = subprocess.run(
                ["adb", "-s", device_id, "install", str(file_path)],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                # 记录到数据库
                app_package = AppPackage(
                    download_url=download_url,
                    file_path=str(file_path),
                    file_size=file_path.stat().st_size,
                    is_downloaded=True,
                    is_installed=True,
                    device_id=device_id
                )
                self.db.add(app_package)
                self.db.commit()
                
                return {
                    "success": True,
                    "message": "APK安装成功",
                    "file_path": str(file_path)
                }
            else:
                return {
                    "success": False,
                    "message": f"APK安装失败: {result.stderr}"
                }
                
        except Exception as e:
            return {"success": False, "message": f"安装APK失败: {str(e)}"}
    
    async def execute_adb_command(self, command: str, device_id: Optional[str] = None) -> Dict[str, Any]:
        """执行ADB命令"""
        try:
            # 如果没有指定设备，使用当前活动设备
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id
            
            # 构建完整的ADB命令
            full_command = ["adb", "-s", device_id] + command.split()
            
            # 执行命令
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            return {
                "success": result.returncode == 0,
                "command": " ".join(full_command),
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
        except Exception as e:
            return {"success": False, "message": f"执行ADB命令失败: {str(e)}"}
    
    async def take_screenshot(self, device_id: str) -> Dict[str, Any]:
        """获取设备截图"""
        try:
            # 生成截图文件名
            timestamp = int(asyncio.get_event_loop().time())
            filename = f"android_screenshot_{timestamp}.png"
            file_path = settings.SCREENSHOTS_DIR / filename
            
            # 执行截图命令
            result = subprocess.run(
                ["adb", "-s", device_id, "exec-out", "screencap", "-p"],
                capture_output=True,
                timeout=10
            )
            
            if result.returncode == 0:
                with open(file_path, 'wb') as f:
                    f.write(result.stdout)
                
                return {
                    "success": True,
                    "file_path": str(file_path),
                    "url": f"/output/screenshots/{filename}"
                }
            else:
                return {"success": False, "message": "截图失败"}
                
        except Exception as e:
            return {"success": False, "message": f"获取截图失败: {str(e)}"}
    
    async def get_device_status(self, device_id: str) -> Dict[str, Any]:
        """获取设备状态"""
        try:
            device = self.db.query(AndroidDevice).filter(
                AndroidDevice.device_id == device_id
            ).first()
            
            if not device:
                return {"success": False, "message": "设备不存在"}
            
            return {
                "success": True,
                "device": {
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "is_connected": device.is_connected,
                    "is_active": device.is_active,
                    "brand": device.brand,
                    "model": device.model,
                    "android_version": device.android_version,
                    "screen_resolution": device.screen_resolution
                }
            }
            
        except Exception as e:
            return {"success": False, "message": f"获取设备状态失败: {str(e)}"}

    # Android端操作方法
    async def get_dom_tree_and_page_screenshot(self, device_id: Optional[str] = None) -> Dict[str, Any]:
        """获取Android设备的DOM树和截图"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"error": "没有活动设备"}
                device_id = active_device.device_id

            # 获取UI层次结构
            ui_dump_result = subprocess.run(
                ["adb", "-s", device_id, "shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if ui_dump_result.returncode != 0:
                return {"error": f"获取UI层次结构失败: {ui_dump_result.stderr}"}

            # 拉取UI层次结构文件
            pull_result = subprocess.run(
                ["adb", "-s", device_id, "pull", "/sdcard/ui_dump.xml", "/tmp/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if pull_result.returncode != 0:
                return {"error": f"拉取UI文件失败: {pull_result.stderr}"}

            # 解析XML文件
            dom_tree = self._parse_android_ui_xml("/tmp/ui_dump.xml")

            # 获取屏幕截图
            screenshot_result = await self.take_screenshot(device_id)
            if not screenshot_result["success"]:
                return {"error": f"获取截图失败: {screenshot_result['message']}"}

            # 在截图上绘制边界框
            annotated_image = self._draw_android_bounding_boxes(
                screenshot_result["file_path"],
                dom_tree
            )

            # 保存标注后的图像
            timestamp = int(time.time())
            filename = f"android_annotated_{timestamp}.png"
            annotated_file_path = settings.SCREENSHOTS_DIR / filename
            annotated_image.save(annotated_file_path)

            # 编码为base64
            base64_image = self._encode_image_to_base64(str(annotated_file_path))

            # 获取屏幕信息
            screen_info = self._get_screen_info(device_id)

            return {
                "base64_image": base64_image,
                "viewport": screen_info,
                "is_scrollable": self._check_if_scrollable(dom_tree),
                "dom_tree": dom_tree,
                "screenshot_path": str(annotated_file_path)
            }

        except Exception as e:
            return {"error": f"获取Android DOM树和截图失败: {str(e)}"}

    def _parse_android_ui_xml(self, xml_file_path: str) -> Dict[str, Any]:
        """解析Android UI XML文件"""
        try:
            tree = ET.parse(xml_file_path)
            root = tree.getroot()

            # 递归解析节点
            dom_tree = self._parse_android_node(root, 0)

            # 添加全局顺序索引
            self._add_sequential_index(dom_tree)

            return dom_tree

        except Exception as e:
            print(f"解析Android UI XML失败: {e}")
            return {}

    def _parse_android_node(self, node, index: int) -> Dict[str, Any]:
        """递归解析Android UI节点"""
        result = {
            "index": index,
            "tag": node.tag,
            "type": "element",
            "payload": {}
        }

        # 解析属性
        for attr_name, attr_value in node.attrib.items():
            if attr_name == "bounds":
                # 解析bounds属性，格式如 "[0,0][1080,1920]"
                bounds = self._parse_bounds(attr_value)
                if bounds:
                    result["payload"]["rect"] = bounds
            elif attr_name in ["text", "content-desc", "resource-id", "class", "package"]:
                if attr_value:
                    result["payload"][attr_name] = attr_value
            elif attr_name in ["clickable", "scrollable", "focusable", "enabled", "selected", "checked"]:
                result["payload"][attr_name] = attr_value.lower() == "true"

        # 处理子节点
        if len(node) > 0:
            children = []
            for i, child in enumerate(node):
                child_result = self._parse_android_node(child, i)
                if child_result:
                    children.append(child_result)
            if children:
                result["children"] = children

        return result

    def _parse_bounds(self, bounds_str: str) -> Optional[Dict[str, int]]:
        """解析bounds字符串，如 '[0,0][1080,1920]'"""
        try:
            import re
            pattern = r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]'
            match = re.match(pattern, bounds_str)
            if match:
                x1, y1, x2, y2 = map(int, match.groups())
                return {
                    "x": x1,
                    "y": y1,
                    "width": x2 - x1,
                    "height": y2 - y1
                }
        except Exception as e:
            print(f"解析bounds失败: {e}")
        return None

    def _add_sequential_index(self, node: Dict[str, Any], index_map: Optional[Dict[str, int]] = None):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}

        node["seq_index"] = index_map["current"]
        index_map["current"] += 1

        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)

    def _draw_android_bounding_boxes(self, screenshot_path: str, dom_tree: Dict[str, Any]):
        """在Android截图上绘制边界框"""
        try:
            # 打开截图
            img = Image.open(screenshot_path)
            draw = ImageDraw.Draw(img, 'RGBA')

            # 尝试加载字体
            try:
                font = ImageFont.truetype("Arial", 14)
            except:
                font = ImageFont.load_default()

            # 重置随机种子
            import random
            random.seed(42)

            # 递归绘制边界框
            self._draw_android_node_bounding_box(draw, dom_tree, img.size, font)

            return img

        except Exception as e:
            print(f"绘制Android边界框失败: {e}")
            return Image.open(screenshot_path)

    def _draw_android_node_bounding_box(self, draw, node, image_size, font):
        """递归地为Android节点绘制边界框"""
        if "payload" in node and "rect" in node["payload"]:
            rect = node["payload"]["rect"]

            x1 = rect["x"]
            y1 = rect["y"]
            x2 = x1 + rect["width"]
            y2 = y1 + rect["height"]

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框
            draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)

            # 构建标签文本
            label_text = str(seq_index)

            # 添加元素信息到标签
            if "text" in node["payload"] and node["payload"]["text"]:
                text = node["payload"]["text"][:10]  # 限制长度
                label_text += f":{text}"
            elif "content-desc" in node["payload"] and node["payload"]["content-desc"]:
                desc = node["payload"]["content-desc"][:10]
                label_text += f":{desc}"

            # 绘制标签
            if label_text:
                try:
                    # 计算文本大小
                    try:
                        bbox = draw.textbbox((0, 0), label_text, font=font)
                        text_width = bbox[2] - bbox[0] + 6
                        text_height = bbox[3] - bbox[1] + 4
                    except:
                        text_width = len(label_text) * 8 + 6
                        text_height = 16

                    # 确保标签不会超出屏幕边界
                    label_x = x1
                    if label_x + text_width > image_size[0]:
                        label_x = image_size[0] - text_width
                    if label_x < 0:
                        label_x = 0

                    label_y = y1 - text_height
                    if label_y < 0:
                        label_y = y2
                        if label_y + text_height > image_size[1]:
                            label_y = image_size[1] - text_height

                    # 绘制标签背景
                    bg_color = border_color + (200,)
                    draw.rectangle(
                        [label_x, label_y, label_x + text_width, label_y + text_height],
                        fill=bg_color
                    )

                    # 绘制文本
                    text_color = self._get_contrasting_text_color(border_color)
                    draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)

                except Exception as e:
                    print(f"绘制Android标签失败: {e}")

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_android_node_bounding_box(draw, child, image_size, font)

    def _generate_random_color(self, seq_index):
        """根据seq_index生成随机但一致的颜色"""
        import random
        import colorsys

        random.seed(hash(str(seq_index)) % 2147483647)

        hue = random.random()
        saturation = 0.7 + random.random() * 0.3
        lightness = 0.4 + random.random() * 0.3

        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        return tuple(int(c * 255) for c in rgb)

    def _get_contrasting_text_color(self, bg_color):
        """根据背景颜色获取对比度高的文本颜色"""
        r, g, b = bg_color[:3]
        brightness = (r * 299 + g * 587 + b * 114) / 1000
        return (255, 255, 255) if brightness < 128 else (0, 0, 0)

    def _get_screen_info(self, device_id: str) -> Dict[str, Any]:
        """获取屏幕信息"""
        try:
            # 获取屏幕分辨率
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "wm", "size"],
                capture_output=True,
                text=True,
                timeout=5
            )

            width, height = 1080, 1920  # 默认值
            if result.returncode == 0:
                output = result.stdout.strip()
                if "Physical size:" in output:
                    size_str = output.split("Physical size: ")[1]
                    if "x" in size_str:
                        width, height = map(int, size_str.split("x"))

            return {
                "width": width,
                "height": height,
                "density": self._get_screen_density(device_id)
            }

        except Exception as e:
            print(f"获取屏幕信息失败: {e}")
            return {"width": 1080, "height": 1920, "density": 2.0}

    def _get_screen_density(self, device_id: str) -> float:
        """获取屏幕密度"""
        try:
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "wm", "density"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                output = result.stdout.strip()
                if "Physical density:" in output:
                    density_str = output.split("Physical density: ")[1]
                    return float(density_str)

            return 2.0  # 默认密度

        except Exception as e:
            print(f"获取屏幕密度失败: {e}")
            return 2.0

    def _check_if_scrollable(self, dom_tree: Dict[str, Any]) -> bool:
        """检查页面是否可滚动"""
        def check_node(node):
            if "payload" in node and node["payload"].get("scrollable", False):
                return True
            if "children" in node:
                for child in node["children"]:
                    if check_node(child):
                        return True
            return False

        return check_node(dom_tree)

    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    # Android端操作方法
    async def click_by_coordinate(self, x: int, y: int, device_id: Optional[str] = None) -> Dict[str, Any]:
        """通过坐标点击"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 执行点击命令
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "input", "tap", str(x), str(y)],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"成功点击坐标 ({x}, {y})",
                    "action": "click",
                    "coordinates": {"x": x, "y": y}
                }
            else:
                return {"success": False, "message": f"点击失败: {result.stderr}"}

        except Exception as e:
            return {"success": False, "message": f"点击失败: {str(e)}"}

    async def click_by_seq_index(self, seq_index: int, device_id: Optional[str] = None) -> Dict[str, Any]:
        """通过seq_index点击元素"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 获取DOM树
            dom_data = await self.get_dom_tree_and_page_screenshot(device_id)
            if "error" in dom_data:
                return {"success": False, "message": dom_data["error"]}

            # 查找指定seq_index的元素
            element_info = self._find_element_by_seq_index(dom_data["dom_tree"], seq_index)
            if not element_info:
                return {"success": False, "message": f"未找到seq_index为{seq_index}的元素"}

            # 获取元素中心坐标
            rect = element_info.get("payload", {}).get("rect", {})
            if not rect:
                return {"success": False, "message": "元素没有位置信息"}

            center_x = rect["x"] + rect["width"] // 2
            center_y = rect["y"] + rect["height"] // 2

            # 点击元素中心
            click_result = await self.click_by_coordinate(center_x, center_y, device_id)
            if click_result["success"]:
                click_result.update({
                    "seq_index": seq_index,
                    "element_info": element_info
                })

            return click_result

        except Exception as e:
            return {"success": False, "message": f"点击失败: {str(e)}"}

    async def input_text(self, text: str, seq_index: Optional[int] = None, x: Optional[int] = None, y: Optional[int] = None, device_id: Optional[str] = None) -> Dict[str, Any]:
        """输入文本"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 如果提供了seq_index，先点击该元素
            if seq_index is not None:
                click_result = await self.click_by_seq_index(seq_index, device_id)
                if not click_result["success"]:
                    return click_result
            elif x is not None and y is not None:
                # 如果提供了坐标，先点击该位置
                click_result = await self.click_by_coordinate(x, y, device_id)
                if not click_result["success"]:
                    return click_result

            # 清空当前输入框内容
            clear_result = subprocess.run(
                ["adb", "-s", device_id, "shell", "input", "keyevent", "KEYCODE_CTRL_A"],
                capture_output=True,
                text=True,
                timeout=5
            )

            # 输入文本
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "input", "text", text],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"成功输入文本: {text}",
                    "action": "input",
                    "text": text,
                    "seq_index": seq_index,
                    "coordinates": {"x": x, "y": y} if x and y else None
                }
            else:
                return {"success": False, "message": f"输入文本失败: {result.stderr}"}

        except Exception as e:
            return {"success": False, "message": f"输入文本失败: {str(e)}"}

    async def scroll_page(self, direction: str = "down", distance: int = 500, device_id: Optional[str] = None) -> Dict[str, Any]:
        """滚动页面"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 获取屏幕信息
            screen_info = self._get_screen_info(device_id)
            center_x = screen_info["width"] // 2
            center_y = screen_info["height"] // 2

            # 计算滚动起点和终点
            if direction == "down":
                start_x, start_y = center_x, center_y + distance // 2
                end_x, end_y = center_x, center_y - distance // 2
            elif direction == "up":
                start_x, start_y = center_x, center_y - distance // 2
                end_x, end_y = center_x, center_y + distance // 2
            elif direction == "left":
                start_x, start_y = center_x + distance // 2, center_y
                end_x, end_y = center_x - distance // 2, center_y
            elif direction == "right":
                start_x, start_y = center_x - distance // 2, center_y
                end_x, end_y = center_x + distance // 2, center_y
            else:
                return {"success": False, "message": f"不支持的滚动方向: {direction}"}

            # 执行滑动命令
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "input", "swipe",
                 str(start_x), str(start_y), str(end_x), str(end_y), "300"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                # 等待滚动完成
                await asyncio.sleep(0.5)

                return {
                    "success": True,
                    "message": f"成功向{direction}滚动{distance}像素",
                    "action": "scroll",
                    "direction": direction,
                    "distance": distance
                }
            else:
                return {"success": False, "message": f"滚动失败: {result.stderr}"}

        except Exception as e:
            return {"success": False, "message": f"滚动失败: {str(e)}"}

    async def press_key(self, key: str, device_id: Optional[str] = None) -> Dict[str, Any]:
        """按键操作"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id

            # 映射按键名称到Android键码
            key_mapping = {
                "back": "KEYCODE_BACK",
                "home": "KEYCODE_HOME",
                "menu": "KEYCODE_MENU",
                "enter": "KEYCODE_ENTER",
                "delete": "KEYCODE_DEL",
                "backspace": "KEYCODE_DEL",
                "space": "KEYCODE_SPACE",
                "tab": "KEYCODE_TAB",
                "escape": "KEYCODE_ESCAPE",
                "up": "KEYCODE_DPAD_UP",
                "down": "KEYCODE_DPAD_DOWN",
                "left": "KEYCODE_DPAD_LEFT",
                "right": "KEYCODE_DPAD_RIGHT"
            }

            keycode = key_mapping.get(key.lower(), key)

            # 执行按键命令
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "input", "keyevent", keycode],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"成功按下按键: {key}",
                    "action": "press_key",
                    "key": key,
                    "keycode": keycode
                }
            else:
                return {"success": False, "message": f"按键操作失败: {result.stderr}"}

        except Exception as e:
            return {"success": False, "message": f"按键操作失败: {str(e)}"}

    def _find_element_by_seq_index(self, node: Dict[str, Any], target_seq_index: int) -> Optional[Dict[str, Any]]:
        """在DOM树中查找指定seq_index的元素"""
        if node.get("seq_index") == target_seq_index:
            return node

        if "children" in node and node["children"]:
            for child in node["children"]:
                result = self._find_element_by_seq_index(child, target_seq_index)
                if result:
                    return result

        return None

    # 实时执行需要的方法
    async def get_current_device_status(self) -> Dict[str, Any]:
        """获取当前活动设备状态"""
        try:
            active_device = self.db.query(AndroidDevice).filter(
                AndroidDevice.is_active == True
            ).first()

            if not active_device:
                return {
                    "is_connected": False,
                    "message": "没有活动设备"
                }

            return {
                "is_connected": active_device.is_connected,
                "device_id": active_device.device_id,
                "device_name": active_device.device_name,
                "message": f"当前设备: {active_device.device_name}"
            }

        except Exception as e:
            return {
                "is_connected": False,
                "error": f"获取设备状态失败: {str(e)}"
            }

    async def execute_scroll_action(self, exec_code: str) -> Dict[str, Any]:
        """执行滚动操作"""
        try:
            # 解析执行代码中的滚动参数
            # 这里需要根据AI生成的代码来执行相应的滚动操作
            # 示例代码可能是: scroll_page("down", 300)

            # 简单的代码解析（实际应该更复杂）
            if "scroll_page" in exec_code:
                if "down" in exec_code:
                    direction = "down"
                elif "up" in exec_code:
                    direction = "up"
                else:
                    direction = "down"

                # 提取距离参数（如果有）
                import re
                distance_match = re.search(r'(\d+)', exec_code)
                distance = int(distance_match.group(1)) if distance_match else 500

                result = await self.scroll_page(direction, distance)
                return result
            else:
                return {
                    "success": False,
                    "error_message": "不支持的滚动操作代码"
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"执行滚动操作失败: {str(e)}"
            }

    async def execute_action(self, exec_code: str) -> Dict[str, Any]:
        """执行具体的操作动作"""
        try:
            # 解析并执行AI生成的操作代码
            # 代码可能包含点击、输入文本等操作

            if "click_by_seq_index" in exec_code or "clickByCoordinate" in exec_code:
                # 提取seq_index
                import re
                seq_match = re.search(r'(\d+)', exec_code)
                if seq_match:
                    seq_index = int(seq_match.group(1))
                    result = await self.click_by_seq_index(seq_index)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析点击操作的seq_index"
                    }

            elif "input_text" in exec_code:
                # 提取文本和seq_index
                import re
                # 匹配类似 input_text_by_coordinate(seq_index, "text")
                match = re.search(r'input_text.*?(\d+).*?"([^"]*)"', exec_code)
                if match:
                    seq_index = int(match.group(1))
                    text = match.group(2)
                    result = await self.input_text(text, seq_index=seq_index)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析输入文本操作的参数"
                    }

            elif "press_key" in exec_code:
                # 提取按键
                import re
                key_match = re.search(r'"([^"]*)"', exec_code)
                if key_match:
                    key = key_match.group(1)
                    result = await self.press_key(key)
                    return result
                else:
                    return {
                        "success": False,
                        "error_message": "无法解析按键操作的参数"
                    }

            else:
                return {
                    "success": False,
                    "error_message": f"不支持的操作代码: {exec_code}"
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"执行操作失败: {str(e)}"
            }

    async def take_screenshot_for_execution(self, device_id: Optional[str] = None) -> Dict[str, Any]:
        """为实时执行获取设备截图"""
        try:
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {
                        "success": False,
                        "message": "没有活动设备"
                    }
                device_id = active_device.device_id

            # 调用原有的截图方法
            result = await self.take_screenshot(device_id)

            # 转换返回格式以符合实时执行的需求
            if result.get("success"):
                return {
                    "success": True,
                    "screenshot_path": result["file_path"],
                    "url": result["url"]
                }
            else:
                return {
                    "success": False,
                    "error_message": result.get("message", "截图失败")
                }

        except Exception as e:
            return {
                "success": False,
                "error_message": f"获取截图失败: {str(e)}"
            }
