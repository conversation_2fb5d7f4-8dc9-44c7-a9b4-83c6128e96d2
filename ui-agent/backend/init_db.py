#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库表和初始化数据
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import init_db


def create_tables():
    """创建所有数据库表"""
    try:
        print("正在初始化数据库...")
        init_db()
        print("✅ 数据库初始化成功！")

    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        sys.exit(1)


def main():
    """主函数"""
    print("🚀 开始初始化数据库...")
    create_tables()
    print("\n🎉 数据库初始化完成！")


if __name__ == "__main__":
    main()
