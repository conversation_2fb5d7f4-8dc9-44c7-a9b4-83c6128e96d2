# UI自动化助手 - 启动指南

## 📋 项目概述

UI自动化助手是一个基于AI的智能自动化测试平台，支持Android和Web端的实时自动化操作。项目采用实时执行模式，用户输入任务描述后可立即执行，无需复杂的任务管理流程。

## 🛠 环境要求

### 系统要求
- **操作系统**: macOS / Linux / Windows
- **Python**: 3.9+
- **Node.js**: 18.0+
- **数据库**: SQLite (默认) / PostgreSQL (可选)

### 必需工具
- **Python包管理**: pip3
- **Node.js包管理**: npm (通过nvm管理)
- **版本控制**: git

## 🚀 快速启动

### 1. 环境准备

#### 安装Python依赖
```bash
cd ui-agent/backend
pip3 install -r requirements.txt
```

#### 安装Node.js依赖
```bash
cd ui-agent/frontend
# 使用nvm管理Node.js版本
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use node
npm install
```

#### 安装额外依赖
```bash
# 安装Sass预处理器
npm install -D sass-embedded

# 安装核心Python包
pip3 install fastapi uvicorn sqlalchemy alembic playwright
```

### 2. 数据库初始化

```bash
cd ui-agent/backend
python3 init_db.py
```

### 3. 启动服务

#### 启动后端服务
```bash
cd ui-agent/backend
PYTHONPATH=/Users/<USER>/code/ui-agent/backend python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 启动前端服务
```bash
cd ui-agent/frontend
PATH=$HOME/.nvm/versions/node/v18.20.4/bin:$PATH npm run dev
```

### 4. 访问应用

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

## 📁 项目结构

```
ui-agent/
├── backend/                 # 后端服务
│   ├── app/                # 应用代码
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   └── services/      # 业务逻辑
│   ├── requirements.txt   # Python依赖
│   └── init_db.py        # 数据库初始化
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   │   ├── views/        # 页面组件
│   │   ├── api/          # API接口
│   │   └── styles/       # 样式文件
│   └── package.json      # Node.js依赖
└── 启动指南.md            # 本文档
```

## 🔧 常见问题解决

### Python依赖问题
```bash
# 如果遇到版本冲突
pip3 install --upgrade pip
pip3 install -r requirements.txt --force-reinstall

# 如果缺少特定包
pip3 install fastapi uvicorn sqlalchemy alembic playwright
```

### Node.js环境问题
```bash
# 确保使用正确的Node.js版本
nvm install 18.20.4
nvm use 18.20.4

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

### 端口占用问题
```bash
# 检查端口占用
lsof -i :8000  # 后端端口
lsof -i :3000  # 前端端口

# 杀死占用进程
kill -9 <PID>
```

## 🎯 功能特性

### 实时执行模式
- ✅ 直接执行任务，无需预先创建
- ✅ 实时状态显示和进度追踪
- ✅ 支持停止正在执行的任务

### Android自动化
- ✅ 基于airtest+poco框架
- ✅ 自动设备检测和连接
- ✅ 智能元素识别和操作

### Web自动化
- ✅ 基于playwright框架
- ✅ 多浏览器支持
- ✅ 智能Web元素交互

### 执行历史
- ✅ 完整的执行记录追踪
- ✅ 详细的步骤信息
- ✅ 成功/失败状态分析

## 📞 技术支持

如果在启动过程中遇到问题，请检查：

1. **环境变量**: 确保Python和Node.js路径正确
2. **依赖版本**: 确保所有依赖包版本兼容
3. **端口冲突**: 确保8000和3000端口未被占用
4. **权限问题**: 确保有足够的文件读写权限

## 🔄 开发模式

### 热重载开发
- 后端使用 `--reload` 参数自动重载
- 前端使用 `npm run dev` 支持热更新

### 调试模式
```bash
# 后端调试
export DEBUG=true
python3 -m uvicorn app.main:app --reload --log-level debug

# 前端调试
npm run dev -- --debug
```

---

**注意**: 首次启动可能需要较长时间来安装依赖和初始化数据库，请耐心等待。
