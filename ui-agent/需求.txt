# role
你是一个资深开发专家，帮我设计和实现下面的需求，并且要有良好的代码编写规范和详细的md文档记录设计细节和流程，必要时可以使用mermaid图表。

## 技术栈和环境要求
1. Android端操作：airtest+poco+python
2. web端操作：playwright+python
3. 当前项目为前端+后端结构，前端负责展示任务所有的配置输入，后端负责执行Android、web端操作
4. 前端使用vue框架，后端使用python，fastapi实现，项目可以通过一行命令启动两个端，部署的时候需要考虑mac、windows
5. 打开前端后首先只有一个输入框，输入后端ip+端口，输入完成后点击连接，才进入页面配置界面

## 需求描述
用户在本地部署启动服务端后，然后打开前端页面，输入ip端口点击连接后，进入页面配置界面，通过配置好一些参数后，通过输入框输入任务或者通过语音输入任务，将任务调用一个大模型的接口结合设定好的提示词，返回一个智能规划的步骤，然后每次执行指定的操作后，都调用一次这个大模型接口将执行的结果给大模型，继续动态规划路径直到任务完成，然后当前客户端的状态为已完成当前任务。如果通过任务输入需要点击开始执行任务，如果是语音模式话说完了后直接开始执行任务。后续记录每次执行任务的历史记录和日志。

## 需求详细描述
1. 打开前端后首先只有一个输入框，输入后端ip+端口，输入完成后点击连接，才进入页面配置界面
2. 有一个地方可以选择是 Android 端还是 web 端
4. 如果切换到 Android 端特有的功能
    1. 会有个设备切换的功能，如果连接到本地有多个设备可以手动切换设备
    2. 有一个输入框支持输入一个 测试包下载的 url，输入后点击安装包（执行流程为先下载这个包，然后通过 adb install 安装到指定的设备）
    3. 有一个输入框支持用户输入adb的命令操作设备，旁边有个运行命令按钮
5. 如果切换到 web 端特有的功能
    1. 有一个 url 输入的地方，旁边有个按钮为前往页面，输入后点击前往就会初始化浏览器，打开页面，后面运行任务的时候基于这个页面开始执行
    2. 有个cookie输入的表格，如果配置了前往页面的时候会加载这个cookie
    3. 有个header设置的地方，如果配置了前往页面的时候会携带这个header
    4. 是否为 h5，如果设置了h5模式，打开页面的时候为h5页面大小，默认为online正常页面大小
    5. 有一个输入框可以输入用户的js代码，点击执行可以通过playwright去执行js代码
    6. web端有个重置功能，点击重置会销毁刚刚的浏览器实例，关闭浏览器关闭页面
6. 执行任务模块里面可以配置大模型的模型名称、api-base、温度三个参数
7. 任务输入模式有两种，一种是通过输入框输入任务，一个是通过语音交互输入任务信息，如果是输入框输入的，输入后需要点击开始执行，才会去执行任务；如果是语音输入的，话说完了后直接开始执行任务
8. 有一个模块展示报告模块，这个你需要帮我设计一下
    1. 每次执行截图并且标记元素的时候，将图片在报告模块展示出来
    2. 展示每个步骤 AI 执行返回的结果数据
    3. 将选择的索引信息通过 dom 树查询出来，并且展示出来
    4. 将报告模块存到一个 html 文件里面，给出文件地址
9. 执行任务前需要校验android设备是否存在，如果是web端需要校验web浏览器页面实例是否存在，没有的话需要提示

## 调用大模型执行任务的流程
可以参考对应的提示词了解对应的流程，调用接口/api/process_complex_ai_exec，参数为(automationType, bdd_script, last_result, last_step_result, max_scroll_times)，post，返回的结果为提示词里面的结构
提示词可以参考目录：prompt
### 这个接口内部实现细节辅助你理解，不用实现：
def process_complex_ai_exec_web(desc:str, last_result:str, last_step_result:str, max_scroll_times:int):
    """通过大模型对bbd完整进行分析，通过坐标进行点击操作

    Args:
        desc (str): bdd完整描述
    """
    logging.info(f"通过大模型对bbd完整进行分析，{desc}")
    result = {}
    try:
        ai_execute_model = configer.GlobalConfig.get_key_value("ai_execute_model")
        openai_api_base = configer.GlobalConfig.get_key_value("ai_execute_api_base")
        temperature = configer.GlobalConfig.get_key_value("ai_execute_temperature")
        llm = get_azure_ai_model(ai_execute_model, openai_api_base=openai_api_base, temperature=temperature)
        res = store.get_debug_client().get_dom_tree_and_page_screenshot(ai_exec=True)
        base64_image, viewport, is_scrollable = res["base64_image"], res["viewport"], res["is_scrollable"]
        result = complex_ai_exec_llm_web(llm, base64_image, desc, viewport, is_scrollable, max_scroll_times, last_result, last_step_result)
        
    except Exception as e:
        import traceback
        logging.error(f"多模态处理发生错误: {e}")
        logging.error(traceback.format_exc())
        result = {"error": str(e)}
    finally:
        logging.info(f"多模态获取目标控件结束，结果为{result}")
        return result

def complex_ai_exec_llm_web(llm, base64_image, desc, viewport, is_scrollable, max_scroll_times, last_result, last_step_result):
    """使用LLM分析UI并找到匹配任务的元素"""
    
    if isinstance(viewport, dict):
        viewport_json = json.dumps(viewport, ensure_ascii=False)
    else:
        viewport_json = viewport
    if isinstance(last_result, dict):
        last_result_json = json.dumps(last_result, ensure_ascii=False)
    else:
        last_result_json = last_result
    
    
    # 构建系统提示
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    prompt_ = prompt_config.COMPLEX_AI_EXEC_PROMPT_WEB
    
    # 提示词优化，替换{}
    desc = desc.replace("{", "{{").replace("}", "}}")
    base64_image = base64_image.replace("{", "{{").replace("}", "}}")
    viewport_json = viewport_json.replace("{", "{{").replace("}", "}}")
    last_result_json = last_result_json.replace("{", "{{").replace("}", "}}")
        
    # 填充bdd_desc、viewport
    prompt_ = prompt_.replace("{{desc}}", desc).replace("{{viewport}}", viewport_json).replace("{{is_scrollable}}", str(is_scrollable)).replace("{{max_scroll_times}}", str(max_scroll_times))
    prompt_ = prompt_.replace("{{last_result}}", last_result_json).replace("{{last_step_result}}", str(last_step_result))
    
    # 创建ChatPromptTemplate
    prompt = ChatPromptTemplate.from_messages([
        ("human", [
            {"type": "text", "text": prompt_},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
        ])
    ])
    
    # 创建输出解析器
    output_parser = JsonOutputParser()
    
    # 修改为使用修复解析器
    fixing_parser = OutputFixingParser.from_llm(llm, output_parser)
    
    # 创建可运行的链
    runnable = prompt | llm | fixing_parser
    # 使用process_ai_text_generate_call处理调用并统计费用
    result = process_ai_text_generate_call(runnable, {})
    try:
        # 确保结果是JSON格式的字符串
        if isinstance(result, dict):
            return json.dumps(result, ensure_ascii=False)
        return result
            
    except Exception as e:
        return json.dumps({
            "error": f"LLM分析失败: {str(e)}"
        }, ensure_ascii=False)

def process_complex_ai_exec_android(desc:str, last_result:str, last_step_result:str, max_scroll_times:int):
    """通过大模型对bbd单个步骤完整进行分析，结合目前配置的action mapping，返回最佳的action_content、element_info

    Args:
        desc (str): bdd单个步骤描述或者UI元素描述

    Returns:
        dict: action_content、element_info
    """
    logging.info(f"通过大模型对bbd单个步骤完整进行分析，{desc}")
    result = {}
    try:
        ai_execute_model = configer.GlobalConfig.get_key_value("ai_execute_model")
        openai_api_base = configer.GlobalConfig.get_key_value("ai_execute_api_base")
        temperature = configer.GlobalConfig.get_key_value("ai_execute_temperature")
        llm = get_azure_ai_model(ai_execute_model, openai_api_base=openai_api_base, temperature=temperature)
        res = store.get_debug_client().get_dom_tree_and_page_screenshot(ai_exec=True)
        base64_image, viewport, is_scrollable = res["base64_image"], res["viewport"], res["is_scrollable"]
        result = complex_ai_exec_llm_android(llm, base64_image, desc, viewport, is_scrollable, max_scroll_times, last_result, last_step_result)
    except Exception as e:
        import traceback
        logging.error(f"多模态处理发生错误: {e}")
        logging.error(traceback.format_exc())
        result = {"error": str(e)}
    finally:
        logging.info(f"多模态获取目标控件结束，结果为{result}")
        return result

def complex_ai_exec_llm_android(llm, base64_image, desc, viewport, is_scrollable, max_scroll_times, last_result, last_step_result):
    """使用LLM分析UI并找到匹配任务的元素"""
    if isinstance(viewport, dict):
        viewport_json = json.dumps(viewport, ensure_ascii=False)
    else:
        viewport_json = viewport
    if isinstance(last_result, dict):
        last_result_json = json.dumps(last_result, ensure_ascii=False)
    else:
        last_result_json = last_result
    # 构建系统提示
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    prompt_ = prompt_config.COMPLEX_AI_EXEC_PROMPT_ANDROID
    
    # 提示词优化，替换{}
    desc = desc.replace("{", "{{").replace("}", "}}")
    base64_image = base64_image.replace("{", "{{").replace("}", "}}")
    viewport_json = viewport_json.replace("{", "{{").replace("}", "}}")
    last_result_json = last_result_json.replace("{", "{{").replace("}", "}}")
        
    # 填充bdd_desc、viewport
    prompt_ = prompt_.replace("{{desc}}", desc).replace("{{viewport}}", viewport_json).replace("{{is_scrollable}}", str(is_scrollable)).replace("{{max_scroll_times}}", str(max_scroll_times))
    prompt_ = prompt_.replace("{{last_result}}", last_result_json).replace("{{last_step_result}}", str(last_step_result))
    
    # 创建ChatPromptTemplate
    prompt = ChatPromptTemplate.from_messages([
        ("human", [
            {"type": "text", "text": prompt_},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
        ])
    ])
    
    # 创建输出解析器
    output_parser = JsonOutputParser()
    
    # 修改为使用修复解析器
    fixing_parser = OutputFixingParser.from_llm(llm, output_parser)
    
    # 创建可运行的链
    runnable = prompt | llm | fixing_parser
    # 使用process_ai_text_generate_call处理调用并统计费用
    result = process_ai_text_generate_call(runnable, {})
    try:
        # 确保结果是JSON格式的字符串
        if isinstance(result, dict):
            return json.dumps(result, ensure_ascii=False)
        return result
            
    except Exception as e:
        return json.dumps({
            "error": f"LLM分析失败: {str(e)}"
        }, ensure_ascii=False)
        
### web端需要实现下面的功能，重新实现（下面的代码是另外一个项目的）：
> 下面的代码主要为参考，里面有个截图上传的可以不用实现，直接运行的时候生成一个本地的目录存放这个图片内容即可

def scroll_page(self, direction, scroll_height=100):
        """
        滚动页面
        :param direction: 滚动方向
        :param scroll_height: 滚动高度
        :return:
        """
        if direction == "down":
            self.page.evaluate('window.scrollTo(0, -{})'.format(scroll_height))
            self.page.wait_for_timeout(1000 * 5)
            print("向下滑动成功")
        elif direction == "up":
            self.page.evaluate('window.scrollTo(0, {})'.format(scroll_height))
            self.page.wait_for_timeout(1000 * 5)
            print("向上滑动成功")
        
    def clickByCoordinate(self, seq_index, is_print=True):
        """
        通过坐标点击元素
        :param x: x坐标
        :param y: y坐标
        :param kwargs: 其他参数
        :return:
        """
        if is_print:
            print("对该控件执行点击操作")
        try:
            # 根据seq_index查找pos
            pos = self.find_center_from_dom_tree_by_seqIndex(seq_index)
            self.page.mouse.click(pos["x"], pos["y"])
        except Exception as e:
            print("点击失败:{}".format(str(e)))
            return
        # 避免点击后页面还未渲染出来场景
        self.page.wait_for_timeout(1000 * 5)
        self.page.wait_for_load_state("load", timeout=30000)
        # 点击完成后判断是否有新开页面，如果有则切换到新页面
        if len(self.context.pages) > 1:
            self.switch_new_page()
        if is_print:
            print("点击成功")
            
    def input_text_by_coordinate(self, seq_index, text, **kwargs):
        """
        通过坐标输入文本
        :param x: x坐标
        :param y: y坐标
        :param text: 文本
        :param kwargs: 其他参数
        :return:
        """
        print("对该控件执行输入文本操作")
        try:
            self.clickByCoordinate(seq_index, is_print=False)
            self.page.keyboard.type(text)
            self.page.wait_for_timeout(1000 * 5)
            print("输入文本成功")
        except Exception as e:
            print("输入文本失败:{}".format(str(e)))

def ai_exec_get_dom_tree_and_page_screenshot(self):
        """ai执行，获取DOM树和页面截图"""
        try:
            # 1. 获取viewport
            viewport = self.page.viewport_size
            # 2. 获取当前页面最大高度，并且通过viewport判断是否可以滑动
            max_height = self.page.evaluate("document.body.scrollHeight")
            is_scrollable = max_height > viewport["height"]
            # 4. 截图
            screenshot = self.get_page_screenshot(full_page=False, scale="css")
            # 获取dom树
            self.dom_tree = self.get_dom_tree()
            # 绘制边界框
            img = self.draw_bounding_boxes(screenshot, self.dom_tree)
            timestamp = int(time.time())
            screenshot_path = self.save_image(img, f"screenshot_{timestamp}.png")
            # 将图像上传到服务器，获取图片url
            image_url = Capture.getImgOnlineUrl(self, screenshot_path, env="fws")
            json_data = {"base64_image": image_url, "viewport": viewport, "is_scrollable": is_scrollable}
            json_str = json.dumps(json_data, ensure_ascii=False)
            # 使用base64编码确保可以安全传输
            compressed_b64 = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
            # 调用getBase64OnlineUrl方法上传这个额外数据
            image_url = Capture.getBase64OnlineUrl(compressed_b64, f"compressed_data_{timestamp}.png", 'png')
            print(f"image_url:{image_url}")
        except Exception as e:
            import traceback
            printUtil.printCaseDevice(f"获取DOM树和页面截图失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
def get_dom_tree(self):
        """获取当前页面的DOM树结构"""
        # 使用JavaScript获取DOM树结构
        dom_tree = self.page.evaluate(r"""() => {
            const isVisibleElement = (node) => {
                if (node.nodeType !== 1) return false;
                const style = window.getComputedStyle(node);
                const rect = node.getBoundingClientRect();
                return true;
                return style.display !== 'none' && 
                       style.visibility !== 'hidden' && 
                       style.opacity !== '0' &&
                       rect.width > 0 && 
                       rect.height > 0;
            };

            const isInteractiveElement = (node) => {
                if (node.nodeType !== 1) return false;
                const interactiveTags = ['a', 'button', 'input', 'select', 'textarea'];
                return interactiveTags.includes(node.tagName.toLowerCase()) ||
                       node.onclick != null ||
                       node.getAttribute('role') === 'button';
            };

            const shouldSkipElement = (node) => {
                // 如果不是元素节点，不跳过
                if (node.nodeType !== 1) return false;
                
                const skipTags = ['script', 'style', 'noscript', 'iframe', 'meta', 'link', 'head'];
                return skipTags.includes(node.tagName.toLowerCase());
            };

            const processNode = (node, index = 0) => {
                // 跳过注释节点和无意义节点
                if (node.nodeType === 8 || shouldSkipElement(node)) return null;
                
                // 如果是文本节点且内容为空或只包含空白字符，则跳过
                if (node.nodeType === 3 && !node.textContent.trim()) {
                    return null;
                }

                const result = {
                    index: index,
                    tag: node.nodeType === 1 ? node.tagName.toLowerCase() : '#text',
                    type: node.nodeType === 3 ? 'text' : 'element'
                };

                // 处理文本节点
                if (node.nodeType === 3) {
                    const text = node.textContent.trim();
                    if (text) {
                        // 移除特殊图标字符，包括"󰁮"（Unicode编码为0xF006E）
                        let cleanedText = text.replace(/[\u{E000}-\u{F8FF}]|[\u{1F000}-\u{1FFFF}]|[\u{2000}-\u{2FFF}]|[\u{F0000}-\u{FFFFF}]/gu, '');
                        // 直接替换特定的图标字符"󰁮"
                        cleanedText = cleanedText.replace(/\udbf0\udc6e/g, '');
                        // 只保留可打印的ASCII字符、空格和常见的中文字符
                        cleanedText = cleanedText.replace(/[^\x20-\x7E\u4E00-\u9FFF\u3000-\u303F\uFF00-\uFFEF\s]/g, '');
                        result.payload = { text: cleanedText };
                    }
                    return result;
                }

                // 处理元素节点
                if (node.nodeType === 1) {
                    const isVisible = isVisibleElement(node);
                    // 如果元素不可见且不是交互式元素，则跳过
                    // if (!isVisible && !isInteractiveElement(node)) {
                    //     return null;
                    // }

                    result.payload = {};
                    
                    // 只保存长度不超过20个字符的文本内容
                    const text = node.textContent.trim();
                    if (text && text.length <= 20) {
                        // 移除特殊图标字符，包括"󰁮"（Unicode编码为0xF006E）
                        let cleanedText = text.replace(/[\u{E000}-\u{F8FF}]|[\u{1F000}-\u{1FFFF}]|[\u{2000}-\u{2FFF}]|[\u{F0000}-\u{FFFFF}]/gu, '');
                        // 直接替换特定的图标字符"󰁮"
                        cleanedText = cleanedText.replace(/\udbf0\udc6e/g, '');
                        // 只保留可打印的ASCII字符、空格和常见的中文字符
                        cleanedText = cleanedText.replace(/[^\x20-\x7E\u4E00-\u9FFF\u3000-\u303F\uFF00-\uFFEF\s]/g, '');
                        result.payload.text = cleanedText;
                    }

                    // 只保存重要属性，且内容长度不超过50个字符
                    const importantAttrs = ['id', 'class', 'name', 'type', 'role', 'aria-label', 'placeholder', 'href', 'src', 'value', 'title', 'for'];
                    for (let attr of node.attributes) {
                        // 优先处理testid、page-module、data-exposure、data-testid等属性
                        // data-exposure属性是一个json字符串，需要提取ubtKey字段，保存到 payload['data-exposure']属性
                        // testid属性如果只是字符串不是json字符串，直接提取，保存到 payload.testid属性
                        // testid属性如果是json字符串，提取referConfig.oid字段，保存到 payload.testid属性
                        // page-module属性如果只是字符串不是json字符串，直接提取，保存到 payload['page-module']属性
                        // data-testid属性如果只是字符串不是json字符串，直接提取，保存到 payload['data-testid']属性
                        if (attr.name === 'data-exposure') {
                            try {
                                result.payload['data-exposure'] = JSON.parse(attr.value).ubtKey;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['data-exposure'] = attr.value;
                            }
                        }
                        if (attr.name === 'testid') {
                            try {
                                if (attr.value.includes('referConfig') && attr.value.includes('oid')) {
                                    result.payload.testid = JSON.parse(attr.value).referConfig.oid;
                                } else if (attr.value.includes('viewID')) {
                                    result.payload.testid = JSON.parse(attr.value).viewID;
                                } else {
                                    result.payload.testid = attr.value;
                                }
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload.testid = attr.value;
                            }
                        }
                        if (attr.name === 'page-module') {
                            try {
                                result.payload['page-module'] = JSON.parse(attr.value).moduleId;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['page-module'] = attr.value;
                            }
                        }
                        if (attr.name === 'data-testid') {
                            try {
                                result.payload['data-testid'] = JSON.parse(attr.value).referConfig.oid;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['data-testid'] = attr.value;
                            }
                        }
                        if (importantAttrs.includes(attr.name) && attr.value) {
                            // 检查属性值长度是否超过50个字符
                            if (attr.value.length <= 50) {
                                // 直接使用原始属性值，不做任何修改
                                result.payload[attr.name] = attr.value;
                            }
                        }
                    }

                    // 获取元素位置和大小（只对可见元素）
                    if (true) {
                        const rect = node.getBoundingClientRect();
                        result.payload.rect = {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        };
                    }

                    result.payload.visible = true;

                    // 处理子节点
                    if (node.childNodes && node.childNodes.length > 0) {
                        const children = [];
                        let childIndex = 0;
                        for (const childNode of node.childNodes) {
                            const childResult = processNode(childNode, childIndex);
                            if (childResult) {
                                children.push(childResult);
                                childIndex++;
                            }
                        }
                        if (children.length > 0) {
                            result.children = children;
                        }
                    }
                }

                return result;
            };

            // 从body开始处理整个DOM树
            return processNode(document.body);
        }""")
        
        # 添加全局顺序索引
        self._add_sequential_index(dom_tree)
        
        return dom_tree
    
    def _add_sequential_index(self, node, index_map=None):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}
        
        node["seq_index"] = index_map["current"]
        index_map["current"] += 1
        
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)
    
    def get_page_screenshot(self, full_page=True, scale="css"):
        """获取页面截图"""
        return self.page.screenshot(full_page=full_page, scale=scale)

    def _generate_random_color(self, seq_index):
        """根据seq_index生成随机但一致的颜色"""
        # 使用seq_index作为种子，确保相同的seq_index总是生成相同的颜色
        random.seed(hash(str(seq_index)) % 2147483647)

        # 生成高饱和度、中等亮度的颜色，确保视觉效果好
        hue = random.random()  # 色相：0-1
        saturation = 0.7 + random.random() * 0.3  # 饱和度：0.7-1.0
        lightness = 0.4 + random.random() * 0.3   # 亮度：0.4-0.7

        # 转换HSL到RGB
        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        return tuple(int(c * 255) for c in rgb)

    def _get_contrasting_text_color(self, bg_color):
        """根据背景颜色获取对比度高的文本颜色"""
        # 计算背景颜色的亮度
        r, g, b = bg_color[:3]  # 只取RGB值，忽略alpha
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        # 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
        return (255, 255, 255) if brightness < 128 else (0, 0, 0)
    
    def draw_bounding_boxes(self, screenshot_bytes, dom_tree):
        """在截图上绘制边界框"""
        # 将截图字节转换为PIL图像
        img = Image.open(BytesIO(screenshot_bytes))
        draw = ImageDraw.Draw(img, 'RGBA')  # 使用RGBA模式以支持透明度

        # 获取页面大小
        viewport_size = self.page.viewport_size
        width = viewport_size["width"]
        height = viewport_size["height"]

        # 尝试加载字体
        font = ImageFont.truetype("Arial", 14)

        # 重置随机种子，确保颜色生成的一致性
        random.seed(42)

        # 递归绘制边界框
        self._draw_node_bounding_box(draw, dom_tree, width, height, font)

        return img
    
    def _draw_node_bounding_box(self, draw, node, viewport_width, viewport_height, font):
        """递归地为每个节点绘制边界框"""
        if "payload" in node and "rect" in node["payload"]:
            rect = node["payload"]["rect"]

            # 使用rect中的坐标和大小信息
            x1 = rect["x"]
            y1 = rect["y"]
            x2 = x1 + rect["width"]
            y2 = y1 + rect["height"]

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框 - 使用随机颜色，增加线条宽度以提高可见性
            draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)

            # 构建标签文本 - 包含seq_index和文本内容（如果有且不太长）
            label_text = str(seq_index)

            # 在边界框顶部绘制标签
            if label_text:
                try:
                    # 限制标签文本总长度
                    if len(label_text) > 25:
                        label_text = label_text[:22] + "..."

                    # 使用字体计算实际文本大小
                    try:
                        bbox = draw.textbbox((0, 0), label_text, font=font)
                        text_width = bbox[2] - bbox[0] + 6  # 添加padding
                        text_height = bbox[3] - bbox[1] + 4  # 添加padding
                    except:
                        # 如果textbbox不可用，使用估算
                        text_width = len(label_text) * 8 + 6
                        text_height = 16

                    # 确保标签不会超出屏幕边界
                    label_x = x1
                    if label_x + text_width > viewport_width:
                        label_x = viewport_width - text_width
                    if label_x < 0:
                        label_x = 0

                    # 确保标签不会超出屏幕顶部
                    label_y = y1 - text_height
                    if label_y < 0:
                        label_y = y2  # 如果顶部放不下，放到底部
                        if label_y + text_height > viewport_height:
                            label_y = viewport_height - text_height

                    # 创建标签背景颜色（使用边界框颜色的半透明版本）
                    bg_color = border_color + (200,)  # 添加alpha通道

                    # 绘制标签背景
                    draw.rectangle(
                        [label_x, label_y, label_x + text_width, label_y + text_height],
                        fill=bg_color
                    )

                    # 获取对比度高的文本颜色
                    text_color = self._get_contrasting_text_color(border_color)

                    # 绘制文本
                    draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)

                except Exception as e:
                    print(f"绘制标签失败: {str(e)}")

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_node_bounding_box(draw, child, viewport_width, viewport_height, font)
    
    def save_image(self, image, filename):
        """保存图像到文件"""
        # 判断是否有output目录，没有的话则创建
        if not os.path.exists("output"):
            os.makedirs("output")
        filepath = os.path.join("output", filename)
        image.save(filepath)
        return filepath
    
    def encode_image_to_base64(self, image_path):
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
        
    def find_center_from_dom_tree_by_seqIndex(self, seq_index):
        """根据seq_index递归查找对应的pos"""
        # 检查当前节点是否匹配目标seq_index
        # 递归遍历子节点
        return self.find_element_by_seq_index(self.dom_tree, seq_index)
    
    def find_element_by_seq_index(self, dom_tree, target_seq_index):
        """根据序列索引在DOM树中查找元素"""
        # 添加空值检查
        if not dom_tree or not isinstance(dom_tree, dict):
            return None
            
        if dom_tree.get("seq_index") == target_seq_index:
            # 添加payload和rect存在性检查
            if "payload" in dom_tree and "rect" in dom_tree["payload"]:
                rect = dom_tree["payload"]["rect"]
                return {
                    "x": rect["x"] + rect["width"] / 2,
                    "y": rect["y"] + rect["height"] / 2
                }
        
        if dom_tree.get("children"):
            for child in dom_tree.get("children"):
                result = self.find_element_by_seq_index(child, target_seq_index)
                if result:
                    # 直接返回结果，不再尝试获取payload和rect
                    return result
        
        return None
    
    def _build_selector(self, element):
        """构建元素选择器"""
        selectors = []
        
        # 尝试使用id
        if element["payload"].get("id"):
            selectors.append(f"#{element['payload']['id']}")
        
        # 尝试使用testid
        if element["payload"].get("testid"):
            selectors.append(f"[data-testid='{element['payload']['testid']}']")
        
        # 尝试使用精确文本
        if element["payload"].get("text"):
            selectors.append(f"text='{element['payload']['text']}'")
        
        # 如果都没有，使用tag和class组合
        if not selectors and element.get("tag"):
            selector = element["tag"]
            if element["payload"].get("class"):
                selector += f".{element['payload']['class'].replace(' ', '.')}"
            selectors.append(selector)
        
        # 返回第一个有效的选择器
        return selectors[0] if selectors else "body"

    def save_compressed_image(self, img, filename, compression_ratio=0.5):
        """
        保存压缩后的图片，压缩到原始大小的指定比例
        
        Args:
            img: PIL Image对象
            filename: 保存的文件名
            compression_ratio: 压缩比例（默认0.5，即压缩到原始大小的50%）
            
        Returns:
            保存的图片路径
        """
        # 获取保存路径，如果output/目录不存在，则创建
        if not os.path.exists("output"):
            os.makedirs("output")
        save_path = os.path.join("output", filename)
        
        # 创建临时内存文件以获取原始大小
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=95)
        original_size = temp_buffer.tell()
        
        # 计算目标大小
        target_size = int(original_size * compression_ratio)
        
        # 初始质量设置
        quality = 85
        
        # 如果只通过降低质量就能达到目标，则使用此方法
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=quality, optimize=True)
        compressed_size = temp_buffer.tell()
        
        # 如果压缩后大小仍然大于目标大小，则同时缩小尺寸
        if compressed_size > target_size:
            # 计算需要的尺寸缩放比例（假设文件大小与像素数近似成正比）
            # 我们需要额外的缩放因子来达到目标大小
            scale_factor = math.sqrt(target_size / compressed_size)
            width, height = img.size
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            
            # 缩小图片
            resized_img = img.resize((new_width, new_height), Image.LANCZOS)
            resized_img.save(save_path, format='JPEG', quality=quality, optimize=True)
        else:
            # 如果仅降低质量就能达到目标，直接保存
            img.save(save_path, format='JPEG', quality=quality, optimize=True)
            
        return save_path
      
### Android端需要实现下面的功能，重新实现（下面的代码是另外一个项目的）：
> 下面的代码主要为参考，里面有个截图上传的可以不用实现，直接运行的时候生成一个本地的目录存放这个图片内容即可
def scroll_page(self, direction, scroll_height=0.3, wait_time=5):
        """
        滚动页面
        :param direction: 滚动方向
        :param scroll_height: 滚动高度
        :param wait_time: 滑动后等待时间
        :return:
        """
        if direction == "up":
            self.poco.scroll("vertical", percent=-scroll_height, duration=1)
            time.sleep(wait_time)
            print("向上滑动成功")
            self.assertTrue(True, "向上滑动成功")
        elif direction == "down":
            self.poco.scroll("vertical", percent=scroll_height, duration=1)
            time.sleep(wait_time)
            print("向下滑动成功")
            self.assertTrue(True, "向下滑动成功")
        else:
            self.assertTrue(False, "滑动方向错误")
            
    #直接通过坐标点击，支持相对坐标和绝对坐标
    #x,y--坐标点 is_relative--是否为相对坐标(0-1范围内的比例值)，默认为True wait_time--点击后等待时间
    def clickByCoordinate(self, seq_index, is_relative=False, wait_time=5, is_print=True):
        '''
        直接通过seq_index点击屏幕，支持相对坐标和绝对坐标
        
        示例:
        1. 点击屏幕中心: Action.clickByCoordinate(0)
        2. 点击绝对坐标点(100, 200): Action.clickByCoordinate(100, 200, is_relative=False)
        '''
        if is_print:
            print("对该控件执行点击操作")
        try:
            pos = self.find_center_from_dom_tree_by_seqIndex(seq_index)
            x = pos[0]
            y = pos[1]
            # touch((x, y)) # 有的页面使用touch点击没有反应
            self.poco.click((x, y))
            time.sleep(wait_time)
            if is_print:
                print("点击成功")
            self.assertTrue(True, "点击成功")
        except Exception as e:
            print("点击失败:{}".format(str(e)))
            self.assertTrue(False, "点击失败")
    
    def input_text_by_coordinate(self, seq_index, text_input, enter=False, is_relative=False, wait_time=5):
        '''
        通过坐标输入文本
        '''
        print("对该控件执行输入文本操作")
        try:
            self.clickByCoordinate(seq_index, is_relative, wait_time, is_print=False)
            text(text_input, enter=enter)
            time.sleep(wait_time)
            print("输入文本成功")
            self.assertTrue(True, "输入文本成功")
        except Exception as e:
            print("输入文本失败:{}".format(str(e)))
            self.assertTrue(False, "输入文本失败")
def ai_exec_get_dom_tree_and_page_screenshot(self):
        """ai执行，获取DOM树和页面截图"""
        try:
            # 1. 获取屏幕分辨率
            screen_width, screen_height = self.get_screen_resolution()
            viewport = {"width": screen_width, "height": screen_height}
            # 2. 判断页面是否可以滚动
            is_scrollable = True
            self.dom_tree = self.get_dom_tree()
            # 2. 获取屏幕截图
            screenshot = self.take_screenshot()
            # 3. 在截图上绘制边界框
            annotated_image = self.draw_bounding_boxes(screenshot, self.dom_tree)
            # 4. 保存标注后的图像
            timestamp = int(time.time())
            annotated_image_path = self.save_image(annotated_image, f"annotated_{timestamp}.png")
            # 将图像上传到服务器，获取图片url
            image_url = Capture.getImgOnlineUrl(self, annotated_image_path, env="fws")
            json_data = {"base64_image": image_url, "viewport": viewport, "is_scrollable": is_scrollable}
            json_str = json.dumps(json_data, ensure_ascii=False)
            # 使用base64编码确保可以安全传输
            compressed_b64 = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
            # 调用getBase64OnlineUrl方法上传这个额外数据
            image_url = Capture.getBase64OnlineUrl(compressed_b64, f"compressed_data_{timestamp}.png", 'png')
            print(f"image_url:{image_url}")
        except Exception as e:
            import traceback
            printUtil.printCaseDevice(f"获取DOM树和页面截图失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
  def find_center_from_dom_tree_by_seqIndex(self, seq_index):
        """根据seq_index递归查找对应的pos"""
        # 检查当前节点是否匹配目标seq_index
        if seq_index == self.dom_tree.get("seq_index"):
            return self.dom_tree.get("payload").get("pos")
        
        # 递归遍历子节点
        return self._find_pos_in_children(seq_index, self.dom_tree.get("children", []))
    
    def _find_pos_in_children(self, seq_index, children):
        """辅助方法：在子节点列表中递归查找特定seq_index的节点位置"""
        if not children:
            return None
            
        for node in children:
            # 检查当前子节点是否匹配
            if node.get("seq_index") == seq_index:
                return node.get("payload", {}).get("pos")
                
            # 递归检查当前子节点的子节点
            pos = self._find_pos_in_children(seq_index, node.get("children", []))
            if pos:
                return pos
                
        # 如果在所有子节点中都找不到匹配的seq_index，则返回None
        return None

def encode_image_to_base64(self, image_path):
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def _add_index_to_dom_tree(self, node, parent_index="", current_index=0):
        """递归地为DOM树添加索引"""
        # 为当前节点添加索引
        index = f"{parent_index}{current_index}" if parent_index else f"{current_index}"
        node["index"] = index
        
        # 如果有子节点，递归处理
        if "children" in node and node["children"]:
            for i, child in enumerate(node["children"]):
                self._add_index_to_dom_tree(child, f"{index}_", i)
    
    def _add_sequential_index(self, node, index_map=None, current_index=0):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}
            
        # 为当前节点添加顺序索引
        node["seq_index"] = index_map["current"]
        index_map["current"] += 1
        
        # 如果有子节点，递归处理
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)
    
    def get_dom_tree(self):
        """获取当前页面的DOM树"""
        # 获取DOM树
        hierarchy = self.poco.agent.hierarchy.dump()
        
        # 添加索引到DOM树
        self._add_index_to_dom_tree(hierarchy)
        
        # 添加全局顺序索引
        self._add_sequential_index(hierarchy)
        
        return hierarchy

    def take_screenshot(self):
        """获取当前屏幕截图"""
        return G.DEVICE.snapshot()
    
    def get_screen_resolution(self):
        """获取屏幕分辨率"""
        display_info = G.DEVICE.display_info
        width = display_info["width"]
        height = display_info["height"]
        orientation = display_info["orientation"]

        # 根据屏幕方向调整宽高
        if orientation in [1, 3]:  # 横屏
            return height, width
        else:  # 竖屏
            return width, height

    def _generate_random_color(self, seq_index):
        """根据seq_index生成随机但一致的颜色"""
        # 使用seq_index作为种子，确保相同的seq_index总是生成相同的颜色
        random.seed(hash(str(seq_index)) % 2147483647)

        # 生成高饱和度、中等亮度的颜色，确保视觉效果好
        hue = random.random()  # 色相：0-1
        saturation = 0.7 + random.random() * 0.3  # 饱和度：0.7-1.0
        lightness = 0.4 + random.random() * 0.3   # 亮度：0.4-0.7

        # 转换HSL到RGB
        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        return tuple(int(c * 255) for c in rgb)

    def _get_contrasting_text_color(self, bg_color):
        """根据背景颜色获取对比度高的文本颜色"""
        # 计算背景颜色的亮度
        r, g, b = bg_color[:3]  # 只取RGB值，忽略alpha
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        # 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
        return (255, 255, 255) if brightness < 128 else (0, 0, 0)
    
    def _draw_node_bounding_box(self, draw, node, screen_width, screen_height, font):
        """递归地为每个节点绘制边界框"""
        # 检查节点是否有位置和大小信息
        if "payload" in node and "pos" in node["payload"] and "size" in node["payload"]:
            # 获取节点的位置和大小
            pos = node["payload"]["pos"]
            size = node["payload"]["size"]

            # 计算边界框坐标
            x1 = int((pos[0] - size[0]/2) * screen_width)
            y1 = int((pos[1] - size[1]/2) * screen_height)
            x2 = int((pos[0] + size[0]/2) * screen_width)
            y2 = int((pos[1] + size[1]/2) * screen_height)

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")
            
            # 构建标签文本
            label_text = f"{seq_index}"

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框 - 使用随机颜色，增加线条宽度以提高可见性
            draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)
            
            
            
            # 在边界框顶部绘制标签
            if label_text:
                try:
                    # 限制标签文本总长度
                    if len(label_text) > 25:
                        label_text = label_text[:22] + "..."

                    # 使用字体计算实际文本大小
                    try:
                        bbox = draw.textbbox((0, 0), label_text, font=font)
                        text_width = bbox[2] - bbox[0] + 6  # 添加padding
                        text_height = bbox[3] - bbox[1] + 4  # 添加padding
                    except:
                        # 如果textbbox不可用，使用估算
                        text_width = len(label_text) * 8 + 6
                        text_height = 16

                    # 确保标签不会超出屏幕边界
                    label_x = x1
                    if label_x + text_width > screen_width:
                        label_x = screen_width - text_width
                    if label_x < 0:
                        label_x = 0

                    # 确保标签不会超出屏幕顶部
                    label_y = y1 - text_height
                    if label_y < 0:
                        label_y = y2  # 如果顶部放不下，放到底部
                        if label_y + text_height > screen_height:
                            label_y = screen_height - text_height

                    # 创建标签背景颜色（使用边界框颜色的半透明版本）
                    bg_color = border_color + (200,)  # 添加alpha通道

                    # 绘制标签背景
                    draw.rectangle(
                        [label_x, label_y, label_x + text_width, label_y + text_height],
                        fill=bg_color
                    )

                    # 获取对比度高的文本颜色
                    text_color = self._get_contrasting_text_color(border_color)

                    # 绘制文本
                    draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)

                except Exception as e:
                    print(f"绘制标签失败: {str(e)}")

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_node_bounding_box(draw, child, screen_width, screen_height, font)
    
    
    def draw_bounding_boxes(self, screenshot, dom_tree):
        """在截图上绘制边界框"""
        # 直接使用PIL处理图像，不需要从OpenCV转换
        img = Image.fromarray(screenshot)
        draw = ImageDraw.Draw(img, 'RGBA')  # 使用RGBA模式以支持透明度

        # 获取屏幕分辨率
        width, height = self.get_screen_resolution()


        font = ImageFont.load_default()

        # 重置随机种子，确保颜色生成的一致性
        random.seed(42)

        # 递归绘制边界框
        self._draw_node_bounding_box(draw, dom_tree, width, height, font)

        # 返回PIL图像的numpy数组形式
        return np.array(img)
    
    def save_image(self, image, filename):
        """保存图像到文件"""
        # 判断是否有output目录，没有的话则创建
        if not os.path.exists("output"):
            os.makedirs("output")
        filepath = os.path.join("output", filename)
        
        # 将numpy数组转为PIL图像并保存
        img = Image.fromarray(image)
        img.save(filepath)
        return filepath        

### 调用大模型接口需要执行流程参考下面的代码，重新实现（下面的代码是另外一个项目的）
def _handle_complex_ai_exec(self, bdd_script: str):
        success, action, action_type, target_name, target_type, target_text, argument, exec_code = False, "", "", "", "", "", "", ""
        execution_failed = False  # 新增：标记执行是否失败
        failure_reason = ""  # 新增：记录失败原因

        try:
            # 设置一个ai最多执行次数
            max_exec_times = 20
            max_scroll_times = 3
            last_result = ""
            last_step_result = ""
            utils.add_ai_detail("complex_ai_exec", f"使用complex_ai_exec智能规划步骤开始", max_scroll_times=max_scroll_times)

            for i in range(max_exec_times):
                if store.get_automation_type() == "web":
                    res = json.loads(process_complex_ai_exec_web(bdd_script, last_result, last_step_result, max_scroll_times))
                else:
                    res = json.loads(process_complex_ai_exec_android(bdd_script, last_result, last_step_result, max_scroll_times))
                utils.add_ai_detail(f"complex_ai_exec step {i}", f"{res.get('next_executed_step')}", result=str(res))

                if len(res.get("step_list")) == 0:
                    execution_failed = True
                    failure_reason = "使用complex_ai_exec智能规划步骤为空"
                    raise Exception(failure_reason)
                if not res.get("next_executed_step"):
                    execution_failed = True
                    failure_reason = "使用complex_ai_exec智能规划步骤为空"
                    raise Exception(failure_reason)

                if res.get("result") == -1 or res.get("result") == 2:
                    # 表示首次执行AI返回的结果
                    if res.get("next_executed_step").get("element_info").get("find_status") == -1:
                        execution_failed = True
                        failure_reason = "没有找到符合的元素"
                        utils.add_ai_detail("[error] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行失败", result=str(res))
                        raise Exception(failure_reason)

                    if res.get("next_executed_step").get("element_info").get("find_status") == -2:
                        utils.add_ai_detail("[滑动页面] complex_ai_exec", f"需要滑动页面查找元素", result=str(res))
                        exec_code = res.get("next_executed_step").get("code_info").get("code_generate")
                        exec_success, msg = store.get_debug_client().exec(exec_code, with_uuid_head=False)
                        if "成功" in msg:
                            exec_success = True
                        else:
                            exec_success = False
                        if exec_success:
                            last_result = res
                            last_step_result = "true"
                            utils.add_ai_detail("Action 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg)
                            continue
                        else:
                            execution_failed = True
                            failure_reason = f"[滚动页面]操作失败：简单指令执行失败\n[错误代码]: {exec_code}\n[错误原因]: {msg}"
                            last_result = res
                            last_step_result = "false"
                            utils.add_ai_detail("Action 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, msg=msg)
                            raise new_when_ex(bdd_script, AirTestGenerateErrorType.DebugExecuteError, f"[滚动页面]操作失败：简单指令执行失败\n[bdd描述]: {bdd_script}\n[错误代码]: {exec_code}\n[错误原因]: {msg}")

                    if res.get("next_executed_step").get("code_info").get("type") == "Assert" or res.get("next_executed_step").get("code_info").get("type") == "Find":
                        if res.get("next_executed_step").get("code_info").get("assert_result") == False:
                            execution_failed = True
                            failure_reason = f"[断言]操作失败：断言失败\n[错误原因]: {res.get('next_executed_step').get('code_info').get('assert_thought')}"
                            last_result = res
                            last_step_result = "false"
                            utils.add_ai_detail("[error] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行失败", result=str(res))
                            raise new_then_ex(bdd_script, AirTestGenerateErrorType.DebugExecuteError, f"[断言]操作失败：断言失败\n[bdd描述]: {bdd_script}\n[错误代码]: {exec_code}\n[错误原因]: {res.get('next_executed_step').get('code_info').get('assert_thought')}")
                        last_result = res
                        last_step_result = "true"
                        utils.add_ai_detail("[success] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行成功", result=str(res))
                        continue

                    if res.get("next_executed_step").get("code_info").get("type") == "Action":
                        exec_code = res.get("next_executed_step").get("code_info").get("code_generate")
                        exec_success, msg = store.get_debug_client().exec(exec_code, with_uuid_head=False)
                        if "成功" in msg:
                            exec_success = True
                        else:
                            exec_success = False
                        if not exec_success:
                            execution_failed = True
                            failure_reason = f"[Action]操作失败：Action操作执行失败\n[错误代码]: {exec_code}\n[错误原因]: {msg}"
                            last_result = res
                            last_step_result = "false"
                            utils.add_ai_detail("Action 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, msg=msg)
                            raise new_when_ex(bdd_script, AirTestGenerateErrorType.DebugExecuteError, f"[Action]操作失败：Action操作执行失败\n[bdd描述]: {bdd_script}\n[错误代码]: {exec_code}\n[错误原因]: {msg}")
                        last_result = res
                        last_step_result = "true"
                        utils.add_ai_detail("[success] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行成功", result=str(res))
                        continue

                if res.get("result") == 0:
                    success = True
                    utils.add_ai_detail("[success] complex_ai_exec", f"使用complex_ai_exec全部执行成功", result=str(res))
                    return

            # 如果循环结束还没有成功，说明达到最大执行次数
            execution_failed = True
            failure_reason = f"[complex_ai_exec]操作失败：执行达到最大次数还没有执行成功"
            utils.add_ai_detail("[error] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行达到最大次数还没有执行成功")
            raise new_when_ex(bdd_script, AirTestGenerateErrorType.DebugExecuteError, f"[complex_ai_exec]操作失败：执行达到最大次数还没有执行成功\n[bdd描述]: {bdd_script}")


## 其他要求
0. 数据库使用sqlite
1. 给出具体的帮助说明文档
2. 通过命令启动，需要考虑 mac、windows 系统，需要考虑执行的系统没有 python 环境
3. 是否可以考虑有一个命令可以将这个项目打包为一个可执行文件，下载安装后，点击执行就可以启动

## 用户使用客户端操作步骤例子
1. 选择使用web端
2. 设置好cookie、header后，输入页面url，点击前往页面，这时候就会通过playwright打开一个浏览器访问这个页面
3. 通过输入框输入任务后，点击开始执行
4. 进行调用大模型执行任务的流程
5. 任务完成后，界面上面有个状态提示，当前任务完成，并且历史记录下刚刚执行任务的所有截图、日志、大模型返回的信息、元素信息等
6. 如果想继续执行的话，直接输入任务，点击开始执行，会接着是一个任务执行后的浏览器页面继续执行
7. 如果想重新开始一个任务，可以点击界面的重置按钮，会关闭浏览器
8. 执行任务的时候需要校验设备或者浏览器页面是否已准备好


代码里面所有标记简单处理、为了简化都需要完整的实现，后续禁止简单实现内容这样行为
另外android使用的adb要使用airtest+poco来，不要直接执行adb的命令，要使用airtest提供的adb方法来连接设备、截图、获取dom树、点击坐标等等
实现的细节流程需要md文档记录