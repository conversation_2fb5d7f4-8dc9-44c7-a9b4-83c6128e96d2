import type { GenFile, GenMessage } from "../../../../codegenv2/types.js";
import type { Message } from "../../../../types.js";
/**
 * Describes the file google/protobuf/empty.proto.
 */
export declare const file_google_protobuf_empty: GenFile;
/**
 * A generic empty message that you can re-use to avoid defining duplicated
 * empty messages in your APIs. A typical example is to use it as the request
 * or the response type of an API method. For instance:
 *
 *     service Foo {
 *       rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty);
 *     }
 *
 *
 * @generated from message google.protobuf.Empty
 */
export type Empty = Message<"google.protobuf.Empty"> & {};
/**
 * A generic empty message that you can re-use to avoid defining duplicated
 * empty messages in your APIs. A typical example is to use it as the request
 * or the response type of an API method. For instance:
 *
 *     service Foo {
 *       rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty);
 *     }
 *
 *
 * @generated from message google.protobuf.Empty
 */
export type EmptyJson = Record<string, never>;
/**
 * Describes the message google.protobuf.Empty.
 * Use `create(EmptySchema)` to create a new message.
 */
export declare const EmptySchema: GenMessage<Empty, {
    jsonType: EmptyJson;
}>;
