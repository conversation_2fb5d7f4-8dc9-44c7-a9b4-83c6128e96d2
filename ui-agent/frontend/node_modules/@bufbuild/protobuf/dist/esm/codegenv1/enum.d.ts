import type { DescFile } from "../descriptors.js";
import type { GenEnum } from "./types.js";
import type { JsonValue } from "../json-value.js";
export { tsEnum } from "../codegenv2/enum.js";
/**
 * Hydrate an enum descriptor.
 *
 * @private
 */
export declare function enumDesc<Shape extends number, JsonType extends JsonValue = JsonValue>(file: DescFile, path: number, ...paths: number[]): GenEnum<Shape, JsonType>;
