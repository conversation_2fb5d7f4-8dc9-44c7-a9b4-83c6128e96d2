{"version": 3, "file": "color-fn.legacy.min.cjs", "sources": ["../node_modules/core-js/internals/global.js", "../node_modules/core-js/internals/fails.js", "../node_modules/core-js/internals/descriptors.js", "../node_modules/core-js/internals/function-bind-native.js", "../node_modules/core-js/internals/function-call.js", "../node_modules/core-js/internals/create-property-descriptor.js", "../node_modules/core-js/internals/function-uncurry-this.js", "../node_modules/core-js/internals/classof-raw.js", "../node_modules/core-js/internals/is-null-or-undefined.js", "../node_modules/core-js/internals/require-object-coercible.js", "../node_modules/core-js/internals/to-indexed-object.js", "../node_modules/core-js/internals/indexed-object.js", "../node_modules/core-js/internals/is-callable.js", "../node_modules/core-js/internals/is-object.js", "../node_modules/core-js/internals/get-built-in.js", "../node_modules/core-js/internals/object-is-prototype-of.js", "../node_modules/core-js/internals/engine-v8-version.js", "../node_modules/core-js/internals/engine-user-agent.js", "../node_modules/core-js/internals/symbol-constructor-detection.js", "../node_modules/core-js/internals/use-symbol-as-uid.js", "../node_modules/core-js/internals/is-symbol.js", "../node_modules/core-js/internals/try-to-string.js", "../node_modules/core-js/internals/a-callable.js", "../node_modules/core-js/internals/get-method.js", "../node_modules/core-js/internals/ordinary-to-primitive.js", "../node_modules/core-js/internals/is-pure.js", "../node_modules/core-js/internals/define-global-property.js", "../node_modules/core-js/internals/shared-store.js", "../node_modules/core-js/internals/shared.js", "../node_modules/core-js/internals/to-object.js", "../node_modules/core-js/internals/has-own-property.js", "../node_modules/core-js/internals/uid.js", "../node_modules/core-js/internals/well-known-symbol.js", "../node_modules/core-js/internals/to-primitive.js", "../node_modules/core-js/internals/to-property-key.js", "../node_modules/core-js/internals/ie8-dom-define.js", "../node_modules/core-js/internals/document-create-element.js", "../node_modules/core-js/internals/object-get-own-property-descriptor.js", "../node_modules/core-js/internals/object-property-is-enumerable.js", "../node_modules/core-js/internals/an-object.js", "../node_modules/core-js/internals/object-define-property.js", "../node_modules/core-js/internals/v8-prototype-define-bug.js", "../node_modules/core-js/internals/create-non-enumerable-property.js", "../node_modules/core-js/internals/inspect-source.js", "../node_modules/core-js/internals/shared-key.js", "../node_modules/core-js/internals/hidden-keys.js", "../node_modules/core-js/internals/internal-state.js", "../node_modules/core-js/internals/weak-map-basic-detection.js", "../node_modules/core-js/internals/make-built-in.js", "../node_modules/core-js/internals/function-name.js", "../node_modules/core-js/internals/define-built-in.js", "../node_modules/core-js/internals/to-integer-or-infinity.js", "../node_modules/core-js/internals/math-trunc.js", "../node_modules/core-js/internals/to-absolute-index.js", "../node_modules/core-js/internals/to-length.js", "../node_modules/core-js/internals/length-of-array-like.js", "../node_modules/core-js/internals/object-keys-internal.js", "../node_modules/core-js/internals/array-includes.js", "../node_modules/core-js/internals/own-keys.js", "../node_modules/core-js/internals/object-get-own-property-names.js", "../node_modules/core-js/internals/enum-bug-keys.js", "../node_modules/core-js/internals/object-get-own-property-symbols.js", "../node_modules/core-js/internals/copy-constructor-properties.js", "../node_modules/core-js/internals/export.js", "../node_modules/core-js/internals/is-forced.js", "../node_modules/core-js/internals/is-possible-prototype.js", "../node_modules/core-js/internals/a-possible-prototype.js", "../node_modules/core-js/internals/object-set-prototype-of.js", "../node_modules/core-js/internals/function-uncurry-this-accessor.js", "../node_modules/core-js/internals/proxy-accessor.js", "../node_modules/core-js/internals/inherit-if-required.js", "../node_modules/core-js/internals/classof.js", "../node_modules/core-js/internals/to-string-tag-support.js", "../node_modules/core-js/internals/to-string.js", "../node_modules/core-js/internals/normalize-string-argument.js", "../node_modules/core-js/internals/install-error-cause.js", "../node_modules/core-js/internals/error-stack-install.js", "../node_modules/core-js/internals/error-stack-clear.js", "../node_modules/core-js/internals/error-stack-installable.js", "../node_modules/core-js/internals/wrap-error-constructor-with-cause.js", "../node_modules/core-js/modules/es.error.cause.js", "../node_modules/core-js/internals/function-apply.js", "../node_modules/core-js/internals/is-array.js", "../node_modules/core-js/internals/array-set-length.js", "../node_modules/core-js/internals/does-not-exceed-safe-integer.js", "../src/multiply-matrices.js", "../src/util.js", "../node_modules/core-js/modules/es.array.push.js", "../src/hooks.js", "../src/adapt.js", "../src/defaults.js", "../src/parse.js", "../src/getColor.js", "../src/space.js", "../src/spaces/xyz-d65.js", "../src/rgbspace.js", "../src/getAll.js", "../src/get.js", "../src/setAll.js", "../src/set.js", "../src/spaces/xyz-d50.js", "../src/spaces/lab.js", "../src/angles.js", "../src/spaces/lch.js", "../src/deltaE/deltaE2000.js", "../src/spaces/oklab.js", "../src/deltaE/deltaEOK.js", "../src/inGamut.js", "../src/clone.js", "../src/distance.js", "../src/deltaE/deltaE76.js", "../src/deltaE/deltaECMC.js", "../src/spaces/xyz-abs-d65.js", "../src/spaces/jzazbz.js", "../src/spaces/jzczhz.js", "../src/deltaE/deltaEJz.js", "../src/spaces/ictcp.js", "../src/deltaE/deltaEITP.js", "../src/spaces/cam16.js", "../src/spaces/hct.js", "../src/deltaE/deltaEHCT.js", "../src/deltaE/index.js", "../src/toGamut.js", "../src/to.js", "../node_modules/core-js/internals/delete-property-or-throw.js", "../src/serialize.js", "../node_modules/core-js/modules/es.array.unshift.js", "../src/spaces/rec2020-linear.js", "../src/spaces/rec2020.js", "../src/spaces/p3-linear.js", "../src/spaces/srgb-linear.js", "../src/keywords.js", "../src/spaces/srgb.js", "../src/spaces/p3.js", "../src/display.js", "../src/luminance.js", "../src/contrast/WCAG21.js", "../src/contrast/APCA.js", "../src/contrast/Michelson.js", "../src/contrast/Weber.js", "../src/contrast/Lstar.js", "../src/spaces/lab-d65.js", "../src/contrast/deltaPhi.js", "../src/chromaticity.js", "../src/deltaE.js", "../src/interpolation.js", "../src/spaces/hsl.js", "../src/spaces/hsv.js", "../src/spaces/hwb.js", "../src/spaces/a98rgb-linear.js", "../src/spaces/a98rgb.js", "../src/spaces/prophoto-linear.js", "../src/spaces/prophoto.js", "../src/spaces/oklch.js", "../src/spaces/luv.js", "../src/spaces/lchuv.js", "../src/spaces/hsluv.js", "../src/spaces/hpluv.js", "../src/spaces/rec2100-pq.js", "../src/spaces/rec2100-hlg.js", "../src/CATs.js", "../src/spaces/acescg.js", "../src/spaces/acescc.js", "../src/contrast.js", "../src/variations.js", "../src/equals.js"], "sourcesContent": ["'use strict';\nvar check = function (it) {\n  return it && it.Math === Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line es/no-global-this -- safe\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  // eslint-disable-next-line no-restricted-globals -- safe\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  check(typeof this == 'object' && this) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n", "'use strict';\nmodule.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n", "'use strict';\nvar fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] !== 7;\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-function-prototype-bind -- safe\n  var test = (function () { /* empty */ }).bind();\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return typeof test != 'function' || test.hasOwnProperty('prototype');\n});\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar call = Function.prototype.call;\n\nmodule.exports = NATIVE_BIND ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};\n", "'use strict';\nmodule.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar call = FunctionPrototype.call;\nvar uncurryThisWithBind = NATIVE_BIND && FunctionPrototype.bind.bind(call, call);\n\nmodule.exports = NATIVE_BIND ? uncurryThisWithBind : function (fn) {\n  return function () {\n    return call.apply(fn, arguments);\n  };\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};\n", "'use strict';\n// we can't use just `it == null` since of `document.all` special case\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot-aec\nmodule.exports = function (it) {\n  return it === null || it === undefined;\n};\n", "'use strict';\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\nvar $TypeError = TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (isNullOrUndefined(it)) throw new $TypeError(\"Can't call method on \" + it);\n  return it;\n};\n", "'use strict';\n// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar $Object = Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !$Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) === 'String' ? split(it, '') : $Object(it);\n} : $Object;\n", "'use strict';\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot\nvar documentAll = typeof document == 'object' && document.all;\n\n// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\n// eslint-disable-next-line unicorn/no-typeof-undefined -- required for testing\nmodule.exports = typeof documentAll == 'undefined' && documentAll !== undefined ? function (argument) {\n  return typeof argument == 'function' || argument === documentAll;\n} : function (argument) {\n  return typeof argument == 'function';\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (argument) {\n  return isCallable(argument) ? argument : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(global[namespace]) : global[namespace] && global[namespace][method];\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);\n", "'use strict';\nvar global = require('../internals/global');\nvar userAgent = require('../internals/engine-user-agent');\n\nvar process = global.process;\nvar Deno = global.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;\n", "'use strict';\nmodule.exports = typeof navigator != 'undefined' && String(navigator.userAgent) || '';\n", "'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\nvar fails = require('../internals/fails');\nvar global = require('../internals/global');\n\nvar $String = global.String;\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol('symbol detection');\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  // nb: Do not call `String` directly to avoid this being optimized out to `symbol+''` which will,\n  // of course, fail.\n  return !$String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n", "'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\n\nmodule.exports = NATIVE_SYMBOL\n  && !Symbol.sham\n  && typeof Symbol.iterator == 'symbol';\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar $Object = Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));\n};\n", "'use strict';\nvar $String = String;\n\nmodule.exports = function (argument) {\n  try {\n    return $String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw new $TypeError(tryToString(argument) + ' is not a function');\n};\n", "'use strict';\nvar aCallable = require('../internals/a-callable');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return isNullOrUndefined(func) ? undefined : aCallable(func);\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar $TypeError = TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw new $TypeError(\"Can't convert object to primitive value\");\n};\n", "'use strict';\nmodule.exports = false;\n", "'use strict';\nvar global = require('../internals/global');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n", "'use strict';\nvar IS_PURE = require('../internals/is-pure');\nvar globalThis = require('../internals/global');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nvar SHARED = '__core-js_shared__';\nvar store = module.exports = globalThis[SHARED] || defineGlobalProperty(SHARED, {});\n\n(store.versions || (store.versions = [])).push({\n  version: '3.36.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2024 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.36.0/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});\n", "'use strict';\nvar store = require('../internals/shared-store');\n\nmodule.exports = function (key, value) {\n  return store[key] || (store[key] = value || {});\n};\n", "'use strict';\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar $Object = Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return $Object(requireObjectCoercible(argument));\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\n// eslint-disable-next-line es/no-object-hasown -- safe\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar Symbol = global.Symbol;\nvar WellKnownSymbolsStore = shared('wks');\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol['for'] || Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name)) {\n    WellKnownSymbolsStore[name] = NATIVE_SYMBOL && hasOwn(Symbol, name)\n      ? Symbol[name]\n      : createWellKnownSymbol('Symbol.' + name);\n  } return WellKnownSymbolsStore[name];\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar $TypeError = TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw new $TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n", "'use strict';\nvar toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thanks to IE8 for its funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a !== 7;\n});\n", "'use strict';\nvar global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};\n", "'use strict';\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw new $TypeError($String(argument) + ' is not an object');\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\nvar anObject = require('../internals/an-object');\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar $TypeError = TypeError;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar $defineProperty = Object.defineProperty;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar ENUMERABLE = 'enumerable';\nvar CONFIGURABLE = 'configurable';\nvar WRITABLE = 'writable';\n\n// `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? V8_PROTOTYPE_DEFINE_BUG ? function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (typeof O === 'function' && P === 'prototype' && 'value' in Attributes && WRITABLE in Attributes && !Attributes[WRITABLE]) {\n    var current = $getOwnPropertyDescriptor(O, P);\n    if (current && current[WRITABLE]) {\n      O[P] = Attributes.value;\n      Attributes = {\n        configurable: CONFIGURABLE in Attributes ? Attributes[CONFIGURABLE] : current[CONFIGURABLE],\n        enumerable: ENUMERABLE in Attributes ? Attributes[ENUMERABLE] : current[ENUMERABLE],\n        writable: false\n      };\n    }\n  } return $defineProperty(O, P, Attributes);\n} : $defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return $defineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw new $TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\n\n// V8 ~ Chrome 36-\n// https://bugs.chromium.org/p/v8/issues/detail?id=3334\nmodule.exports = DESCRIPTORS && fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(function () { /* empty */ }, 'prototype', {\n    value: 42,\n    writable: false\n  }).prototype !== 42;\n});\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar store = require('../internals/shared-store');\n\nvar functionToString = uncurryThis(Function.toString);\n\n// this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\nif (!isCallable(store.inspectSource)) {\n  store.inspectSource = function (it) {\n    return functionToString(it);\n  };\n}\n\nmodule.exports = store.inspectSource;\n", "'use strict';\nvar shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n", "'use strict';\nmodule.exports = {};\n", "'use strict';\nvar NATIVE_WEAK_MAP = require('../internals/weak-map-basic-detection');\nvar global = require('../internals/global');\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\nvar shared = require('../internals/shared-store');\nvar sharedKey = require('../internals/shared-key');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar OBJECT_ALREADY_INITIALIZED = 'Object already initialized';\nvar TypeError = global.TypeError;\nvar WeakMap = global.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw new TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP || shared.state) {\n  var store = shared.state || (shared.state = new WeakMap());\n  /* eslint-disable no-self-assign -- prototype methods protection */\n  store.get = store.get;\n  store.has = store.has;\n  store.set = store.set;\n  /* eslint-enable no-self-assign -- prototype methods protection */\n  set = function (it, metadata) {\n    if (store.has(it)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    store.set(it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return store.get(it) || {};\n  };\n  has = function (it) {\n    return store.has(it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    if (hasOwn(it, STATE)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    createNonEnumerableProperty(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return hasOwn(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return hasOwn(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar WeakMap = global.WeakMap;\n\nmodule.exports = isCallable(WeakMap) && /native code/.test(String(WeakMap));\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar CONFIGURABLE_FUNCTION_NAME = require('../internals/function-name').CONFIGURABLE;\nvar inspectSource = require('../internals/inspect-source');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar enforceInternalState = InternalStateModule.enforce;\nvar getInternalState = InternalStateModule.get;\nvar $String = String;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nvar stringSlice = uncurryThis(''.slice);\nvar replace = uncurryThis(''.replace);\nvar join = uncurryThis([].join);\n\nvar CONFIGURABLE_LENGTH = DESCRIPTORS && !fails(function () {\n  return defineProperty(function () { /* empty */ }, 'length', { value: 8 }).length !== 8;\n});\n\nvar TEMPLATE = String(String).split('String');\n\nvar makeBuiltIn = module.exports = function (value, name, options) {\n  if (stringSlice($String(name), 0, 7) === 'Symbol(') {\n    name = '[' + replace($String(name), /^Symbol\\(([^)]*)\\).*$/, '$1') + ']';\n  }\n  if (options && options.getter) name = 'get ' + name;\n  if (options && options.setter) name = 'set ' + name;\n  if (!hasOwn(value, 'name') || (CONFIGURABLE_FUNCTION_NAME && value.name !== name)) {\n    if (DESCRIPTORS) defineProperty(value, 'name', { value: name, configurable: true });\n    else value.name = name;\n  }\n  if (CONFIGURABLE_LENGTH && options && hasOwn(options, 'arity') && value.length !== options.arity) {\n    defineProperty(value, 'length', { value: options.arity });\n  }\n  try {\n    if (options && hasOwn(options, 'constructor') && options.constructor) {\n      if (DESCRIPTORS) defineProperty(value, 'prototype', { writable: false });\n    // in V8 ~ Chrome 53, prototypes of some methods, like `Array.prototype.values`, are non-writable\n    } else if (value.prototype) value.prototype = undefined;\n  } catch (error) { /* empty */ }\n  var state = enforceInternalState(value);\n  if (!hasOwn(state, 'source')) {\n    state.source = join(TEMPLATE, typeof name == 'string' ? name : '');\n  } return value;\n};\n\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n// eslint-disable-next-line no-extend-native -- required\nFunction.prototype.toString = makeBuiltIn(function toString() {\n  return isCallable(this) && getInternalState(this).source || inspectSource(this);\n}, 'toString');\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar hasOwn = require('../internals/has-own-property');\n\nvar FunctionPrototype = Function.prototype;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;\n\nvar EXISTS = hasOwn(FunctionPrototype, 'name');\n// additional protection from minified / mangled / dropped function names\nvar PROPER = EXISTS && (function something() { /* empty */ }).name === 'something';\nvar CONFIGURABLE = EXISTS && (!DESCRIPTORS || (DESCRIPTORS && getDescriptor(FunctionPrototype, 'name').configurable));\n\nmodule.exports = {\n  EXISTS: EXISTS,\n  PROPER: PROPER,\n  CONFIGURABLE: CONFIGURABLE\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar definePropertyModule = require('../internals/object-define-property');\nvar makeBuiltIn = require('../internals/make-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nmodule.exports = function (O, key, value, options) {\n  if (!options) options = {};\n  var simple = options.enumerable;\n  var name = options.name !== undefined ? options.name : key;\n  if (isCallable(value)) makeBuiltIn(value, name, options);\n  if (options.global) {\n    if (simple) O[key] = value;\n    else defineGlobalProperty(key, value);\n  } else {\n    try {\n      if (!options.unsafe) delete O[key];\n      else if (O[key]) simple = true;\n    } catch (error) { /* empty */ }\n    if (simple) O[key] = value;\n    else definePropertyModule.f(O, key, {\n      value: value,\n      enumerable: false,\n      configurable: !options.nonConfigurable,\n      writable: !options.nonWritable\n    });\n  } return O;\n};\n", "'use strict';\nvar trunc = require('../internals/math-trunc');\n\n// `ToIntegerOrInfinity` abstract operation\n// https://tc39.es/ecma262/#sec-tointegerorinfinity\nmodule.exports = function (argument) {\n  var number = +argument;\n  // eslint-disable-next-line no-self-compare -- NaN check\n  return number !== number || number === 0 ? 0 : trunc(number);\n};\n", "'use strict';\nvar ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `Math.trunc` method\n// https://tc39.es/ecma262/#sec-math.trunc\n// eslint-disable-next-line es/no-math-trunc -- safe\nmodule.exports = Math.trunc || function trunc(x) {\n  var n = +x;\n  return (n > 0 ? floor : ceil)(n);\n};\n", "'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toIntegerOrInfinity(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n", "'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  var len = toIntegerOrInfinity(argument);\n  return len > 0 ? min(len, 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n", "'use strict';\nvar toLength = require('../internals/to-length');\n\n// `LengthOfArrayLike` abstract operation\n// https://tc39.es/ecma262/#sec-lengthofarraylike\nmodule.exports = function (obj) {\n  return toLength(obj.length);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar hasOwn = require('../internals/has-own-property');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar indexOf = require('../internals/array-includes').indexOf;\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar push = uncurryThis([].push);\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !hasOwn(hiddenKeys, key) && hasOwn(O, key) && push(result, key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (hasOwn(O, key = names[i++])) {\n    ~indexOf(result, key) || push(result, key);\n  }\n  return result;\n};\n", "'use strict';\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\nvar createMethod = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = lengthOfArrayLike(O);\n    if (length === 0) return !IS_INCLUDES && -1;\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare -- NaN check\n    if (IS_INCLUDES && el !== el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare -- NaN check\n      if (value !== value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) {\n      if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.includes` method\n  // https://tc39.es/ecma262/#sec-array.prototype.includes\n  includes: createMethod(true),\n  // `Array.prototype.indexOf` method\n  // https://tc39.es/ecma262/#sec-array.prototype.indexof\n  indexOf: createMethod(false)\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertySymbolsModule = require('../internals/object-get-own-property-symbols');\nvar anObject = require('../internals/an-object');\n\nvar concat = uncurryThis([].concat);\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? concat(keys, getOwnPropertySymbols(it)) : keys;\n};\n", "'use strict';\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\n// eslint-disable-next-line es/no-object-getownpropertynames -- safe\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n", "'use strict';\n// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n", "'use strict';\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\nexports.f = Object.getOwnPropertySymbols;\n", "'use strict';\nvar hasOwn = require('../internals/has-own-property');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source, exceptions) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!hasOwn(target, key) && !(exceptions && hasOwn(exceptions, key))) {\n      defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n    }\n  }\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = global;\n  } else if (STATIC) {\n    target = global[TARGET] || defineGlobalProperty(TARGET, {});\n  } else {\n    target = global[TARGET] && global[TARGET].prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty == typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    defineBuiltIn(target, key, sourceProperty, options);\n  }\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value === POLYFILL ? true\n    : value === NATIVE ? false\n    : isCallable(detection) ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nmodule.exports = function (argument) {\n  return isObject(argument) || argument === null;\n};\n", "'use strict';\nvar isPossiblePrototype = require('../internals/is-possible-prototype');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\nmodule.exports = function (argument) {\n  if (isPossiblePrototype(argument)) return argument;\n  throw new $TypeError(\"Can't set \" + $String(argument) + ' as a prototype');\n};\n", "'use strict';\n/* eslint-disable no-proto -- safe */\nvar uncurryThisAccessor = require('../internals/function-uncurry-this-accessor');\nvar anObject = require('../internals/an-object');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = uncurryThisAccessor(Object.prototype, '__proto__', 'set');\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    anObject(O);\n    aPossiblePrototype(proto);\n    if (CORRECT_SETTER) setter(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nmodule.exports = function (object, key, method) {\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));\n  } catch (error) { /* empty */ }\n};\n", "'use strict';\nvar defineProperty = require('../internals/object-define-property').f;\n\nmodule.exports = function (Target, Source, key) {\n  key in Target || defineProperty(Target, key, {\n    configurable: true,\n    get: function () { return Source[key]; },\n    set: function (it) { Source[key] = it; }\n  });\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\n\n// makes subclassing work correct for wrapped built-ins\nmodule.exports = function ($this, dummy, Wrapper) {\n  var NewTarget, NewTargetPrototype;\n  if (\n    // it can work only with native `setPrototypeOf`\n    setPrototypeOf &&\n    // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n    isCallable(NewTarget = dummy.constructor) &&\n    NewTarget !== Wrapper &&\n    isObject(NewTargetPrototype = NewTarget.prototype) &&\n    NewTargetPrototype !== Wrapper.prototype\n  ) setPrototypeOf($this, NewTargetPrototype);\n  return $this;\n};\n", "'use strict';\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar $Object = Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) === 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) === 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n", "'use strict';\nvar classof = require('../internals/classof');\n\nvar $String = String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw new TypeError('Cannot convert a Symbol value to a string');\n  return $String(argument);\n};\n", "'use strict';\nvar toString = require('../internals/to-string');\n\nmodule.exports = function (argument, $default) {\n  return argument === undefined ? arguments.length < 2 ? '' : $default : toString(argument);\n};\n", "'use strict';\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\n// `InstallErrorCause` abstract operation\n// https://tc39.es/proposal-error-cause/#sec-errorobjects-install-error-cause\nmodule.exports = function (O, options) {\n  if (isObject(options) && 'cause' in options) {\n    createNonEnumerableProperty(O, 'cause', options.cause);\n  }\n};\n", "'use strict';\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar clearErrorStack = require('../internals/error-stack-clear');\nvar ERROR_STACK_INSTALLABLE = require('../internals/error-stack-installable');\n\n// non-standard V8\nvar captureStackTrace = Error.captureStackTrace;\n\nmodule.exports = function (error, C, stack, dropEntries) {\n  if (ERROR_STACK_INSTALLABLE) {\n    if (captureStackTrace) captureStackTrace(error, C);\n    else createNonEnumerableProperty(error, 'stack', clearErrorStack(stack, dropEntries));\n  }\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar $Error = Error;\nvar replace = uncurryThis(''.replace);\n\nvar TEST = (function (arg) { return String(new $Error(arg).stack); })('zxcasd');\n// eslint-disable-next-line redos/no-vulnerable -- safe\nvar V8_OR_CHAKRA_STACK_ENTRY = /\\n\\s*at [^:]*:[^\\n]*/;\nvar IS_V8_OR_CHAKRA_STACK = V8_OR_CHAKRA_STACK_ENTRY.test(TEST);\n\nmodule.exports = function (stack, dropEntries) {\n  if (IS_V8_OR_CHAKRA_STACK && typeof stack == 'string' && !$Error.prepareStackTrace) {\n    while (dropEntries--) stack = replace(stack, V8_OR_CHAKRA_STACK_ENTRY, '');\n  } return stack;\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = !fails(function () {\n  var error = new Error('a');\n  if (!('stack' in error)) return true;\n  // eslint-disable-next-line es/no-object-defineproperty -- safe\n  Object.defineProperty(error, 'stack', createPropertyDescriptor(1, 7));\n  return error.stack !== 7;\n});\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar hasOwn = require('../internals/has-own-property');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar proxyAccessor = require('../internals/proxy-accessor');\nvar inheritIfRequired = require('../internals/inherit-if-required');\nvar normalizeStringArgument = require('../internals/normalize-string-argument');\nvar installErrorCause = require('../internals/install-error-cause');\nvar installErrorStack = require('../internals/error-stack-install');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IS_PURE = require('../internals/is-pure');\n\nmodule.exports = function (FULL_NAME, wrapper, FORCED, IS_AGGREGATE_ERROR) {\n  var STACK_TRACE_LIMIT = 'stackTraceLimit';\n  var OPTIONS_POSITION = IS_AGGREGATE_ERROR ? 2 : 1;\n  var path = FULL_NAME.split('.');\n  var ERROR_NAME = path[path.length - 1];\n  var OriginalError = getBuiltIn.apply(null, path);\n\n  if (!OriginalError) return;\n\n  var OriginalErrorPrototype = OriginalError.prototype;\n\n  // V8 9.3- bug https://bugs.chromium.org/p/v8/issues/detail?id=12006\n  if (!IS_PURE && hasOwn(OriginalErrorPrototype, 'cause')) delete OriginalErrorPrototype.cause;\n\n  if (!FORCED) return OriginalError;\n\n  var BaseError = getBuiltIn('Error');\n\n  var WrappedError = wrapper(function (a, b) {\n    var message = normalizeStringArgument(IS_AGGREGATE_ERROR ? b : a, undefined);\n    var result = IS_AGGREGATE_ERROR ? new OriginalError(a) : new OriginalError();\n    if (message !== undefined) createNonEnumerableProperty(result, 'message', message);\n    installErrorStack(result, WrappedError, result.stack, 2);\n    if (this && isPrototypeOf(OriginalErrorPrototype, this)) inheritIfRequired(result, this, WrappedError);\n    if (arguments.length > OPTIONS_POSITION) installErrorCause(result, arguments[OPTIONS_POSITION]);\n    return result;\n  });\n\n  WrappedError.prototype = OriginalErrorPrototype;\n\n  if (ERROR_NAME !== 'Error') {\n    if (setPrototypeOf) setPrototypeOf(WrappedError, BaseError);\n    else copyConstructorProperties(WrappedError, BaseError, { name: true });\n  } else if (DESCRIPTORS && STACK_TRACE_LIMIT in OriginalError) {\n    proxyAccessor(WrappedError, OriginalError, STACK_TRACE_LIMIT);\n    proxyAccessor(WrappedError, OriginalError, 'prepareStackTrace');\n  }\n\n  copyConstructorProperties(WrappedError, OriginalError);\n\n  if (!IS_PURE) try {\n    // Safari 13- bug: WebAssembly errors does not have a proper `.name`\n    if (OriginalErrorPrototype.name !== ERROR_NAME) {\n      createNonEnumerableProperty(OriginalErrorPrototype, 'name', ERROR_NAME);\n    }\n    OriginalErrorPrototype.constructor = WrappedError;\n  } catch (error) { /* empty */ }\n\n  return WrappedError;\n};\n", "'use strict';\n/* eslint-disable no-unused-vars -- required for functions `.length` */\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar apply = require('../internals/function-apply');\nvar wrapErrorConstructorWithCause = require('../internals/wrap-error-constructor-with-cause');\n\nvar WEB_ASSEMBLY = 'WebAssembly';\nvar WebAssembly = global[WEB_ASSEMBLY];\n\n// eslint-disable-next-line es/no-error-cause -- feature detection\nvar FORCED = new Error('e', { cause: 7 }).cause !== 7;\n\nvar exportGlobalErrorCauseWrapper = function (ERROR_NAME, wrapper) {\n  var O = {};\n  O[ERROR_NAME] = wrapErrorConstructorWithCause(ERROR_NAME, wrapper, FORCED);\n  $({ global: true, constructor: true, arity: 1, forced: FORCED }, O);\n};\n\nvar exportWebAssemblyErrorCauseWrapper = function (ERROR_NAME, wrapper) {\n  if (WebAssembly && WebAssembly[ERROR_NAME]) {\n    var O = {};\n    O[ERROR_NAME] = wrapErrorConstructorWithCause(WEB_ASSEMBLY + '.' + ERROR_NAME, wrapper, FORCED);\n    $({ target: WEB_ASSEMBLY, stat: true, constructor: true, arity: 1, forced: FORCED }, O);\n  }\n};\n\n// https://tc39.es/ecma262/#sec-nativeerror\nexportGlobalErrorCauseWrapper('Error', function (init) {\n  return function Error(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('EvalError', function (init) {\n  return function EvalError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('RangeError', function (init) {\n  return function RangeError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('ReferenceError', function (init) {\n  return function ReferenceError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('SyntaxError', function (init) {\n  return function SyntaxError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('TypeError', function (init) {\n  return function TypeError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('URIError', function (init) {\n  return function URIError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('CompileError', function (init) {\n  return function CompileError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('LinkError', function (init) {\n  return function LinkError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('RuntimeError', function (init) {\n  return function RuntimeError(message) { return apply(init, this, arguments); };\n});\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});\n", "'use strict';\nvar classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\n// eslint-disable-next-line es/no-array-isarray -- safe\nmodule.exports = Array.isArray || function isArray(argument) {\n  return classof(argument) === 'Array';\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar isArray = require('../internals/is-array');\n\nvar $TypeError = TypeError;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Safari < 13 does not throw an error in this case\nvar SILENT_ON_NON_WRITABLE_LENGTH_SET = DESCRIPTORS && !function () {\n  // makes no sense without proper strict mode support\n  if (this !== undefined) return true;\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).length = 1;\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n}();\n\nmodule.exports = SILENT_ON_NON_WRITABLE_LENGTH_SET ? function (O, length) {\n  if (isArray(O) && !getOwnPropertyDescriptor(O, 'length').writable) {\n    throw new $TypeError('Cannot set read only .length');\n  } return O.length = length;\n} : function (O, length) {\n  return O.length = length;\n};\n", "'use strict';\nvar $TypeError = TypeError;\nvar MAX_SAFE_INTEGER = 0x1FFFFFFFFFFFFF; // 2 ** 53 - 1 == 9007199254740991\n\nmodule.exports = function (it) {\n  if (it > MAX_SAFE_INTEGER) throw $TypeError('Maximum allowed index exceeded');\n  return it;\n};\n", "// A is m x n. B is n x p. product is m x p.\nexport default function multiplyMatrices (A, B) {\n\tlet m = A.length;\n\n\tif (!Array.isArray(A[0])) {\n\t\t// A is vector, convert to [[a, b, c, ...]]\n\t\tA = [A];\n\t}\n\n\tif (!Array.isArray(B[0])) {\n\t\t// B is vector, convert to [[a], [b], [c], ...]]\n\t\tB = B.map(x => [x]);\n\t}\n\n\tlet p = B[0].length;\n\tlet B_cols = B[0].map((_, i) => B.map(x => x[i])); // transpose B\n\tlet product = A.map(row => B_cols.map(col => {\n\t\tlet ret = 0;\n\n\t\tif (!Array.isArray(row)) {\n\t\t\tfor (let c of col) {\n\t\t\t\tret += row * c;\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tfor (let i = 0; i < row.length; i++) {\n\t\t\tret += row[i] * (col[i] || 0);\n\t\t}\n\n\t\treturn ret;\n\t}));\n\n\tif (m === 1) {\n\t\tproduct = product[0]; // Avoid [[a, b, c, ...]]\n\t}\n\n\tif (p === 1) {\n\t\treturn product.map(x => x[0]); // Avoid [[a], [b], [c], ...]]\n\t}\n\n\treturn product;\n}\n", "/**\n * Various utility functions\n */\n\nexport {default as multiplyMatrices} from \"./multiply-matrices.js\";\n\n/**\n * Check if a value is a string (including a String object)\n * @param {*} str - Value to check\n * @returns {boolean}\n */\nexport function isString (str) {\n\treturn type(str) === \"string\";\n}\n\n/**\n * Determine the internal JavaScript [[Class]] of an object.\n * @param {*} o - Value to check\n * @returns {string}\n */\nexport function type (o) {\n\tlet str = Object.prototype.toString.call(o);\n\n\treturn (str.match(/^\\[object\\s+(.*?)\\]$/)[1] || \"\").toLowerCase();\n}\n\nexport function serializeNumber (n, {precision, unit }) {\n\tif (isNone(n)) {\n\t\treturn \"none\";\n\t}\n\n\treturn toPrecision(n, precision) + (unit ?? \"\");\n}\n\n/**\n * Check if a value corresponds to a none argument\n * @param {*} n - Value to check\n * @returns {boolean}\n */\nexport function isNone (n) {\n\treturn Number.isNaN(n) || (n instanceof Number && n?.none);\n}\n\n/**\n * Replace none values with 0\n */\nexport function skipNone (n) {\n\treturn isNone(n) ? 0 : n;\n}\n\n/**\n * Round a number to a certain number of significant digits\n * @param {number} n - The number to round\n * @param {number} precision - Number of significant digits\n */\nexport function toPrecision (n, precision) {\n\tif (n === 0) {\n\t\treturn 0;\n\t}\n\tlet integer = ~~n;\n\tlet digits = 0;\n\tif (integer && precision) {\n\t\tdigits = ~~Math.log10(Math.abs(integer)) + 1;\n\t}\n\tconst multiplier = 10.0 ** (precision - digits);\n\treturn Math.floor(n * multiplier + 0.5) / multiplier;\n}\n\nconst angleFactor = {\n\tdeg: 1,\n\tgrad: 0.9,\n\trad: 180 / Math.PI,\n\tturn: 360,\n};\n\n/**\n* Parse a CSS function, regardless of its name and arguments\n* @param String str String to parse\n* @return {{name, args, rawArgs}}\n*/\nexport function parseFunction (str) {\n\tif (!str) {\n\t\treturn;\n\t}\n\n\tstr = str.trim();\n\n\tconst isFunctionRegex = /^([a-z]+)\\((.+?)\\)$/i;\n\tconst isNumberRegex = /^-?[\\d.]+$/;\n\tconst unitValueRegex = /%|deg|g?rad|turn$/;\n\tconst singleArgument = /\\/?\\s*(none|[-\\w.]+(?:%|deg|g?rad|turn)?)/g;\n\tlet parts = str.match(isFunctionRegex);\n\n\tif (parts) {\n\t\t// It is a function, parse args\n\t\tlet args = [];\n\t\tparts[2].replace(singleArgument, ($0, rawArg) => {\n\t\t\tlet match = rawArg.match(unitValueRegex);\n\t\t\tlet arg = rawArg;\n\n\t\t\tif (match) {\n\t\t\t\tlet unit = match[0];\n\t\t\t\t// Drop unit from value\n\t\t\t\tlet unitlessArg = arg.slice(0, -unit.length);\n\n\t\t\t\tif (unit === \"%\") {\n\t\t\t\t\t// Convert percentages to 0-1 numbers\n\t\t\t\t\targ = new Number(unitlessArg / 100);\n\t\t\t\t\targ.type = \"<percentage>\";\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// Multiply angle by appropriate factor for its unit\n\t\t\t\t\targ = new Number(unitlessArg * angleFactor[unit]);\n\t\t\t\t\targ.type = \"<angle>\";\n\t\t\t\t\targ.unit = unit;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (isNumberRegex.test(arg)) {\n\t\t\t\t// Convert numerical args to numbers\n\t\t\t\targ = new Number(arg);\n\t\t\t\targ.type = \"<number>\";\n\t\t\t}\n\t\t\telse if (arg === \"none\") {\n\t\t\t\targ = new Number(NaN);\n\t\t\t\targ.none = true;\n\t\t\t}\n\n\t\t\tif ($0.startsWith(\"/\")) {\n\t\t\t\t// It's alpha\n\t\t\t\targ = arg instanceof Number ? arg : new Number(arg);\n\t\t\t\targ.alpha = true;\n\t\t\t}\n\n\t\t\tif (typeof arg === \"object\" && arg instanceof Number) {\n\t\t\t\targ.raw = rawArg;\n\t\t\t}\n\n\t\t\targs.push(arg);\n\t\t});\n\n\t\treturn {\n\t\t\tname: parts[1].toLowerCase(),\n\t\t\trawName: parts[1],\n\t\t\trawArgs: parts[2],\n\t\t\t// An argument could be (as of css-color-4):\n\t\t\t// a number, percentage, degrees (hue), ident (in color())\n\t\t\targs,\n\t\t};\n\t}\n}\n\nexport function last (arr) {\n\treturn arr[arr.length - 1];\n}\n\nexport function interpolate (start, end, p) {\n\tif (isNaN(start)) {\n\t\treturn end;\n\t}\n\n\tif (isNaN(end)) {\n\t\treturn start;\n\t}\n\n\treturn start + (end - start) * p;\n}\n\nexport function interpolateInv (start, end, value) {\n\treturn (value - start) / (end - start);\n}\n\nexport function mapRange (from, to, value) {\n\treturn interpolate(to[0], to[1], interpolateInv(from[0], from[1], value));\n}\n\nexport function parseCoordGrammar (coordGrammars) {\n\treturn coordGrammars.map(coordGrammar => {\n\t\treturn coordGrammar.split(\"|\").map(type => {\n\t\t\ttype = type.trim();\n\t\t\tlet range = type.match(/^(<[a-z]+>)\\[(-?[.\\d]+),\\s*(-?[.\\d]+)\\]?$/);\n\n\t\t\tif (range) {\n\t\t\t\tlet ret = new String(range[1]);\n\t\t\t\tret.range = [+range[2], +range[3]];\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn type;\n\t\t});\n\t});\n}\n\n/**\n * Clamp value between the minimum and maximum\n * @param {number} min minimum value to return\n * @param {number} val the value to return if it is between min and max\n * @param {number} max maximum value to return\n * @returns number\n */\nexport function clamp (min, val, max) {\n\treturn Math.max(Math.min(max, val), min);\n}\n\n/**\n * Copy sign of one value to another.\n * @param {number} - to number to copy sign to\n * @param {number} - from number to copy sign from\n * @returns number\n */\nexport function copySign (to, from) {\n\treturn Math.sign(to) === Math.sign(from) ? to : -to;\n}\n\n/**\n * Perform pow on a signed number and copy sign to result\n * @param {number} - base the base number\n * @param {number} - exp the exponent\n * @returns number\n */\nexport function spow (base, exp) {\n\treturn copySign(Math.abs(base) ** exp, base);\n}\n\n/**\n * Perform a divide, but return zero if the numerator is zero\n * @param {number} n - the numerator\n * @param {number} d - the denominator\n * @returns number\n */\nexport function zdiv (n, d) {\n\treturn (d === 0) ? 0 : n / d;\n}\n\n/**\n * Perform a bisect on a sorted list and locate the insertion point for\n * a value in arr to maintain sorted order.\n * @param {number[]} arr - array of sorted numbers\n * @param {number} value - value to find insertion point for\n * @param {number} lo - used to specify a the low end of a subset of the list\n * @param {number} hi - used to specify a the high end of a subset of the list\n * @returns number\n */\nexport function bisectLeft (arr, value, lo = 0, hi = arr.length) {\n\twhile (lo < hi) {\n\t\tconst mid = (lo + hi) >> 1;\n\t\tif (arr[mid] < value) {\n\t\t\tlo = mid + 1;\n\t\t}\n\t\telse {\n\t\t\thi = mid;\n\t\t}\n\t}\n\treturn lo;\n}\n", "'use strict';\nvar $ = require('../internals/export');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar setArrayLength = require('../internals/array-set-length');\nvar doesNotExceedSafeInteger = require('../internals/does-not-exceed-safe-integer');\nvar fails = require('../internals/fails');\n\nvar INCORRECT_TO_LENGTH = fails(function () {\n  return [].push.call({ length: 0x100000000 }, 1) !== 4294967297;\n});\n\n// V8 <= 121 and Safari <= 15.4; FF < 23 throws InternalError\n// https://bugs.chromium.org/p/v8/issues/detail?id=12681\nvar properErrorOnNonWritableLength = function () {\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).push();\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n};\n\nvar FORCED = INCORRECT_TO_LENGTH || !properErrorOnNonWritableLength();\n\n// `Array.prototype.push` method\n// https://tc39.es/ecma262/#sec-array.prototype.push\n$({ target: 'Array', proto: true, arity: 1, forced: FORCED }, {\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  push: function push(item) {\n    var O = toObject(this);\n    var len = lengthOfArrayLike(O);\n    var argCount = arguments.length;\n    doesNotExceedSafeInteger(len + argCount);\n    for (var i = 0; i < argCount; i++) {\n      O[len] = arguments[i];\n      len++;\n    }\n    setArrayLength(O, len);\n    return len;\n  }\n});\n", "/**\n * A class for adding deep extensibility to any piece of JS code\n */\nexport class Hooks {\n\tadd (name, callback, first) {\n\t\tif (typeof arguments[0] != \"string\") {\n\t\t\t// Multiple hooks\n\t\t\tfor (var name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\tthis[name] = this[name] || [];\n\n\t\t\tif (callback) {\n\t\t\t\tthis[name][first ? \"unshift\" : \"push\"](callback);\n\t\t\t}\n\t\t}, this);\n\t}\n\n\trun (name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * The instance of {@link Hooks} used throughout Color.js\n */\nconst hooks = new Hooks();\n\nexport default hooks;\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\n\nexport const WHITES = {\n\t// for compatibility, the four-digit chromaticity-derived ones everyone else uses\n\tD50: [0.3457 / 0.3585, 1.00000, (1.0 - 0.3457 - 0.3585) / 0.3585],\n\tD65: [0.3127 / 0.3290, 1.00000, (1.0 - 0.3127 - 0.3290) / 0.3290],\n};\n\nexport function getWhite (name) {\n\tif (Array.isArray(name)) {\n\t\treturn name;\n\t}\n\n\treturn WHITES[name];\n}\n\n// Adapt XYZ from white point W1 to W2\nexport default function adapt (W1, W2, XYZ, options = {}) {\n\tW1 = getWhite(W1);\n\tW2 = getWhite(W2);\n\n\tif (!W1 || !W2) {\n\t\tthrow new TypeError(`Missing white point to convert ${!W1 ? \"from\" : \"\"}${!W1 && !W2 ? \"/\" : \"\"}${!W2 ? \"to\" : \"\"}`);\n\t}\n\n\tif (W1 === W2) {\n\t\t// Same whitepoints, no conversion needed\n\t\treturn XYZ;\n\t}\n\n\tlet env = {W1, W2, XYZ, options};\n\n\thooks.run(\"chromatic-adaptation-start\", env);\n\n\tif (!env.M) {\n\t\tif (env.W1 === WHITES.D65 && env.W2 === WHITES.D50) {\n\t\t\tenv.M = [\n\t\t\t\t[ 1.0479297925449969, 0.022946870601609652, -0.05019226628920524 ],\n\t\t\t\t[ 0.02962780877005599, 0.9904344267538799, -0.017073799063418826 ],\n\t\t\t\t[ -0.009243040646204504, 0.015055191490298152, 0.7518742814281371 ],\n\t\t\t];\n\t\t}\n\t\telse if (env.W1 === WHITES.D50 && env.W2 === WHITES.D65) {\n\n\t\t\tenv.M = [\n\t\t\t\t[ 0.955473421488075, -0.02309845494876471, 0.06325924320057072 ],\n\t\t\t\t[ -0.0283697093338637, 1.0099953980813041, 0.021041441191917323 ],\n\t\t\t\t[ 0.012314014864481998, -0.020507649298898964, 1.330365926242124 ],\n\t\t\t];\n\t\t}\n\t}\n\n\thooks.run(\"chromatic-adaptation-end\", env);\n\n\tif (env.M) {\n\t\treturn multiplyMatrices(env.M, env.XYZ);\n\t}\n\telse {\n\t\tthrow new TypeError(\"Only Bradford CAT with white points D50 and D65 supported for now.\");\n\t}\n}\n", "// Global defaults one may want to configure\nexport default {\n\tgamut_mapping: \"css\",\n\tprecision: 5,\n\tdeltaE: \"76\", // Default deltaE method\n\tverbose: globalThis?.process?.env?.NODE_ENV?.toLowerCase() !== \"test\",\n\twarn: function warn (msg) {\n\t\tif (this.verbose) {\n\t\t\tglobalThis?.console?.warn?.(msg);\n\t\t}\n\t},\n};\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\n\nconst noneTypes = new Set([\"<number>\", \"<percentage>\", \"<angle>\"]);\n\n/**\n * Validates the coordinates of a color against a format's coord grammar and\n * maps the coordinates to the range or refRange of the coordinates.\n * @param {ColorSpace} space - Colorspace the coords are in\n * @param {object} format - the format object to validate against\n * @param {string} name - the name of the color function. e.g. \"oklab\" or \"color\"\n * @returns {object[]} - an array of type metadata for each coordinate\n */\nfunction coerceCoords (space, format, name, coords) {\n\tlet types = Object.entries(space.coords).map(([id, coordMeta], i) => {\n\t\tlet coordGrammar = format.coordGrammar[i];\n\t\tlet arg = coords[i];\n\t\tlet providedType = arg?.type;\n\n\t\t// Find grammar alternative that matches the provided type\n\t\t// Non-strict equals is intentional because we are comparing w/ string objects\n\t\tlet type;\n\t\tif (arg.none) {\n\t\t\ttype = coordGrammar.find(c => noneTypes.has(c));\n\t\t}\n\t\telse {\n\t\t\ttype = coordGrammar.find(c => c == providedType);\n\t\t}\n\n\t\t// Check that each coord conforms to its grammar\n\t\tif (!type) {\n\t\t\t// Type does not exist in the grammar, throw\n\t\t\tlet coordName = coordMeta.name || id;\n\t\t\tthrow new TypeError(`${providedType ?? arg.raw} not allowed for ${coordName} in ${name}()`);\n\t\t}\n\n\t\tlet fromRange = type.range;\n\n\t\tif (providedType === \"<percentage>\") {\n\t\t\tfromRange ||= [0, 1];\n\t\t}\n\n\t\tlet toRange = coordMeta.range || coordMeta.refRange;\n\n\t\tif (fromRange && toRange) {\n\t\t\tcoords[i] = util.mapRange(fromRange, toRange, coords[i]);\n\t\t}\n\n\t\treturn type;\n\t});\n\n\treturn types;\n}\n\n\n/**\n * Convert a CSS Color string to a color object\n * @param {string} str\n * @param {object} [options]\n * @param {object} [options.meta] - Object for additional information about the parsing\n * @returns {Color}\n */\nexport default function parse (str, {meta} = {}) {\n\tlet env = {\"str\": String(str)?.trim()};\n\thooks.run(\"parse-start\", env);\n\n\tif (env.color) {\n\t\treturn env.color;\n\t}\n\n\tenv.parsed = util.parseFunction(env.str);\n\n\tif (env.parsed) {\n\t\t// Is a functional syntax\n\t\tlet name = env.parsed.name;\n\n\t\tif (name === \"color\") {\n\t\t\t// color() function\n\t\t\tlet id = env.parsed.args.shift();\n\t\t\t// Check against both <dashed-ident> and <ident> versions\n\t\t\tlet alternateId = id.startsWith(\"--\") ? id.substring(2) : `--${id}`;\n\t\t\tlet ids = [id, alternateId];\n\t\t\tlet alpha = env.parsed.rawArgs.indexOf(\"/\") > 0 ? env.parsed.args.pop() : 1;\n\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\tlet colorSpec = space.getFormat(\"color\");\n\n\t\t\t\tif (colorSpec) {\n\t\t\t\t\tif (ids.includes(colorSpec.id) || colorSpec.ids?.filter((specId) => ids.includes(specId)).length) {\n\t\t\t\t\t\t// From https://drafts.csswg.org/css-color-4/#color-function\n\t\t\t\t\t\t// If more <number>s or <percentage>s are provided than parameters that the colorspace takes, the excess <number>s at the end are ignored.\n\t\t\t\t\t\t// If less <number>s or <percentage>s are provided than parameters that the colorspace takes, the missing parameters default to 0. (This is particularly convenient for multichannel printers where the additional inks are spot colors or varnishes that most colors on the page won’t use.)\n\t\t\t\t\t\tconst coords = Object.keys(space.coords).map((_, i) => env.parsed.args[i] || 0);\n\n\t\t\t\t\t\tlet types;\n\n\t\t\t\t\t\tif (colorSpec.coordGrammar) {\n\t\t\t\t\t\t\ttypes = coerceCoords(space, colorSpec, \"color\", coords);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (meta) {\n\t\t\t\t\t\t\tObject.assign(meta, {formatId: \"color\", types});\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (colorSpec.id.startsWith(\"--\") && !id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a non-standard space and not currently supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use prefixed color(${colorSpec.id}) instead of color(${id}).`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (id.startsWith(\"--\") && !colorSpec.id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a standard space and supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use color(${colorSpec.id}) instead of prefixed color(${id}).`);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {spaceId: space.id, coords, alpha};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Not found\n\t\t\tlet didYouMean = \"\";\n\t\t\tlet registryId = id in ColorSpace.registry ? id : alternateId;\n\t\t\tif (registryId in ColorSpace.registry) {\n\t\t\t\t// Used color space id instead of color() id, these are often different\n\t\t\t\tlet cssId = ColorSpace.registry[registryId].formats?.color?.id;\n\n\t\t\t\tif (cssId) {\n\t\t\t\t\tdidYouMean = `Did you mean color(${cssId})?`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthrow new TypeError(`Cannot parse color(${id}). ` + (didYouMean || \"Missing a plugin?\"));\n\t\t}\n\t\telse {\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\t// color space specific function\n\t\t\t\tlet format = space.getFormat(name);\n\t\t\t\tif (format && format.type === \"function\") {\n\t\t\t\t\tlet alpha = 1;\n\n\t\t\t\t\tif (format.lastAlpha || util.last(env.parsed.args).alpha) {\n\t\t\t\t\t\talpha = env.parsed.args.pop();\n\t\t\t\t\t}\n\n\t\t\t\t\tlet coords = env.parsed.args;\n\n\t\t\t\t\tlet types;\n\n\t\t\t\t\tif (format.coordGrammar) {\n\t\t\t\t\t\ttypes = coerceCoords(space, format, name, coords);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tObject.assign(meta, {formatId: format.name, types});\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tspaceId: space.id,\n\t\t\t\t\t\tcoords, alpha,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\telse {\n\t\t// Custom, colorspace-specific format\n\t\tfor (let space of ColorSpace.all) {\n\t\t\tfor (let formatId in space.formats) {\n\t\t\t\tlet format = space.formats[formatId];\n\n\t\t\t\tif (format.type !== \"custom\") {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (format.test && !format.test(env.str)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tlet color = format.parse(env.str);\n\n\t\t\t\tif (color) {\n\t\t\t\t\tcolor.alpha ??= 1;\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tmeta.formatId = formatId;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t// If we're here, we couldn't parse\n\tthrow new TypeError(`Could not parse ${str} as a color. Missing a plugin?`);\n}\n", "import ColorSpace from \"./space.js\";\nimport {isString} from \"./util.js\";\nimport parse from \"./parse.js\";\n\n/**\n * Resolves a color reference (object or string) to a plain color object\n * @param {Color | {space, coords, alpha} | string | Array<Color | {space, coords, alpha} | string> } color\n * @returns {{space, coords, alpha} | Array<{space, coords, alpha}}>\n */\nexport default function getColor (color) {\n\tif (Array.isArray(color)) {\n\t\treturn color.map(getColor);\n\t}\n\n\tif (!color) {\n\t\tthrow new TypeError(\"Empty color reference\");\n\t}\n\n\tif (isString(color)) {\n\t\tcolor = parse(color);\n\t}\n\n\t// Object fixup\n\tlet space = color.space || color.spaceId;\n\n\tif (!(space instanceof ColorSpace)) {\n\t\t// Convert string id to color space object\n\t\tcolor.space = ColorSpace.get(space);\n\t}\n\n\tif (color.alpha === undefined) {\n\t\tcolor.alpha = 1;\n\t}\n\n\treturn color;\n}\n", "import {type, parseCoordGrammar, serialize<PERSON><PERSON>ber, mapRange} from \"./util.js\";\nimport {getWhite} from \"./adapt.js\";\nimport hooks from \"./hooks.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Class to represent a color space\n */\nexport default class ColorSpace {\n\tconstructor (options) {\n\t\tthis.id = options.id;\n\t\tthis.name = options.name;\n\t\tthis.base = options.base ? ColorSpace.get(options.base) : null;\n\t\tthis.aliases = options.aliases;\n\n\t\tif (this.base) {\n\t\t\tthis.fromBase = options.fromBase;\n\t\t\tthis.toBase = options.toBase;\n\t\t}\n\n\t\t// Coordinate metadata\n\n\t\tlet coords = options.coords ?? this.base.coords;\n\n\t\tfor (let name in coords) {\n\t\t\tif (!(\"name\" in coords[name])) {\n\t\t\t\tcoords[name].name = name;\n\t\t\t}\n\t\t}\n\t\tthis.coords = coords;\n\n\t\t// White point\n\n\t\tlet white = options.white ?? this.base.white ?? \"D65\";\n\t\tthis.white = getWhite(white);\n\n\t\t// Sort out formats\n\n\t\tthis.formats = options.formats ?? {};\n\n\t\tfor (let name in this.formats) {\n\t\t\tlet format = this.formats[name];\n\t\t\tformat.type ||= \"function\";\n\t\t\tformat.name ||= name;\n\t\t}\n\n\t\tif (!this.formats.color?.id) {\n\t\t\tthis.formats.color = {\n\t\t\t\t...this.formats.color ?? {},\n\t\t\t\tid: options.cssId || this.id,\n\t\t\t};\n\t\t}\n\n\t\t// Gamut space\n\n\t\tif (options.gamutSpace) {\n\t\t\t// Gamut space explicitly specified\n\t\t\tthis.gamutSpace = options.gamutSpace === \"self\" ? this : ColorSpace.get(options.gamutSpace);\n\t\t}\n\t\telse {\n\t\t\t// No gamut space specified, calculate a sensible default\n\t\t\tif (this.isPolar) {\n\t\t\t\t// Do not check gamut through polar coordinates\n\t\t\t\tthis.gamutSpace = this.base;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.gamutSpace =  this;\n\t\t\t}\n\t\t}\n\n\t\t// Optimize inGamut for unbounded spaces\n\t\tif (this.gamutSpace.isUnbounded) {\n\t\t\tthis.inGamut = (coords, options) => {\n\t\t\t\treturn true;\n\t\t\t};\n\t\t}\n\n\t\t// Other stuff\n\t\tthis.referred = options.referred;\n\n\t\t// Compute ancestors and store them, since they will never change\n\t\tObject.defineProperty(this, \"path\", {\n\t\t\tvalue: getPath(this).reverse(),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t});\n\n\t\thooks.run(\"colorspace-init-end\", this);\n\t}\n\n\tinGamut (coords, {epsilon = ε} = {}) {\n\t\tif (!this.equals(this.gamutSpace)) {\n\t\t\tcoords = this.to(this.gamutSpace, coords);\n\t\t\treturn this.gamutSpace.inGamut(coords, {epsilon});\n\t\t}\n\n\t\tlet coordMeta = Object.values(this.coords);\n\n\t\treturn coords.every((c, i) => {\n\t\t\tlet meta = coordMeta[i];\n\n\t\t\tif (meta.type !== \"angle\" && meta.range) {\n\t\t\t\tif (Number.isNaN(c)) {\n\t\t\t\t\t// NaN is always in gamut\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tlet [min, max] = meta.range;\n\t\t\t\treturn (min === undefined || c >= min - epsilon)\n\t\t\t\t    && (max === undefined || c <= max + epsilon);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t});\n\t}\n\n\tget isUnbounded () {\n\t\treturn Object.values(this.coords).every(coord => !(\"range\" in coord));\n\t}\n\n\tget cssId () {\n\t\treturn this.formats?.color?.id || this.id;\n\t}\n\n\tget isPolar () {\n\t\tfor (let id in this.coords) {\n\t\t\tif (this.coords[id].type === \"angle\") {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tgetFormat (format) {\n\t\tif (typeof format === \"object\") {\n\t\t\tformat = processFormat(format, this);\n\t\t\treturn format;\n\t\t}\n\n\t\tlet ret;\n\t\tif (format === \"default\") {\n\t\t\t// Get first format\n\t\t\tret = Object.values(this.formats)[0];\n\t\t}\n\t\telse {\n\t\t\tret = this.formats[format];\n\t\t}\n\n\t\tif (ret) {\n\t\t\tret = processFormat(ret, this);\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Check if this color space is the same as another color space reference.\n\t * Allows proxying color space objects and comparing color spaces with ids.\n\t * @param {string | ColorSpace} space ColorSpace object or id to compare to\n\t * @returns {boolean}\n\t */\n\tequals (space) {\n\t\tif (!space) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this === space || this.id === space || this.id === space.id;\n\t}\n\n\tto (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (this.equals(space)) {\n\t\t\t// Same space, no change needed\n\t\t\treturn coords;\n\t\t}\n\n\t\t// Convert NaN to 0, which seems to be valid in every coordinate of every color space\n\t\tcoords = coords.map(c => Number.isNaN(c) ? 0 : c);\n\n\t\t// Find connection space = lowest common ancestor in the base tree\n\t\tlet myPath = this.path;\n\t\tlet otherPath = space.path;\n\n\t\tlet connectionSpace, connectionSpaceIndex;\n\n\t\tfor (let i = 0; i < myPath.length; i++) {\n\t\t\tif (myPath[i].equals(otherPath[i])) {\n\t\t\t\tconnectionSpace = myPath[i];\n\t\t\t\tconnectionSpaceIndex = i;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (!connectionSpace) {\n\t\t\t// This should never happen\n\t\t\tthrow new Error(`Cannot convert between color spaces ${this} and ${space}: no connection space was found`);\n\t\t}\n\n\t\t// Go up from current space to connection space\n\t\tfor (let i = myPath.length - 1; i > connectionSpaceIndex; i--) {\n\t\t\tcoords = myPath[i].toBase(coords);\n\t\t}\n\n\t\t// Go down from connection space to target space\n\t\tfor (let i = connectionSpaceIndex + 1; i < otherPath.length; i++) {\n\t\t\tcoords = otherPath[i].fromBase(coords);\n\t\t}\n\n\t\treturn coords;\n\t}\n\n\tfrom (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\treturn space.to(this, coords);\n\t}\n\n\ttoString () {\n\t\treturn `${this.name} (${this.id})`;\n\t}\n\n\tgetMinCoords () {\n\t\tlet ret = [];\n\n\t\tfor (let id in this.coords) {\n\t\t\tlet meta = this.coords[id];\n\t\t\tlet range = meta.range || meta.refRange;\n\t\t\tret.push(range?.min ?? 0);\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tstatic registry = {};\n\n\t// Returns array of unique color spaces\n\tstatic get all () {\n\t\treturn [...new Set(Object.values(ColorSpace.registry))];\n\t}\n\n\tstatic register (id, space) {\n\t\tif (arguments.length === 1) {\n\t\t\tspace = arguments[0];\n\t\t\tid = space.id;\n\t\t}\n\n\t\tspace = this.get(space);\n\n\t\tif (this.registry[id] && this.registry[id] !== space) {\n\t\t\tthrow new Error(`Duplicate color space registration: '${id}'`);\n\t\t}\n\t\tthis.registry[id] = space;\n\n\t\t// Register aliases when called without an explicit ID.\n\t\tif (arguments.length === 1 && space.aliases) {\n\t\t\tfor (let alias of space.aliases) {\n\t\t\t\tthis.register(alias, space);\n\t\t\t}\n\t\t}\n\n\t\treturn space;\n\t}\n\n\t/**\n\t * Lookup ColorSpace object by name\n\t * @param {ColorSpace | string} name\n\t */\n\tstatic get (space, ...alternatives) {\n\t\tif (!space || space instanceof ColorSpace) {\n\t\t\treturn space;\n\t\t}\n\n\t\tlet argType = type(space);\n\n\t\tif (argType === \"string\") {\n\t\t\t// It's a color space id\n\t\t\tlet ret = ColorSpace.registry[space.toLowerCase()];\n\n\t\t\tif (!ret) {\n\t\t\t\tthrow new TypeError(`No color space found with id = \"${space}\"`);\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tif (alternatives.length) {\n\t\t\treturn ColorSpace.get(...alternatives);\n\t\t}\n\n\t\tthrow new TypeError(`${space} is not a valid color space`);\n\t}\n\n\t/**\n\t * Get metadata about a coordinate of a color space\n\t *\n\t * @static\n\t * @param {Array | string} ref\n\t * @param {ColorSpace | string} [workingSpace]\n\t * @return {Object}\n\t */\n\tstatic resolveCoord (ref, workingSpace) {\n\t\tlet coordType = type(ref);\n\t\tlet space, coord;\n\n\t\tif (coordType === \"string\") {\n\t\t\tif (ref.includes(\".\")) {\n\t\t\t\t// Absolute coordinate\n\t\t\t\t[space, coord] = ref.split(\".\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Relative coordinate\n\t\t\t\t[space, coord] = [, ref];\n\t\t\t}\n\t\t}\n\t\telse if (Array.isArray(ref)) {\n\t\t\t[space, coord] = ref;\n\t\t}\n\t\telse {\n\t\t\t// Object\n\t\t\tspace = ref.space;\n\t\t\tcoord = ref.coordId;\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (!space) {\n\t\t\tspace = workingSpace;\n\t\t}\n\n\t\tif (!space) {\n\t\t\tthrow new TypeError(`Cannot resolve coordinate reference ${ref}: No color space specified and relative references are not allowed here`);\n\t\t}\n\n\t\tcoordType = type(coord);\n\n\t\tif (coordType === \"number\" || coordType === \"string\" && coord >= 0) {\n\t\t\t// Resolve numerical coord\n\t\t\tlet meta = Object.entries(space.coords)[coord];\n\n\t\t\tif (meta) {\n\t\t\t\treturn {space, id: meta[0], index: coord, ...meta[1]};\n\t\t\t}\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tlet normalizedCoord = coord.toLowerCase();\n\n\t\tlet i = 0;\n\t\tfor (let id in space.coords) {\n\t\t\tlet meta = space.coords[id];\n\n\t\t\tif (id.toLowerCase() === normalizedCoord || meta.name?.toLowerCase() === normalizedCoord) {\n\t\t\t\treturn {space, id, index: i, ...meta};\n\t\t\t}\n\n\t\t\ti++;\n\t\t}\n\n\t\tthrow new TypeError(`No \"${coord}\" coordinate found in ${space.name}. Its coordinates are: ${Object.keys(space.coords).join(\", \")}`);\n\t}\n\n\tstatic DEFAULT_FORMAT = {\n\t\ttype: \"functions\",\n\t\tname: \"color\",\n\t};\n}\n\nfunction getPath (space) {\n\tlet ret = [space];\n\n\tfor (let s = space; s = s.base;) {\n\t\tret.push(s);\n\t}\n\n\treturn ret;\n}\n\nfunction processFormat (format, {coords} = {}) {\n\tif (format.coords && !format.coordGrammar) {\n\t\tformat.type ||= \"function\";\n\t\tformat.name ||= \"color\";\n\n\t\t// Format has not been processed\n\t\tformat.coordGrammar = parseCoordGrammar(format.coords);\n\n\t\tlet coordFormats = Object.entries(coords).map(([id, coordMeta], i) => {\n\t\t\t// Preferred format for each coord is the first one\n\t\t\tlet outputType = format.coordGrammar[i][0];\n\n\t\t\tlet fromRange = coordMeta.range || coordMeta.refRange;\n\t\t\tlet toRange = outputType.range, suffix = \"\";\n\n\t\t\t// Non-strict equals intentional since outputType could be a string object\n\t\t\tif (outputType == \"<percentage>\") {\n\t\t\t\ttoRange = [0, 100];\n\t\t\t\tsuffix = \"%\";\n\t\t\t}\n\t\t\telse if (outputType == \"<angle>\") {\n\t\t\t\tsuffix = \"deg\";\n\t\t\t}\n\n\t\t\treturn  {fromRange, toRange, suffix};\n\t\t});\n\n\t\tformat.serializeCoords = (coords, precision) => {\n\t\t\treturn coords.map((c, i) => {\n\t\t\t\tlet {fromRange, toRange, suffix} = coordFormats[i];\n\n\t\t\t\tif (fromRange && toRange) {\n\t\t\t\t\tc = mapRange(fromRange, toRange, c);\n\t\t\t\t}\n\n\t\t\t\tc = serializeNumber(c, {precision, unit: suffix});\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t};\n\t}\n\n\treturn format;\n}\n", "import ColorSpace from \"../space.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d65\",\n\tname: \"XYZ D65\",\n\tcoords: {\n\t\tx: {name: \"X\"},\n\t\ty: {name: \"Y\"},\n\t\tz: {name: \"Z\"},\n\t},\n\twhite: \"D65\",\n\tformats: {\n\t\tcolor: {\n\t\t\tids: [\"xyz-d65\", \"xyz\"],\n\t\t},\n\t},\n\taliases: [\"xyz\"],\n});\n", "import ColorSpace from \"./space.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport adapt from \"./adapt.js\";\nimport XYZ_D65 from \"./spaces/xyz-d65.js\";\n\n/**\n * Convenience class for RGB color spaces\n * @extends {ColorSpace}\n */\nexport default class RGBColorSpace extends ColorSpace {\n\t/**\n\t * Creates a new RGB ColorSpace.\n\t * If coords are not specified, they will use the default RGB coords.\n\t * Instead of `fromBase()` and `toBase()` functions,\n\t * you can specify to/from XYZ matrices and have `toBase()` and `fromBase()` automatically generated.\n\t * @param {*} options - Same options as {@link ColorSpace} plus:\n\t * @param {number[][]} options.toXYZ_M - Matrix to convert to XYZ\n\t * @param {number[][]} options.fromXYZ_M - Matrix to convert from XYZ\n\t */\n\tconstructor (options) {\n\t\tif (!options.coords) {\n\t\t\toptions.coords = {\n\t\t\t\tr: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"Red\",\n\t\t\t\t},\n\t\t\t\tg: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t\tb: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif (!options.base) {\n\t\t\toptions.base = XYZ_D65;\n\t\t}\n\n\t\tif (options.toXYZ_M && options.fromXYZ_M) {\n\t\t\toptions.toBase ??= rgb => {\n\t\t\t\tlet xyz = multiplyMatrices(options.toXYZ_M, rgb);\n\n\t\t\t\tif (this.white !== this.base.white) {\n\t\t\t\t\t// Perform chromatic adaptation\n\t\t\t\t\txyz = adapt(this.white, this.base.white, xyz);\n\t\t\t\t}\n\n\t\t\t\treturn xyz;\n\t\t\t};\n\n\t\t\toptions.fromBase ??= xyz => {\n\t\t\t\txyz = adapt(this.base.white, this.white, xyz);\n\t\t\t\treturn multiplyMatrices(options.fromXYZ_M, xyz);\n\t\t\t};\n\t\t}\n\n\t\toptions.referred ??= \"display\";\n\n\t\tsuper(options);\n\t}\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\n/**\n * Get the coordinates of a color in any color space\n * @param {Color} color\n * @param {string | ColorSpace} [space = color.space] The color space to convert to. Defaults to the color's current space\n * @returns {number[]} The color coordinates in the given color space\n */\nexport default function getAll (color, space) {\n\tcolor = getColor(color);\n\n\tif (!space || color.space.equals(space)) {\n\t\t// No conversion needed\n\t\treturn color.coords.slice();\n\t}\n\n\tspace = ColorSpace.get(space);\n\treturn space.from(color);\n}\n", "import ColorSpace from \"./space.js\";\nimport getAll from \"./getAll.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function get (color, prop) {\n\tcolor = getColor(color);\n\n\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\tlet coords = getAll(color, space);\n\treturn coords[index];\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function setAll (color, space, coords) {\n\tcolor = getColor(color);\n\n\tspace = ColorSpace.get(space);\n\tcolor.coords = space.to(color.space, coords);\n\treturn color;\n}\n\nsetAll.returns = \"color\";\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\nimport get from \"./get.js\";\nimport getAll from \"./getAll.js\";\nimport setAll from \"./setAll.js\";\nimport {type} from \"./util.js\";\n\n// Set properties and return current instance\nexport default function set (color, prop, value) {\n\tcolor = getColor(color);\n\n\tif (arguments.length === 2 && type(arguments[1]) === \"object\") {\n\t\t// Argument is an object literal\n\t\tlet object = arguments[1];\n\t\tfor (let p in object) {\n\t\t\tset(color, p, object[p]);\n\t\t}\n\t}\n\telse {\n\t\tif (typeof value === \"function\") {\n\t\t\tvalue = value(get(color, prop));\n\t\t}\n\n\t\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\t\tlet coords = getAll(color, space);\n\t\tcoords[index] = value;\n\t\tsetAll(color, space, coords);\n\t}\n\n\treturn color;\n}\n\nset.returns = \"color\";\n", "import ColorSpace from \"../space.js\";\nimport adapt from \"../adapt.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d50\",\n\tname: \"XYZ D50\",\n\twhite: \"D50\",\n\tbase: XYZ_D65,\n\tfromBase: coords => adapt(XYZ_D65.white, \"D50\", coords),\n\ttoBase: coords => adapt(\"D50\", XYZ_D65.white, coords),\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d50 from \"./xyz-d50.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D50;\n\nexport default new ColorSpace({\n\tid: \"lab\",\n\tname: \"Lab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D50, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d50,\n\t// Convert D50-adapted XYX to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D50-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "export function constrain (angle) {\n\treturn ((angle % 360) + 360) % 360;\n}\n\nexport function adjust (arc, angles) {\n\tif (arc === \"raw\") {\n\t\treturn angles;\n\t}\n\n\tlet [a1, a2] = angles.map(constrain);\n\n\tlet angleDiff = a2 - a1;\n\n\tif (arc === \"increasing\") {\n\t\tif (angleDiff < 0) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\telse if (arc === \"decreasing\") {\n\t\tif (angleDiff > 0) {\n\t\t\ta1 += 360;\n\t\t}\n\t}\n\telse if (arc === \"longer\") {\n\t\tif (-180 < angleDiff && angleDiff < 180) {\n\t\t\tif (angleDiff > 0) {\n\t\t\t\ta1 += 360;\n\t\t\t}\n\t\t\telse {\n\t\t\t\ta2 += 360;\n\t\t\t}\n\t\t}\n\t}\n\telse if (arc === \"shorter\") {\n\t\tif (angleDiff > 180) {\n\t\t\ta1 += 360;\n\t\t}\n\t\telse if (angleDiff < -180) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\n\treturn [a1, a2];\n}\n", "import ColorSpace from \"../space.js\";\nimport Lab from \"./lab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lch\",\n\tname: \"<PERSON><PERSON>\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 150],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Lab,\n\tfromBase (Lab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = Lab;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // a\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // b\n\t\t];\n\t},\n\n\tformats: {\n\t\t\"lch\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import defaults from \"../defaults.js\";\nimport lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// deltaE2000 is a statistically significant improvement\n// and is recommended by the CIE and Idealliance\n// especially for color differences less than 10 deltaE76\n// but is wicked complicated\n// and many implementations have small errors!\n// DeltaE2000 is also discontinuous; in case this\n// matters to you, use deltaECMC instead.\n\nconst Gfactor = 25 ** 7;\nconst π = Math.PI;\nconst r2d = 180 / π;\nconst d2r = π / 180;\n\nfunction pow7 (x) {\n\t// Faster than x ** 7 or Math.pow(x, 7)\n\n\tconst x2 = x * x;\n\tconst x7 = x2 * x2 * x2 * x;\n\n\treturn x7;\n}\n\nexport default function (color, sample, {kL = 1, kC = 1, kH = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and the function parameter as the sample,\n\t// calculate deltaE 2000.\n\n\t// This implementation assumes the parametric\n\t// weighting factors kL, kC and kH\n\t// for the influence of viewing conditions\n\t// are all 1, as sadly seems typical.\n\t// kL should be increased for lightness texture or noise\n\t// and kC increased for chroma noise\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet C1 = lch.from(lab, [L1, a1, b1])[1];\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\tlet Cbar = (C1 + C2) / 2; // mean Chroma\n\n\t// calculate a-axis asymmetry factor from mean Chroma\n\t// this turns JND ellipses for near-neutral colors back into circles\n\tlet C7 = pow7(Cbar);\n\n\tlet G = 0.5 * (1 - Math.sqrt(C7 / (C7 + Gfactor)));\n\n\t// scale a axes by asymmetry factor\n\t// this by the way is why there is no Lab2000 colorspace\n\tlet adash1 = (1 + G) * a1;\n\tlet adash2 = (1 + G) * a2;\n\n\t// calculate new Chroma from scaled a and original b axes\n\tlet Cdash1 = Math.sqrt(adash1 ** 2 + b1 ** 2);\n\tlet Cdash2 = Math.sqrt(adash2 ** 2 + b2 ** 2);\n\n\t// calculate new hues, with zero hue for true neutrals\n\t// and in degrees, not radians\n\n\tlet h1 = (adash1 === 0 && b1 === 0) ? 0 : Math.atan2(b1, adash1);\n\tlet h2 = (adash2 === 0 && b2 === 0) ? 0 : Math.atan2(b2, adash2);\n\n\tif (h1 < 0) {\n\t\th1 += 2 * π;\n\t}\n\tif (h2 < 0) {\n\t\th2 += 2 * π;\n\t}\n\n\th1 *= r2d;\n\th2 *= r2d;\n\n\t// Lightness and Chroma differences; sign matters\n\tlet ΔL = L2 - L1;\n\tlet ΔC = Cdash2 - Cdash1;\n\n\t// Hue difference, getting the sign correct\n\tlet hdiff = h2 - h1;\n\tlet hsum = h1 + h2;\n\tlet habs = Math.abs(hdiff);\n\tlet Δh;\n\n\tif (Cdash1 * Cdash2 === 0) {\n\t\tΔh = 0;\n\t}\n\telse if (habs <= 180) {\n\t\tΔh = hdiff;\n\t}\n\telse if (hdiff > 180) {\n\t\tΔh = hdiff - 360;\n\t}\n\telse if (hdiff < -180) {\n\t\tΔh = hdiff + 360;\n\t}\n\telse {\n\t\tdefaults.warn(\"the unthinkable has happened\");\n\t}\n\n\t// weighted Hue difference, more for larger Chroma\n\tlet ΔH = 2 * Math.sqrt(Cdash2 * Cdash1) * Math.sin(Δh * d2r / 2);\n\n\t// calculate mean Lightness and Chroma\n\tlet Ldash = (L1 + L2) / 2;\n\tlet Cdash = (Cdash1 + Cdash2) / 2;\n\tlet Cdash7 = pow7(Cdash);\n\n\t// Compensate for non-linearity in the blue region of Lab.\n\t// Four possibilities for hue weighting factor,\n\t// depending on the angles, to get the correct sign\n\tlet hdash;\n\tif (Cdash1 * Cdash2 === 0) {\n\t\thdash = hsum;   // which should be zero\n\t}\n\telse if (habs <= 180) {\n\t\thdash = hsum / 2;\n\t}\n\telse if (hsum < 360) {\n\t\thdash = (hsum + 360) / 2;\n\t}\n\telse {\n\t\thdash = (hsum - 360) / 2;\n\t}\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor\n\t// a background with L=50 is assumed\n\tlet lsq = (Ldash - 50) ** 2;\n\tlet SL = 1 + ((0.015 * lsq) / Math.sqrt(20 + lsq));\n\n\t// SC Chroma factor, similar to those in CMC and deltaE 94 formulae\n\tlet SC = 1 + 0.045 * Cdash;\n\n\t// Cross term T for blue non-linearity\n\tlet T = 1;\n\tT -= (0.17 * Math.cos((     hdash - 30)  * d2r));\n\tT += (0.24 * Math.cos(  2 * hdash        * d2r));\n\tT += (0.32 * Math.cos(((3 * hdash) + 6)  * d2r));\n\tT -= (0.20 * Math.cos(((4 * hdash) - 63) * d2r));\n\n\t// SH Hue factor depends on Chroma,\n\t// as well as adjusted hue angle like deltaE94.\n\tlet SH = 1 + 0.015 * Cdash * T;\n\n\t// RT Hue rotation term compensates for rotation of JND ellipses\n\t// and Munsell constant hue lines\n\t// in the medium-high Chroma blue region\n\t// (Hue 225 to 315)\n\tlet Δθ = 30 * Math.exp(-1 * (((hdash - 275) / 25) ** 2));\n\tlet RC = 2 * Math.sqrt(Cdash7 / (Cdash7 + Gfactor));\n\tlet RT = -1 * Math.sin(2 * Δθ * d2r) * RC;\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (kL * SL)) ** 2;\n\tdE += (ΔC / (kC * SC)) ** 2;\n\tdE += (ΔH / (kH * SH)) ** 2;\n\tdE += RT * (ΔC / (kC * SC)) * (ΔH / (kH * SH));\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\n// Recalculated for consistent reference white\n// see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484\nconst XY<PERSON>toLMS_M = [\n\t[ 0.8190224379967030, 0.3619062600528904, -0.1288737815209879 ],\n\t[ 0.0329836539323885, 0.9292868615863434,  0.0361446663506424 ],\n\t[ 0.0481771893596242, 0.2642395317527308,  0.6335478284694309 ],\n];\n// inverse of XYZtoLMS_M\nconst LMStoXYZ_M = [\n\t[  1.2268798758459243, -0.5578149944602171,  0.2813910456659647 ],\n\t[ -0.0405757452148008,  1.1122868032803170, -0.0717110580655164 ],\n\t[ -0.0763729366746601, -0.4214933324022432,  1.5869240198367816 ],\n];\nconst LMStoLab_M = [\n\t[ 0.2104542683093140,  0.7936177747023054, -0.0040720430116193 ],\n\t[ 1.9779985324311684, -2.4285922420485799,  0.4505937096174110 ],\n\t[ 0.0259040424655478,  0.7827717124575296, -0.8086757549230774 ],\n];\n// LMStoIab_M inverted\nconst LabtoLMS_M = [\n\t[ 1.0000000000000000,  0.3963377773761749,  0.2158037573099136 ],\n\t[ 1.0000000000000000, -0.1055613458156586, -0.0638541728258133 ],\n\t[ 1.0000000000000000, -0.0894841775298119, -1.2914855480194092 ],\n];\n\nexport default new ColorSpace({\n\tid: \"oklab\",\n\tname: \"Oklab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t},\n\n\t// Note that XYZ is relative to D65\n\twhite: \"D65\",\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\t// non-linearity\n\t\tlet LMSg = LMS.map(val => Math.cbrt(val));\n\n\t\treturn multiplyMatrices(LMStoLab_M, LMSg);\n\n\t},\n\ttoBase (OKLab) {\n\t\t// move to LMS cone domain\n\t\tlet LMSg = multiplyMatrices(LabtoLMS_M, OKLab);\n\n\t\t// restore linearity\n\t\tlet LMS = LMSg.map(val => val ** 3);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n\n\tformats: {\n\t\t\"oklab\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in CIE Lab\n\nimport oklab from \"../spaces/oklab.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaEOK, term by term as root sum of squares\n\tlet [L1, a1, b1] = oklab.from(color);\n\tlet [L2, a2, b2] = oklab.from(sample);\n\tlet ΔL = L1 - L2;\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\treturn Math.sqrt(ΔL ** 2 + Δa ** 2 + Δb ** 2);\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Check if a color is in gamut of either its own or another color space\n * @return {Boolean} Is the color in gamut?\n */\nexport default function inGamut (color, space, {epsilon = ε} = {}) {\n\tcolor = getColor(color);\n\n\tif (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tlet coords = color.coords;\n\n\tif (space !== color.space) {\n\t\tcoords = space.from(color);\n\t}\n\n\treturn space.inGamut(coords, {epsilon});\n}\n", "export default function clone (color) {\n\treturn {\n\t\tspace: color.space,\n\t\tcoords: color.coords.slice(),\n\t\talpha: color.alpha,\n\t};\n}\n", "import ColorSpace from \"./space.js\";\n\n/**\n * Euclidean distance of colors in an arbitrary color space\n */\nexport default function distance (color1, color2, space = \"lab\") {\n\tspace = ColorSpace.get(space);\n\n\t// Assume getColor() is called on color in space.from()\n\tlet coords1 = space.from(color1);\n\tlet coords2 = space.from(color2);\n\n\treturn Math.sqrt(coords1.reduce((acc, c1, i) => {\n\t\tlet c2 = coords2[i];\n\t\tif (isNaN(c1) || isNaN(c2)) {\n\t\t\treturn acc;\n\t\t}\n\n\t\treturn acc + (c2 - c1) ** 2;\n\t}, 0));\n}\n", "import distance from \"../distance.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function deltaE76 (color, sample) {\n\t// Assume getColor() is called in the distance function\n\treturn distance(color, sample, \"lab\");\n}\n", "import lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// CMC by the Color Measurement Committee of the\n// Bradford Society of Dyeists and Colorsts, 1994.\n// Uses LCH rather than Lab,\n// with different weights for L, C and H differences\n// A nice increase in accuracy for modest increase in complexity\nconst π = Math.PI;\nconst d2r = π / 180;\n\nexport default function (color, sample, {l = 2, c = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE CMC.\n\n\t// This implementation assumes the parametric\n\t// weighting factors l:c are 2:1\n\t// which is typical for non-textile uses.\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet [, C1, H1] = lch.from(lab, [L1, a1, b1]);\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// let [L1, a1, b1] = color.getAll(lab);\n\t// let C1 = color.get(\"lch.c\");\n\t// let H1 = color.get(\"lch.h\");\n\t// let [L2, a2, b2] = sample.getAll(lab);\n\t// let C2 = sample.get(\"lch.c\");\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\t// we don't need H2 as ΔH is calculated from Δa, Δb and ΔC\n\n\t// Lightness and Chroma differences\n\t// These are (color - sample), unlike deltaE2000\n\tlet ΔL = L1 - L2;\n\tlet ΔC = C1 - C2;\n\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\n\t// weighted Hue difference, less for larger Chroma difference\n\n\tlet H2 = (Δa ** 2) + (Δb ** 2) - (ΔC ** 2);\n\t// due to roundoff error it is possible that, for zero a and b,\n\t// ΔC > Δa + Δb is 0, resulting in attempting\n\t// to take the square root of a negative number\n\n\t// trying instead the equation from Industrial Color Physics\n\t// By Georg A. Klein\n\n\t// let ΔH = ((a1 * b2) - (a2 * b1)) / Math.sqrt(0.5 * ((C2 * C1) + (a2 * a1) + (b2 * b1)));\n\t// console.log({ΔH});\n\t// This gives the same result to 12 decimal places\n\t// except it sometimes NaNs when trying to root a negative number\n\n\t// let ΔH = Math.sqrt(H2); we never actually use the root, it gets squared again!!\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor, depends entirely on L1 not L2\n\tlet SL = 0.511;\t// linear portion of the Y to L transfer function\n\tif (L1 >= 16) {\t// cubic portion\n\t\tSL = (0.040975 * L1) / (1 + 0.01765 * L1);\n\t}\n\n\t// SC Chroma factor\n\tlet SC = ((0.0638 * C1) / (1 + 0.0131 * C1)) + 0.638;\n\n\t// Cross term T for blue non-linearity\n\tlet T;\n\tif (Number.isNaN(H1)) {\n\t\tH1 = 0;\n\t}\n\n\tif (H1 >= 164 && H1 <= 345) {\n\t\tT = 0.56 + Math.abs(0.2 * Math.cos((H1 + 168) * d2r));\n\t}\n\telse {\n\t\tT = 0.36 + Math.abs(0.4 * Math.cos((H1 + 35) * d2r));\n\t}\n\t// console.log({T});\n\n\t// SH Hue factor also depends on C1,\n\tlet C4 = Math.pow(C1, 4);\n\tlet F = Math.sqrt(C4 / (C4 + 1900));\n\tlet SH = SC * ((F * T) + 1 - F);\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (l * SL)) ** 2;\n\tdE += (ΔC / (c * SC)) ** 2;\n\tdE += (H2 / (SH ** 2));\n\t// dE += (ΔH / SH)  ** 2;\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nconst Yw = 203;\t// absolute luminance of media white\n\nexport default new ColorSpace({\n// Absolute CIE XYZ, with a D65 whitepoint,\n// as used in most HDR colorspaces as a starting point.\n// SDR spaces are converted per BT.2048\n// so that diffuse, media white is 203 cd/m²\n\tid: \"xyz-abs-d65\",\n\tcssId: \"--xyz-abs-d65\",\n\tname: \"Absolute XYZ D65\",\n\tcoords: {\n\t\tx: {\n\t\t\trefRange: [0, 9504.7],\n\t\t\tname: \"Xa\",\n\t\t},\n\t\ty: {\n\t\t\trefRange: [0, 10000],\n\t\t\tname: \"Ya\",\n\t\t},\n\t\tz: {\n\t\t\trefRange: [0, 10888.3],\n\t\t\tname: \"Za\",\n\t\t},\n\t},\n\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// Make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\treturn XYZ.map (v => Math.max(v * Yw, 0));\n\t},\n\ttoBase (AbsXYZ) {\n\t\t// Convert to media-white relative XYZ\n\t\treturn AbsXYZ.map(v => Math.max(v / Yw, 0));\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst b = 1.15;\nconst g = 0.66;\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\nconst p = 1.7 * 2523 / (2 ** 5);\nconst pinv = (2 ** 5) / (1.7 * 2523);\nconst d = -0.56;\nconst d0 = 1.6295499532821566E-11;\n\nconst XYZtoCone_M = [\n\t[  0.41478972, 0.579999,  0.0146480 ],\n\t[ -0.2015100,  1.120649,  0.0531008 ],\n\t[ -0.0166008,  0.264800,  0.6684799 ],\n];\n// XYZtoCone_M inverted\nconst Coneto<PERSON>Y<PERSON>_M = [\n\t[  1.9242264357876067,  -1.0047923125953657,  0.037651404030618   ],\n\t[  0.35031676209499907,  0.7264811939316552, -0.06538442294808501 ],\n\t[ -0.09098281098284752, -0.3127282905230739,  1.5227665613052603  ],\n];\nconst ConetoIab_M = [\n\t[  0.5,       0.5,       0        ],\n\t[  3.524000, -4.066708,  0.542708 ],\n\t[  0.199076,  1.096799, -1.295875 ],\n];\n// ConetoIab_M inverted\nconst IabtoCone_M = [\n\t[ 1,                   0.1386050432715393,   0.05804731615611886 ],\n\t[ 0.9999999999999999, -0.1386050432715393,  -0.05804731615611886 ],\n\t[ 0.9999999999999998, -0.09601924202631895, -0.8118918960560388  ],\n];\n\nexport default new ColorSpace({\n\tid: \"jzazbz\",\n\tname: \"Jzazbz\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Jz\",\n\t\t},\n\t\taz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t\tbz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// First make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\t// BT.2048 says media white Y=203 at PQ 58\n\n\t\tlet [ Xa, Ya, Za ] = XYZ;\n\n\t\t// modify X and Y\n\t\tlet Xm = (b * Xa) - ((b - 1) * Za);\n\t\tlet Ym = (g * Ya) - ((g - 1) * Xa);\n\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoCone_M, [ Xm, Ym, Za ]);\n\n\t\t// PQ-encode LMS\n\t\tlet PQLMS = LMS.map (function (val) {\n\t\t\tlet num = c1 + (c2 * ((val / 10000) ** n));\n\t\t\tlet denom = 1 + (c3 * ((val / 10000) ** n));\n\n\t\t\treturn (num / denom)  ** p;\n\t\t});\n\n\t\t// almost there, calculate Iz az bz\n\t\tlet [ Iz, az, bz] = multiplyMatrices(ConetoIab_M, PQLMS);\n\t\t// console.log({Iz, az, bz});\n\n\t\tlet Jz = ((1 + d) * Iz) / (1 + (d * Iz)) - d0;\n\t\treturn [Jz, az, bz];\n\t},\n\ttoBase (Jzazbz) {\n\t\tlet [Jz, az, bz] = Jzazbz;\n\t\tlet Iz = (Jz + d0) / (1 + d - d * (Jz + d0));\n\n\t\t// bring into LMS cone domain\n\t\tlet PQLMS = multiplyMatrices(IabtoCone_M, [ Iz, az, bz ]);\n\n\t\t// convert from PQ-coded to linear-light\n\t\tlet LMS = PQLMS.map(function (val) {\n\t\t\tlet num = (c1 - (val ** pinv));\n\t\t\tlet denom = (c3 * (val ** pinv)) - c2;\n\t\t\tlet x = 10000 * ((num / denom) ** ninv);\n\n\t\t\treturn (x); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\n\t\t// modified abs XYZ\n\t\tlet [ Xm, Ym, Za ] = multiplyMatrices(ConetoXYZ_M, LMS);\n\n\t\t// restore standard D50 relative XYZ, relative to media white\n\t\tlet Xa = (Xm + ((b - 1) * Za)) / b;\n\t\tlet Ya = (Ym + ((g - 1) * Xa)) / g;\n\t\treturn [ Xa, Ya, Za ];\n\t},\n\n\tformats: {\n\t\t// https://drafts.csswg.org/css-color-hdr/#Jzazbz\n\t\t\"color\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport <PERSON><PERSON><PERSON>b<PERSON> from \"./jzazbz.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"jzczhz\",\n\tname: \"J<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\tcz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\thz: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Jzazbz,\n\tfromBase (jzazbz) {\n\t\t// Convert to polar form\n\t\tlet [Jz, az, bz] = jzazbz;\n\t\tlet hue;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(az) < ε && Math.abs(bz) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(bz, az) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tJz, // Jz is still Jz\n\t\t\tMath.sqrt(az ** 2 + bz ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (jzczhz) {\n\t\t// Convert from polar form\n\t\t// debugger;\n\t\treturn [\n\t\t\tjzczhz[0], // Jz is still Jz\n\t\t\tjzczhz[1] * Math.cos(jzczhz[2] * Math.PI / 180), // az\n\t\t\tjzczhz[1] * Math.sin(jzczhz[2] * Math.PI / 180),  // bz\n\t\t];\n\t},\n});\n", "import jzczhz from \"../spaces/jzczhz.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// Uses JzCzHz, which has improved perceptual uniformity\n// and thus a simple Euclidean root-sum of ΔL² ΔC² ΔH²\n// gives good results.\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in JzCzHz.\n\tlet [Jz1, Cz1, Hz1] = jzczhz.from(color);\n\tlet [Jz2, Cz2, Hz2] = jzczhz.from(sample);\n\n\t// Lightness and Chroma differences\n\t// sign does not matter as they are squared.\n\tlet ΔJ = Jz1 - Jz2;\n\tlet ΔC = Cz1 - Cz2;\n\n\t// length of chord for ΔH\n\tif ((Number.isNaN(Hz1)) && (Number.isNaN(Hz2))) {\n\t\t// both undefined hues\n\t\tHz1 = 0;\n\t\tHz2 = 0;\n\t}\n\telse if (Number.isNaN(Hz1)) {\n\t\t// one undefined, set to the defined hue\n\t\tHz1 = Hz2;\n\t}\n\telse if (Number.isNaN(Hz2)) {\n\t\tHz2 = Hz1;\n\t}\n\n\tlet Δh = Hz1 - Hz2;\n\tlet ΔH = 2 * Math.sqrt(Cz1 * Cz2) * Math.sin((Δh / 2) * (Math.PI / 180));\n\n\treturn Math.sqrt(ΔJ ** 2 + ΔC ** 2 + ΔH ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst c1 = 3424 / 4096;\nconst c2 = 2413 / 128;\nconst c3 = 2392 / 128;\nconst m1 = 2610 / 16384;\nconst m2 = 2523 / 32;\nconst im1 = 16384 / 2610;\nconst im2 = 32 / 2523;\n\n// The matrix below includes the 4% crosstalk components\n// and is from the Dolby \"What is ICtCp\" paper\"\nconst XYZtoLMS_M = [\n\t[  0.3592832590121217,  0.6976051147779502, -0.0358915932320290 ],\n\t[ -0.1920808463704993,  1.1004767970374321,  0.0753748658519118 ],\n\t[  0.0070797844607479,  0.0748396662186362,  0.8433265453898765 ],\n];\n// linear-light Rec.2020 to LMS, again with crosstalk\n// rational terms from <PERSON>,\n// Encoding High Dynamic Range andWide Color Gamut Imagery, p.97\n// and ITU-R BT.2124-0 p.2\n/*\nconst Rec2020toLMS_M = [\n\t[ 1688 / 4096,  2146 / 4096,   262 / 4096 ],\n\t[  683 / 4096,  2951 / 4096,   462 / 4096 ],\n\t[   99 / 4096,   309 / 4096,  3688 / 4096 ]\n];\n*/\n// this includes the Ebner LMS coefficients,\n// the rotation, and the scaling to [-0.5,0.5] range\n// rational terms from Fröhlich p.97\n// and ITU-R BT.2124-0 pp.2-3\nconst LMStoIPT_M = [\n\t[  2048 / 4096,   2048 / 4096,       0      ],\n\t[  6610 / 4096, -13613 / 4096,  7003 / 4096 ],\n\t[ 17933 / 4096, -17390 / 4096,  -543 / 4096 ],\n];\n\n// inverted matrices, calculated from the above\nconst IPTtoLMS_M = [\n\t[ 0.9999999999999998,  0.0086090370379328,  0.1110296250030260 ],\n\t[ 0.9999999999999998, -0.0086090370379328, -0.1110296250030259 ],\n\t[ 0.9999999999999998,  0.5600313357106791, -0.3206271749873188 ],\n];\n/*\nconst LMStoRec2020_M = [\n\t[ 3.4375568932814012112,   -2.5072112125095058195,   0.069654319228104608382],\n\t[-0.79142868665644156125,   1.9838372198740089874,  -0.19240853321756742626 ],\n\t[-0.025646662911506476363, -0.099240248643945566751, 1.1248869115554520431  ]\n];\n*/\nconst LMStoXYZ_M = [\n\t[  2.0701522183894223, -1.3263473389671563,  0.2066510476294053 ],\n\t[  0.3647385209748072,  0.6805660249472273, -0.0453045459220347 ],\n\t[ -0.0497472075358123, -0.0492609666966131,  1.1880659249923042 ],\n];\n\n// Only the PQ form of ICtCp is implemented here. There is also an HLG form.\n// from Dolby, \"WHAT IS ICTCP?\"\n// https://professional.dolby.com/siteassets/pdfs/ictcp_dolbywhitepaper_v071.pdf\n// and\n// Dolby, \"Perceptual Color Volume\n// Measuring the Distinguishable Colors of HDR and WCG Displays\"\n// https://professional.dolby.com/siteassets/pdfs/dolby-vision-measuring-perceptual-color-volume-v7.1.pdf\nexport default new ColorSpace({\n\tid: \"ictcp\",\n\tname: \"ICTCP\",\n\t// From BT.2100-2 page 7:\n\t// During production, signal values are expected to exceed the\n\t// range E′ = [0.0 : 1.0]. This provides processing headroom and avoids\n\t// signal degradation during cascaded processing. Such values of E′,\n\t// below 0.0 or exceeding 1.0, should not be clipped during production\n\t// and exchange.\n\t// Values below 0.0 should not be clipped in reference displays (even\n\t// though they represent “negative” light) to allow the black level of\n\t// the signal (LB) to be properly set using test signals known as “PLUGE”\n\tcoords: {\n\t\ti: {\n\t\t\trefRange: [0, 1],\t// Constant luminance,\n\t\t\tname: \"I\",\n\t\t},\n\t\tct: {\n\t\t\trefRange: [-0.5, 0.5],\t// Full BT.2020 gamut in range [-0.5, 0.5]\n\t\t\tname: \"CT\",\n\t\t},\n\t\tcp: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t\tname: \"CP\",\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\treturn LMStoICtCp(LMS);\n\t},\n\ttoBase (ICtCp) {\n\t\tlet LMS = ICtCptoLMS(ICtCp);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n});\n\nfunction LMStoICtCp (LMS) {\n\t// apply the PQ EOTF\n\t// we can't ever be dividing by zero because of the \"1 +\" in the denominator\n\tlet PQLMS = LMS.map (function (val) {\n\t\tlet num = c1 + (c2 * ((val / 10000) ** m1));\n\t\tlet denom = 1 + (c3 * ((val / 10000) ** m1));\n\n\t\treturn (num / denom)  ** m2;\n\t});\n\n\t// LMS to IPT, with rotation for Y'C'bC'r compatibility\n\treturn multiplyMatrices(LMStoIPT_M, PQLMS);\n}\n\nfunction ICtCptoLMS (ICtCp) {\n\tlet PQLMS = multiplyMatrices(IPTtoLMS_M, ICtCp);\n\n\t// From BT.2124-0 Annex 2 Conversion 3\n\tlet LMS = PQLMS.map (function (val) {\n\t\tlet num  = Math.max((val ** im2) - c1, 0);\n\t\tlet denom = (c2 - (c3 * (val ** im2)));\n\t\treturn 10000 * ((num / denom) ** im1);\n\t});\n\n\treturn LMS;\n}\n", "import ictcp from \"../spaces/ictcp.js\";\nimport getColor from \"../getColor.js\";\n\n// Delta E in ICtCp space,\n// which the ITU calls Delta E ITP, which is shorter\n// formulae from ITU Rec. ITU-R BT.2124-0\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in ICtCp\n\t// which is simply the Euclidean distance\n\n\tlet [ I1, T1, P1 ] = ictcp.from(color);\n\tlet [ I2, T2, P2 ] = ictcp.from(sample);\n\n\t// the 0.25 factor is to undo the encoding scaling in Ct\n\t// the 720 is so that 1 deltaE = 1 JND\n\t// per  ITU-R BT.2124-0 p.3\n\n\treturn 720 * Math.sqrt((I1 - I2) ** 2 + (0.25 * (T1 - T2) ** 2) + (P1 - P2) ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices, interpolate, copySign, spow, zdiv, bisectLeft} from \"../util.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst adaptedCoef = 0.42;\nconst adaptedCoefInv = 1 / adaptedCoef;\nconst tau = 2 * Math.PI;\n\nconst cat16 = [\n\t[  0.401288,  0.650173, -0.051461 ],\n\t[ -0.250268,  1.204414,  0.045854 ],\n\t[ -0.002079,  0.048952,  0.953127 ],\n];\n\nconst cat16Inv = [\n\t[1.8620678550872327, -1.0112546305316843, 0.14918677544445175],\n\t[0.38752654323613717, 0.6214474419314753, -0.008973985167612518],\n\t[-0.015841498849333856, -0.03412293802851557, 1.0499644368778496],\n];\n\nconst m1 = [\n\t[460.0, 451.0, 288.0],\n\t[460.0, -891.0, -261.0],\n\t[460.0, -220.0, -6300.0],\n];\n\nconst surroundMap = {\n\tdark: [0.8, 0.525, 0.8],\n\tdim: [0.9, 0.59, 0.9],\n\taverage: [1, 0.69, 1],\n};\n\nconst hueQuadMap = {\n\t// Red, Yellow, Green, Blue, Red\n\th: [20.14, 90.00, 164.25, 237.53, 380.14],\n\te: [0.8, 0.7, 1.0, 1.2, 0.8],\n\tH: [0.0, 100.0, 200.0, 300.0, 400.0],\n};\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\n\nexport function adapt (coords, fl) {\n\tconst temp = coords.map(c => {\n\t\tconst x = spow(fl * Math.abs(c) * 0.01, adaptedCoef);\n\t\treturn 400 * copySign(x, c) / (x + 27.13);\n\t});\n\treturn temp;\n}\n\nexport function unadapt (adapted, fl) {\n\tconst constant = 100 / fl * (27.13 ** adaptedCoefInv);\n\treturn adapted.map(c => {\n\t\tconst cabs = Math.abs(c);\n\t\treturn copySign(constant * spow(cabs / (400 - cabs), adaptedCoefInv), c);\n\t});\n}\n\nexport function hueQuadrature (h) {\n\tlet hp = constrain(h);\n\tif (hp <= hueQuadMap.h[0]) {\n\t\thp += 360;\n\t}\n\n\tconst i = bisectLeft(hueQuadMap.h, hp) - 1;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\tconst Hi = hueQuadMap.H[i];\n\n\tconst t = (hp - hi) / ei;\n\treturn Hi + (100 * t) / (t + (hii - hp) / eii);\n}\n\nexport function invHueQuadrature (H) {\n\tlet Hp = ((H % 400 + 400) % 400);\n\tconst i = Math.floor(0.01 * Hp);\n\tHp = Hp % 100;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\n\treturn constrain(\n\t\t(Hp * (eii * hi - ei * hii) - 100 * hi * eii) /\n\t\t(Hp * (eii - ei) - 100 * eii),\n\t);\n}\n\nexport function environment (\n\trefWhite,\n\tadaptingLuminance,\n\tbackgroundLuminance,\n\tsurround,\n\tdiscounting,\n) {\n\n\tconst env = {};\n\n\tenv.discounting = discounting;\n\tenv.refWhite = refWhite;\n\tenv.surround = surround;\n\tconst xyzW = refWhite.map(c => {\n\t\treturn c * 100;\n\t});\n\n\t// The average luminance of the environment in `cd/m^2cd/m` (a.k.a. nits)\n\tenv.la = adaptingLuminance;\n\t// The relative luminance of the nearby background\n\tenv.yb = backgroundLuminance;\n\t// Absolute luminance of the reference white.\n\tconst yw = xyzW[1];\n\n\t// Cone response for reference white\n\tconst rgbW = multiplyMatrices(cat16, xyzW);\n\n\t// Surround: dark, dim, and average\n\tsurround = surroundMap[env.surround];\n\tconst f = surround[0];\n\tenv.c = surround[1];\n\tenv.nc = surround[2];\n\n\tconst k = 1 / (5 * env.la + 1);\n\tconst k4 = k ** 4;\n\n\t// Factor of luminance level adaptation\n\tenv.fl = (k4 * env.la + 0.1 * (1 - k4) * (1 - k4) * Math.cbrt(5 * env.la));\n\tenv.flRoot = env.fl ** 0.25;\n\n\tenv.n = env.yb / yw;\n\tenv.z = 1.48 + Math.sqrt(env.n);\n\tenv.nbb = 0.725 * (env.n ** -0.2);\n\tenv.ncb = env.nbb;\n\n\t// Degree of adaptation calculating if not discounting\n\t// illuminant (assumed eye is fully adapted)\n\tconst d = (discounting) ?\n\t\t1 :\n\t\tMath.max(\n\t\t\tMath.min(f * (1 - 1 / 3.6 * Math.exp((-env.la - 42) / 92)), 1),\n\t\t\t0,\n\t\t);\n\tenv.dRgb = rgbW.map(c => {\n\t\treturn interpolate(1, yw / c, d);\n\t});\n\tenv.dRgbInv = env.dRgb.map(c => {\n\t\treturn 1 / c;\n\t});\n\n\t// Achromatic response\n\tconst rgbCW = rgbW.map((c, i) => {\n\t\treturn c * env.dRgb[i];\n\t});\n\tconst rgbAW = adapt(rgbCW, env.fl);\n\tenv.aW = env.nbb * (2 * rgbAW[0] + rgbAW[1] + 0.05 * rgbAW[2]);\n\n\t// console.log(env);\n\n\treturn env;\n}\n\n// Pre-calculate everything we can with the viewing conditions\nconst viewingConditions = environment(\n\twhite,\n\t64 / Math.PI * 0.2, 20,\n\t\"average\",\n\tfalse,\n);\n\nexport function fromCam16 (cam16, env) {\n\n\t// These check ensure one, and only one attribute for a\n\t// given category is provided.\n\tif (!((cam16.J !== undefined) ^ (cam16.Q !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'J' or 'Q'\");\n\t}\n\n\tif (!((cam16.C !== undefined) ^ (cam16.M !== undefined) ^ (cam16.s !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'C', 'M' or 's'\");\n\t}\n\n\t// Hue is absolutely required\n\tif (!((cam16.h !== undefined) ^ (cam16.H !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'h' or 'H'\");\n\t}\n\n\t// Black\n\tif (cam16.J === 0.0 || cam16.Q === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Break hue into Cartesian components\n\tlet hRad = 0.0;\n\tif (cam16.h !== undefined) {\n\t\thRad = constrain(cam16.h) * deg2rad;\n\t}\n\telse {\n\t\thRad = invHueQuadrature(cam16.H) * deg2rad;\n\t}\n\n\tconst cosh = Math.cos(hRad);\n\tconst sinh = Math.sin(hRad);\n\n\t// Calculate `Jroot` from one of the lightness derived coordinates.\n\tlet Jroot = 0.0;\n\tif (cam16.J !== undefined) {\n\t\tJroot = spow(cam16.J, 1 / 2) * 0.1;\n\t}\n\telse if (cam16.Q !== undefined) {\n\t\tJroot = 0.25 * env.c * cam16.Q / ((env.aW + 4) * env.flRoot);\n\t}\n\n\t// Calculate the `t` value from one of the chroma derived coordinates\n\tlet alpha = 0.0;\n\tif (cam16.C !== undefined) {\n\t\talpha = cam16.C / Jroot;\n\t}\n\telse if (cam16.M !== undefined) {\n\t\talpha = (cam16.M / env.flRoot) / Jroot;\n\t}\n\telse if (cam16.s !== undefined) {\n\t\talpha = 0.0004 * (cam16.s ** 2) * (env.aW + 4) / env.c;\n\t}\n\tconst t = spow(\n\t\talpha * Math.pow(1.64 - Math.pow(0.29, env.n), -0.73),\n\t\t10 / 9,\n\t);\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\t// Achromatic response\n\tconst A = env.aW * spow(Jroot, 2 / env.c / env.z);\n\n\t// Calculate red-green and yellow-blue components\n\tconst p1 = 5e4 / 13 * env.nc * env.ncb * et;\n\tconst p2 = A / env.nbb;\n\tconst r = (\n\t\t23 * (p2 + 0.305) *\n\t\tzdiv(t, 23 * p1 + t * (11 * cosh + 108 * sinh))\n\t);\n\tconst a = r * cosh;\n\tconst b = r * sinh;\n\n\t// Calculate back from cone response to XYZ\n\tconst rgb_c = unadapt(\n\t\tmultiplyMatrices(m1, [p2, a, b]).map(c => {\n\t\t\treturn c * 1 / 1403;\n\t\t}),\n\t\tenv.fl,\n\t);\n\treturn multiplyMatrices(\n\t\tcat16Inv,\n\t\trgb_c.map((c, i) => {\n\t\t\treturn c * env.dRgbInv[i];\n\t\t}),\n\t).map(c => {\n\t\treturn c / 100;\n\t});\n}\n\n\nexport function toCam16 (xyzd65, env) {\n\t// Cone response\n\tconst xyz100 = xyzd65.map(c => {\n\t\treturn c * 100;\n\t});\n\tconst rgbA = adapt(\n\t\tmultiplyMatrices(cat16, xyz100).map((c, i) => {\n\t\t\treturn c * env.dRgb[i];\n\t\t}),\n\t\tenv.fl,\n\t);\n\n\t// Calculate hue from red-green and yellow-blue components\n\tconst a = rgbA[0] + (-12 * rgbA[1] + rgbA[2]) / 11;\n\tconst b = (rgbA[0] + rgbA[1] - 2 * rgbA[2]) / 9;\n\tconst hRad = ((Math.atan2(b, a) % tau) + tau) % tau;\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\tconst t = (\n\t\t5e4 / 13 * env.nc * env.ncb *\n\t\tzdiv(\n\t\t\tet * Math.sqrt(a ** 2 + b ** 2),\n\t\t\trgbA[0] + rgbA[1] + 1.05 * rgbA[2] + 0.305,\n\t\t)\n\t);\n\tconst alpha = spow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, env.n), 0.73);\n\n\t// Achromatic response\n\tconst A = env.nbb * (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]);\n\n\tconst Jroot = spow(A / env.aW, 0.5 * env.c * env.z);\n\n\t// Lightness\n\tconst J = 100 * spow(Jroot, 2);\n\n\t// Brightness\n\tconst Q = (4 / env.c * Jroot * (env.aW + 4) * env.flRoot);\n\n\t// Chroma\n\tconst C = alpha * Jroot;\n\n\t// Colorfulness\n\tconst M = C * env.flRoot;\n\n\t// Hue\n\tconst h = constrain(hRad * rad2deg);\n\n\t// Hue quadrature\n\tconst H = hueQuadrature(h);\n\n\t// Saturation\n\tconst s = 50 * spow(env.c * alpha / (env.aW + 4), 1 / 2);\n\n\t// console.log({J: J, C: C, h: h, s: s, Q: Q, M: M, H: H});\n\n\treturn {J: J, C: C, h: h, s: s, Q: Q, M: M, H: H};\n}\n\n\n// Provided as a way to directly evaluate the CAM16 model\n// https://observablehq.com/@jrus/cam16: reference implementation\n// https://arxiv.org/pdf/1802.06067.pdf: Nico Schlömer\n// https://onlinelibrary.wiley.com/doi/pdf/10.1002/col.22324: hue quadrature\n// https://www.researchgate.net/publication/318152296_Comprehensive_color_solutions_CAM16_CAT16_and_CAM16-UCS\n// Results compared against: https://github.com/colour-science/colour\nexport default new ColorSpace({\n\tid: \"cam16-jmh\",\n\tcssId: \"--cam16-jmh\",\n\tname: \"CAM16-JMh\",\n\tcoords: {\n\t\tj: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"J\",\n\t\t},\n\t\tm: {\n\t\t\trefRange: [0, 105.0],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\tconst cam16 = toCam16(xyz, viewingConditions);\n\t\treturn [cam16.J, cam16.M, cam16.h];\n\t},\n\ttoBase (cam16) {\n\t\treturn fromCam16(\n\t\t\t{J: cam16[0], M: cam16[1], h: cam16[2]},\n\t\t\tviewingConditions,\n\t\t);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {fromCam16, toCam16, environment} from \"./cam16.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nfunction toLstar (y) {\n\t// Convert XYZ Y to L*\n\n\tconst fy = (y > ε) ? Math.cbrt(y) : (κ * y + 16) / 116;\n\treturn (116.0 * fy) - 16.0;\n}\n\nfunction fromLstar (lstar) {\n\t// Convert L* back to XYZ Y\n\n\treturn (lstar > 8) ?  Math.pow((lstar + 16) / 116, 3) : lstar / κ;\n}\n\nfunction fromHct (coords, env) {\n\t// Use Newton's method to try and converge as quick as possible or\n\t// converge as close as we can. While the requested precision is achieved\n\t// most of the time, it may not always be achievable. Especially past the\n\t// visible spectrum, the algorithm will likely struggle to get the same\n\t// precision. If, for whatever reason, we cannot achieve the accuracy we\n\t// seek in the allotted iterations, just return the closest we were able to\n\t// get.\n\n\tlet [h, c, t] = coords;\n\tlet xyz = [];\n\tlet j = 0;\n\n\t// Shortcut out for black\n\tif (t === 0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Calculate the Y we need to target\n\tlet y = fromLstar(t);\n\n\t// A better initial guess yields better results. Polynomials come from\n\t// curve fitting the T vs J response.\n\tif (t > 0) {\n\t\tj = 0.00379058511492914 * t ** 2 + 0.608983189401032 * t + 0.9155088574762233;\n\t}\n\telse {\n\t\tj = 9.514440756550361e-06 * t ** 2 + 0.08693057439788597 * t - 21.928975842194614;\n\t}\n\n\t// Threshold of how close is close enough, and max number of attempts.\n\t// More precision and more attempts means more time spent iterating. Higher\n\t// required precision gives more accuracy but also increases the chance of\n\t// not hitting the goal. 2e-12 allows us to convert round trip with\n\t// reasonable accuracy of six decimal places or more.\n\tconst threshold = 2e-12;\n\tconst max_attempts = 15;\n\n\tlet attempt = 0;\n\tlet last = Infinity;\n\tlet best = j;\n\n\t// Try to find a J such that the returned y matches the returned y of the L*\n\twhile (attempt <= max_attempts) {\n\t\txyz = fromCam16({J: j, C: c, h: h}, env);\n\n\t\t// If we are within range, return XYZ\n\t\t// If we are closer than last time, save the values\n\t\tconst delta = Math.abs(xyz[1] - y);\n\t\tif (delta < last) {\n\t\t\tif (delta <= threshold) {\n\t\t\t\treturn xyz;\n\t\t\t}\n\t\t\tbest = j;\n\t\t\tlast = delta;\n\t\t}\n\n\t\t// f(j_root) = (j ** (1 / 2)) * 0.1\n\t\t// f(j) = ((f(j_root) * 100) ** 2) / j - 1 = 0\n\t\t// f(j_root) = Y = y / 100\n\t\t// f(j) = (y ** 2) / j - 1\n\t\t// f'(j) = (2 * y) / j\n\t\tj = j - (xyz[1] - y) * j / (2 * xyz[1]);\n\n\t\tattempt += 1;\n\t}\n\n\t// We could not acquire the precision we desired,\n\t// return our closest attempt.\n\treturn fromCam16({J: j, C: c, h: h}, env);\n}\n\nfunction toHct (xyz, env) {\n\t// Calculate HCT by taking the L* of CIE LCh D65 and CAM16 chroma and hue.\n\n\tconst t = toLstar(xyz[1]);\n\tif (t === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\tconst cam16 = toCam16(xyz, viewingConditions);\n\treturn [constrain(cam16.h), cam16.C, t];\n}\n\n// Pre-calculate everything we can with the viewing conditions\nexport const viewingConditions = environment(\n\twhite, 200 / Math.PI * fromLstar(50.0),\n\tfromLstar(50.0) * 100,\n\t\"average\",\n\tfalse,\n);\n\n// https://material.io/blog/science-of-color-design\n// This is not a port of the material-color-utilities,\n// but instead implements the full color space as described,\n// combining CAM16 JCh and Lab D65. This does not clamp conversion\n// to HCT to specific chroma bands and provides support for wider\n// gamuts than Google currently supports and does so at a greater\n// precision (> 8 bits back to sRGB).\n// This implementation comes from https://github.com/facelessuser/coloraide\n// which is licensed under MIT.\nexport default new ColorSpace({\n\tid: \"hct\",\n\tname: \"HCT\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 145],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\tt: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Tone\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\treturn toHct(xyz, viewingConditions);\n\t},\n\ttoBase (hct) {\n\t\treturn fromHct(hct, viewingConditions);\n\t},\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hct\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import hct from \"../spaces/hct.js\";\nimport {viewingConditions} from \"../spaces/hct.js\";\nimport getColor from \"../getColor.js\";\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\nconst ucsCoeff = [1.00, 0.007, 0.0228];\n\n/**\n* Convert HCT chroma and hue (CAM16 JMh colorfulness and hue) using UCS logic for a and b.\n* @param {number[]} coords - HCT coordinates.\n* @return {number[]}\n*/\nfunction convertUcsAb (coords) {\n\t// We want the distance between the actual color.\n\t// If chroma is negative, it will throw off our calculations.\n\t// Normally, converting back to the base and forward will correct it.\n\t// If we have a negative chroma after this, then we have a color that\n\t// cannot resolve to positive chroma.\n\tif (coords[1] < 0) {\n\t\tcoords = hct.fromBase(hct.toBase(coords));\n\t}\n\n\t// Only in extreme cases (usually outside the visible spectrum)\n\t// can the input value for log become negative.\n\t// Avoid domain error by forcing a zero result via \"max\" if necessary.\n\tconst M = Math.log(Math.max(1 + ucsCoeff[2] * coords[1] * viewingConditions.flRoot, 1.0)) / ucsCoeff[2];\n\tconst hrad = coords[0] * deg2rad;\n\tconst a = M * Math.cos(hrad);\n\tconst b = M * Math.sin(hrad);\n\n\treturn [coords[2], a, b];\n}\n\n\n/**\n* Color distance using HCT.\n* @param {Color} color - Color to compare.\n* @param {Color} sample - Color to compare.\n* @return {number[]}\n*/\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\tlet [ t1, a1, b1 ] = convertUcsAb(hct.from(color));\n\tlet [ t2, a2, b2 ] = convertUcsAb(hct.from(sample));\n\n\t// Use simple euclidean distance with a and b using UCS conversion\n\t// and LCh lightness (HCT tone).\n\treturn Math.sqrt((t1 - t2) ** 2 + (a1 - a2) ** 2 + (b1 - b2) ** 2);\n}\n", "import deltaE76 from \"./deltaE76.js\";\nimport deltaECMC from \"./deltaECMC.js\";\nimport deltaE2000 from \"./deltaE2000.js\";\nimport deltaEJz from \"./deltaEJz.js\";\nimport deltaEITP from \"./deltaEITP.js\";\nimport deltaE<PERSON> from \"./deltaEOK.js\";\nimport deltaEHCT from \"./deltaEHCT.js\";\n\nexport {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n\nexport default {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport deltaE2000 from \"./deltaE/deltaE2000.js\";\nimport deltaEOK from \"./deltaE/deltaEOK.js\";\nimport inGamut from \"./inGamut.js\";\nimport to from \"./to.js\";\nimport get from \"./get.js\";\nimport oklab from \"./spaces/oklab.js\";\nimport set from \"./set.js\";\nimport clone from \"./clone.js\";\nimport getColor from \"./getColor.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\nimport {WHITES} from \"./adapt.js\";\n\n/**\n * Calculate the epsilon to 2 degrees smaller than the specified JND.\n * @param {Number} jnd - The target \"just noticeable difference\".\n * @returns {Number}\n */\nfunction calcEpsilon (jnd) {\n\t// Calculate the epsilon to 2 degrees smaller than the specified JND.\n\n\tconst order = (!jnd) ? 0 : Math.floor(Math.log10(Math.abs(jnd)));\n\t// Limit to an arbitrary value to ensure value is never too small and causes infinite loops.\n\treturn Math.max(parseFloat(`1e${order - 2}`), 1e-6);\n}\n\nconst GMAPPRESET = {\n\t\"hct\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 2,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: {},\n\t},\n\t\"hct-tonal\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 0,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: { channel: \"hct.t\", min: 0, max: 100 },\n\t},\n};\n\n/**\n * Force coordinates to be in gamut of a certain color space.\n * Mutates the color it is passed.\n * @param {Object|string} options object or spaceId string\n * @param {string} options.method - How to force into gamut.\n *        If \"clip\", coordinates are just clipped to their reference range.\n *        If \"css\", coordinates are reduced according to the CSS 4 Gamut Mapping Algorithm.\n *        If in the form [colorSpaceId].[coordName], that coordinate is reduced\n *        until the color is in gamut. Please note that this may produce nonsensical\n *        results for certain coordinates (e.g. hue) or infinite loops if reducing the coordinate never brings the color in gamut.\n * @param {ColorSpace|string} options.space - The space whose gamut we want to map to\n * @param {string} options.deltaEMethod - The delta E method to use while performing gamut mapping.\n *        If no method is specified, delta E 2000 is used.\n * @param {Number} options.jnd - The \"just noticeable difference\" to target.\n * @param {Object} options.blackWhiteClamp - Used to configure SDR black and clamping.\n *        \"channel\" indicates the \"space.channel\" to use for determining when to clamp.\n *        \"min\" indicates the lower limit for black clamping and \"max\" indicates the upper\n *        limit for white clamping.\n */\n\nexport default function toGamut (\n\tcolor,\n\t{\n\t\tmethod = defaults.gamut_mapping,\n\t\tspace = undefined,\n\t\tdeltaEMethod = \"\",\n\t\tjnd = 2,\n\t\tblackWhiteClamp = {},\n\t} = {},\n) {\n\tcolor = getColor(color);\n\n\tif (util.isString(arguments[1])) {\n\t\tspace = arguments[1];\n\t}\n\telse if (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\n\t// 3 spaces:\n\t// color.space: current color space\n\t// space: space whose gamut we are mapping to\n\t// mapSpace: space with the coord we're reducing\n\n\tif (inGamut(color, space, { epsilon: 0 })) {\n\t\treturn color;\n\t}\n\n\tlet spaceColor;\n\tif (method === \"css\") {\n\t\tspaceColor = toGamutCSS(color, { space });\n\t}\n\telse {\n\t\tif (method !== \"clip\" && !inGamut(color, space)) {\n\n\t\t\tif (Object.prototype.hasOwnProperty.call(GMAPPRESET, method)) {\n\t\t\t\t({method, jnd, deltaEMethod, blackWhiteClamp} = GMAPPRESET[method]);\n\t\t\t}\n\n\t\t\t// Get the correct delta E method\n\t\t\tlet de = deltaE2000;\n\t\t\tif (deltaEMethod !== \"\") {\n\t\t\t\tfor (let m in deltaEMethods) {\n\t\t\t\t\tif (\"deltae\" + deltaEMethod.toLowerCase() === m.toLowerCase()) {\n\t\t\t\t\t\tde = deltaEMethods[m];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet clipped = toGamut(to(color, space), { method: \"clip\", space });\n\t\t\tif (de(color, clipped) > jnd) {\n\n\t\t\t\t// Clamp to SDR white and black if required\n\t\t\t\tif (Object.keys(blackWhiteClamp).length === 3) {\n\t\t\t\t\tlet channelMeta = ColorSpace.resolveCoord(blackWhiteClamp.channel);\n\t\t\t\t\tlet channel = get(to(color, channelMeta.space), channelMeta.id);\n\t\t\t\t\tif (util.isNone(channel)) {\n\t\t\t\t\t\tchannel = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (channel >= blackWhiteClamp.max) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: WHITES[\"D65\"] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t\telse if (channel <= blackWhiteClamp.min) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: [0, 0, 0] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Reduce a coordinate of a certain color space until the color is in gamut\n\t\t\t\tlet coordMeta = ColorSpace.resolveCoord(method);\n\t\t\t\tlet mapSpace = coordMeta.space;\n\t\t\t\tlet coordId = coordMeta.id;\n\n\t\t\t\tlet mappedColor = to(color, mapSpace);\n\t\t\t\t// If we were already in the mapped color space, we need to resolve undefined channels\n\t\t\t\tmappedColor.coords.forEach((c, i) => {\n\t\t\t\t\tif (util.isNone(c)) {\n\t\t\t\t\t\tmappedColor.coords[i] = 0;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tlet bounds = coordMeta.range || coordMeta.refRange;\n\t\t\t\tlet min = bounds[0];\n\t\t\t\tlet ε = calcEpsilon(jnd);\n\t\t\t\tlet low = min;\n\t\t\t\tlet high = get(mappedColor, coordId);\n\n\t\t\t\twhile (high - low > ε) {\n\t\t\t\t\tlet clipped = clone(mappedColor);\n\t\t\t\t\tclipped = toGamut(clipped, { space, method: \"clip\" });\n\t\t\t\t\tlet deltaE = de(mappedColor, clipped);\n\n\t\t\t\t\tif (deltaE - jnd < ε) {\n\t\t\t\t\t\tlow = get(mappedColor, coordId);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thigh = get(mappedColor, coordId);\n\t\t\t\t\t}\n\n\t\t\t\t\tset(mappedColor, coordId, (low + high) / 2);\n\t\t\t\t}\n\n\t\t\t\tspaceColor = to(mappedColor, space);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tspaceColor = clipped;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tspaceColor = to(color, space);\n\t\t}\n\n\t\tif (method === \"clip\" // Dumb coord clipping\n\t\t\t// finish off smarter gamut mapping with clip to get rid of ε, see #17\n\t\t\t|| !inGamut(spaceColor, space, { epsilon: 0 })\n\t\t) {\n\t\t\tlet bounds = Object.values(space.coords).map(c => c.range || []);\n\n\t\t\tspaceColor.coords = spaceColor.coords.map((c, i) => {\n\t\t\t\tlet [min, max] = bounds[i];\n\n\t\t\t\tif (min !== undefined) {\n\t\t\t\t\tc = Math.max(min, c);\n\t\t\t\t}\n\n\t\t\t\tif (max !== undefined) {\n\t\t\t\t\tc = Math.min(c, max);\n\t\t\t\t}\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t}\n\t}\n\n\tif (space !== color.space) {\n\t\tspaceColor = to(spaceColor, color.space);\n\t}\n\n\tcolor.coords = spaceColor.coords;\n\treturn color;\n}\n\ntoGamut.returns = \"color\";\n\n// The reference colors to be used if lightness is out of the range 0-1 in the\n// `Oklch` space. These are created in the `Oklab` space, as it is used by the\n// DeltaEOK calculation, so it is guaranteed to be imported.\nconst COLORS = {\n\tWHITE: { space: oklab, coords: [1, 0, 0] },\n\tBLACK: { space: oklab, coords: [0, 0, 0] },\n};\n\n/**\n * Given a color `origin`, returns a new color that is in gamut using\n * the CSS Gamut Mapping Algorithm. If `space` is specified, it will be in gamut\n * in `space`, and returned in `space`. Otherwise, it will be in gamut and\n * returned in the color space of `origin`.\n * @param {Object} origin\n * @param {Object} options\n * @param {ColorSpace|string} options.space\n * @returns {Color}\n */\nexport function toGamutCSS (origin, {space} = {}) {\n\tconst JND = 0.02;\n\tconst ε = 0.0001;\n\n\torigin = getColor(origin);\n\n\tif (!space) {\n\t\tspace = origin.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tconst oklchSpace = ColorSpace.get(\"oklch\");\n\n\tif (space.isUnbounded) {\n\t\treturn to(origin, space);\n\t}\n\n\tconst origin_OKLCH = to(origin, oklchSpace);\n\tlet L = origin_OKLCH.coords[0];\n\n\t// return media white or black, if lightness is out of range\n\tif (L >= 1) {\n\t\tconst white = to(COLORS.WHITE, space);\n\t\twhite.alpha = origin.alpha;\n\t\treturn to(white, space);\n\t}\n\tif (L <= 0) {\n\t\tconst black = to(COLORS.BLACK, space);\n\t\tblack.alpha = origin.alpha;\n\t\treturn to(black, space);\n\t}\n\n\tif (inGamut(origin_OKLCH, space, {epsilon: 0})) {\n\t\treturn to(origin_OKLCH, space);\n\t}\n\n\tfunction clip (_color) {\n\t\tconst destColor = to(_color, space);\n\t\tconst spaceCoords = Object.values(space.coords);\n\t\tdestColor.coords = destColor.coords.map((coord, index) => {\n\t\t\tif (\"range\" in spaceCoords[index]) {\n\t\t\t\tconst [min, max] =  spaceCoords[index].range;\n\t\t\t\treturn util.clamp(min, coord, max);\n\t\t\t}\n\t\t\treturn coord;\n\t\t});\n\t\treturn destColor;\n\t}\n\tlet min = 0;\n\tlet max = origin_OKLCH.coords[1];\n\tlet min_inGamut = true;\n\tlet current = clone(origin_OKLCH);\n\tlet clipped = clip(current);\n\n\tlet E = deltaEOK(clipped, current);\n\tif (E < JND) {\n\t\treturn clipped;\n\t}\n\n\twhile ((max - min) > ε) {\n\t\tconst chroma = (min + max) / 2;\n\t\tcurrent.coords[1] = chroma;\n\t\tif (min_inGamut && inGamut(current, space, {epsilon: 0})) {\n\t\t\tmin = chroma;\n\t\t}\n\t\telse {\n\t\t\tclipped = clip(current);\n\t\t\tE = deltaEOK(clipped, current);\n\t\t\tif (E < JND) {\n\t\t\t\tif ((JND - E < ε)) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tmin_inGamut = false;\n\t\t\t\t\tmin = chroma;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tmax = chroma;\n\t\t\t}\n\t\t}\n\t}\n\treturn clipped;\n}\n", "import getColor from \"./getColor.js\";\nimport ColorSpace from \"./space.js\";\nimport toGamut from \"./toGamut.js\";\n\n/**\n * Convert to color space and return a new color\n * @param {Object|string} space - Color space object or id\n * @param {Object} options\n * @param {boolean} options.inGamut - Whether to force resulting color in gamut\n * @returns {Color}\n */\nexport default function to (color, space, {inGamut} = {}) {\n\tcolor = getColor(color);\n\tspace = ColorSpace.get(space);\n\n\tlet coords = space.from(color);\n\tlet ret = {space, coords, alpha: color.alpha};\n\n\tif (inGamut) {\n\t\tret = toGamut(ret, inGamut === true ? undefined : inGamut);\n\t}\n\n\treturn ret;\n}\n\nto.returns = \"color\";\n", "'use strict';\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (O, P) {\n  if (!delete O[P]) throw new $TypeError('Cannot delete property ' + tryToString(P) + ' of ' + tryToString(O));\n};\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport getColor from \"./getColor.js\";\nimport checkInGamut from \"./inGamut.js\";\nimport toGamut from \"./toGamut.js\";\nimport clone from \"./clone.js\";\n\n/**\n * Generic toString() method, outputs a color(spaceId ...coords) function, a functional syntax, or custom formats defined by the color space\n * @param {Object} options\n * @param {number} options.precision - Significant digits\n * @param {boolean} options.inGamut - Adjust coordinates to fit in gamut first? [default: false]\n */\nexport default function serialize (color, {\n\tprecision = defaults.precision,\n\tformat = \"default\",\n\tinGamut = true,\n\t...customOptions\n} = {}) {\n\tlet ret;\n\n\tcolor = getColor(color);\n\n\tlet formatId = format;\n\tformat = color.space.getFormat(format)\n\t       ?? color.space.getFormat(\"default\")\n\t       ?? ColorSpace.DEFAULT_FORMAT;\n\n\t// The assignment to coords and inGamut needs to stay in the order they are now\n\t// The order of the assignment was changed as a workaround for a bug in Next.js\n\t// See this issue for details: https://github.com/color-js/color.js/issues/260\n\n\tlet coords = color.coords.slice(); // clone so we can manipulate it\n\n\tinGamut ||= format.toGamut;\n\n\tif (inGamut && !checkInGamut(color)) {\n\t\t// FIXME what happens if the color contains NaNs?\n\t\tcoords = toGamut(clone(color), inGamut === true ? undefined : inGamut).coords;\n\t}\n\n\tif (format.type === \"custom\") {\n\t\tcustomOptions.precision = precision;\n\n\t\tif (format.serialize) {\n\t\t\tret = format.serialize(coords, color.alpha, customOptions);\n\t\t}\n\t\telse {\n\t\t\tthrow new TypeError(`format ${formatId} can only be used to parse colors, not for serialization`);\n\t\t}\n\t}\n\telse {\n\t\t// Functional syntax\n\t\tlet name = format.name || \"color\";\n\n\t\tif (format.serializeCoords) {\n\t\t\tcoords = format.serializeCoords(coords, precision);\n\t\t}\n\t\telse {\n\t\t\tif (precision !== null) {\n\t\t\t\tcoords = coords.map(c => {\n\t\t\t\t\treturn util.serializeNumber(c, {precision});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tlet args = [...coords];\n\n\t\tif (name === \"color\") {\n\t\t\t// If output is a color() function, add colorspace id as first argument\n\t\t\tlet cssId = format.id || format.ids?.[0] || color.space.id;\n\t\t\targs.unshift(cssId);\n\t\t}\n\n\t\tlet alpha = color.alpha;\n\t\tif (precision !== null) {\n\t\t\talpha = util.serializeNumber(alpha, {precision});\n\t\t}\n\n\t\tlet strAlpha = color.alpha >= 1 || format.noAlpha ? \"\" : `${format.commas ? \",\" : \" /\"} ${alpha}`;\n\t\tret = `${name}(${args.join(format.commas ? \", \" : \" \")}${strAlpha})`;\n\t}\n\n\treturn ret;\n}\n", "'use strict';\nvar $ = require('../internals/export');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar setArrayLength = require('../internals/array-set-length');\nvar deletePropertyOrThrow = require('../internals/delete-property-or-throw');\nvar doesNotExceedSafeInteger = require('../internals/does-not-exceed-safe-integer');\n\n// IE8-\nvar INCORRECT_RESULT = [].unshift(0) !== 1;\n\n// V8 ~ Chrome < 71 and Safari <= 15.4, FF < 23 throws InternalError\nvar properErrorOnNonWritableLength = function () {\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).unshift();\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n};\n\nvar FORCED = INCORRECT_RESULT || !properErrorOnNonWritableLength();\n\n// `Array.prototype.unshift` method\n// https://tc39.es/ecma262/#sec-array.prototype.unshift\n$({ target: 'Array', proto: true, arity: 1, forced: FORCED }, {\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  unshift: function unshift(item) {\n    var O = toObject(this);\n    var len = lengthOfArrayLike(O);\n    var argCount = arguments.length;\n    if (argCount) {\n      doesNotExceedSafeInteger(len + argCount);\n      var k = len;\n      while (k--) {\n        var to = k + argCount;\n        if (k in O) O[to] = O[k];\n        else deletePropertyOrThrow(O, to);\n      }\n      for (var j = 0; j < argCount; j++) {\n        O[j] = arguments[j];\n      }\n    } return setArrayLength(O, len + argCount);\n  }\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light rec2020 values to CIE XYZ\n// using  D65 (no chromatic adaptation)\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// 0 is actually calculated as  4.994106574466076e-17\nconst toXYZ_M = [\n\t[ 0.6369580483012914, 0.14461690358620832,  0.1688809751641721  ],\n\t[ 0.2627002120112671, 0.6779980715188708,   0.05930171646986196 ],\n\t[ 0.000000000000000,  0.028072693049087428, 1.060985057710791   ],\n];\n\n// from ITU-R BT.2124-0 Annex 2 p.3\nconst fromXYZ_M = [\n\t[  1.716651187971268,  -0.355670783776392, -0.253366281373660  ],\n\t[ -0.666684351832489,   1.616481236634939,  0.0157685458139111 ],\n\t[  0.017639857445311,  -0.042770613257809,  0.942103121235474  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"rec2020-linear\",\n\tcssId: \"--rec2020-linear\",\n\tname: \"Linear REC.2020\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n// import sRGB from \"./srgb.js\";\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\n\nexport default new RGBColorSpace({\n\tid: \"rec2020\",\n\tname: \"REC.2020\",\n\tbase: REC2020Linear,\n\t// Non-linear transfer function from Rec. ITU-R BT.2020-2 table 4\n\ttoBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val < β * 4.5) {\n\t\t\t\treturn val / 4.5;\n\t\t\t}\n\n\t\t\treturn Math.pow((val + α - 1) / α, 1 / 0.45);\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val >= β) {\n\t\t\t\treturn α * Math.pow(val, 0.45) - (α - 1);\n\t\t\t}\n\n\t\t\treturn 4.5 * val;\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\nconst toXYZ_M = [\n\t[0.4865709486482162, 0.26566769316909306, 0.1982172852343625],\n\t[0.2289745640697488, 0.6917385218365064,  0.079286914093745],\n\t[0.0000000000000000, 0.04511338185890264, 1.043944368900976],\n];\n\nconst fromXYZ_M = [\n\t[ 2.493496911941425,   -0.9313836179191239, -0.40271078445071684],\n\t[-0.8294889695615747,   1.7626640603183463,  0.023624685841943577],\n\t[ 0.03584583024378447, -0.07617238926804182, 0.9568845240076872],\n];\n\nexport default new RGBColorSpace({\n\tid: \"p3-linear\",\n\tcssId: \"--display-p3-linear\",\n\tname: \"Linear P3\",\n\twhite: \"D65\",\n\tto<PERSON><PERSON><PERSON>_<PERSON>,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// This is the linear-light version of sRGB\n// as used for example in SVG filters\n// or in Canvas\n\n// This matrix was calculated directly from the RGB and white chromaticities\n// when rounded to 8 decimal places, it agrees completely with the official matrix\n// see https://github.com/w3c/csswg-drafts/issues/5922\nconst toXYZ_M = [\n\t[ 0.41239079926595934, 0.357584339383878,   0.1804807884018343  ],\n\t[ 0.21263900587151027, 0.715168678767756,   0.07219231536073371 ],\n\t[ 0.01933081871559182, 0.11919477979462598, 0.9505321522496607  ],\n];\n\n// This matrix is the inverse of the above;\n// again it agrees with the official definition when rounded to 8 decimal places\nexport const fromXYZ_M = [\n\t[  3.2409699419045226,  -1.537383177570094,   -0.4986107602930034  ],\n\t[ -0.9692436362808796,   1.8759675015077202,   0.04155505740717559 ],\n\t[  0.05563007969699366, -0.20397695888897652,  1.0569715142428786  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"srgb-linear\",\n\tname: \"Linear sRGB\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "/* List of CSS color keywords\n * Note that this does not include currentColor, transparent,\n * or system colors\n */\n\n// To produce: Visit https://www.w3.org/TR/css-color-4/#named-colors\n// and run in the console:\n// copy($$(\"tr\", $(\".named-color-table tbody\")).map(tr => `\"${tr.cells[2].textContent.trim()}\": [${tr.cells[4].textContent.trim().split(/\\s+/).map(c => c === \"0\"? \"0\" : c === \"255\"? \"1\" : c + \" / 255\").join(\", \")}]`).join(\",\\n\"))\nexport default {\n\t\"aliceblue\": [240 / 255, 248 / 255, 1],\n\t\"antiquewhite\": [250 / 255, 235 / 255, 215 / 255],\n\t\"aqua\": [0, 1, 1],\n\t\"aquamarine\": [127 / 255, 1, 212 / 255],\n\t\"azure\": [240 / 255, 1, 1],\n\t\"beige\": [245 / 255, 245 / 255, 220 / 255],\n\t\"bisque\": [1, 228 / 255, 196 / 255],\n\t\"black\": [0, 0, 0],\n\t\"blanchedalmond\": [1, 235 / 255, 205 / 255],\n\t\"blue\": [0, 0, 1],\n\t\"blueviolet\": [138 / 255, 43 / 255, 226 / 255],\n\t\"brown\": [165 / 255, 42 / 255, 42 / 255],\n\t\"burlywood\": [222 / 255, 184 / 255, 135 / 255],\n\t\"cadetblue\": [95 / 255, 158 / 255, 160 / 255],\n\t\"chartreuse\": [127 / 255, 1, 0],\n\t\"chocolate\": [210 / 255, 105 / 255, 30 / 255],\n\t\"coral\": [1, 127 / 255, 80 / 255],\n\t\"cornflowerblue\": [100 / 255, 149 / 255, 237 / 255],\n\t\"cornsilk\": [1, 248 / 255, 220 / 255],\n\t\"crimson\": [220 / 255, 20 / 255, 60 / 255],\n\t\"cyan\": [0, 1, 1],\n\t\"darkblue\": [0, 0, 139 / 255],\n\t\"darkcyan\": [0, 139 / 255, 139 / 255],\n\t\"darkgoldenrod\": [184 / 255, 134 / 255, 11 / 255],\n\t\"darkgray\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkgreen\": [0, 100 / 255, 0],\n\t\"darkgrey\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkkhaki\": [189 / 255, 183 / 255, 107 / 255],\n\t\"darkmagenta\": [139 / 255, 0, 139 / 255],\n\t\"darkolivegreen\": [85 / 255, 107 / 255, 47 / 255],\n\t\"darkorange\": [1, 140 / 255, 0],\n\t\"darkorchid\": [153 / 255, 50 / 255, 204 / 255],\n\t\"darkred\": [139 / 255, 0, 0],\n\t\"darksalmon\": [233 / 255, 150 / 255, 122 / 255],\n\t\"darkseagreen\": [143 / 255, 188 / 255, 143 / 255],\n\t\"darkslateblue\": [72 / 255, 61 / 255, 139 / 255],\n\t\"darkslategray\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkslategrey\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkturquoise\": [0, 206 / 255, 209 / 255],\n\t\"darkviolet\": [148 / 255, 0, 211 / 255],\n\t\"deeppink\": [1, 20 / 255, 147 / 255],\n\t\"deepskyblue\": [0, 191 / 255, 1],\n\t\"dimgray\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dimgrey\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dodgerblue\": [30 / 255, 144 / 255, 1],\n\t\"firebrick\": [178 / 255, 34 / 255, 34 / 255],\n\t\"floralwhite\": [1, 250 / 255, 240 / 255],\n\t\"forestgreen\": [34 / 255, 139 / 255, 34 / 255],\n\t\"fuchsia\": [1, 0, 1],\n\t\"gainsboro\": [220 / 255, 220 / 255, 220 / 255],\n\t\"ghostwhite\": [248 / 255, 248 / 255, 1],\n\t\"gold\": [1, 215 / 255, 0],\n\t\"goldenrod\": [218 / 255, 165 / 255, 32 / 255],\n\t\"gray\": [128 / 255, 128 / 255, 128 / 255],\n\t\"green\": [0, 128 / 255, 0],\n\t\"greenyellow\": [173 / 255, 1, 47 / 255],\n\t\"grey\": [128 / 255, 128 / 255, 128 / 255],\n\t\"honeydew\": [240 / 255, 1, 240 / 255],\n\t\"hotpink\": [1, 105 / 255, 180 / 255],\n\t\"indianred\": [205 / 255, 92 / 255, 92 / 255],\n\t\"indigo\": [75 / 255, 0, 130 / 255],\n\t\"ivory\": [1, 1, 240 / 255],\n\t\"khaki\": [240 / 255, 230 / 255, 140 / 255],\n\t\"lavender\": [230 / 255, 230 / 255, 250 / 255],\n\t\"lavenderblush\": [1, 240 / 255, 245 / 255],\n\t\"lawngreen\": [124 / 255, 252 / 255, 0],\n\t\"lemonchiffon\": [1, 250 / 255, 205 / 255],\n\t\"lightblue\": [173 / 255, 216 / 255, 230 / 255],\n\t\"lightcoral\": [240 / 255, 128 / 255, 128 / 255],\n\t\"lightcyan\": [224 / 255, 1, 1],\n\t\"lightgoldenrodyellow\": [250 / 255, 250 / 255, 210 / 255],\n\t\"lightgray\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightgreen\": [144 / 255, 238 / 255, 144 / 255],\n\t\"lightgrey\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightpink\": [1, 182 / 255, 193 / 255],\n\t\"lightsalmon\": [1, 160 / 255, 122 / 255],\n\t\"lightseagreen\": [32 / 255, 178 / 255, 170 / 255],\n\t\"lightskyblue\": [135 / 255, 206 / 255, 250 / 255],\n\t\"lightslategray\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightslategrey\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightsteelblue\": [176 / 255, 196 / 255, 222 / 255],\n\t\"lightyellow\": [1, 1, 224 / 255],\n\t\"lime\": [0, 1, 0],\n\t\"limegreen\": [50 / 255, 205 / 255, 50 / 255],\n\t\"linen\": [250 / 255, 240 / 255, 230 / 255],\n\t\"magenta\": [1, 0, 1],\n\t\"maroon\": [128 / 255, 0, 0],\n\t\"mediumaquamarine\": [102 / 255, 205 / 255, 170 / 255],\n\t\"mediumblue\": [0, 0, 205 / 255],\n\t\"mediumorchid\": [186 / 255, 85 / 255, 211 / 255],\n\t\"mediumpurple\": [147 / 255, 112 / 255, 219 / 255],\n\t\"mediumseagreen\": [60 / 255, 179 / 255, 113 / 255],\n\t\"mediumslateblue\": [123 / 255, 104 / 255, 238 / 255],\n\t\"mediumspringgreen\": [0, 250 / 255, 154 / 255],\n\t\"mediumturquoise\": [72 / 255, 209 / 255, 204 / 255],\n\t\"mediumvioletred\": [199 / 255, 21 / 255, 133 / 255],\n\t\"midnightblue\": [25 / 255, 25 / 255, 112 / 255],\n\t\"mintcream\": [245 / 255, 1, 250 / 255],\n\t\"mistyrose\": [1, 228 / 255, 225 / 255],\n\t\"moccasin\": [1, 228 / 255, 181 / 255],\n\t\"navajowhite\": [1, 222 / 255, 173 / 255],\n\t\"navy\": [0, 0, 128 / 255],\n\t\"oldlace\": [253 / 255, 245 / 255, 230 / 255],\n\t\"olive\": [128 / 255, 128 / 255, 0],\n\t\"olivedrab\": [107 / 255, 142 / 255, 35 / 255],\n\t\"orange\": [1, 165 / 255, 0],\n\t\"orangered\": [1, 69 / 255, 0],\n\t\"orchid\": [218 / 255, 112 / 255, 214 / 255],\n\t\"palegoldenrod\": [238 / 255, 232 / 255, 170 / 255],\n\t\"palegreen\": [152 / 255, 251 / 255, 152 / 255],\n\t\"paleturquoise\": [175 / 255, 238 / 255, 238 / 255],\n\t\"palevioletred\": [219 / 255, 112 / 255, 147 / 255],\n\t\"papayawhip\": [1, 239 / 255, 213 / 255],\n\t\"peachpuff\": [1, 218 / 255, 185 / 255],\n\t\"peru\": [205 / 255, 133 / 255, 63 / 255],\n\t\"pink\": [1, 192 / 255, 203 / 255],\n\t\"plum\": [221 / 255, 160 / 255, 221 / 255],\n\t\"powderblue\": [176 / 255, 224 / 255, 230 / 255],\n\t\"purple\": [128 / 255, 0, 128 / 255],\n\t\"rebeccapurple\": [102 / 255, 51 / 255, 153 / 255],\n\t\"red\": [1, 0, 0],\n\t\"rosybrown\": [188 / 255, 143 / 255, 143 / 255],\n\t\"royalblue\": [65 / 255, 105 / 255, 225 / 255],\n\t\"saddlebrown\": [139 / 255, 69 / 255, 19 / 255],\n\t\"salmon\": [250 / 255, 128 / 255, 114 / 255],\n\t\"sandybrown\": [244 / 255, 164 / 255, 96 / 255],\n\t\"seagreen\": [46 / 255, 139 / 255, 87 / 255],\n\t\"seashell\": [1, 245 / 255, 238 / 255],\n\t\"sienna\": [160 / 255, 82 / 255, 45 / 255],\n\t\"silver\": [192 / 255, 192 / 255, 192 / 255],\n\t\"skyblue\": [135 / 255, 206 / 255, 235 / 255],\n\t\"slateblue\": [106 / 255, 90 / 255, 205 / 255],\n\t\"slategray\": [112 / 255, 128 / 255, 144 / 255],\n\t\"slategrey\": [112 / 255, 128 / 255, 144 / 255],\n\t\"snow\": [1, 250 / 255, 250 / 255],\n\t\"springgreen\": [0, 1, 127 / 255],\n\t\"steelblue\": [70 / 255, 130 / 255, 180 / 255],\n\t\"tan\": [210 / 255, 180 / 255, 140 / 255],\n\t\"teal\": [0, 128 / 255, 128 / 255],\n\t\"thistle\": [216 / 255, 191 / 255, 216 / 255],\n\t\"tomato\": [1, 99 / 255, 71 / 255],\n\t\"turquoise\": [64 / 255, 224 / 255, 208 / 255],\n\t\"violet\": [238 / 255, 130 / 255, 238 / 255],\n\t\"wheat\": [245 / 255, 222 / 255, 179 / 255],\n\t\"white\": [1, 1, 1],\n\t\"whitesmoke\": [245 / 255, 245 / 255, 245 / 255],\n\t\"yellow\": [1, 1, 0],\n\t\"yellowgreen\": [154 / 255, 205 / 255, 50 / 255],\n};\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport sRGBLinear from \"./srgb-linear.js\";\nimport KEYWORDS from \"../keywords.js\";\n\nlet coordGrammar = Array(3).fill(\"<percentage> | <number>[0, 255]\");\nlet coordGrammarNumber = Array(3).fill(\"<number>[0, 255]\");\n\nexport default new RGBColorSpace({\n\tid: \"srgb\",\n\tname: \"sRGB\",\n\tbase: sRGBLinear,\n\tfromBase: rgb => {\n\t\t// convert an array of linear-light sRGB values in the range 0.0-1.0\n\t\t// to gamma corrected form\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs > 0.0031308) {\n\t\t\t\treturn sign * (1.055 * (abs ** (1 / 2.4)) - 0.055);\n\t\t\t}\n\n\t\t\treturn 12.92 * val;\n\t\t});\n\t},\n\ttoBase: rgb => {\n\t\t// convert an array of sRGB values in the range 0.0 - 1.0\n\t\t// to linear light (un-companded) form.\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs <= 0.04045) {\n\t\t\t\treturn val / 12.92;\n\t\t\t}\n\n\t\t\treturn sign * (((abs + 0.055) / 1.055) ** 2.4);\n\t\t});\n\t},\n\tformats: {\n\t\t\"rgb\": {\n\t\t\tcoords: coordGrammar,\n\t\t},\n\t\t\"rgb_number\": {\n\t\t\tname: \"rgb\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t\tnoAlpha: true,\n\t\t},\n\t\t\"color\": { /* use defaults */ },\n\t\t\"rgba\": {\n\t\t\tcoords: coordGrammar,\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t\t\"rgba_number\": {\n\t\t\tname: \"rgba\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t},\n\t\t\"hex\": {\n\t\t\ttype: \"custom\",\n\t\t\ttoGamut: true,\n\t\t\ttest: str => /^#([a-f0-9]{3,4}){1,2}$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tif (str.length <= 5) {\n\t\t\t\t\t// #rgb or #rgba, duplicate digits\n\t\t\t\t\tstr = str.replace(/[a-f0-9]/gi, \"$&$&\");\n\t\t\t\t}\n\n\t\t\t\tlet rgba = [];\n\t\t\t\tstr.replace(/[a-f0-9]{2}/gi, component => {\n\t\t\t\t\trgba.push(parseInt(component, 16) / 255);\n\t\t\t\t});\n\n\t\t\t\treturn {\n\t\t\t\t\tspaceId: \"srgb\",\n\t\t\t\t\tcoords: rgba.slice(0, 3),\n\t\t\t\t\talpha: rgba.slice(3)[0],\n\t\t\t\t};\n\t\t\t},\n\t\t\tserialize: (coords, alpha, {\n\t\t\t\tcollapse = true, // collapse to 3-4 digit hex when possible?\n\t\t\t} = {}) => {\n\t\t\t\tif (alpha < 1) {\n\t\t\t\t\tcoords.push(alpha);\n\t\t\t\t}\n\n\t\t\t\tcoords = coords.map(c => Math.round(c * 255));\n\n\t\t\t\tlet collapsible = collapse && coords.every(c => c % 17 === 0);\n\n\t\t\t\tlet hex = coords.map(c => {\n\t\t\t\t\tif (collapsible) {\n\t\t\t\t\t\treturn (c / 17).toString(16);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn c.toString(16).padStart(2, \"0\");\n\t\t\t\t}).join(\"\");\n\n\t\t\t\treturn \"#\" + hex;\n\t\t\t},\n\t\t},\n\t\t\"keyword\": {\n\t\t\ttype: \"custom\",\n\t\t\ttest: str => /^[a-z]+$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tstr = str.toLowerCase();\n\t\t\t\tlet ret = {spaceId: \"srgb\", coords: null, alpha: 1};\n\n\t\t\t\tif (str === \"transparent\") {\n\t\t\t\t\tret.coords = KEYWORDS.black;\n\t\t\t\t\tret.alpha = 0;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tret.coords = KEYWORDS[str];\n\t\t\t\t}\n\n\t\t\t\tif (ret.coords) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport P3Linear from \"./p3-linear.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new RGBColorSpace({\n\tid: \"p3\",\n\tcssId: \"display-p3\",\n\tname: \"P3\",\n\tbase: P3Linear,\n\t// Gamma encoding/decoding is the same as sRGB\n\tfromBase: sRGB.fromBase,\n\ttoBase: sRGB.toBase,\n});\n", "import { isNone, skipNone } from \"./util.js\";\nimport defaults from \"./defaults.js\";\nimport to from \"./to.js\";\nimport serialize from \"./serialize.js\";\nimport clone from \"./clone.js\";\nimport REC2020 from \"./spaces/rec2020.js\";\nimport P3 from \"./spaces/p3.js\";\nimport Lab from \"./spaces/lab.js\";\nimport sRGB from \"./spaces/srgb.js\";\n\n// Default space for CSS output. Code in Color.js makes this wider if there's a DOM available\ndefaults.display_space = sRGB;\n\nlet supportsNone;\n\nif (typeof CSS !== \"undefined\" && CSS.supports) {\n\t// Find widest supported color space for CSS\n\tfor (let space of [Lab, REC2020, P3]) {\n\t\tlet coords = space.getMinCoords();\n\t\tlet color = {space, coords, alpha: 1};\n\t\tlet str = serialize(color);\n\n\t\tif (CSS.supports(\"color\", str)) {\n\t\t\tdefaults.display_space = space;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\n/**\n * Returns a serialization of the color that can actually be displayed in the browser.\n * If the default serialization can be displayed, it is returned.\n * Otherwise, the color is converted to Lab, REC2020, or P3, whichever is the widest supported.\n * In Node.js, this is basically equivalent to `serialize()` but returns a `String` object instead.\n *\n * @export\n * @param {{space, coords} | Color | string} color\n * @param {*} [options={}] Options to be passed to serialize()\n * @param {ColorSpace | string} [options.space = defaults.display_space] Color space to use for serialization if default is not supported\n * @returns {String} String object containing the serialized color with a color property containing the converted color (or the original, if no conversion was necessary)\n */\nexport default function display (color, {space = defaults.display_space, ...options} = {}) {\n\tlet ret = serialize(color, options);\n\n\tif (typeof CSS === \"undefined\" || CSS.supports(\"color\", ret) || !defaults.display_space) {\n\t\tret = new String(ret);\n\t\tret.color = color;\n\t}\n\telse {\n\t\t// If we're here, what we were about to output is not supported\n\t\tlet fallbackColor = color;\n\n\t\t// First, check if the culprit is none values\n\t\tlet hasNone = color.coords.some(isNone) || isNone(color.alpha);\n\n\t\tif (hasNone) {\n\t\t\t// Does the browser support none values?\n\t\t\tif (!(supportsNone ??= CSS.supports(\"color\", \"hsl(none 50% 50%)\"))) {\n\t\t\t\t// Nope, try again without none\n\t\t\t\tfallbackColor = clone(color);\n\t\t\t\tfallbackColor.coords = fallbackColor.coords.map(skipNone);\n\t\t\t\tfallbackColor.alpha = skipNone(fallbackColor.alpha);\n\n\t\t\t\tret = serialize(fallbackColor, options);\n\n\t\t\t\tif (CSS.supports(\"color\", ret)) {\n\t\t\t\t\t// We're done, now it's supported\n\t\t\t\t\tret = new String(ret);\n\t\t\t\t\tret.color = fallbackColor;\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// If we're here, the color function is not supported\n\t\t// Fall back to fallback space\n\t\tfallbackColor = to(fallbackColor, space);\n\t\tret = new String(serialize(fallbackColor, options));\n\t\tret.color = fallbackColor;\n\t}\n\n\treturn ret;\n}\n", "/**\n * Relative luminance\n */\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport xyz_d65 from \"./spaces/xyz-d65.js\";\n\nexport function getLuminance (color) {\n\t// Assume getColor() is called on color in get()\n\treturn get(color, [xyz_d65, \"y\"]);\n}\n\nexport function setLuminance (color, value) {\n\t// Assume getColor() is called on color in set()\n\tset(color, [xyz_d65, \"y\"], value);\n}\n\nexport function register (Color) {\n\tObject.defineProperty(Color.prototype, \"luminance\", {\n\t\tget () {\n\t\t\treturn getLuminance(this);\n\t\t},\n\t\tset (value) {\n\t\t\tsetLuminance(this, value);\n\t\t},\n\t});\n}\n", "// WCAG 2.0 contrast https://www.w3.org/TR/WCAG20-TECHS/G18.html\n// Simple contrast, with fixed 5% viewing flare contribution\n// Symmetric, does not matter which is foreground and which is background\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrastWCAG21 (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn (Y1 + .05) / (Y2 + .05);\n}\n", "// APCA 0.0.98G\n// https://github.com/Myndex/apca-w3\n// see also https://github.com/w3c/silver/issues/643\n\nimport getColor from \"../getColor.js\";\nimport to from \"../to.js\";\n\n// exponents\nconst normBG = 0.56;\nconst normTXT = 0.57;\nconst revTXT = 0.62;\nconst revBG = 0.65;\n\n// clamps\nconst blkThrs = 0.022;\nconst blkClmp = 1.414;\nconst loClip = 0.1;\nconst deltaYmin = 0.0005;\n\n// scalers\n// see https://github.com/w3c/silver/issues/645\nconst scaleBoW = 1.14;\nconst loBoWoffset = 0.027;\nconst scaleWoB = 1.14;\nconst loWoBoffset = 0.027;\n\nfunction fclamp (Y) {\n\tif (Y >= blkThrs) {\n\t\treturn Y;\n\t}\n\treturn Y + (blkThrs - Y) ** blkClmp;\n}\n\nfunction linearize (val) {\n\tlet sign = val < 0 ? -1 : 1;\n\tlet abs = Math.abs(val);\n\treturn sign * Math.pow(abs, 2.4);\n}\n\n// Not symmetric, requires a foreground (text) color, and a background color\nexport default function contrastAPCA (background, foreground) {\n\tforeground = getColor(foreground);\n\tbackground = getColor(background);\n\n\tlet S;\n\tlet C;\n\tlet Sapc;\n\n\t// Myndex as-published, assumes sRGB inputs\n\tlet R, G, B;\n\n\tforeground = to(foreground, \"srgb\");\n\t// Should these be clamped to in-gamut values?\n\n\t// Calculates \"screen luminance\" with non-standard simple gamma EOTF\n\t// weights should be from CSS Color 4, not the ones here which are via Myndex and copied from Lindbloom\n\t[R, G, B] = foreground.coords;\n\tlet lumTxt = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\tbackground = to(background, \"srgb\");\n\t[R, G, B] = background.coords;\n\tlet lumBg = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\t// toe clamping of very dark values to account for flare\n\tlet Ytxt = fclamp(lumTxt);\n\tlet Ybg = fclamp(lumBg);\n\n\t// are we \"Black on White\" (dark on light), or light on dark?\n\tlet BoW = Ybg > Ytxt;\n\n\t// why is this a delta, when Y is not perceptually uniform?\n\t// Answer: it is a noise gate, see\n\t// https://github.com/LeaVerou/color.js/issues/208\n\tif (Math.abs(Ybg - Ytxt) < deltaYmin) {\n\t\tC = 0;\n\t}\n\telse {\n\t\tif (BoW) {\n\t\t\t// dark text on light background\n\t\t\tS = Ybg ** normBG - Ytxt ** normTXT;\n\t\t\tC = S * scaleBoW;\n\t\t}\n\t\telse {\n\t\t\t// light text on dark background\n\t\t\tS = Ybg ** revBG - Ytxt ** revTXT;\n\t\t\tC = S * scaleWoB;\n\t\t}\n\t}\n\tif (Math.abs(C) < loClip) {\n\t\tSapc = 0;\n\t}\n\telse if (C > 0) {\n\t\t// not clear whether Woffset is loBoWoffset or loWoBoffset\n\t\t// but they have the same value\n\t\tSapc = C - loBoWoffset;\n\t}\n\telse {\n\t\tSapc = C + loBoWoffset;\n\t}\n\n\treturn Sapc * 100;\n}\n", "// Michelson  luminance contrast\n// the relation between the spread and the sum of the two luminances\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrast<PERSON><PERSON><PERSON> (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\tlet denom = (Y1 + Y2);\n\treturn denom === 0 ? 0 : (Y1 - Y2) / denom;\n}\n", "// Weber luminance contrast\n// The difference between the two luminances divided by the lower luminance\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\n// the darkest sRGB color above black is #000001 and this produces\n// a plain Weber contrast of ~45647.\n// So, setting the divide-by-zero result at 50000 is a reasonable\n// max clamp for the plain Weber\nconst max = 50000;\n\nexport default function contrastWeber (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn Y2 === 0 ? max : (Y1 - Y2) / Y2;\n}\n", "// CIE Lightness difference, as used by Google Material Design\n// Google HCT Tone is the same as CIE Lightness\n// https://material.io/blog/science-of-color-design\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab from \"../spaces/lab.js\";\n\nexport default function contrastLstar (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet L1 = get(color1, [lab, \"l\"]);\n\tlet L2 = get(color2, [lab, \"l\"]);\n\n\treturn Math.abs(L1 - L2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D65;\n\nexport default new ColorSpace({\n\tid: \"lab-d65\",\n\tname: \"Lab D65\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D65, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d65,\n\t// Convert D65-adapted XYZ to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D65-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab-d65\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// Delta Phi Star perceptual lightness contrast\n// See https://github.com/Myndex/deltaphistar\n// The (difference between two Lstars each raised to phi) raised to (1/phi)\n// Symmetric, does not matter which is foreground and which is background\n\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab_d65 from \"../spaces/lab-d65.js\";\n\nconst phi = Math.pow(5, 0.5) * 0.5 + 0.5; // Math.phi can be used if Math.js\n\nexport default function contrastDeltaPhi (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Lstr1 = get(color1, [lab_d65, \"l\"]);\n\tlet Lstr2 = get(color2, [lab_d65, \"l\"]);\n\n\tlet deltaPhiStar = Math.abs(Math.pow(Lstr1, phi) - Math.pow(Lstr2, phi));\n\n\tlet contrast = Math.pow(deltaPhiStar, (1 / phi)) * Math.SQRT2 - 40;\n\n\treturn (contrast < 7.5) ? 0.0 : contrast ;\n}\n", "import xyz_d65 from \"./spaces/xyz-d65.js\";\nimport getAll from \"./getAll.js\";\n\n// Chromaticity coordinates\nexport function uv (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet denom = X + 15 * Y + 3 * Z;\n\treturn [4 * X / denom, 9 * Y / denom];\n}\n\nexport function xy (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet  sum = X + Y + Z;\n\treturn [X / sum, Y / sum];\n}\n\nexport function register (Color) {\n\t// no setters, as lightness information is lost\n\t// when converting color to chromaticity\n\tObject.defineProperty(Color.prototype, \"uv\", {\n\t\tget () {\n\t\t\treturn uv(this);\n\t\t},\n\t});\n\n\tObject.defineProperty(Color.prototype, \"xy\", {\n\t\tget () {\n\t\t\treturn xy(this);\n\t\t},\n\t});\n}\n", "import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nexport default function deltaE (c1, c2, o = {}) {\n\tif (isString(o)) {\n\t\to = {method: o};\n\t}\n\n\tlet {method = defaults.deltaE, ...rest} = o;\n\n\tfor (let m in deltaEMethods) {\n\t\tif (\"deltae\" + method.toLowerCase() === m.toLowerCase()) {\n\t\t\treturn deltaEMethods[m](c1, c2, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown deltaE method: ${method}`);\n}\n", "/**\n * Functions related to color interpolation\n */\nimport ColorSpace from \"./space.js\";\nimport {type, interpolate} from \"./util.js\";\nimport getColor from \"./getColor.js\";\nimport clone from \"./clone.js\";\nimport to from \"./to.js\";\nimport toGamut from \"./toGamut.js\";\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport defaults from \"./defaults.js\";\nimport * as angles from \"./angles.js\";\nimport deltaE from \"./deltaE.js\";\n\n/**\n * Return an intermediate color between two colors\n * Signatures: mix(c1, c2, p, options)\n *             mix(c1, c2, options)\n *             mix(color)\n * @param {Color | string} c1 The first color\n * @param {Color | string} [c2] The second color\n * @param {number} [p=.5] A 0-1 percentage where 0 is c1 and 1 is c2\n * @param {Object} [o={}]\n * @return {Color}\n */\nexport function mix (c1, c2, p = .5, o = {}) {\n\t[c1, c2] = [getColor(c1), getColor(c2)];\n\n\tif (type(p) === \"object\") {\n\t\t[p, o] = [.5, p];\n\t}\n\n\tlet r = range(c1, c2, o);\n\treturn r(p);\n}\n\n/**\n *\n * @param {Color | string | Function} c1 The first color or a range\n * @param {Color | string} [c2] The second color if c1 is not a range\n * @param {Object} [options={}]\n * @return {Color[]}\n */\nexport function steps (c1, c2, options = {}) {\n\tlet colorRange;\n\n\tif (isRange(c1)) {\n\t\t// Tweaking existing range\n\t\t[colorRange, options] = [c1, c2];\n\t\t[c1, c2] = colorRange.rangeArgs.colors;\n\t}\n\n\tlet {\n\t\tmaxDeltaE, deltaEMethod,\n\t\tsteps = 2, maxSteps = 1000,\n\t\t...rangeOptions\n\t} = options;\n\n\tif (!colorRange) {\n\t\t[c1, c2] = [getColor(c1), getColor(c2)];\n\t\tcolorRange = range(c1, c2, rangeOptions);\n\t}\n\n\tlet totalDelta = deltaE(c1, c2);\n\tlet actualSteps = maxDeltaE > 0 ? Math.max(steps, Math.ceil(totalDelta / maxDeltaE) + 1) : steps;\n\tlet ret = [];\n\n\tif (maxSteps !== undefined) {\n\t\tactualSteps = Math.min(actualSteps, maxSteps);\n\t}\n\n\tif (actualSteps === 1) {\n\t\tret = [{p: .5, color: colorRange(.5)}];\n\t}\n\telse {\n\t\tlet step = 1 / (actualSteps - 1);\n\t\tret = Array.from({length: actualSteps}, (_, i) => {\n\t\t\tlet p = i * step;\n\t\t\treturn {p, color: colorRange(p)};\n\t\t});\n\t}\n\n\tif (maxDeltaE > 0) {\n\t\t// Iterate over all stops and find max deltaE\n\t\tlet maxDelta = ret.reduce((acc, cur, i) => {\n\t\t\tif (i === 0) {\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tlet ΔΕ = deltaE(cur.color, ret[i - 1].color, deltaEMethod);\n\t\t\treturn Math.max(acc, ΔΕ);\n\t\t}, 0);\n\n\t\twhile (maxDelta > maxDeltaE) {\n\t\t\t// Insert intermediate stops and measure maxDelta again\n\t\t\t// We need to do this for all pairs, otherwise the midpoint shifts\n\t\t\tmaxDelta = 0;\n\n\t\t\tfor (let i = 1; (i < ret.length) && (ret.length < maxSteps); i++) {\n\t\t\t\tlet prev = ret[i - 1];\n\t\t\t\tlet cur = ret[i];\n\n\t\t\t\tlet p = (cur.p + prev.p) / 2;\n\t\t\t\tlet color = colorRange(p);\n\t\t\t\tmaxDelta = Math.max(maxDelta, deltaE(color, prev.color), deltaE(color, cur.color));\n\t\t\t\tret.splice(i, 0, {p, color: colorRange(p)});\n\t\t\t\ti++;\n\t\t\t}\n\t\t}\n\t}\n\n\tret = ret.map(a => a.color);\n\n\treturn ret;\n}\n\n/**\n * Interpolate to color2 and return a function that takes a 0-1 percentage\n * @param {Color | string | Function} color1 The first color or an existing range\n * @param {Color | string} [color2] If color1 is a color, this is the second color\n * @param {Object} [options={}]\n * @returns {Function} A function that takes a 0-1 percentage and returns a color\n */\nexport function range (color1, color2, options = {}) {\n\tif (isRange(color1)) {\n\t\t// Tweaking existing range\n\t\tlet [r, options] = [color1, color2];\n\n\t\treturn range(...r.rangeArgs.colors, {...r.rangeArgs.options, ...options});\n\t}\n\n\tlet {space, outputSpace, progression, premultiplied} = options;\n\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\t// Make sure we're working on copies of these colors\n\tcolor1 = clone(color1);\n\tcolor2 = clone(color2);\n\n\tlet rangeArgs = {colors: [color1, color2], options};\n\n\tif (space) {\n\t\tspace = ColorSpace.get(space);\n\t}\n\telse {\n\t\tspace = ColorSpace.registry[defaults.interpolationSpace] || color1.space;\n\t}\n\n\toutputSpace = outputSpace ? ColorSpace.get(outputSpace) : space;\n\n\tcolor1 = to(color1, space);\n\tcolor2 = to(color2, space);\n\n\t// Gamut map to avoid areas of flat color\n\tcolor1 = toGamut(color1);\n\tcolor2 = toGamut(color2);\n\n\t// Handle hue interpolation\n\t// See https://github.com/w3c/csswg-drafts/issues/4735#issuecomment-635741840\n\tif (space.coords.h && space.coords.h.type === \"angle\") {\n\t\tlet arc = options.hue = options.hue || \"shorter\";\n\n\t\tlet hue = [space, \"h\"];\n\t\tlet [θ1, θ2] = [get(color1, hue), get(color2, hue)];\n\t\t// Undefined hues must be evaluated before hue fix-up to properly\n\t\t// calculate hue arcs between undefined and defined hues.\n\t\t// See https://github.com/w3c/csswg-drafts/issues/9436#issuecomment-1746957545\n\t\tif (isNaN(θ1) && !isNaN(θ2)) {\n\t\t\tθ1 = θ2;\n\t\t}\n\t\telse if (isNaN(θ2) && !isNaN(θ1)) {\n\t\t\tθ2 = θ1;\n\t\t}\n\t\t[θ1, θ2] = angles.adjust(arc, [θ1, θ2]);\n\t\tset(color1, hue, θ1);\n\t\tset(color2, hue, θ2);\n\t}\n\n\tif (premultiplied) {\n\t\t// not coping with polar spaces yet\n\t\tcolor1.coords = color1.coords.map(c => c * color1.alpha);\n\t\tcolor2.coords = color2.coords.map(c => c * color2.alpha);\n\t}\n\n\treturn Object.assign(p => {\n\t\tp = progression ? progression(p) : p;\n\t\tlet coords = color1.coords.map((start, i) => {\n\t\t\tlet end = color2.coords[i];\n\t\t\treturn interpolate(start, end, p);\n\t\t});\n\n\t\tlet alpha = interpolate(color1.alpha, color2.alpha, p);\n\t\tlet ret = {space, coords, alpha};\n\n\t\tif (premultiplied) {\n\t\t\t// undo premultiplication\n\t\t\tret.coords = ret.coords.map(c => c / alpha);\n\t\t}\n\n\t\tif (outputSpace !== space) {\n\t\t\tret = to(ret, outputSpace);\n\t\t}\n\n\t\treturn ret;\n\t}, {\n\t\trangeArgs,\n\t});\n}\n\nexport function isRange (val) {\n\treturn type(val) === \"function\" && !!val.rangeArgs;\n}\n\ndefaults.interpolationSpace = \"lab\";\n\nexport function register (Color) {\n\tColor.defineFunction(\"mix\", mix, {returns: \"color\"});\n\tColor.defineFunction(\"range\", range, {returns: \"function<color>\"});\n\tColor.defineFunction(\"steps\", steps, {returns: \"array<color>\"});\n}\n", "import ColorSpace from \"../space.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new ColorSpace({\n\tid: \"hsl\",\n\tname: \"H<PERSON>\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: sRGB,\n\n\t// Adapted from https://drafts.csswg.org/css-color-4/better-rgbToHsl.js\n\tfromBase: rgb => {\n\t\tlet max = Math.max(...rgb);\n\t\tlet min = Math.min(...rgb);\n\t\tlet [r, g, b] = rgb;\n\t\tlet [h, s, l] = [NaN, 0, (min + max) / 2];\n\t\tlet d = max - min;\n\n\t\tif (d !== 0) {\n\t\t\ts = (l === 0 || l === 1) ? 0 : (max - l) / Math.min(l, 1 - l);\n\n\t\t\tswitch (max) {\n\t\t\t\tcase r: h = (g - b) / d + (g < b ? 6 : 0); break;\n\t\t\t\tcase g: h = (b - r) / d + 2; break;\n\t\t\t\tcase b: h = (r - g) / d + 4;\n\t\t\t}\n\n\t\t\th = h * 60;\n\t\t}\n\n\t\t// Very out of gamut colors can produce negative saturation\n\t\t// If so, just rotate the hue by 180 and use a positive saturation\n\t\t// see https://github.com/w3c/csswg-drafts/issues/9222\n\t\tif (s < 0) {\n\t\t\th += 180;\n\t\t\ts = Math.abs(s);\n\t\t}\n\n\t\tif (h >= 360) {\n\t\t\th -= 360;\n\t\t}\n\n\t\treturn [h, s * 100, l * 100];\n\t},\n\n\t// Adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_RGB_alternative\n\ttoBase: hsl => {\n\t\tlet [h, s, l] = hsl;\n\t\th = h % 360;\n\n\t\tif (h < 0) {\n\t\t\th += 360;\n\t\t}\n\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tfunction f (n) {\n\t\t\tlet k = (n + h / 30) % 12;\n\t\t\tlet a = s * Math.min(l, 1 - l);\n\t\t\treturn l - a * Math.max(-1, Math.min(k - 3, 9 - k, 1));\n\t\t}\n\n\t\treturn [f(0), f(8), f(4)];\n\t},\n\n\tformats: {\n\t\t\"hsl\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t},\n\t\t\"hsla\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSL from \"./hsl.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hsv\",\n\tname: \"HSV\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tv: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Value\",\n\t\t},\n\t},\n\n\tbase: HSL,\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\tfromBase (hsl) {\n\t\tlet [h, s, l] = hsl;\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tlet v = l + s * Math.min(l, 1 - l);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\tv === 0 ? 0 : 200 * (1 - l / v), // s\n\t\t\t100 * v,\n\t\t];\n\t},\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\ttoBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\ts /= 100;\n\t\tv /= 100;\n\n\t\tlet l = v * (1 - s / 2);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\t(l === 0 || l === 1) ? 0 : ((v - l) / Math.min(l, 1 - l)) * 100,\n\t\t\tl * 100,\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSV from \"./hsv.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hwb\",\n\tname: \"HWB\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\tw: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Whiteness\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Blackness\",\n\t\t},\n\t},\n\n\tbase: HSV,\n\tfromBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\treturn [h, v * (100 - s) / 100, 100 - v];\n\t},\n\ttoBase (hwb) {\n\t\tlet [h, w, b] = hwb;\n\n\t\t// Now convert percentages to [0..1]\n\t\tw /= 100;\n\t\tb /= 100;\n\n\t\t// Achromatic check (white plus black >= 1)\n\t\tlet sum = w + b;\n\t\tif (sum >= 1) {\n\t\t\tlet gray = w / sum;\n\t\t\treturn [h, 0, gray * 100];\n\t\t}\n\n\t\tlet v = (1 - b);\n\t\tlet s = (v === 0) ? 0 : 1 - w / v;\n\t\treturn [h, s * 100, v * 100];\n\t},\n\n\tformats: {\n\t\t\"hwb\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light a98-rgb values to CIE XYZ\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// has greater numerical precision than section ******* of\n// https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf\n// but the values below were calculated from first principles\n// from the chromaticity coordinates of R G B W\nconst toXYZ_M = [\n\t[ 0.5766690429101305,   0.1855582379065463,   0.1882286462349947  ],\n\t[ 0.29734497525053605,  0.6273635662554661,   0.07529145849399788 ],\n\t[ 0.02703136138641234,  0.07068885253582723,  0.9913375368376388  ],\n];\n\nconst fromXYZ_M = [\n\t[  2.0415879038107465,    -0.5650069742788596,   -0.34473135077832956 ],\n\t[ -0.9692436362808795,     1.8759675015077202,    0.04155505740717557 ],\n\t[  0.013444280632031142,  -0.11836239223101838,   1.0151749943912054  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb-linear\",\n\tcssId: \"--a98-rgb-linear\",\n\tname: \"Linear Adobe® 98 RGB compatible\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport A98Linear from \"./a98rgb-linear.js\";\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb\",\n\tcssId: \"a98-rgb\",\n\tname: \"Adobe® 98 RGB compatible\",\n\tbase: A98Linear,\n\ttoBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 563 / 256) * Math.sign(val)),\n\tfromBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 256 / 563) * Math.sign(val)),\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport XYZ_D50 from \"./xyz-d50.js\";\n\n// convert an array of  prophoto-rgb values to CIE XYZ\n// using  D50 (so no chromatic adaptation needed afterwards)\n// matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy\n// see https://github.com/w3c/csswg-drafts/issues/7675\nconst toXYZ_M = [\n\t[ 0.79776664490064230,  0.13518129740053308,  0.03134773412839220 ],\n\t[ 0.28807482881940130,  0.71183523424187300,  0.00008993693872564 ],\n\t[ 0.00000000000000000,  0.00000000000000000,  0.82510460251046020 ],\n];\n\nconst fromXYZ_M = [\n\t[  1.34578688164715830, -0.25557208737979464, -0.05110186497554526 ],\n\t[ -0.54463070512490190,  1.50824774284514680,  0.02052744743642139 ],\n\t[  0.00000000000000000,  0.00000000000000000,  1.21196754563894520 ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"prophoto-linear\",\n\tcssId: \"--prophoto-rgb-linear\",\n\tname: \"Linear ProPhoto\",\n\twhite: \"D50\",\n\tbase: XYZ_D50,\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport ProPhotoLinear from \"./prophoto-linear.js\";\n\nconst Et = 1 / 512;\nconst Et2 = 16 / 512;\n\nexport default new RGBColorSpace({\n\tid: \"prophoto\",\n\tcssId: \"prophoto-rgb\",\n\tname: \"ProPhoto\",\n\tbase: ProPhotoLinear,\n\ttoBase (RGB) {\n\t\t// Transfer curve is gamma 1.8 with a small linear portion\n\t\treturn RGB.map(v => v < Et2 ? v / 16 : v ** 1.8);\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(v => v >= Et ? v ** (1 / 1.8) : 16 * v);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport OKLab from \"./oklab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"oklch\",\n\tname: \"Oklch\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 0.4],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\twhite: \"D65\",\n\n\tbase: OKLab,\n\tfromBase (oklab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = oklab;\n\t\tlet h;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\th = NaN;\n\t\t}\n\t\telse {\n\t\t\th = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // OKLab L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(h), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\t// Convert from polar form\n\ttoBase (oklch) {\n\t\tlet [L, C, h] = oklch;\n\t\tlet a, b;\n\n\t\t// check for NaN hue\n\t\tif (isNaN(h)) {\n\t\t\ta = 0;\n\t\t\tb = 0;\n\t\t}\n\t\telse {\n\t\t\ta = C * Math.cos(h * Math.PI / 180);\n\t\t\tb = C * Math.sin(h * Math.PI / 180);\n\t\t}\n\n\t\treturn [ L, a, b ];\n\t},\n\n\tformats: {\n\t\t\"oklch\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[0,1]\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {uv} from \"../chromaticity.js\";\nimport {isNone, skipNone} from \"../util.js\";\n\nlet white = WHITES.D65;\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\nconst [U_PRIME_WHITE, V_PRIME_WHITE] = uv({space: xyz_d65, coords: white});\n\nexport default new ColorSpace({\n\tid: \"luv\",\n\tname: \"Luv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\t// Reference ranges from https://facelessuser.github.io/coloraide/colors/luv/\n\t\tu: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t\tv: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t},\n\n\twhite: white,\n\tbase: xyz_d65,\n\n\t// Convert D65-adapted XYZ to Luv\n\t// https://en.wikipedia.org/wiki/CIELUV#The_forward_transformation\n\tfromBase (XYZ) {\n\t\tlet xyz = [skipNone(XYZ[0]), skipNone(XYZ[1]), skipNone(XYZ[2])];\n\t\tlet y = xyz[1];\n\n\t\tlet [up, vp] = uv({space: xyz_d65, coords: xyz});\n\n\t\t// Protect against XYZ of [0, 0, 0]\n\t\tif (!Number.isFinite(up) || !Number.isFinite(vp)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tlet L = y <= ε ? κ * y : 116 * Math.cbrt(y) - 16;\n\t\treturn [\n\t\t\tL,\n\t\t\t13 * L * (up - U_PRIME_WHITE),\n\t\t\t13 * L * (vp - V_PRIME_WHITE),\n\t\t ];\n\t},\n\n\t// Convert Luv to D65-adapted XYZ\n\t// https://en.wikipedia.org/wiki/CIELUV#The_reverse_transformation\n\ttoBase (Luv) {\n\t\tlet [L, u, v] = Luv;\n\n\t\t// Protect against division by zero and NaN Lightness\n\t\tif (L === 0 || isNone(L)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tu = skipNone(u);\n\t\tv = skipNone(v);\n\n\t\tlet up = (u / (13 * L)) + U_PRIME_WHITE;\n\t\tlet vp = (v / (13 * L)) + V_PRIME_WHITE;\n\n\t\tlet y = L <= 8 ? L / κ : Math.pow((L + 16) / 116, 3);\n\n\t\treturn [\n\t\t\ty * ((9 * up) / (4 * vp)),\n\t\t\ty,\n\t\t\ty * ((12 - 3 * up - 20 * vp) / (4 * vp)),\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--luv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport Luv from \"./luv.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lchuv\",\n\tname: \"<PERSON>Chuv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 220],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Luv,\n\tfromBase (Luv) {\n\t\t// Convert to polar form\n\t\tlet [L, u, v] = Luv;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(u) < ε && Math.abs(v) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(v, u) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(u ** 2 + v ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // u\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // v\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--lchuv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport sRGB from \"./srgb.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOriginAngle (slope, intercept, angle) {\n\tconst d = intercept / (Math.sin(angle) - slope * Math.cos(angle));\n\treturn d < 0 ? Infinity : d;\n}\n\nexport function calculateBoundingLines (l) {\n\tconst sub1 = Math.pow(l + 16, 3) / 1560896;\n\tconst sub2 = sub1 > ε ? sub1 : l / κ;\n\tconst s1r = sub2 * (284517 * m_r0 - 94839 * m_r2);\n\tconst s2r = sub2 * (838422 * m_r2 + 769860 * m_r1 + 731718 * m_r0);\n\tconst s3r = sub2 * (632260 * m_r2 - 126452 * m_r1);\n\tconst s1g = sub2 * (284517 * m_g0 - 94839 * m_g2);\n\tconst s2g = sub2 * (838422 * m_g2 + 769860 * m_g1 + 731718 * m_g0);\n\tconst s3g = sub2 * (632260 * m_g2 - 126452 * m_g1);\n\tconst s1b = sub2 * (284517 * m_b0 - 94839 * m_b2);\n\tconst s2b = sub2 * (838422 * m_b2 + 769860 * m_b1 + 731718 * m_b0);\n\tconst s3b = sub2 * (632260 * m_b2 - 126452 * m_b1);\n\n\treturn {\n\t\tr0s: s1r / s3r,\n\t\tr0i: s2r * l / s3r,\n\t\tr1s: s1r / (s3r + 126452),\n\t\tr1i: (s2r - 769860) * l / (s3r + 126452),\n\t\tg0s: s1g / s3g,\n\t\tg0i: s2g * l / s3g,\n\t\tg1s: s1g / (s3g + 126452),\n\t\tg1i: (s2g - 769860) * l / (s3g + 126452),\n\t\tb0s: s1b / s3b,\n\t\tb0i: s2b * l / s3b,\n\t\tb1s: s1b / (s3b + 126452),\n\t\tb1i: (s2b - 769860) * l / (s3b + 126452),\n\t};\n}\n\nfunction calcMaxChromaHsluv (lines, h) {\n\tconst hueRad = h / 360 * Math.PI * 2;\n\tconst r0 = distanceFromOriginAngle(lines.r0s, lines.r0i, hueRad);\n\tconst r1 = distanceFromOriginAngle(lines.r1s, lines.r1i, hueRad);\n\tconst g0 = distanceFromOriginAngle(lines.g0s, lines.g0i, hueRad);\n\tconst g1 = distanceFromOriginAngle(lines.g1s, lines.g1i, hueRad);\n\tconst b0 = distanceFromOriginAngle(lines.b0s, lines.b0i, hueRad);\n\tconst b1 = distanceFromOriginAngle(lines.b1s, lines.b1i, hueRad);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hsluv\",\n\tname: \"HSLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: sRGB,\n\n\t// Convert LCHuv to HSLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\ts = c / max * 100;\n\t\t}\n\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HSLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\nimport {calculateBoundingLines} from \"./hsluv.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOrigin (slope, intercept) {\n\treturn Math.abs(intercept) / Math.sqrt(Math.pow(slope, 2) + 1);\n}\n\nfunction calcMaxChromaHpluv (lines) {\n\tlet r0 = distanceFromOrigin(lines.r0s, lines.r0i);\n\tlet r1 = distanceFromOrigin(lines.r1s, lines.r1i);\n\tlet g0 = distanceFromOrigin(lines.g0s, lines.g0i);\n\tlet g1 = distanceFromOrigin(lines.g1s, lines.g1i);\n\tlet b0 = distanceFromOrigin(lines.b0s, lines.b0i);\n\tlet b1 = distanceFromOrigin(lines.b1s, lines.b1i);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hpluv\",\n\tname: \"HPLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: \"self\",\n\n\t// Convert LCHuv to HPLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines);\n\t\t\ts = c / max * 100;\n\t\t}\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HPLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hpluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\nconst Yw = 203;\t// absolute luminance of media white, cd/m²\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst m = 2523 / (2 ** 5);\nconst minv = (2 ** 5) / 2523;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\n\nexport default new RGBColorSpace({\n\tid: \"rec2100pq\",\n\tcssId: \"rec2100-pq\",\n\tname: \"REC.2100-PQ\",\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given PQ encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = ((Math.max(((val ** minv) - c1), 0) / (c2 - (c3 * (val ** minv)))) ** ninv);\n\t\t\treturn (x * 10000 / Yw); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// returnPQ encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = Math.max(val * Yw / 10000, 0); \t// absolute luminance of peak white is 10,000 cd/m².\n\t\t\tlet num = (c1 + (c2 * (x ** n)));\n\t\t\tlet denom = (1 + (c3 * (x ** n)));\n\n\t\t\treturn ((num / denom)  ** m);\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\n// FIXME see https://github.com/LeaVerou/color.js/issues/190\n\nconst a = 0.17883277;\nconst b = 0.28466892; // 1 - (4 * a)\nconst c = 0.55991073; // 0.5 - a * Math.log(4 *a)\n\nconst scale = 3.7743;\t// Place 18% grey at HLG 0.38, so media white at 0.75\n\nexport default new RGBColorSpace({\n\tid: \"rec2100hlg\",\n\tcssId: \"rec2100-hlg\",\n\tname: \"REC.2100-HLG\",\n\treferred: \"scene\",\n\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given HLG encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\t// first the HLG EOTF\n\t\t\t// ITU-R BT.2390-10 p.30 section\n\t\t\t// 6.3 The hybrid log-gamma electro-optical transfer function (EOTF)\n\t\t\t// Then scale by 3 so media white is 1.0\n\t\t\tif (val <= 0.5) {\n\t\t\t\treturn (val ** 2) / 3 * scale;\n\t\t\t}\n\t\t\treturn ((Math.exp((val - c) / a) + b) / 12) * scale;\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// where diffuse white is 1.0,\n\t\t// return HLG encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\t// first scale to put linear-light media white at 1/3\n\t\t\tval /= scale;\n\t\t\t// now the HLG OETF\n\t\t\t// ITU-R BT.2390-10 p.23\n\t\t\t// 6.1 The hybrid log-gamma opto-electronic transfer function (OETF)\n\t\t\tif (val <= 1 / 12) {\n\t\t\t\treturn Math.sqrt(3 * val);\n\t\t\t}\n\t\t\treturn a * Math.log(12 * val - b) + c;\n\t\t});\n\t},\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport {WHITES} from \"./adapt.js\";\n\nexport const CATs = {};\n\nhooks.add(\"chromatic-adaptation-start\", env => {\n\tif (env.options.method) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nhooks.add(\"chromatic-adaptation-end\", env => {\n\tif (!env.M) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nexport function defineCAT ({id, toCone_M, fromCone_M}) {\n\t// Use id, toCone_M, fromCone_M like variables\n\tCATs[id] = arguments[0];\n}\n\nexport function adapt (W1, W2, id = \"Bradford\") {\n\t// adapt from a source whitepoint or illuminant W1\n\t// to a destination whitepoint or illuminant W2,\n\t// using the given chromatic adaptation transform (CAT)\n\t// debugger;\n\tlet method = CATs[id];\n\n\tlet [ρs, γs, βs] = multiplyMatrices(method.toCone_M, W1);\n\tlet [ρd, γd, βd] = multiplyMatrices(method.toCone_M, W2);\n\n\t// all practical illuminants have non-zero XYZ so no division by zero can occur below\n\tlet scale = [\n\t\t[ρd / ρs,  0,        0      ],\n\t\t[0,        γd / γs,  0      ],\n\t\t[0,        0,        βd / βs],\n\t];\n\t// console.log({scale});\n\n\tlet scaled_cone_M = multiplyMatrices(scale, method.toCone_M);\n\tlet adapt_M\t= multiplyMatrices(method.fromCone_M, scaled_cone_M);\n\t// console.log({scaled_cone_M, adapt_M});\n\treturn adapt_M;\n}\n\ndefineCAT({\n\tid: \"von Kries\",\n\ttoCone_M: [\n\t\t[  0.4002400,  0.7076000, -0.0808100 ],\n\t\t[ -0.2263000,  1.1653200,  0.0457000 ],\n\t\t[  0.0000000,  0.0000000,  0.9182200 ],\n\t],\n\tfromCone_M: [\n\t\t[ 1.8599363874558397, -1.1293816185800916,   0.21989740959619328     ],\n\t\t[ 0.3611914362417676,  0.6388124632850422,  -0.000006370596838649899 ],\n\t\t[ 0,                   0,                    1.0890636230968613      ],\n\t],\n});\n\ndefineCAT({\n\tid: \"Bradford\",\n\t// Convert an array of XYZ values in the range 0.0 - 1.0\n\t// to cone fundamentals\n\ttoCone_M: [\n\t\t[  0.8951000,  0.2664000, -0.1614000 ],\n\t\t[ -0.7502000,  1.7135000,  0.0367000 ],\n\t\t[  0.0389000, -0.0685000,  1.0296000 ],\n\t],\n\t// and back\n\tfromCone_M: [\n\t\t[  0.9869929054667121, -0.14705425642099013, 0.15996265166373122  ],\n\t\t[  0.4323052697233945,  0.5183602715367774,  0.049291228212855594 ],\n\t\t[ -0.00852866457517732, 0.04004282165408486, 0.96848669578755     ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT02\",\n\t// with complete chromatic adaptation to W2, so D = 1.0\n\ttoCone_M: [\n\t\t[  0.7328000,  0.4296000, -0.1624000 ],\n\t\t[ -0.7036000,  1.6975000,  0.0061000 ],\n\t\t[  0.0030000,  0.0136000,  0.9834000 ],\n\t],\n\tfromCone_M: [\n\t\t[  1.0961238208355142,   -0.27886900021828726, 0.18274517938277307 ],\n\t\t[  0.4543690419753592,    0.4735331543074117,  0.07209780371722911 ],\n\t\t[ -0.009627608738429355, -0.00569803121611342, 1.0153256399545427  ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT16\",\n\ttoCone_M: [\n\t\t[  0.401288,  0.650173, -0.051461 ],\n\t\t[ -0.250268,  1.204414,  0.045854 ],\n\t\t[ -0.002079,  0.048952,  0.953127 ],\n\t],\n\t// the extra precision is needed to avoid roundtripping errors\n\tfromCone_M: [\n\t\t[  1.862067855087233,   -1.0112546305316845,  0.14918677544445172  ],\n\t\t[  0.3875265432361372,   0.6214474419314753, -0.008973985167612521 ],\n\t\t[ -0.01584149884933386, -0.03412293802851557, 1.0499644368778496   ],\n\t],\n});\n\nObject.assign(WHITES, {\n\t// whitepoint values from ASTM E308-01 with 10nm spacing, 1931 2 degree observer\n\t// all normalized to Y (luminance) = 1.00000\n\t// Illuminant A is a tungsten electric light, giving a very warm, orange light.\n\tA:   [1.09850, 1.00000, 0.35585],\n\n\t// Illuminant C was an early approximation to daylight: illuminant A with a blue filter.\n\tC:   [0.98074, 1.000000, 1.18232],\n\n\t// The daylight series of illuminants simulate natural daylight.\n\t// The color temperature (in degrees Kelvin/100) ranges from\n\t// cool, overcast daylight (D50) to bright, direct sunlight (D65).\n\tD55: [0.95682, 1.00000, 0.92149],\n\tD75: [0.94972, 1.00000, 1.22638],\n\n\t// Equal-energy illuminant, used in two-stage CAT16\n\tE:   [1.00000, 1.00000, 1.00000],\n\n\t// The F series of illuminants represent fluorescent lights\n\tF2:  [0.99186, 1.00000, 0.67393],\n\tF7:  [0.95041, 1.00000, 1.08747],\n\tF11: [1.00962, 1.00000, 0.64350],\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport {WHITES} from \"../adapt.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\n\n// The ACES whitepoint\n// see TB-2018-001 Derivation of the ACES White Point CIE Chromaticity Coordinates\n// also https://github.com/ampas/aces-dev/blob/master/documents/python/TB-2018-001/aces_wp.py\n// Similar to D60\nWHITES.ACES = [0.32168 / 0.33767, 1.00000, (1.00000 - 0.32168 - 0.33767) / 0.33767];\n\n// convert an array of linear-light ACEScc values to CIE XYZ\nconst toXYZ_M = [\n\t[  0.6624541811085053,   0.13400420645643313,  0.1561876870049078  ],\n\t[  0.27222871678091454,  0.6740817658111484,   0.05368951740793705 ],\n\t[ -0.005574649490394108, 0.004060733528982826, 1.0103391003129971  ],\n];\nconst fromXYZ_M = [\n\t[  1.6410233796943257,   -0.32480329418479,    -0.23642469523761225  ],\n\t[ -0.6636628587229829,    1.6153315916573379,   0.016756347685530137 ],\n\t[  0.011721894328375376, -0.008284441996237409, 0.9883948585390215   ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"acescg\",\n\tcssId: \"--acescg\",\n\tname: \"ACEScg\",\n\n\t// ACEScg – A scene-referred, linear-light encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescg/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\tcoords: {\n\t\tr: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\n\treferred: \"scene\",\n\n\twhite: WHITES.ACES,\n\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n\n// export default Color;\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\nimport ACEScg from \"./acescg.js\";\n\nconst ε = 2 ** -16;\n\n// the smallest value which, in the 32bit IEEE 754 float encoding,\n// decodes as a non-negative value\nconst ACES_min_nonzero = -0.35828683;\n\n// brightest encoded value, decodes to 65504\nconst ACES_cc_max = (Math.log2(65504) + 9.72) / 17.52; // 1.468\n\nexport default new RGBColorSpace({\n\tid: \"acescc\",\n\tcssId: \"--acescc\",\n\tname: \"ACEScc\",\n\t// see S-2014-003 ACEScc – A Logarithmic Encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescc/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\n\t// Appendix A: \"Very small ACES scene referred values below 7 1/4 stops\n\t// below 18% middle gray are encoded as negative ACEScc values.\n\t// These values should be preserved per the encoding in Section 4.4\n\t// so that all positive ACES values are maintained.\"\n\tcoords: {\n\t\tr: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\treferred: \"scene\",\n\n\tbase: ACEScg,\n\t// from section 4.4.2 Decoding Function\n\ttoBase (RGB) {\n\t\tconst low = (9.72 - 15) / 17.52; // -0.3014\n\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= low) {\n\t\t\t\treturn (2 ** ((val * 17.52) - 9.72) - ε) * 2; // very low values, below -0.3014\n\t\t\t}\n\t\t\telse if (val < ACES_cc_max) {\n\t\t\t\treturn 2 ** ((val * 17.52) - 9.72);\n\t\t\t}\n\t\t\telse { // val >= ACES_cc_max\n\t\t\t\treturn 65504;\n\t\t\t}\n\t\t});\n\t},\n\n\t// Non-linear encoding function from S-2014-003, section 4.4.1 Encoding Function\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= 0) {\n\t\t\t\treturn (Math.log2(ε) + 9.72) / 17.52; // -0.3584\n\t\t\t}\n\t\t\telse if (val < ε) {\n\t\t\t\treturn  (Math.log2(ε + val * 0.5) + 9.72) / 17.52;\n\t\t\t}\n\t\t\telse { // val >= ε\n\t\t\t\treturn  (Math.log2(val) + 9.72) / 17.52;\n\t\t\t}\n\t\t});\n\t},\n\t// encoded media white (rgb 1,1,1) => linear  [ 222.861, 222.861, 222.861 ]\n\t// encoded media black (rgb 0,0,0) => linear [ 0.0011857, 0.0011857, 0.0011857]\n});\n", "import getColor from \"./getColor.js\";\n// import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport * as contrastAlgorithms from \"./contrast/index.js\";\n\nexport default function contrast (background, foreground, o = {}) {\n\tif (isString(o)) {\n\t\to = {algorithm: o};\n\t}\n\n\tlet {algorithm, ...rest} = o;\n\n\tif (!algorithm) {\n\t\tlet algorithms = Object.keys(contrastAlgorithms).map(a => a.replace(/^contrast/, \"\")).join(\", \");\n\t\tthrow new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${algorithms}`);\n\t}\n\n\tbackground = getColor(background);\n\tforeground = getColor(foreground);\n\n\tfor (let a in contrastAlgorithms) {\n\t\tif (\"contrast\" + algorithm.toLowerCase() === a.toLowerCase()) {\n\t\t\treturn contrastAlgorithms[a](background, foreground, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown contrast algorithm: ${algorithm}`);\n}\n", "import ColorSpace from \"./space.js\";\nimport set from \"./set.js\";\n\nexport function lighten (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 + amount));\n}\n\nexport function darken (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 - amount));\n}\n", "import getColor from \"./getColor.js\";\n\nexport default function equals (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\treturn color1.space === color2.space\n\t       && color1.alpha === color2.alpha\n\t       && color1.coords.every((c, i) => c === color2.coords[i]);\n}\n"], "names": ["check", "it", "Math", "global", "globalThis", "window", "self", "this", "Function", "fails", "exec", "error", "require$$0", "descriptors", "Object", "defineProperty", "get", "functionBindNative", "test", "bind", "hasOwnProperty", "NATIVE_BIND", "call", "prototype", "functionCall", "apply", "arguments", "createPropertyDescriptor", "bitmap", "value", "enumerable", "configurable", "writable", "FunctionPrototype", "uncurryThisWithBind", "functionUncurryThis", "fn", "uncurryThis", "toString", "stringSlice", "slice", "classofRaw", "isNullOrUndefined", "$TypeError", "TypeError", "requireObjectCoercible", "IndexedObject", "require$$1", "classof", "require$$2", "$Object", "split", "indexedObject", "propertyIsEnumerable", "toIndexedObject", "documentAll", "document", "all", "isCallable", "undefined", "argument", "isObject", "getBuiltIn", "namespace", "method", "length", "objectIsPrototypeOf", "isPrototypeOf", "match", "version", "userAgent", "engineUserAgent", "navigator", "String", "process", "<PERSON><PERSON>", "versions", "v8", "engineV8Version", "V8_VERSION", "$String", "symbolConstructorDetection", "getOwnPropertySymbols", "symbol", "Symbol", "sham", "NATIVE_SYMBOL", "useSymbolAsUid", "iterator", "USE_SYMBOL_AS_UID", "require$$3", "isSymbol", "$Symbol", "tryToString", "aCallable", "getMethod", "V", "P", "func", "ordinaryToPrimitive", "input", "pref", "val", "valueOf", "isPure", "defineGlobalProperty", "key", "IS_PURE", "SHARED", "store", "sharedStoreModule", "exports", "push", "mode", "copyright", "license", "source", "shared", "toObject", "hasOwnProperty_1", "hasOwn", "id", "postfix", "random", "uid", "require$$4", "require$$5", "WellKnownSymbolsStore", "createWellKnownSymbol", "withoutSetter", "wellKnownSymbol", "name", "TO_PRIMITIVE", "toPrimitive", "result", "exoticToPrim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTORS", "createElement", "EXISTS", "documentCreateElement", "ie8DomDefine", "a", "propertyIsEnumerableModule", "$propertyIsEnumerable", "getOwnPropertyDescriptor", "NASHORN_BUG", "objectPropertyIsEnumerable", "f", "descriptor", "require$$6", "IE8_DOM_DEFINE", "require$$7", "$getOwnPropertyDescriptor", "objectGetOwnPropertyDescriptor", "O", "anObject", "V8_PROTOTYPE_DEFINE_BUG", "v8PrototypeDefineBug", "$defineProperty", "ENUMERABLE", "CONFIGURABLE", "WRITABLE", "objectDefineProperty", "Attributes", "current", "definePropertyModule", "createNonEnumerableProperty", "object", "functionToString", "inspectSource", "keys", "sharedKey", "hiddenKeys", "set", "has", "NATIVE_WEAK_MAP", "WeakMap", "weakMapBasicDetection", "OBJECT_ALREADY_INITIALIZED", "state", "metadata", "facade", "STATE", "internalState", "enforce", "getter<PERSON>or", "TYPE", "type", "CONFIGURABLE_FUNCTION_NAME", "getDescriptor", "PROPER", "functionName", "InternalStateModule", "enforceInternalState", "getInternalState", "replace", "join", "CONFIGURABLE_LENGTH", "TEMPLATE", "makeBuiltIn", "makeBuiltInModule", "options", "getter", "setter", "arity", "constructor", "defineBuiltIn", "simple", "unsafe", "nonConfigurable", "nonWritable", "trunc", "ceil", "floor", "math<PERSON>runc", "x", "n", "toIntegerOrInfinity", "number", "max", "min", "toAbsoluteIndex", "index", "integer", "to<PERSON><PERSON><PERSON>", "len", "lengthOfArrayLike", "obj", "indexOf", "createMethod", "IS_INCLUDES", "$this", "el", "fromIndex", "arrayIncludes", "includes", "objectKeysInternal", "names", "i", "getOwnPropertyNamesModule", "internalObjectKeys", "enumBugKeys", "concat", "objectGetOwnPropertyNames", "getOwnPropertyNames", "getOwnPropertySymbolsModule", "objectGetOwnPropertySymbols", "ownKeys", "getOwnPropertyDescriptorModule", "copyConstructorProperties", "target", "exceptions", "isForced", "replacement", "feature", "detection", "data", "normalize", "POLYFILL", "NATIVE", "string", "toLowerCase", "isForced_1", "_export", "targetProperty", "sourceProperty", "TARGET", "GLOBAL", "STATIC", "stat", "dontCallGetSet", "forced", "isPossiblePrototype", "aPossiblePrototype", "uncurry<PERSON><PERSON><PERSON><PERSON><PERSON>or", "functionUncurryThisAccessor", "objectSetPrototypeOf", "setPrototypeOf", "CORRECT_SETTER", "Array", "proto", "__proto__", "proxyAccessor", "Target", "Source", "inheritIfRequired", "dummy", "Wrapper", "<PERSON><PERSON><PERSON><PERSON>", "NewTargetPrototype", "TO_STRING_TAG_SUPPORT", "toStringTagSupport", "TO_STRING_TAG", "CORRECT_ARGUMENTS", "tag", "tryGet", "callee", "normalizeStringArgument", "$default", "installErrorCause", "cause", "clearErrorStack", "$Error", "Error", "TEST", "stack", "V8_OR_CHAKRA_STACK_ENTRY", "IS_V8_OR_CHAKRA_STACK", "errorStackClear", "dropEntries", "prepareStackTrace", "ERROR_STACK_INSTALLABLE", "errorStackInstallable", "captureStackTrace", "errorStackInstall", "C", "require$$8", "require$$9", "installErrorStack", "require$$10", "require$$11", "require$$12", "wrapErrorConstructorWithCause", "FULL_NAME", "wrapper", "FORCED", "IS_AGGREGATE_ERROR", "STACK_TRACE_LIMIT", "OPTIONS_POSITION", "path", "ERROR_NAME", "OriginalError", "OriginalErrorPrototype", "BaseError", "WrappedError", "b", "message", "$", "functionApply", "Reflect", "WEB_ASSEMBLY", "WebAssembly", "exportGlobalErrorCauseWrapper", "exportWebAssemblyErrorCauseWrapper", "init", "isArray", "SILENT_ON_NON_WRITABLE_LENGTH_SET", "arraySetLength", "doesNotExceedSafeInteger", "multiplyMatrices", "A", "B", "m", "map", "p", "B_cols", "_", "product", "row", "col", "ret", "c", "isString", "str", "o", "serializeNumber", "_ref", "precision", "unit", "isNone", "digits", "log10", "abs", "multiplier", "toPrecision", "Number", "isNaN", "none", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properErrorOnNonWritableLength", "item", "argCount", "angleFactor", "deg", "grad", "rad", "PI", "turn", "interpolate", "start", "end", "mapRange", "from", "to", "interpolateInv", "copySign", "sign", "spow", "base", "exp", "zdiv", "d", "<PERSON>s", "add", "callback", "first", "for<PERSON>ach", "run", "env", "context", "hooks", "WHITES", "D50", "D65", "<PERSON><PERSON><PERSON><PERSON>", "adapt", "W1", "W2", "XYZ", "M", "defaults", "gamut_mapping", "deltaE", "verbose", "_globalThis$process", "NODE_ENV", "warn", "msg", "_globalThis$console", "_globalThis$console$w", "console", "noneTypes", "Set", "coerceCoords", "space", "format", "coords", "types", "entries", "coordMeta", "coordGrammar", "arg", "providedType", "find", "coordName", "raw", "fromRange", "range", "to<PERSON><PERSON><PERSON>", "refRange", "util", "parse", "_String", "meta", "trim", "color", "parsed", "isNumberRegex", "unitValueRegex", "singleArgument", "parts", "args", "$0", "rawArg", "unitlessArg", "NaN", "startsWith", "alpha", "rawName", "rawArgs", "shift", "alternateId", "substring", "ids", "pop", "ColorSpace", "colorSpec", "getFormat", "_colorSpec$ids", "filter", "specId", "assign", "formatId", "spaceId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registryId", "registry", "_ColorSpace$registry$", "cssId", "formats", "lastAlpha", "arr", "_color$alpha", "getColor", "ε", "_options$coords", "_options$white", "_options$formats", "_this$formats$color", "aliases", "fromBase", "toBase", "white", "_this$formats$color2", "gamutSpace", "isPolar", "isUnbounded", "inGamut", "referred", "<PERSON><PERSON><PERSON>", "reverse", "epsilon", "equals", "values", "every", "coord", "_this$formats", "processFormat", "connectionSpace", "connectionSpaceIndex", "myPath", "otherPath", "getMinCoords", "_range$min", "static", "register", "alias", "_len", "alternatives", "_key", "resolveCoord", "ref", "workingSpace", "coordType", "coordId", "normalizedCoord", "_meta$name", "s", "coordFormats", "_ref2", "outputType", "suffix", "serializeCoords", "xyz_d65", "y", "z", "RGBColorSpace", "_options$referred", "_options$toBase", "_options$fromBase", "r", "g", "XYZ_D65", "toXYZ_M", "fromXYZ_M", "rgb", "xyz", "super", "getAll", "prop", "setAll", "returns", "XYZ_D50", "ε3", "κ", "lab", "l", "xyz_d50", "cbrt", "Lab", "pow", "constrain", "angle", "lch", "h", "hue", "L", "atan2", "sqrt", "constrainAngle", "LCH", "Lightness", "Chroma", "<PERSON><PERSON>", "cos", "sin", "Gfactor", "π", "r2d", "d2r", "pow7", "x2", "deltaE2000", "sample", "kL", "kC", "kH", "L1", "a1", "b1", "C1", "L2", "a2", "b2", "C2", "C7", "G", "adash1", "adash2", "Cdash1", "Cdash2", "h1", "h2", "Δh", "ΔL", "ΔC", "hdiff", "hsum", "habs", "hdash", "ΔH", "Ldash", "Cdash", "Cdash7", "lsq", "SL", "SC", "T", "SH", "Δθ", "RC", "dE", "XYZtoLMS_M", "LMStoXYZ_M", "LMStoLab_M", "LabtoLMS_M", "OKLab", "LMSg", "LMS", "oklab", "deltaEOK", "Δa", "Δb", "clone", "distance", "color1", "color2", "coords1", "coords2", "reduce", "acc", "c1", "c2", "deltaE76", "deltaECMC", "H1", "H2", "C4", "F", "XYZ_Abs_D65", "v", "AbsXYZ", "c3", "pinv", "d0", "XYZtoCone_M", "ConetoXYZ_M", "ConetoIab_M", "IabtoCone_M", "Jzazbz", "jz", "az", "bz", "Xa", "Ya", "<PERSON>a", "PQLMS", "Iz", "Jz", "Xm", "Ym", "jzczhz", "cz", "hz", "jzazbz", "deltaEJz", "Jz1", "Cz1", "Hz1", "Jz2", "Cz2", "Hz2", "ΔJ", "m1", "m2", "im1", "im2", "LMStoIPT_M", "IPTtoLMS_M", "ictcp", "ct", "cp", "LMStoICtCp", "ICtCp", "ICtCptoLMS", "deltaEITP", "I1", "T1", "P1", "I2", "T2", "P2", "<PERSON><PERSON><PERSON><PERSON>", "adaptedCoefInv", "tau", "cat16", "cat16Inv", "surroundMap", "dark", "dim", "average", "hueQuadMap", "e", "H", "rad2deg", "deg2rad", "fl", "temp", "environment", "refWhite", "adaptingLuminance", "backgroundLuminance", "surround", "discounting", "xyzW", "la", "yb", "yw", "rgbW", "nc", "k4", "flRoot", "nbb", "ncb", "dRgb", "dRgbInv", "rgbCW", "rgbAW", "aW", "viewingConditions", "fromCam16", "cam16", "J", "Q", "hRad", "Hp", "hi", "hii", "ei", "eii", "invHueQuadrature", "cosh", "sinh", "<PERSON><PERSON>", "t", "et", "p1", "p2", "rgb_c", "adapted", "constant", "cabs", "unadapt", "toCam16", "xyzd65", "xyz100", "rgbA", "hp", "lo", "mid", "bisectLeft", "hueQuadrature", "j", "fromLstar", "lstar", "toHct", "hct", "attempt", "last", "Infinity", "delta", "fromHct", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "convertUcsAb", "log", "hrad", "deltaEHCT", "t1", "t2", "deltaEMethods", "GMAPPRESET", "jnd", "deltaEMethod", "blackWhiteClamp", "channel", "toGamut", "spaceColor", "toGamutCSS", "de", "clipped", "channelMeta", "mapSpace", "mappedColor", "order", "parseFloat", "calcEpsilon", "low", "high", "bounds", "COLORS", "WHITE", "BLACK", "origin", "JND", "oklchSpace", "origin_OKLCH", "black", "clip", "_color", "destColor", "spaceCoords", "min_inGamut", "E", "chroma", "deletePropertyOrThrow", "serialize", "_color$space$getForma", "customOptions", "DEFAULT_FORMAT", "checkInGamut", "_format$ids", "unshift", "strAlpha", "noAlpha", "commas", "k", "REC2020Linear", "α", "β", "REC2020", "RGB", "P3Linear", "sRGBLinear", "KEYWORDS", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "whitesmoke", "yellow", "yellowgreen", "fill", "coordGrammarNumber", "sRGB", "rgb_number", "rgba", "rgba_number", "hex", "component", "parseInt", "collapse", "round", "collapsible", "padStart", "keyword", "P3", "supportsNone", "display_space", "CSS", "supports", "getLuminance", "contrastWCAG21", "Y1", "Y2", "blkThrs", "blkClmp", "fclamp", "Y", "linearize", "contrastAPCA", "background", "foreground", "S", "Sapc", "R", "lumTxt", "lumBg", "Ytxt", "Ybg", "BoW", "<PERSON><PERSON><PERSON><PERSON>", "denom", "<PERSON><PERSON><PERSON><PERSON>", "contrastLstar", "lab_d65", "phi", "contrastDeltaPhi", "Lstr1", "Lstr2", "deltaPhiStar", "contrast", "SQRT2", "uv", "X", "Z", "rest", "isRange", "rangeArgs", "colors", "outputSpace", "progression", "premultiplied", "interpolationSpace", "arc", "θ1", "θ2", "angles", "angleDiff", "HSL", "hsl", "hsla", "HSV", "hsv", "hwb", "w", "sum", "A98Linear", "a98rgb", "ProPhotoLinear", "prophoto", "oklch", "U_PRIME_WHITE", "V_PRIME_WHITE", "<PERSON><PERSON>", "u", "up", "vp", "isFinite", "LCHuv", "m_r0", "m_r1", "m_r2", "m_g0", "m_g1", "m_g2", "m_b0", "m_b1", "m_b2", "distanceFromOriginAngle", "slope", "intercept", "calculateBoundingLines", "sub1", "sub2", "s1r", "s2r", "s3r", "s1g", "s2g", "s3g", "s1b", "s2b", "s3b", "r0s", "r0i", "r1s", "r1i", "g0s", "g0i", "g1s", "g1i", "b0s", "b0i", "b1s", "b1i", "calcMaxChromaHsluv", "lines", "hueRad", "r0", "r1", "g0", "g1", "b0", "hsluv", "distanceFromOrigin", "calcMaxChromaHpluv", "hpluv", "minv", "rec2100Pq", "scale", "rec2100Hlg", "CATs", "defineCAT", "toCone_M", "fromCone_M", "ρs", "γs", "βs", "ρd", "γd", "βd", "scaled_cone_M", "D55", "D75", "F2", "F7", "F11", "ACES", "ACEScg", "ACES_min_nonzero", "ACES_cc_max", "log2", "acescc", "algorithm", "algorithms", "contrastAlgorithms", "amount", "fallbackColor", "_supportsNone", "some", "colorRange", "maxDeltaE", "steps", "maxSteps", "rangeOptions", "totalDelta", "actualSteps", "step", "max<PERSON><PERSON><PERSON>", "cur", "ΔΕ", "prev", "splice"], "mappings": "0MACA,IAAIA,EAAQ,SAAUC,GACpB,OAAOA,GAAMA,EAAGC,OAASA,MAAQD,CACnC,SAGcE,EAEZH,EAA2B,iBAAdI,YAA0BA,aACvCJ,EAAuB,iBAAVK,QAAsBA,SAEnCL,EAAqB,iBAARM,MAAoBA,OACjCN,EAAuB,iBAAVG,GAAsBA,IACnCH,EAAqB,iBAARO,GAAoBA,IAEjC,WAAe,OAAOA,IAAO,CAA7B,IAAoCC,SAAS,cAATA,yDCdxBC,EAAG,SAAUC,GACzB,IACE,QAASA,GACV,CAAC,MAAOC,GACP,OAAO,CACR,mCCLH,IAAIF,EAAQG,WAGZC,GAAkBJ,GAAM,WAEtB,OAA+E,IAAxEK,OAAOC,eAAe,GAAI,EAAG,CAAEC,IAAK,WAAc,OAAO,CAAI,IAAI,EAC1E,mCCNA,IAAIP,EAAQG,WAEZK,GAAkBR,GAAM,WAEtB,IAAIS,EAAO,WAA4B,EAAEC,OAEzC,MAAsB,mBAARD,GAAsBA,EAAKE,eAAe,YAC1D,mCCPA,IAAIC,EAAcT,IAEdU,EAAOd,SAASe,UAAUD,YAEhBE,EAAGH,EAAcC,EAAKH,KAAKG,GAAQ,WAC/C,OAAOA,EAAKG,MAAMH,EAAMI,6ICL1BC,EAAiB,SAAUC,EAAQC,GACjC,MAAO,CACLC,aAAuB,EAATF,GACdG,eAAyB,EAATH,GAChBI,WAAqB,EAATJ,GACZC,MAAOA,qCCLX,IAAIR,EAAcT,IAEdqB,EAAoBzB,SAASe,UAC7BD,EAAOW,EAAkBX,KACzBY,EAAsBb,GAAeY,EAAkBd,KAAKA,KAAKG,EAAMA,UAE3Ea,EAAiBd,EAAca,EAAsB,SAAUE,GAC7D,OAAO,WACL,OAAOd,EAAKG,MAAMW,EAAIV,UAC1B,qCCTA,IAAIW,EAAczB,KAEd0B,EAAWD,EAAY,CAAE,EAACC,UAC1BC,EAAcF,EAAY,GAAGG,cAEnBC,EAAG,SAAUxC,GACzB,OAAOsC,EAAYD,EAASrC,GAAK,GAAI,kCCJzByC,EAAG,SAAUzC,GACzB,OAAOA,0CCHT,IAAIyC,EAAoB9B,KAEpB+B,EAAaC,iBAIHC,EAAG,SAAU5C,GACzB,GAAIyC,EAAkBzC,GAAK,MAAM,IAAI0C,EAAW,wBAA0B1C,GAC1E,OAAOA,mCCPT,IAAI6C,+BCDJ,IAAIT,EAAczB,KACdH,EAAQsC,IACRC,EAAUC,KAEVC,EAAUpC,OACVqC,EAAQd,EAAY,GAAGc,cAGbC,EAAG3C,GAAM,WAGrB,OAAQyC,EAAQ,KAAKG,qBAAqB,EAC5C,IAAK,SAAUpD,GACb,MAAuB,WAAhB+C,EAAQ/C,GAAmBkD,EAAMlD,EAAI,IAAMiD,EAAQjD,EAC3D,EAAGiD,EDbgBtC,GAChBiC,EAAyBE,YAEfO,EAAG,SAAUrD,GACzB,OAAO6C,EAAcD,EAAuB5C,qCEJ9C,IAAIsD,EAAiC,iBAAZC,UAAwBA,SAASC,WAK1DC,OAAuC,IAAfH,QAA8CI,IAAhBJ,EAA4B,SAAUK,GAC1F,MAA0B,mBAAZA,GAA0BA,IAAaL,CACtD,EAAG,SAAUK,GACZ,MAA0B,mBAAZA,mCCThB,IAAIF,EAAa9C,YAEHiD,EAAG,SAAU5D,GACzB,MAAoB,iBAANA,EAAwB,OAAPA,EAAcyD,EAAWzD,oCCH1D,IAAIE,EAASS,IACT8C,EAAaX,YAMjBe,EAAiB,SAAUC,EAAWC,GACpC,OAAOtC,UAAUuC,OAAS,GALFL,EAKgBzD,EAAO4D,GAJxCL,EAAWE,GAAYA,OAAWD,GAIoBxD,EAAO4D,IAAc5D,EAAO4D,GAAWC,GALtF,IAAUJ,qCCH1B,IAAIvB,EAAczB,YAElBsD,EAAiB7B,EAAY,GAAG8B,+CCFhC,IAOIC,EAAOC,EAPPlE,EAASS,IACT0D,WCDJC,EAAqC,oBAAbC,WAA4BC,OAAOD,UAAUF,YAAc,IDG/EI,EAAUvE,EAAOuE,QACjBC,EAAOxE,EAAOwE,KACdC,EAAWF,GAAWA,EAAQE,UAAYD,GAAQA,EAAKN,QACvDQ,EAAKD,GAAYA,EAASC,UAG1BA,IAIFR,GAHAD,EAAQS,EAAG1B,MAAM,MAGD,GAAK,GAAKiB,EAAM,GAAK,EAAI,IAAMA,EAAM,GAAKA,EAAM,MAK7DC,GAAWC,MACdF,EAAQE,EAAUF,MAAM,iBACVA,EAAM,IAAM,MACxBA,EAAQE,EAAUF,MAAM,oBACbC,GAAWD,EAAM,IAIhCU,EAAiBT,kCEzBjB,IAAIU,EAAanE,KACbH,EAAQsC,IAGRiC,EAFS/B,IAEQwB,cAGPQ,IAAKnE,OAAOoE,wBAA0BzE,GAAM,WACxD,IAAI0E,EAASC,OAAO,oBAKpB,OAAQJ,EAAQG,MAAarE,OAAOqE,aAAmBC,UAEpDA,OAAOC,MAAQN,GAAcA,EAAa,EAC/C,oCChBA,IAAIO,EAAgB1E,YAEpB2E,EAAiBD,IACXF,OAAOC,MACkB,iBAAnBD,OAAOI,yCCLnB,IAAI1B,EAAalD,KACb8C,EAAaX,KACboB,EAAgBlB,KAChBwC,EAAoBC,KAEpBxC,EAAUpC,cAEd6E,EAAiBF,EAAoB,SAAUxF,GAC7C,MAAoB,iBAANA,CACf,EAAG,SAAUA,GACZ,IAAI2F,EAAU9B,EAAW,UACzB,OAAOJ,EAAWkC,IAAYzB,EAAcyB,EAAQrE,UAAW2B,EAAQjD,qCCXzE,IAAI+E,EAAUP,cAEAoB,EAAG,SAAUjC,GACzB,IACE,OAAOoB,EAAQpB,EAChB,CAAC,MAAOjD,GACP,MAAO,QACR,qCCPH,IAAI+C,EAAa9C,KACbiF,EAAc9C,KAEdJ,EAAaC,iBAGHkD,EAAG,SAAUlC,GACzB,GAAIF,EAAWE,GAAW,OAAOA,EACjC,MAAM,IAAIjB,EAAWkD,EAAYjC,GAAY,0DCR/C,IAAIkC,EAAYlF,KACZ8B,EAAoBK,YAIxBgD,GAAiB,SAAUC,EAAGC,GAC5B,IAAIC,EAAOF,EAAEC,GACb,OAAOvD,EAAkBwD,QAAQvC,EAAYmC,EAAUI,uCCPzD,IAAI5E,EAAOV,IACP8C,EAAaX,KACbc,EAAWZ,KAEXN,EAAaC,iBAIjBuD,GAAiB,SAAUC,EAAOC,GAChC,IAAIjE,EAAIkE,EACR,GAAa,WAATD,GAAqB3C,EAAWtB,EAAKgE,EAAM9D,YAAcuB,EAASyC,EAAMhF,EAAKc,EAAIgE,IAAS,OAAOE,EACrG,GAAI5C,EAAWtB,EAAKgE,EAAMG,WAAa1C,EAASyC,EAAMhF,EAAKc,EAAIgE,IAAS,OAAOE,EAC/E,GAAa,WAATD,GAAqB3C,EAAWtB,EAAKgE,EAAM9D,YAAcuB,EAASyC,EAAMhF,EAAKc,EAAIgE,IAAS,OAAOE,EACrG,MAAM,IAAI3D,EAAW,yKCbvB6D,IAAiB,sCCAjB,IAAIrG,EAASS,IAGTG,EAAiBD,OAAOC,sBAE5B0F,GAAiB,SAAUC,EAAK7E,GAC9B,IACEd,EAAeZ,EAAQuG,EAAK,CAAE7E,MAAOA,EAAOE,cAAc,EAAMC,UAAU,GAC3E,CAAC,MAAOrB,GACPR,EAAOuG,GAAO7E,CACf,CAAC,OAAOA,8CCVX,IAAI8E,EAAU/F,KACVR,EAAa2C,IACb0D,EAAuBxD,KAEvB2D,EAAS,qBACTC,EAAQC,GAAcC,QAAG3G,EAAWwG,IAAWH,EAAqBG,EAAQ,CAAA,UAE/EC,EAAMjC,WAAaiC,EAAMjC,SAAW,KAAKoC,KAAK,CAC7C3C,QAAS,SACT4C,KAAMN,EAAU,OAAS,SACzBO,UAAW,4CACXC,QAAS,2DACTC,OAAQ,sFCZV,IAAIP,EAAQjG,YAEZyG,GAAiB,SAAUX,EAAK7E,GAC9B,OAAOgF,EAAMH,KAASG,EAAMH,GAAO7E,GAAS,CAAA,uCCH9C,IAAIgB,EAAyBjC,KAEzBsC,EAAUpC,cAIAwG,GAAG,SAAU1D,GACzB,OAAOV,EAAQL,EAAuBe,wCCPxC,IAAIvB,EAAczB,KACd0G,EAAWvE,KAEX3B,EAAiBiB,EAAY,CAAE,EAACjB,uBAKtBmG,GAAGzG,OAAO0G,QAAU,SAAgBvH,EAAIyG,GACpD,OAAOtF,EAAekG,EAASrH,GAAKyG,uCCTtC,IAAIrE,EAAczB,KAEd6G,EAAK,EACLC,EAAUxH,KAAKyH,SACfrF,EAAWD,EAAY,GAAIC,iBAEjBsF,GAAG,SAAUlB,GACzB,MAAO,gBAAqB/C,IAAR+C,EAAoB,GAAKA,GAAO,KAAOpE,IAAWmF,EAAKC,EAAS,wCCPtF,IAAIvH,EAASS,IACTyG,EAAStE,KACTyE,EAASvE,KACT2E,EAAMlC,KACNJ,EAAgBuC,KAChBpC,EAAoBqC,KAEpB1C,EAASjF,EAAOiF,OAChB2C,EAAwBV,EAAO,OAC/BW,EAAwBvC,EAAoBL,EAAY,KAAKA,EAASA,GAAUA,EAAO6C,eAAiBL,SAE9FM,GAAG,SAAUC,GAKvB,OAJGX,EAAOO,EAAuBI,KACjCJ,EAAsBI,GAAQ7C,GAAiBkC,EAAOpC,EAAQ+C,GAC1D/C,EAAO+C,GACPH,EAAsB,UAAYG,IAC/BJ,EAAsBI,uCChBjC,IAAI7G,EAAOV,IACPiD,EAAWd,KACX4C,EAAW1C,KACX8C,EAAYL,KACZS,EAAsB0B,KACtBK,EAAkBJ,KAElBnF,EAAaC,UACbwF,EAAeF,EAAgB,sBAInCG,GAAiB,SAAUjC,EAAOC,GAChC,IAAKxC,EAASuC,IAAUT,EAASS,GAAQ,OAAOA,EAChD,IACIkC,EADAC,EAAexC,EAAUK,EAAOgC,GAEpC,GAAIG,EAAc,CAGhB,QAFa5E,IAAT0C,IAAoBA,EAAO,WAC/BiC,EAAShH,EAAKiH,EAAcnC,EAAOC,IAC9BxC,EAASyE,IAAW3C,EAAS2C,GAAS,OAAOA,EAClD,MAAM,IAAI3F,EAAW,0CACtB,CAED,YADagB,IAAT0C,IAAoBA,EAAO,UACxBF,EAAoBC,EAAOC,uCCvBpC,IAAIgC,EAAczH,KACd+E,EAAW5C,YAIDyF,GAAG,SAAU5E,GACzB,IAAI8C,EAAM2B,EAAYzE,EAAU,UAChC,OAAO+B,EAASe,GAAOA,EAAMA,EAAM,uCCPrC,IAAI+B,EAAc7H,IACdH,EAAQsC,IACR2F,kCCFJ,IAAIvI,EAASS,IACTiD,EAAWd,KAEXS,EAAWrD,EAAOqD,SAElBmF,EAAS9E,EAASL,IAAaK,EAASL,EAASkF,sBAEvCE,GAAG,SAAU3I,GACzB,OAAO0I,EAASnF,EAASkF,cAAczI,GAAM,CAAA,GDN3BgD,UAGpB4F,IAAkBJ,IAAgBhI,GAAM,WAEtC,OAES,IAFFK,OAAOC,eAAe2H,EAAc,OAAQ,IAAK,CACtD1H,IAAK,WAAc,OAAO,CAAI,IAC7B8H,CACL,sCEVA,IAAIL,EAAc7H,IACdU,EAAOyB,IACPgG,gCCFJ,IAAIC,EAAwB,CAAE,EAAC3F,qBAE3B4F,EAA2BnI,OAAOmI,yBAGlCC,EAAcD,IAA6BD,EAAsB1H,KAAK,CAAE,EAAG,GAAK,UAIpF6H,GAAAC,EAAYF,EAAc,SAA8BlD,GACtD,IAAIqD,EAAaJ,EAAyB1I,KAAMyF,GAChD,QAASqD,GAAcA,EAAWvH,UACnC,EAAGkH,KDV6B/F,GAC7BtB,EAA2B+D,KAC3BpC,EAAkBuE,KAClBW,EAAgBV,KAChBN,EAAS8B,KACTC,EAAiBC,KAGjBC,EAA4B3I,OAAOmI,gCAI9BS,EAAAN,EAAGX,EAAcgB,EAA4B,SAAkCE,EAAG1D,GAGzF,GAFA0D,EAAIrG,EAAgBqG,GACpB1D,EAAIuC,EAAcvC,GACdsD,EAAgB,IAClB,OAAOE,EAA0BE,EAAG1D,EACxC,CAAI,MAAOtF,GAAsB,CAC/B,GAAI6G,EAAOmC,EAAG1D,GAAI,OAAOtE,GAA0BL,EAAKyH,EAA2BK,EAAGO,EAAG1D,GAAI0D,EAAE1D,yEEpBjG,IAAIpC,EAAWjD,KAEXoE,EAAUP,OACV9B,EAAaC,iBAGHgH,GAAG,SAAUhG,GACzB,GAAIC,EAASD,GAAW,OAAOA,EAC/B,MAAM,IAAIjB,EAAWqC,EAAQpB,GAAY,yDCR3C,IAAI6E,EAAc7H,IACd2I,EAAiBxG,KACjB8G,kCCFJ,IAAIpB,EAAc7H,IACdH,EAAQsC,WAIZ+G,GAAiBrB,GAAehI,GAAM,WAEpC,OAGiB,KAHVK,OAAOC,gBAAe,WAAY,GAAiB,YAAa,CACrEc,MAAO,GACPG,UAAU,IACTT,SACL,IDT8B0B,GAC1B2G,EAAWlE,KACX8C,EAAgBX,KAEhBlF,EAAaC,UAEbmH,EAAkBjJ,OAAOC,eAEzB0I,EAA4B3I,OAAOmI,yBACnCe,EAAa,aACbC,EAAe,eACfC,EAAW,kBAIfC,GAAAf,EAAYX,EAAcoB,EAA0B,SAAwBF,EAAG1D,EAAGmE,GAIhF,GAHAR,EAASD,GACT1D,EAAIuC,EAAcvC,GAClB2D,EAASQ,GACQ,mBAANT,GAA0B,cAAN1D,GAAqB,UAAWmE,GAAcF,KAAYE,IAAeA,EAAWF,GAAW,CAC5H,IAAIG,EAAUZ,EAA0BE,EAAG1D,GACvCoE,GAAWA,EAAQH,KACrBP,EAAE1D,GAAKmE,EAAWvI,MAClBuI,EAAa,CACXrI,aAAckI,KAAgBG,EAAaA,EAAWH,GAAgBI,EAAQJ,GAC9EnI,WAAYkI,KAAcI,EAAaA,EAAWJ,GAAcK,EAAQL,GACxEhI,UAAU,GAGf,CAAC,OAAO+H,EAAgBJ,EAAG1D,EAAGmE,EAChC,EAAGL,EAAkB,SAAwBJ,EAAG1D,EAAGmE,GAIlD,GAHAR,EAASD,GACT1D,EAAIuC,EAAcvC,GAClB2D,EAASQ,GACLb,EAAgB,IAClB,OAAOQ,EAAgBJ,EAAG1D,EAAGmE,EACjC,CAAI,MAAOzJ,GAAsB,CAC/B,GAAI,QAASyJ,GAAc,QAASA,EAAY,MAAM,IAAIzH,EAAW,2BAErE,MADI,UAAWyH,IAAYT,EAAE1D,GAAKmE,EAAWvI,OACtC8H,yCEzCT,IAAIlB,EAAc7H,IACd0J,EAAuBvH,KACvBpB,EAA2BsB,YAEjBsH,GAAG9B,EAAc,SAAU+B,EAAQ9D,EAAK7E,GACpD,OAAOyI,EAAqBlB,EAAEoB,EAAQ9D,EAAK/E,EAAyB,EAAGE,GACzE,EAAI,SAAU2I,EAAQ9D,EAAK7E,GAEzB,OADA2I,EAAO9D,GAAO7E,EACP2I,uGCRT,IAAInI,EAAczB,KACd8C,EAAaX,KACb8D,EAAQ5D,KAERwH,EAAmBpI,EAAY7B,SAAS8B,iBAGvCoB,EAAWmD,EAAM6D,iBACpB7D,EAAM6D,cAAgB,SAAUzK,GAC9B,OAAOwK,EAAiBxK,EAC5B,GAGcyK,GAAG7D,EAAM6D,iDCbvB,IAAIrD,EAASzG,KACTgH,EAAM7E,KAEN4H,EAAOtD,EAAO,eAEJuD,GAAG,SAAUlE,GACzB,OAAOiE,EAAKjE,KAASiE,EAAKjE,GAAOkB,EAAIlB,sCCNvCmE,GAAiB,CAAA,sCCAjB,IAYIC,EAAK9J,EAAK+J,EAZVC,kCCAJ,IAAI7K,EAASS,IACT8C,EAAaX,KAEbkI,EAAU9K,EAAO8K,eAErBC,GAAiBxH,EAAWuH,IAAY,cAAc/J,KAAKuD,OAAOwG,IDL5CrK,GAClBT,EAAS4C,IACTc,EAAWZ,KACXsH,EAA8B7E,KAC9B8B,EAASK,KACTR,EAASS,KACT8C,EAAYtB,KACZuB,EAAarB,KAEb2B,EAA6B,6BAC7BvI,EAAYzC,EAAOyC,UACnBqI,EAAU9K,EAAO8K,QAgBrB,GAAID,GAAmB3D,EAAO+D,MAAO,CACnC,IAAIvE,EAAQQ,EAAO+D,QAAU/D,EAAO+D,MAAQ,IAAIH,GAEhDpE,EAAM7F,IAAM6F,EAAM7F,IAClB6F,EAAMkE,IAAMlE,EAAMkE,IAClBlE,EAAMiE,IAAMjE,EAAMiE,IAElBA,EAAM,SAAU7K,EAAIoL,GAClB,GAAIxE,EAAMkE,IAAI9K,GAAK,MAAM,IAAI2C,EAAUuI,GAGvC,OAFAE,EAASC,OAASrL,EAClB4G,EAAMiE,IAAI7K,EAAIoL,GACPA,CACX,EACErK,EAAM,SAAUf,GACd,OAAO4G,EAAM7F,IAAIf,IAAO,CAAA,CAC5B,EACE8K,EAAM,SAAU9K,GACd,OAAO4G,EAAMkE,IAAI9K,EACrB,CACA,KAAO,CACL,IAAIsL,EAAQX,EAAU,SACtBC,EAAWU,IAAS,EACpBT,EAAM,SAAU7K,EAAIoL,GAClB,GAAI7D,EAAOvH,EAAIsL,GAAQ,MAAM,IAAI3I,EAAUuI,GAG3C,OAFAE,EAASC,OAASrL,EAClBsK,EAA4BtK,EAAIsL,EAAOF,GAChCA,CACX,EACErK,EAAM,SAAUf,GACd,OAAOuH,EAAOvH,EAAIsL,GAAStL,EAAGsL,GAAS,EAC3C,EACER,EAAM,SAAU9K,GACd,OAAOuH,EAAOvH,EAAIsL,EACtB,CACC,QAEDC,GAAiB,CACfV,IAAKA,EACL9J,IAAKA,EACL+J,IAAKA,EACLU,QArDY,SAAUxL,GACtB,OAAO8K,EAAI9K,GAAMe,EAAIf,GAAM6K,EAAI7K,EAAI,CAAA,EACrC,EAoDEyL,UAlDc,SAAUC,GACxB,OAAO,SAAU1L,GACf,IAAImL,EACJ,IAAKvH,EAAS5D,KAAQmL,EAAQpK,EAAIf,IAAK2L,OAASD,EAC9C,MAAM,IAAI/I,EAAU,0BAA4B+I,EAAO,aACvD,OAAOP,CACb,CACA,8CEzBA,IAAI/I,EAAczB,KACdH,EAAQsC,IACRW,EAAaT,KACbuE,EAAS9B,KACT+C,EAAcZ,IACdgE,kCCLJ,IAAIpD,EAAc7H,IACd4G,EAASzE,KAETd,EAAoBzB,SAASe,UAE7BuK,EAAgBrD,GAAe3H,OAAOmI,yBAEtCN,EAASnB,EAAOvF,EAAmB,QAEnC8J,EAASpD,GAA0D,cAAhD,WAAqC,EAAER,KAC1D8B,EAAetB,KAAYF,GAAgBA,GAAeqD,EAAc7J,EAAmB,QAAQF,qBAEvGiK,GAAiB,CACfrD,OAAQA,EACRoD,OAAQA,EACR9B,aAAcA,GDViBnC,GAAsCmC,aACnES,EAAgBpB,KAChB2C,EAAsBzC,KAEtB0C,EAAuBD,EAAoBR,QAC3CU,EAAmBF,EAAoBjL,IACvCgE,EAAUP,OAEV1D,EAAiBD,OAAOC,eACxBwB,EAAcF,EAAY,GAAGG,OAC7B4J,EAAU/J,EAAY,GAAG+J,SACzBC,EAAOhK,EAAY,GAAGgK,MAEtBC,EAAsB7D,IAAgBhI,GAAM,WAC9C,OAAsF,IAA/EM,GAAe,WAA2B,GAAE,SAAU,CAAEc,MAAO,IAAKoC,MAC7E,IAEIsI,EAAW9H,OAAOA,QAAQtB,MAAM,UAEhCqJ,EAAcC,GAAA1F,QAAiB,SAAUlF,EAAOsG,EAAMuE,GACf,YAArCnK,EAAYyC,EAAQmD,GAAO,EAAG,KAChCA,EAAO,IAAMiE,EAAQpH,EAAQmD,GAAO,wBAAyB,MAAQ,KAEnEuE,GAAWA,EAAQC,SAAQxE,EAAO,OAASA,GAC3CuE,GAAWA,EAAQE,SAAQzE,EAAO,OAASA,KAC1CX,EAAO3F,EAAO,SAAYgK,GAA8BhK,EAAMsG,OAASA,KACtEM,EAAa1H,EAAec,EAAO,OAAQ,CAAEA,MAAOsG,EAAMpG,cAAc,IACvEF,EAAMsG,KAAOA,GAEhBmE,GAAuBI,GAAWlF,EAAOkF,EAAS,UAAY7K,EAAMoC,SAAWyI,EAAQG,OACzF9L,EAAec,EAAO,SAAU,CAAEA,MAAO6K,EAAQG,QAEnD,IACMH,GAAWlF,EAAOkF,EAAS,gBAAkBA,EAAQI,YACnDrE,GAAa1H,EAAec,EAAO,YAAa,CAAEG,UAAU,IAEvDH,EAAMN,YAAWM,EAAMN,eAAYoC,EAClD,CAAI,MAAOhD,GAAsB,CAC/B,IAAIyK,EAAQc,EAAqBrK,GAG/B,OAFG2F,EAAO4D,EAAO,YACjBA,EAAMhE,OAASiF,EAAKE,EAAyB,iBAARpE,EAAmBA,EAAO,KACxDtG,CACX,SAIArB,SAASe,UAAUe,SAAWkK,GAAY,WACxC,OAAO9I,EAAWnD,OAAS4L,EAAiB5L,MAAM6G,QAAUsD,EAAcnK,KAC3E,GAAE,0DErDH,IAAImD,EAAa9C,KACb0J,EAAuBvH,KACvByJ,EAAcvJ,KACdwD,EAAuBf,YAEbqH,GAAG,SAAUpD,EAAGjD,EAAK7E,EAAO6K,GACnCA,IAASA,EAAU,IACxB,IAAIM,EAASN,EAAQ5K,WACjBqG,OAAwBxE,IAAjB+I,EAAQvE,KAAqBuE,EAAQvE,KAAOzB,EAEvD,GADIhD,EAAW7B,IAAQ2K,EAAY3K,EAAOsG,EAAMuE,GAC5CA,EAAQvM,OACN6M,EAAQrD,EAAEjD,GAAO7E,EAChB4E,EAAqBC,EAAK7E,OAC1B,CACL,IACO6K,EAAQO,OACJtD,EAAEjD,KAAMsG,GAAS,UADErD,EAAEjD,EAEpC,CAAM,MAAO/F,GAAsB,CAC3BqM,EAAQrD,EAAEjD,GAAO7E,EAChByI,EAAqBlB,EAAEO,EAAGjD,EAAK,CAClC7E,MAAOA,EACPC,YAAY,EACZC,cAAe2K,EAAQQ,gBACvBlL,UAAW0K,EAAQS,aAEtB,CAAC,OAAOxD,mGCzBX,IAAIyD,kCCAJ,IAAIC,EAAOnN,KAAKmN,KACZC,EAAQpN,KAAKoN,aAKHC,GAAGrN,KAAKkN,OAAS,SAAeI,GAC5C,IAAIC,GAAKD,EACT,OAAQC,EAAI,EAAIH,EAAQD,GAAMI,ODRpB7M,UAIE8M,GAAG,SAAU9J,GACzB,IAAI+J,GAAU/J,EAEd,OAAO+J,GAAWA,GAAqB,IAAXA,EAAe,EAAIP,EAAMO,uCEPvD,IAAID,EAAsB9M,KAEtBgN,EAAM1N,KAAK0N,IACXC,EAAM3N,KAAK2N,WAKfC,GAAiB,SAAUC,EAAO9J,GAChC,IAAI+J,EAAUN,EAAoBK,GAClC,OAAOC,EAAU,EAAIJ,EAAII,EAAU/J,EAAQ,GAAK4J,EAAIG,EAAS/J,uCCV/D,IAAIyJ,EAAsB9M,KAEtBiN,EAAM3N,KAAK2N,WAIDI,GAAG,SAAUrK,GACzB,IAAIsK,EAAMR,EAAoB9J,GAC9B,OAAOsK,EAAM,EAAIL,EAAIK,EAAK,kBAAoB,sCCRhD,IAAID,EAAWrN,YAIDuN,GAAG,SAAUC,GACzB,OAAOH,EAASG,EAAInK,4CCLtB,IAAI5B,EAAczB,KACd4G,EAASzE,KACTO,EAAkBL,KAClBoL,kCCHJ,IAAI/K,EAAkB1C,KAClBkN,EAAkB/K,KAClBoL,EAAoBlL,KAGpBqL,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAOC,EAAIC,GAC1B,IAAI/E,EAAIrG,EAAgBkL,GACpBvK,EAASkK,EAAkBxE,GAC/B,GAAe,IAAX1F,EAAc,OAAQsK,IAAgB,EAC1C,IACI1M,EADAkM,EAAQD,EAAgBY,EAAWzK,GAIvC,GAAIsK,GAAeE,GAAOA,GAAI,KAAOxK,EAAS8J,GAG5C,IAFAlM,EAAQ8H,EAAEoE,OAEIlM,EAAO,OAAO,OAEvB,KAAMoC,EAAS8J,EAAOA,IAC3B,IAAKQ,GAAeR,KAASpE,IAAMA,EAAEoE,KAAWU,EAAI,OAAOF,GAAeR,GAAS,EACnF,OAAQQ,IAAgB,CAC9B,CACA,SAEAI,GAAiB,CAGfC,SAAUN,GAAa,GAGvBD,QAASC,GAAa,ID5BV5I,GAAuC2I,QACjDxD,EAAahD,KAEbb,EAAO3E,EAAY,GAAG2E,aAE1B6H,GAAiB,SAAUrE,EAAQsE,GACjC,IAGIpI,EAHAiD,EAAIrG,EAAgBkH,GACpBuE,EAAI,EACJzG,EAAS,GAEb,IAAK5B,KAAOiD,GAAInC,EAAOqD,EAAYnE,IAAQc,EAAOmC,EAAGjD,IAAQM,EAAKsB,EAAQ5B,GAE1E,KAAOoI,EAAM7K,OAAS8K,GAAOvH,EAAOmC,EAAGjD,EAAMoI,EAAMC,SAChDV,EAAQ/F,EAAQ5B,IAAQM,EAAKsB,EAAQ5B,IAExC,OAAO4B,8KElBT,IAAIxE,EAAalD,KACbyB,EAAcU,KACdiM,kCCFJ,IAAIC,EAAqBrO,KAGrBiK,eCFJqE,GAAiB,CACf,cACA,iBACA,gBACA,uBACA,iBACA,WACA,aDL2BC,OAAO,SAAU,oBAKrCC,GAAAhG,EAAGtI,OAAOuO,qBAAuB,SAA6B1F,GACrE,OAAOsF,EAAmBtF,EAAGkB,ODPC5H,GAC5BqM,aGFKC,GAAAnG,EAAGtI,OAAOoE,2BHGf0E,EAAW/B,KAEXsH,EAAS9M,EAAY,GAAG8M,eAG5BK,GAAiB1L,EAAW,UAAW,YAAc,SAAiB7D,GACpE,IAAI0K,EAAOqE,EAA0B5F,EAAEQ,EAAS3J,IAC5CiF,EAAwBoK,EAA4BlG,EACxD,OAAOlE,EAAwBiK,EAAOxE,EAAMzF,EAAsBjF,IAAO0K,sCIZ3E,IAAInD,EAAS5G,KACT4O,EAAUzM,KACV0M,EAAiCxM,KACjCqH,EAAuB5E,YAE3BgK,GAAiB,SAAUC,EAAQvI,EAAQwI,GAIzC,IAHA,IAAIjF,EAAO6E,EAAQpI,GACfrG,EAAiBuJ,EAAqBlB,EACtCH,EAA2BwG,EAA+BrG,EACrD2F,EAAI,EAAGA,EAAIpE,EAAK1G,OAAQ8K,IAAK,CACpC,IAAIrI,EAAMiE,EAAKoE,GACVvH,EAAOmI,EAAQjJ,IAAUkJ,GAAcpI,EAAOoI,EAAYlJ,IAC7D3F,EAAe4O,EAAQjJ,EAAKuC,EAAyB7B,EAAQV,GAEhE,sCCdH,IAAIvG,EAASS,IACTqI,EAA2BlG,KAA2DqG,EACtFmB,EAA8BtH,KAC9B8J,EAAgBrH,KAChBe,EAAuBoB,KACvB6H,EAA4B5H,KAC5B+H,kCCNJ,IAAIpP,EAAQG,IACR8C,EAAaX,KAEb+M,EAAc,kBAEdD,EAAW,SAAUE,EAASC,GAChC,IAAInO,EAAQoO,EAAKC,EAAUH,IAC3B,OAAOlO,IAAUsO,GACbtO,IAAUuO,IACV1M,EAAWsM,GAAavP,EAAMuP,KAC5BA,EACR,EAEIE,EAAYL,EAASK,UAAY,SAAUG,GAC7C,OAAO5L,OAAO4L,GAAQjE,QAAQ0D,EAAa,KAAKQ,aAClD,EAEIL,EAAOJ,EAASI,KAAO,GACvBG,EAASP,EAASO,OAAS,IAC3BD,EAAWN,EAASM,SAAW,WAEnCI,GAAiBV,EDfFvG,UAiBfkH,GAAiB,SAAU9D,EAAStF,GAClC,IAGYuI,EAAQjJ,EAAK+J,EAAgBC,EAAgBrH,EAHrDsH,EAASjE,EAAQiD,OACjBiB,EAASlE,EAAQvM,OACjB0Q,EAASnE,EAAQoE,KASrB,GANEnB,EADEiB,EACOzQ,EACA0Q,EACA1Q,EAAOwQ,IAAWlK,EAAqBkK,EAAQ,CAAA,GAE/CxQ,EAAOwQ,IAAWxQ,EAAOwQ,GAAQpP,UAEhC,IAAKmF,KAAOU,EAAQ,CAQ9B,GAPAsJ,EAAiBtJ,EAAOV,GAGtB+J,EAFE/D,EAAQqE,gBACV1H,EAAaJ,EAAyB0G,EAAQjJ,KACf2C,EAAWxH,MACpB8N,EAAOjJ,IACtBmJ,EAASe,EAASlK,EAAMiK,GAAUE,EAAS,IAAM,KAAOnK,EAAKgG,EAAQsE,cAE5CrN,IAAnB8M,EAA8B,CAC3C,UAAWC,UAAyBD,EAAgB,SACpDf,EAA0BgB,EAAgBD,EAC3C,EAEG/D,EAAQrH,MAASoL,GAAkBA,EAAepL,OACpDkF,EAA4BmG,EAAgB,QAAQ,GAEtD3D,EAAc4C,EAAQjJ,EAAKgK,EAAgBhE,EAC5C,sCEpDH,IAAI7I,EAAWjD,YAEDqQ,GAAG,SAAUrN,GACzB,OAAOC,EAASD,IAA0B,OAAbA,sCCH/B,IAAIqN,EAAsBrQ,KAEtBoE,EAAUP,OACV9B,EAAaC,iBAEHsO,GAAG,SAAUtN,GACzB,GAAIqN,EAAoBrN,GAAW,OAAOA,EAC1C,MAAM,IAAIjB,EAAW,aAAeqC,EAAQpB,GAAY,uDCN1D,IAAIuN,kCCDJ,IAAI9O,EAAczB,KACdkF,EAAY/C,YAEhBqO,GAAiB,SAAU5G,EAAQ9D,EAAK1C,GACtC,IAEE,OAAO3B,EAAYyD,EAAUhF,OAAOmI,yBAAyBuB,EAAQ9D,GAAK1C,IAC9E,CAAI,MAAOrD,GAAsB,GDNPC,GACtBgJ,EAAW7G,KACXmO,EAAqBjO,YAMXoO,GAAGvQ,OAAOwQ,iBAAmB,aAAe,CAAE,EAAG,WAC7D,IAEI1E,EAFA2E,GAAiB,EACjBrQ,EAAO,CAAA,EAEX,KACE0L,EAASuE,EAAoBrQ,OAAOS,UAAW,YAAa,QACrDL,EAAM,IACbqQ,EAAiBrQ,aAAgBsQ,KACrC,CAAI,MAAO7Q,GAAsB,CAC/B,OAAO,SAAwBgJ,EAAG8H,GAKhC,OAJA7H,EAASD,GACTuH,EAAmBO,GACfF,EAAgB3E,EAAOjD,EAAG8H,GACzB9H,EAAE+H,UAAYD,EACZ9H,CACX,EAf+D,QAgBzDhG,sCEzBN,IAAI5C,EAAiBH,KAA+CwI,SAEpEuI,GAAiB,SAAUC,EAAQC,EAAQnL,GACzCA,KAAOkL,GAAU7Q,EAAe6Q,EAAQlL,EAAK,CAC3C3E,cAAc,EACdf,IAAK,WAAc,OAAO6Q,EAAOnL,EAAO,EACxCoE,IAAK,SAAU7K,GAAM4R,EAAOnL,GAAOzG,CAAK,wCCN5C,IAAIyD,EAAa9C,KACbiD,EAAWd,KACXuO,EAAiBrO,YAGrB6O,GAAiB,SAAUtD,EAAOuD,EAAOC,GACvC,IAAIC,EAAWC,EAUf,OAPEZ,GAEA5N,EAAWuO,EAAYF,EAAMjF,cAC7BmF,IAAcD,GACdnO,EAASqO,EAAqBD,EAAU1Q,YACxC2Q,IAAuBF,EAAQzQ,WAC/B+P,EAAe9C,EAAO0D,GACjB1D,sCChBT,IAAI2D,kCCAJ,IAGIjR,EAAO,CAAA,SAEXA,EALsBN,IAEFsH,CAAgB,gBAGd,IAEtBkK,GAAkC,eAAjB3N,OAAOvD,GDPIN,GACxB8C,EAAaX,KACbN,EAAaQ,KAGboP,EAFkB3M,IAEFwC,CAAgB,eAChChF,EAAUpC,OAGVwR,EAAwE,cAApD7P,EAAW,WAAc,OAAOf,SAAY,CAAjC,WAUnCsB,GAAiBmP,EAAwB1P,EAAa,SAAUxC,GAC9D,IAAI0J,EAAG4I,EAAKjK,EACZ,YAAc3E,IAAP1D,EAAmB,YAAqB,OAAPA,EAAc,OAEO,iBAAjDsS,EAXD,SAAUtS,EAAIyG,GACzB,IACE,OAAOzG,EAAGyG,EACd,CAAI,MAAO/F,GAAsB,CACjC,CAOoB6R,CAAO7I,EAAIzG,EAAQjD,GAAKoS,IAA8BE,EAEpED,EAAoB7P,EAAWkH,GAEF,YAA5BrB,EAAS7F,EAAWkH,KAAoBjG,EAAWiG,EAAE8I,QAAU,YAAcnK,sCE3BpF,IAAItF,EAAUpC,KAEVoE,EAAUP,cAEAnC,GAAG,SAAUsB,GACzB,GAA0B,WAAtBZ,EAAQY,GAAwB,MAAM,IAAIhB,UAAU,6CACxD,OAAOoC,EAAQpB,uCCNjB,IAAItB,EAAW1B,YAEf8R,GAAiB,SAAU9O,EAAU+O,GACnC,YAAoBhP,IAAbC,EAAyBlC,UAAUuC,OAAS,EAAI,GAAK0O,EAAWrQ,EAASsB,0CCHlF,IAAIC,EAAWjD,KACX2J,EAA8BxH,YAIlC6P,GAAiB,SAAUjJ,EAAG+C,GACxB7I,EAAS6I,IAAY,UAAWA,GAClCnC,EAA4BZ,EAAG,QAAS+C,EAAQmG,2CCPpD,IAAItI,EAA8B3J,KAC9BkS,kCCDJ,IAAIzQ,EAAczB,KAEdmS,EAASC,MACT5G,EAAU/J,EAAY,GAAG+J,SAEzB6G,EAAgCxO,OAAO,IAAIsO,EAAuB,UAAXG,OAEvDC,EAA2B,uBAC3BC,EAAwBD,EAAyBjS,KAAK+R,UAE1DI,GAAiB,SAAUH,EAAOI,GAChC,GAAIF,GAAyC,iBAATF,IAAsBH,EAAOQ,kBAC/D,KAAOD,KAAeJ,EAAQ9G,EAAQ8G,EAAOC,EAA0B,IACvE,OAAOD,GDZWnQ,GAClByQ,kCEFJ,IAAI/S,EAAQG,IACRe,EAA2BoB,YAE/B0Q,IAAkBhT,GAAM,WACtB,IAAIE,EAAQ,IAAIqS,MAAM,KACtB,QAAM,UAAWrS,KAEjBG,OAAOC,eAAeJ,EAAO,QAASgB,EAAyB,EAAG,IAC3C,IAAhBhB,EAAMuS,MACf,IFP8BjQ,GAG1ByQ,EAAoBV,MAAMU,yBAEhBC,GAAG,SAAUhT,EAAOiT,EAAGV,EAAOI,GACtCE,IACEE,EAAmBA,EAAkB/S,EAAOiT,GAC3CrJ,EAA4B5J,EAAO,QAASmS,EAAgBI,EAAOI,yCGV5E,IAAIxP,EAAalD,KACb4G,EAASzE,KACTwH,EAA8BtH,KAC9BkB,EAAgBuB,KAChB4L,EAAiBzJ,KACjB6H,EAA4B5H,KAC5B6J,EAAgBrI,KAChBwI,EAAoBtI,KACpBkJ,EAA0BmB,KAC1BjB,EAAoBkB,KACpBC,EAAoBC,KACpBvL,EAAcwL,IACdtN,EAAUuN,YAEAC,GAAG,SAAUC,EAAWC,EAASC,EAAQC,GACrD,IAAIC,EAAoB,kBACpBC,EAAmBF,EAAqB,EAAI,EAC5CG,EAAON,EAAUjR,MAAM,KACvBwR,EAAaD,EAAKA,EAAKzQ,OAAS,GAChC2Q,EAAgB9Q,EAAWrC,MAAM,KAAMiT,GAE3C,GAAKE,EAAL,CAEA,IAAIC,EAAyBD,EAAcrT,UAK3C,IAFKoF,GAAWa,EAAOqN,EAAwB,iBAAiBA,EAAuBhC,OAElFyB,EAAQ,OAAOM,EAEpB,IAAIE,EAAYhR,EAAW,SAEvBiR,EAAeV,GAAQ,SAAUvL,EAAGkM,GACtC,IAAIC,EAAUvC,EAAwB6B,EAAqBS,EAAIlM,OAAGnF,GAC9D2E,EAASiM,EAAqB,IAAIK,EAAc9L,GAAK,IAAI8L,EAK7D,YAJgBjR,IAAZsR,GAAuB1K,EAA4BjC,EAAQ,UAAW2M,GAC1ElB,EAAkBzL,EAAQyM,EAAczM,EAAO4K,MAAO,GAClD3S,MAAQ4D,EAAc0Q,EAAwBtU,OAAOuR,EAAkBxJ,EAAQ/H,KAAMwU,GACrFrT,UAAUuC,OAASwQ,GAAkB7B,EAAkBtK,EAAQ5G,UAAU+S,IACtEnM,CACX,IAcE,GAZAyM,EAAaxT,UAAYsT,EAEN,UAAfF,EACErD,EAAgBA,EAAeyD,EAAcD,GAC5CpF,EAA0BqF,EAAcD,EAAW,CAAE3M,MAAM,IACvDM,GAAe+L,KAAqBI,IAC7CjD,EAAcoD,EAAcH,EAAeJ,GAC3C7C,EAAcoD,EAAcH,EAAe,sBAG7ClF,EAA0BqF,EAAcH,IAEnCjO,EAAS,IAERkO,EAAuB1M,OAASwM,GAClCpK,EAA4BsK,EAAwB,OAAQF,GAE9DE,EAAuB/H,YAAciI,CACzC,CAAI,MAAOpU,GAAsB,CAE/B,OAAOoU,CAzCoB,sCCpB7B,IAAIG,EAAItU,KACJT,EAAS4C,IACTtB,kCCHJ,IAAIJ,EAAcT,IAEdqB,EAAoBzB,SAASe,UAC7BE,EAAQQ,EAAkBR,MAC1BH,EAAOW,EAAkBX,YAG7B6T,GAAmC,iBAAXC,SAAuBA,QAAQ3T,QAAUJ,EAAcC,EAAKH,KAAKM,GAAS,WAChG,OAAOH,EAAKG,MAAMA,EAAOC,UAC3B,MDNYuB,GACRkR,EAAgCzO,KAEhC2P,EAAe,cACfC,EAAcnV,EAAOkV,GAGrBf,EAAgD,IAAvC,IAAItB,MAAM,IAAK,CAAEH,MAAO,IAAKA,MAEtC0C,EAAgC,SAAUZ,EAAYN,GACxD,IAAI1K,EAAI,CAAA,EACRA,EAAEgL,GAAcR,EAA8BQ,EAAYN,EAASC,GACnEY,EAAE,CAAE/U,QAAQ,EAAM2M,aAAa,EAAMD,MAAO,EAAGmE,OAAQsD,GAAU3K,EACnE,EAEI6L,EAAqC,SAAUb,EAAYN,GAC7D,GAAIiB,GAAeA,EAAYX,GAAa,CAC1C,IAAIhL,EAAI,CAAA,EACRA,EAAEgL,GAAcR,EAA8BkB,EAAe,IAAMV,EAAYN,EAASC,GACxFY,EAAE,CAAEvF,OAAQ0F,EAAcvE,MAAM,EAAMhE,aAAa,EAAMD,MAAO,EAAGmE,OAAQsD,GAAU3K,EACtF,CACH,EAGA4L,EAA8B,SAAS,SAAUE,GAC/C,OAAO,SAAeR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WAC5D,IACA6T,EAA8B,aAAa,SAAUE,GACnD,OAAO,SAAmBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WAChE,IACA6T,EAA8B,cAAc,SAAUE,GACpD,OAAO,SAAoBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WACjE,IACA6T,EAA8B,kBAAkB,SAAUE,GACxD,OAAO,SAAwBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WACrE,IACA6T,EAA8B,eAAe,SAAUE,GACrD,OAAO,SAAqBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WAClE,IACA6T,EAA8B,aAAa,SAAUE,GACnD,OAAO,SAAmBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WAChE,IACA6T,EAA8B,YAAY,SAAUE,GAClD,OAAO,SAAkBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WAC/D,IACA8T,EAAmC,gBAAgB,SAAUC,GAC3D,OAAO,SAAsBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WACnE,IACA8T,EAAmC,aAAa,SAAUC,GACxD,OAAO,SAAmBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WAChE,IACA8T,EAAmC,gBAAgB,SAAUC,GAC3D,OAAO,SAAsBR,GAAW,OAAOxT,EAAMgU,EAAMlV,KAAMmB,WACnE,yEExDA,IAAIsB,EAAUpC,YAKA8U,GAAGlE,MAAMkE,SAAW,SAAiB9R,GACjD,MAA6B,UAAtBZ,EAAQY,uCCNjB,IAAI6E,EAAc7H,IACd8U,EAAU3S,KAEVJ,EAAaC,UAEbqG,EAA2BnI,OAAOmI,yBAGlC0M,EAAoClN,IAAgB,WAEtD,QAAa9E,IAATpD,KAAoB,OAAO,EAC/B,IAEEO,OAAOC,eAAe,GAAI,SAAU,CAAEiB,UAAU,IAASiC,OAAS,CACnE,CAAC,MAAOtD,GACP,OAAOA,aAAiBiC,SACzB,CACH,CATwD,UAWxDgT,GAAiBD,EAAoC,SAAUhM,EAAG1F,GAChE,GAAIyR,EAAQ/L,KAAOV,EAAyBU,EAAG,UAAU3H,SACvD,MAAM,IAAIW,EAAW,gCACrB,OAAOgH,EAAE1F,OAASA,CACtB,EAAI,SAAU0F,EAAG1F,GACf,OAAO0F,EAAE1F,OAASA,sCCxBpB,IAAItB,EAAaC,iBAGHiT,GAAG,SAAU5V,GACzB,GAAIA,EAHiB,iBAGM,MAAM0C,EAAW,kCAC5C,OAAO1C,GCLM,SAAS6V,GAAkBC,EAAGC,GAC5C,IAAIC,EAAIF,EAAE9R,OAELuN,MAAMkE,QAAQK,EAAE,MAEpBA,EAAI,CAACA,IAGDvE,MAAMkE,QAAQM,EAAE,MAEpBA,EAAIA,EAAEE,KAAI1I,GAAK,CAACA,MAGjB,IAAI2I,EAAIH,EAAE,GAAG/R,OACTmS,EAASJ,EAAE,GAAGE,KAAI,CAACG,EAAGtH,IAAMiH,EAAEE,KAAI1I,GAAKA,EAAEuB,OACzCuH,EAAUP,EAAEG,KAAIK,GAAOH,EAAOF,KAAIM,IACrC,IAAIC,EAAM,EAEV,IAAKjF,MAAMkE,QAAQa,GAAM,CACxB,IAAK,IAAIG,KAAKF,EACbC,GAAOF,EAAMG,EAGd,OAAOD,CACR,CAEA,IAAK,IAAI1H,EAAI,EAAGA,EAAIwH,EAAItS,OAAQ8K,IAC/B0H,GAAOF,EAAIxH,IAAMyH,EAAIzH,IAAM,GAG5B,OAAO0H,CAAG,MAOX,OAJU,IAANR,IACHK,EAAUA,EAAQ,IAGT,IAANH,EACIG,EAAQJ,KAAI1I,GAAKA,EAAE,KAGpB8I,CACR,CChCO,SAASK,GAAUC,GACzB,MAAqB,WAAdhL,GAAKgL,EACb,CAOO,SAAShL,GAAMiL,GAGrB,OAFU/V,OAAOS,UAAUe,SAAShB,KAAKuV,GAE7BzS,MAAM,wBAAwB,IAAM,IAAIkM,aACrD,CAEO,SAASwG,GAAiBrJ,EAACsJ,GAAsB,IAApBC,UAACA,EAASC,KAAEA,GAAMF,EACrD,OAAIG,GAAOzJ,GACH,OA2BF,SAAsBA,EAAGuJ,GAC/B,GAAU,IAANvJ,EACH,OAAO,EAER,IAAIO,IAAYP,EACZ0J,EAAS,EACTnJ,GAAWgJ,IACdG,EAA2C,IAAhCjX,KAAKkX,MAAMlX,KAAKmX,IAAIrJ,KAEhC,MAAMsJ,EAAa,KAASN,EAAYG,GACxC,OAAOjX,KAAKoN,MAAMG,EAAI6J,EAAa,IAAOA,CAC3C,CAnCQC,CAAY9J,EAAGuJ,IAAcC,QAAAA,EAAQ,GAC7C,CAOO,SAASC,GAAQzJ,GACvB,OAAO+J,OAAOC,MAAMhK,IAAOA,aAAa+J,SAAU/J,eAAAA,EAAGiK,KACtD,CAKO,SAASC,GAAUlK,GACzB,OAAOyJ,GAAOzJ,GAAK,EAAIA,CACxB,kCC/CA,IAAIyH,EAAItU,KACJ0G,EAAWvE,KACXoL,EAAoBlL,KACpB2U,EAAiBlS,KACjBmQ,EAA2BhO,KAsB/BqN,EAAE,CAAEvF,OAAQ,QAAS8B,OAAO,EAAM5E,MAAO,EAAGmE,OArBhClJ,GAEcrH,EAAM,WAC9B,OAAoD,aAA7C,GAAGuG,KAAK1F,KAAK,CAAE2C,OAAQ,YAAe,EAC/C,MAIqC,WACnC,IAEEnD,OAAOC,eAAe,GAAI,SAAU,CAAEiB,UAAU,IAASgF,MAC1D,CAAC,MAAOrG,GACP,OAAOA,aAAiBiC,SACzB,CACH,CAEqCiV,IAIyB,CAE5D7Q,KAAM,SAAc8Q,GAClB,IAAInO,EAAIrC,EAAS/G,MACb2N,EAAMC,EAAkBxE,GACxBoO,EAAWrW,UAAUuC,OACzB4R,EAAyB3H,EAAM6J,GAC/B,IAAK,IAAIhJ,EAAI,EAAGA,EAAIgJ,EAAUhJ,IAC5BpF,EAAEuE,GAAOxM,UAAUqN,GACnBb,IAGF,OADA0J,EAAejO,EAAGuE,GACXA,CACR,OD4BH,MAAM8J,GAAc,CACnBC,IAAK,EACLC,KAAM,GACNC,IAAK,IAAMjY,KAAKkY,GAChBC,KAAM,KAmFA,SAASC,GAAaC,EAAOC,EAAKrC,GACxC,OAAIsB,MAAMc,GACFC,EAGJf,MAAMe,GACFD,EAGDA,GAASC,EAAMD,GAASpC,CAChC,CAMO,SAASsC,GAAUC,EAAMC,EAAI9W,GACnC,OAAOyW,GAAYK,EAAG,GAAIA,EAAG,GALvB,SAAyBJ,EAAOC,EAAK3W,GAC3C,OAAQA,EAAQ0W,IAAUC,EAAMD,EACjC,CAGkCK,CAAeF,EAAK,GAAIA,EAAK,GAAI7W,GACnE,CAoCO,SAASgX,GAAUF,EAAID,GAC7B,OAAOxY,KAAK4Y,KAAKH,KAAQzY,KAAK4Y,KAAKJ,GAAQC,GAAMA,CAClD,CAQO,SAASI,GAAMC,EAAMC,GAC3B,OAAOJ,GAAS3Y,KAAKmX,IAAI2B,IAASC,EAAKD,EACxC,CAQO,SAASE,GAAMzL,EAAG0L,GACxB,OAAc,IAANA,EAAW,EAAI1L,EAAI0L,CAC5B,CEpOO,MAAMC,GACZC,GAAAA,CAAKlR,EAAMmR,EAAUC,GACpB,GAA2B,iBAAhB7X,UAAU,IASpB8P,MAAMkE,QAAQvN,GAAQA,EAAO,CAACA,IAAOqR,SAAQ,SAAUrR,GACvD5H,KAAK4H,GAAQ5H,KAAK4H,IAAS,GAEvBmR,GACH/Y,KAAK4H,GAAMoR,EAAQ,UAAY,QAAQD,EAExC,GAAE/Y,WAbF,IAAK,IAAI4H,KAAQzG,UAAU,GAC1BnB,KAAK8Y,IAAIlR,EAAMzG,UAAU,GAAGyG,GAAOzG,UAAU,GAahD,CAEA+X,GAAAA,CAAKtR,EAAMuR,GACVnZ,KAAK4H,GAAQ5H,KAAK4H,IAAS,GAC3B5H,KAAK4H,GAAMqR,SAAQ,SAAUF,GAC5BA,EAAShY,KAAKoY,GAAOA,EAAIC,QAAUD,EAAIC,QAAUD,EAAKA,EACvD,GACD,EAMD,MAAME,GAAQ,IAAIR,GC/BLS,GAAS,CAErBC,IAAK,CAAC,MAAS,MAAQ,EAAS,MAA0B,OAC1DC,IAAK,CAAC,MAAS,KAAQ,EAAS,MAA0B,OAGpD,SAASC,GAAU7R,GACzB,OAAIqJ,MAAMkE,QAAQvN,GACVA,EAGD0R,GAAO1R,EACf,CAGe,SAAS8R,GAAOC,EAAIC,EAAIC,GAAmB,IAAd1N,EAAOhL,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAIrD,GAHAwY,EAAKF,GAASE,GACdC,EAAKH,GAASG,IAETD,IAAOC,EACX,MAAM,IAAIvX,UAAW,kCAAkCsX,EAAc,GAAT,SAAeA,GAAOC,EAAW,GAAN,MAAYA,EAAY,GAAP,QAGzG,GAAID,IAAOC,EAEV,OAAOC,EAGR,IAAIV,EAAM,CAACQ,KAAIC,KAAIC,MAAK1N,WAwBxB,GAtBAkN,GAAMH,IAAI,6BAA8BC,GAEnCA,EAAIW,IACJX,EAAIQ,KAAOL,GAAOE,KAAOL,EAAIS,KAAON,GAAOC,IAC9CJ,EAAIW,EAAI,CACP,CAAE,mBAAoB,qBAAuB,oBAC7C,CAAE,mBAAqB,mBAAqB,qBAC5C,EAAG,oBAAsB,oBAAsB,oBAGxCX,EAAIQ,KAAOL,GAAOC,KAAOJ,EAAIS,KAAON,GAAOE,MAEnDL,EAAIW,EAAI,CACP,CAAE,kBAAoB,mBAAqB,oBAC3C,EAAG,kBAAoB,mBAAoB,qBAC3C,CAAE,qBAAuB,oBAAsB,sBAKlDT,GAAMH,IAAI,2BAA4BC,GAElCA,EAAIW,EACP,OAAOvE,GAAiB4D,EAAIW,EAAGX,EAAIU,KAGnC,MAAM,IAAIxX,UAAU,qEAEtB,QC5De0X,GAAA,CACdC,cAAe,MACfvD,UAAW,EACXwD,OAAQ,KACRC,QAA+D,UAAtDra,OAAAA,qBAAAA,YAAmBsa,QAATA,GAAVta,WAAYsE,eAAOgW,IAAAA,IAAK,QAALA,GAAnBA,GAAqBhB,WAAGgB,IAAAA,IAAU,QAAVA,GAAxBA,GAA0BC,gBAAQ,IAAAD,QAAA,EAAlCA,GAAoCpK,eAC7CsK,KAAM,SAAeC,GACF,IAAAC,EAAAC,EAAdxa,KAAKka,UACE,OAAVra,iBAAU,IAAVA,YAAmB,QAAT0a,EAAV1a,WAAY4a,eAAO,IAAAF,GAAMC,QAANA,EAAnBD,EAAqBF,YAArBG,IAAyBA,GAAzBA,EAAAzZ,KAAAwZ,EAA4BD,GAE9B,GCLD,MAAMI,GAAY,IAAIC,IAAI,CAAC,WAAY,eAAgB,YAUvD,SAASC,GAAcC,EAAOC,EAAQlT,EAAMmT,GAC3C,IAAIC,EAAQza,OAAO0a,QAAQJ,EAAME,QAAQpF,KAAI,CAAAa,EAAkBhI,KAAM,IAOhEnD,GAP0CnE,EAAIgU,GAAU1E,EACxD2E,EAAeL,EAAOK,aAAa3M,GACnC4M,EAAML,EAAOvM,GACb6M,EAAeD,aAAAA,EAAAA,EAAK/P,KAaxB,GAPCA,EADG+P,EAAIjE,KACAgE,EAAaG,MAAKnF,GAAKuE,GAAUlQ,IAAI2L,KAGrCgF,EAAaG,MAAKnF,GAAKA,GAAKkF,KAI/BhQ,EAAM,CAEV,IAAIkQ,EAAYL,EAAUtT,MAAQV,EAClC,MAAM,IAAI7E,UAAW,GAAEgZ,QAAAA,EAAgBD,EAAII,uBAAuBD,QAAgB3T,MACnF,CAEA,IAAI6T,EAAYpQ,EAAKqQ,MAEA,iBAAjBL,IACHI,IAAAA,EAAc,CAAC,EAAG,KAGnB,IAAIE,EAAUT,EAAUQ,OAASR,EAAUU,SAM3C,OAJIH,GAAaE,IAChBZ,EAAOvM,GAAKqN,GAAcJ,EAAWE,EAASZ,EAAOvM,KAG/CnD,CAAI,IAGZ,OAAO2P,CACR,CAUe,SAASc,GAAOzF,GAAkB,IAAA0F,EAAA,IAAbC,KAACA,GAAK7a,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GACxCgY,EAAM,CAAC9C,IAAkB,QAAb0F,EAAE7X,OAAOmS,UAAI,IAAA0F,OAAA,EAAXA,EAAaE,QAG/B,GAFA5C,GAAMH,IAAI,cAAeC,GAErBA,EAAI+C,MACP,OAAO/C,EAAI+C,MAKZ,GAFA/C,EAAIgD,OLQE,SAAwB9F,GAC9B,IAAKA,EACJ,OAGDA,EAAMA,EAAI4F,OAEV,MACMG,EAAgB,aAChBC,EAAiB,oBACjBC,EAAiB,6CACvB,IAAIC,EAAQlG,EAAIxS,MAJQ,wBAMxB,GAAI0Y,EAAO,CAEV,IAAIC,EAAO,GA6CX,OA5CAD,EAAM,GAAG1Q,QAAQyQ,GAAgB,CAACG,EAAIC,KACrC,IAAI7Y,EAAQ6Y,EAAO7Y,MAAMwY,GACrBjB,EAAMsB,EAEV,GAAI7Y,EAAO,CACV,IAAI6S,EAAO7S,EAAM,GAEb8Y,EAAcvB,EAAInZ,MAAM,GAAIyU,EAAKhT,QAExB,MAATgT,GAEH0E,EAAM,IAAInE,OAAO0F,EAAc,KAC/BvB,EAAI/P,KAAO,iBAIX+P,EAAM,IAAInE,OAAO0F,EAAclF,GAAYf,IAC3C0E,EAAI/P,KAAO,UACX+P,EAAI1E,KAAOA,EAEZ,MACQ0F,EAAczb,KAAKya,IAE3BA,EAAM,IAAInE,OAAOmE,GACjBA,EAAI/P,KAAO,YAEK,SAAR+P,IACRA,EAAM,IAAInE,OAAO2F,KACjBxB,EAAIjE,MAAO,GAGRsF,EAAGI,WAAW,OAEjBzB,EAAMA,aAAenE,OAASmE,EAAM,IAAInE,OAAOmE,GAC/CA,EAAI0B,OAAQ,GAGM,iBAAR1B,GAAoBA,aAAenE,SAC7CmE,EAAII,IAAMkB,GAGXF,EAAK/V,KAAK2U,EAAI,IAGR,CACNxT,KAAM2U,EAAM,GAAGxM,cACfgN,QAASR,EAAM,GACfS,QAAST,EAAM,GAGfC,OAEF,CACD,CK7EcX,CAAmB1C,EAAI9C,KAEhC8C,EAAIgD,OAAQ,CAEf,IAAIvU,EAAOuR,EAAIgD,OAAOvU,KAEtB,GAAa,UAATA,EAAkB,CAErB,IAAIV,EAAKiS,EAAIgD,OAAOK,KAAKS,QAErBC,EAAchW,EAAG2V,WAAW,MAAQ3V,EAAGiW,UAAU,GAAM,KAAIjW,IAC3DkW,EAAM,CAAClW,EAAIgW,GACXJ,EAAQ3D,EAAIgD,OAAOa,QAAQlP,QAAQ,KAAO,EAAIqL,EAAIgD,OAAOK,KAAKa,MAAQ,EAE1E,IAAK,IAAIxC,KAASyC,GAAWpa,IAAK,CACjC,IAAIqa,EAAY1C,EAAM2C,UAAU,SAEjB,IAAAC,EAAf,GAAIF,EACH,GAAIH,EAAI/O,SAASkP,EAAUrW,KAAoBuW,QAAjBA,EAAIF,EAAUH,WAAVK,IAAaA,GAAbA,EAAeC,QAAQC,GAAWP,EAAI/O,SAASsP,KAASja,OAAQ,CAIjG,MAAMqX,EAASxa,OAAO6J,KAAKyQ,EAAME,QAAQpF,KAAI,CAACG,EAAGtH,IAAM2K,EAAIgD,OAAOK,KAAKhO,IAAM,IAE7E,IAAIwM,EAmBJ,OAjBIuC,EAAUpC,eACbH,EAAQJ,GAAaC,EAAO0C,EAAW,QAASxC,IAG7CiB,GACHzb,OAAOqd,OAAO5B,EAAM,CAAC6B,SAAU,QAAS7C,UAGrCuC,EAAUrW,GAAG2V,WAAW,QAAU3V,EAAG2V,WAAW,OACnD9C,GAASM,KAAM,GAAEQ,EAAMjT,gGACa2V,EAAUrW,wBAAwBA,OAEnEA,EAAG2V,WAAW,QAAUU,EAAUrW,GAAG2V,WAAW,OACnD9C,GAASM,KAAM,GAAEQ,EAAMjT,qEACI2V,EAAUrW,iCAAiCA,OAGhE,CAAC4W,QAASjD,EAAM3T,GAAI6T,SAAQ+B,QACpC,CAEF,CAGA,IAAIiB,EAAa,GACbC,EAAa9W,KAAMoW,GAAWW,SAAW/W,EAAKgW,EAClD,GAAIc,KAAcV,GAAWW,SAAU,CAAA,IAAAC,EAEtC,IAAIC,UAAKD,EAAGZ,GAAWW,SAASD,GAAYI,eAAO,IAAAF,GAAO,QAAPA,EAAvCA,EAAyChC,aAAK,IAAAgC,OAAA,EAA9CA,EAAgDhX,GAExDiX,IACHJ,EAAc,sBAAqBI,MAErC,CAEA,MAAM,IAAI9b,UAAW,sBAAqB6E,QAAW6W,GAAc,qBACpE,CAEC,IAAK,IAAIlD,KAASyC,GAAWpa,IAAK,CAEjC,IAAI4X,EAASD,EAAM2C,UAAU5V,GAC7B,GAAIkT,GAA0B,aAAhBA,EAAOzP,KAAqB,CACzC,IAAIyR,EAAQ,GAERhC,EAAOuD,YLUMC,EKViBnF,EAAIgD,OAAOK,KLW1C8B,EAAIA,EAAI5a,OAAS,IKX+BoZ,SAClDA,EAAQ3D,EAAIgD,OAAOK,KAAKa,OAGzB,IAEIrC,EAFAD,EAAS5B,EAAIgD,OAAOK,KAYxB,OARI1B,EAAOK,eACVH,EAAQJ,GAAaC,EAAOC,EAAQlT,EAAMmT,IAGvCiB,GACHzb,OAAOqd,OAAO5B,EAAM,CAAC6B,SAAU/C,EAAOlT,KAAMoT,UAGtC,CACN8C,QAASjD,EAAM3T,GACf6T,SAAQ+B,QAEV,CACD,CAEF,MAGC,IAAK,IAAIjC,KAASyC,GAAWpa,IAC5B,IAAK,IAAI2a,KAAYhD,EAAMuD,QAAS,CACnC,IAAItD,EAASD,EAAMuD,QAAQP,GAE3B,GAAoB,WAAhB/C,EAAOzP,KACV,SAGD,GAAIyP,EAAOna,OAASma,EAAOna,KAAKwY,EAAI9C,KACnC,SAGD,IAAI6F,EAAQpB,EAAOgB,MAAM3C,EAAI9C,KAElB,IAAAkI,EAAX,GAAIrC,EAOH,OANWqC,QAAXA,EAAArC,EAAMY,aAAKyB,IAAAA,IAAXrC,EAAMY,MAAU,GAEZd,IACHA,EAAK6B,SAAWA,GAGV3B,CAET,CLvCI,IAAeoC,EK6CrB,MAAM,IAAIjc,UAAW,mBAAkBgU,kCACxC,CC5Le,SAASmI,GAAUtC,GACjC,GAAIjL,MAAMkE,QAAQ+G,GACjB,OAAOA,EAAMvG,IAAI6I,IAGlB,IAAKtC,EACJ,MAAM,IAAI7Z,UAAU,yBAGjB+T,GAAS8F,KACZA,EAAQJ,GAAMI,IAIf,IAAIrB,EAAQqB,EAAMrB,OAASqB,EAAM4B,QAWjC,OATMjD,aAAiByC,KAEtBpB,EAAMrB,MAAQyC,GAAW7c,IAAIoa,SAGVzX,IAAhB8Y,EAAMY,QACTZ,EAAMY,MAAQ,GAGRZ,CACR,CC9BA,MAAMuC,GAAI,MAKK,MAAMnB,GACpB/Q,WAAAA,CAAaJ,GAAS,IAAAuS,EAAAlI,EAAAmI,EAAAC,EAAAC,EACrB7e,KAAKkH,GAAKiF,EAAQjF,GAClBlH,KAAK4H,KAAOuE,EAAQvE,KACpB5H,KAAKyY,KAAOtM,EAAQsM,KAAO6E,GAAW7c,IAAI0L,EAAQsM,MAAQ,KAC1DzY,KAAK8e,QAAU3S,EAAQ2S,QAEnB9e,KAAKyY,OACRzY,KAAK+e,SAAW5S,EAAQ4S,SACxB/e,KAAKgf,OAAS7S,EAAQ6S,QAKvB,IAAIjE,EAAuB,QAAjB2D,EAAGvS,EAAQ4O,cAAM,IAAA2D,EAAAA,EAAI1e,KAAKyY,KAAKsC,OAEzC,IAAK,IAAInT,KAAQmT,EACV,SAAUA,EAAOnT,KACtBmT,EAAOnT,GAAMA,KAAOA,GAGtB5H,KAAK+a,OAASA,EAId,IAAIkE,UAAKzI,EAAgB,QAAhBmI,EAAGxS,EAAQ8S,aAAK,IAAAN,EAAAA,EAAI3e,KAAKyY,KAAKwG,aAAK,IAAAzI,EAAAA,EAAI,MAChDxW,KAAKif,MAAQxF,GAASwF,GAItBjf,KAAKoe,QAAyB,QAAlBQ,EAAGzS,EAAQiS,eAAO,IAAAQ,EAAAA,EAAI,CAAA,EAElC,IAAK,IAAIhX,KAAQ5H,KAAKoe,QAAS,CAC9B,IAAItD,EAAS9a,KAAKoe,QAAQxW,GAC1BkT,EAAOzP,OAAPyP,EAAOzP,KAAS,YAChByP,EAAOlT,OAAPkT,EAAOlT,KAASA,EACjB,CAE6B,IAAAsX,EAAN,QAAnBL,EAAC7e,KAAKoe,QAAQlC,aAAK,IAAA2C,GAAlBA,EAAoB3X,KACxBlH,KAAKoe,QAAQlC,MAAQ,IACC,QAArBgD,EAAGlf,KAAKoe,QAAQlC,aAAK,IAAAgD,EAAAA,EAAI,CAAE,EAC3BhY,GAAIiF,EAAQgS,OAASne,KAAKkH,KAMxBiF,EAAQgT,WAEXnf,KAAKmf,WAAoC,SAAvBhT,EAAQgT,WAAwBnf,KAAOsd,GAAW7c,IAAI0L,EAAQgT,YAI5Enf,KAAKof,QAERpf,KAAKmf,WAAanf,KAAKyY,KAGvBzY,KAAKmf,WAAcnf,KAKjBA,KAAKmf,WAAWE,cACnBrf,KAAKsf,QAAU,CAACvE,EAAQ5O,KAChB,GAKTnM,KAAKuf,SAAWpT,EAAQoT,SAGxBhf,OAAOC,eAAeR,KAAM,OAAQ,CACnCsB,MAAOke,GAAQxf,MAAMyf,UACrBhe,UAAU,EACVF,YAAY,EACZC,cAAc,IAGf6X,GAAMH,IAAI,sBAAuBlZ,KAClC,CAEAsf,OAAAA,CAASvE,GAA4B,IAApB2E,QAACA,EAAUjB,IAAEtd,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAChC,IAAKnB,KAAK2f,OAAO3f,KAAKmf,YAErB,OADApE,EAAS/a,KAAKoY,GAAGpY,KAAKmf,WAAYpE,GAC3B/a,KAAKmf,WAAWG,QAAQvE,EAAQ,CAAC2E,YAGzC,IAAIxE,EAAY3a,OAAOqf,OAAO5f,KAAK+a,QAEnC,OAAOA,EAAO8E,OAAM,CAAC1J,EAAG3H,KACvB,IAAIwN,EAAOd,EAAU1M,GAErB,GAAkB,UAAdwN,EAAK3Q,MAAoB2Q,EAAKN,MAAO,CACxC,GAAIzE,OAAOC,MAAMf,GAEhB,OAAO,EAGR,IAAK7I,EAAKD,GAAO2O,EAAKN,MACtB,YAAgBtY,IAARkK,GAAqB6I,GAAK7I,EAAMoS,UACxBtc,IAARiK,GAAqB8I,GAAK9I,EAAMqS,EACzC,CAEA,OAAO,CAAI,GAEb,CAEA,eAAIL,GACH,OAAO9e,OAAOqf,OAAO5f,KAAK+a,QAAQ8E,OAAMC,KAAW,UAAWA,IAC/D,CAEA,SAAI3B,GAAS,IAAA4B,EACZ,OAAmBA,QAAZA,EAAI/f,KAACoe,eAAO2B,IAAAA,GAAO,QAAPA,EAAZA,EAAc7D,aAAd6D,IAAmBA,OAAnBA,EAAAA,EAAqB7Y,KAAMlH,KAAKkH,EACxC,CAEA,WAAIkY,GACH,IAAK,IAAIlY,KAAMlH,KAAK+a,OACnB,GAA6B,UAAzB/a,KAAK+a,OAAO7T,GAAImE,KACnB,OAAO,EAIT,OAAO,CACR,CAEAmS,SAAAA,CAAW1C,GACV,GAAsB,iBAAXA,EAEV,OADAA,EAASkF,GAAclF,EAAQ9a,MAIhC,IAAIkW,EASJ,OANCA,EAFc,YAAX4E,EAEGva,OAAOqf,OAAO5f,KAAKoe,SAAS,GAG5Bpe,KAAKoe,QAAQtD,GAGhB5E,GACHA,EAAM8J,GAAc9J,EAAKlW,MAClBkW,GAGD,IACR,CAQAyJ,MAAAA,CAAQ9E,GACP,QAAKA,IAIE7a,OAAS6a,GAAS7a,KAAKkH,KAAO2T,GAAS7a,KAAKkH,KAAO2T,EAAM3T,GACjE,CAEAkR,EAAAA,CAAIyC,EAAOE,GACV,GAAyB,IAArB5Z,UAAUuC,OAAc,CAC3B,MAAMwY,EAAQsC,GAAS3D,IACtBA,EAAOE,GAAU,CAACmB,EAAMrB,MAAOqB,EAAMnB,OACvC,CAIA,GAFAF,EAAQyC,GAAW7c,IAAIoa,GAEnB7a,KAAK2f,OAAO9E,GAEf,OAAOE,EAIRA,EAASA,EAAOpF,KAAIQ,GAAKc,OAAOC,MAAMf,GAAK,EAAIA,IAG/C,IAGI8J,EAAiBC,EAHjBC,EAASngB,KAAKmU,KACdiM,EAAYvF,EAAM1G,KAItB,IAAK,IAAI3F,EAAI,EAAGA,EAAI2R,EAAOzc,QACtByc,EAAO3R,GAAGmR,OAAOS,EAAU5R,IADGA,IAEjCyR,EAAkBE,EAAO3R,GACzB0R,EAAuB1R,EAOzB,IAAKyR,EAEJ,MAAM,IAAIxN,MAAO,uCAAsCzS,YAAY6a,oCAIpE,IAAK,IAAIrM,EAAI2R,EAAOzc,OAAS,EAAG8K,EAAI0R,EAAsB1R,IACzDuM,EAASoF,EAAO3R,GAAGwQ,OAAOjE,GAI3B,IAAK,IAAIvM,EAAI0R,EAAuB,EAAG1R,EAAI4R,EAAU1c,OAAQ8K,IAC5DuM,EAASqF,EAAU5R,GAAGuQ,SAAShE,GAGhC,OAAOA,CACR,CAEA5C,IAAAA,CAAM0C,EAAOE,GACZ,GAAyB,IAArB5Z,UAAUuC,OAAc,CAC3B,MAAMwY,EAAQsC,GAAS3D,IACtBA,EAAOE,GAAU,CAACmB,EAAMrB,MAAOqB,EAAMnB,OACvC,CAIA,OAFAF,EAAQyC,GAAW7c,IAAIoa,IAEVzC,GAAGpY,KAAM+a,EACvB,CAEAhZ,QAAAA,GACC,MAAQ,GAAE/B,KAAK4H,SAAS5H,KAAKkH,KAC9B,CAEAmZ,YAAAA,GACC,IAAInK,EAAM,GAEV,IAAK,IAAIhP,KAAMlH,KAAK+a,OAAQ,CAAA,IAAAuF,EAC3B,IAAItE,EAAOhc,KAAK+a,OAAO7T,GACnBwU,EAAQM,EAAKN,OAASM,EAAKJ,SAC/B1F,EAAIzP,KAAe6Z,QAAXA,EAAC5E,aAAK,EAALA,EAAOpO,eAAGgT,EAAAA,EAAI,EACxB,CAEA,OAAOpK,CACR,CAEAqK,gBAAkB,CAAA,EAGlB,cAAWrd,GACV,MAAO,IAAI,IAAIyX,IAAIpa,OAAOqf,OAAOtC,GAAWW,WAC7C,CAEA,eAAOuC,CAAUtZ,EAAI2T,GAQpB,GAPyB,IAArB1Z,UAAUuC,SAEbwD,GADA2T,EAAQ1Z,UAAU,IACP+F,IAGZ2T,EAAQ7a,KAAKS,IAAIoa,GAEb7a,KAAKie,SAAS/W,IAAOlH,KAAKie,SAAS/W,KAAQ2T,EAC9C,MAAM,IAAIpI,MAAO,wCAAuCvL,MAKzD,GAHAlH,KAAKie,SAAS/W,GAAM2T,EAGK,IAArB1Z,UAAUuC,QAAgBmX,EAAMiE,QACnC,IAAK,IAAI2B,KAAS5F,EAAMiE,QACvB9e,KAAKwgB,SAASC,EAAO5F,GAIvB,OAAOA,CACR,CAMA,UAAOpa,CAAKoa,GACX,IAAKA,GAASA,aAAiByC,GAC9B,OAAOzC,EAKR,GAAgB,WAFFxP,GAAKwP,GAEO,CAEzB,IAAI3E,EAAMoH,GAAWW,SAASpD,EAAM9K,eAEpC,IAAKmG,EACJ,MAAM,IAAI7T,UAAW,mCAAkCwY,MAGxD,OAAO3E,CACR,CAAC,IAAAwK,IAAAA,EAAAvf,UAAAuC,OAhBoBid,MAAY1P,MAAAyP,EAAAA,EAAAA,OAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAZD,EAAYC,EAAAzf,GAAAA,UAAAyf,GAkBjC,GAAID,EAAajd,OAChB,OAAO4Z,GAAW7c,OAAOkgB,GAG1B,MAAM,IAAIte,UAAW,GAAEwY,+BACxB,CAUA,mBAAOgG,CAAcC,EAAKC,GACzB,IACIlG,EAAOiF,EADPkB,EAAY3V,GAAKyV,GA4BrB,GAzBkB,WAAdE,EACCF,EAAIzS,SAAS,MAEfwM,EAAOiF,GAASgB,EAAIle,MAAM,MAI1BiY,EAAOiF,GAAS,CAAA,CAAGgB,GAGb7P,MAAMkE,QAAQ2L,IACrBjG,EAAOiF,GAASgB,GAIjBjG,EAAQiG,EAAIjG,MACZiF,EAAQgB,EAAIG,SAGbpG,EAAQyC,GAAW7c,IAAIoa,GAElBA,IACJA,EAAQkG,IAGJlG,EACJ,MAAM,IAAIxY,UAAW,uCAAsCye,4EAK5D,GAFAE,EAAY3V,GAAKyU,GAEC,WAAdkB,GAAwC,WAAdA,GAA0BlB,GAAS,EAAG,CAEnE,IAAI9D,EAAOzb,OAAO0a,QAAQJ,EAAME,QAAQ+E,GAExC,GAAI9D,EACH,MAAO,CAACnB,QAAO3T,GAAI8U,EAAK,GAAIxO,MAAOsS,KAAU9D,EAAK,GAEpD,CAEAnB,EAAQyC,GAAW7c,IAAIoa,GAEvB,IAAIqG,EAAkBpB,EAAM/P,cAExBvB,EAAI,EACR,IAAK,IAAItH,KAAM2T,EAAME,OAAQ,CAAA,IAAAoG,EAC5B,IAAInF,EAAOnB,EAAME,OAAO7T,GAExB,GAAIA,EAAG6I,gBAAkBmR,IAA4B,QAATC,EAAAnF,EAAKpU,YAAI,IAAAuZ,OAAA,EAATA,EAAWpR,iBAAkBmR,EACxE,MAAO,CAACrG,QAAO3T,KAAIsG,MAAOgB,KAAMwN,GAGjCxN,GACD,CAEA,MAAM,IAAInM,UAAW,OAAMyd,0BAA8BjF,EAAMjT,8BAA8BrH,OAAO6J,KAAKyQ,EAAME,QAAQjP,KAAK,QAC7H,CAEAyU,sBAAwB,CACvBlV,KAAM,YACNzD,KAAM,SAIR,SAAS4X,GAAS3E,GACjB,IAAI3E,EAAM,CAAC2E,GAEX,IAAK,IAAIuG,EAAIvG,EAAOuG,EAAIA,EAAE3I,MACzBvC,EAAIzP,KAAK2a,GAGV,OAAOlL,CACR,CAEA,SAAS8J,GAAelF,GAAuB,IAAfC,OAACA,GAAO5Z,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAC1C,GAAI2Z,EAAOC,SAAWD,EAAOK,aAAc,CAC1CL,EAAOzP,OAAPyP,EAAOzP,KAAS,YAChByP,EAAOlT,OAAPkT,EAAOlT,KAAS,SAGhBkT,EAAOK,aAAiCL,EAAOC,OPlO3BpF,KAAIwF,GACjBA,EAAavY,MAAM,KAAK+S,KAAItK,IAElC,IAAIqQ,GADJrQ,EAAOA,EAAK4Q,QACKpY,MAAM,6CAEvB,GAAI6X,EAAO,CACV,IAAIxF,EAAM,IAAIhS,OAAOwX,EAAM,IAE3B,OADAxF,EAAIwF,MAAQ,EAAEA,EAAM,IAAKA,EAAM,IACxBxF,CACR,CAEA,OAAO7K,CAAI,MOyNZ,IAAIgW,EAAe9gB,OAAO0a,QAAQF,GAAQpF,KAAI,CAAA2L,EAAkB9S,KAAM,IAAtBtH,EAAIgU,GAAUoG,EAEzDC,EAAazG,EAAOK,aAAa3M,GAAG,GAEpCiN,EAAYP,EAAUQ,OAASR,EAAUU,SACzCD,EAAU4F,EAAW7F,MAAO8F,EAAS,GAWzC,MARkB,gBAAdD,GACH5F,EAAU,CAAC,EAAG,KACd6F,EAAS,KAEa,WAAdD,IACRC,EAAS,OAGF,CAAC/F,YAAWE,UAAS6F,SAAO,IAGrC1G,EAAO2G,gBAAkB,CAAC1G,EAAQtE,IAC1BsE,EAAOpF,KAAI,CAACQ,EAAG3H,KACrB,IAAIiN,UAACA,EAASE,QAAEA,EAAO6F,OAAEA,GAAUH,EAAa7S,GAQhD,OANIiN,GAAaE,IAChBxF,EAAI+B,GAASuD,EAAWE,EAASxF,IAGlCA,EAAII,GAAgBJ,EAAG,CAACM,YAAWC,KAAM8K,GAEjC,GAGX,CAEA,OAAO1G,CACR,CCrbA,IAAe4G,GAAA,IAAIpE,GAAW,CAC7BpW,GAAI,UACJU,KAAM,UACNmT,OAAQ,CACP9N,EAAG,CAACrF,KAAM,KACV+Z,EAAG,CAAC/Z,KAAM,KACVga,EAAG,CAACha,KAAM,MAEXqX,MAAO,MACPb,QAAS,CACRlC,MAAO,CACNkB,IAAK,CAAC,UAAW,SAGnB0B,QAAS,CAAC,SCPI,MAAM+C,WAAsBvE,GAU1C/Q,WAAAA,CAAaJ,GAAS,IAAA2V,EAsBqBC,EAAAC,GArBrC7V,EAAQ4O,SACZ5O,EAAQ4O,OAAS,CAChBkH,EAAG,CACFvG,MAAO,CAAC,EAAG,GACX9T,KAAM,OAEPsa,EAAG,CACFxG,MAAO,CAAC,EAAG,GACX9T,KAAM,SAEP6M,EAAG,CACFiH,MAAO,CAAC,EAAG,GACX9T,KAAM,UAKJuE,EAAQsM,OACZtM,EAAQsM,KAAO0J,IAGZhW,EAAQiW,SAAWjW,EAAQkW,aAChBN,QAAdA,EAAA5V,EAAQ6S,cAAM+C,IAAAA,IAAd5V,EAAQ6S,OAAWsD,IAClB,IAAIC,EAAMhN,GAAiBpJ,EAAQiW,QAASE,GAO5C,OALItiB,KAAKif,QAAUjf,KAAKyY,KAAKwG,QAE5BsD,EAAM7I,GAAM1Z,KAAKif,MAAOjf,KAAKyY,KAAKwG,MAAOsD,IAGnCA,CAAG,GAGKP,QAAhBA,EAAA7V,EAAQ4S,gBAAQiD,IAAAA,IAAhB7V,EAAQ4S,SAAawD,IACpBA,EAAM7I,GAAM1Z,KAAKyY,KAAKwG,MAAOjf,KAAKif,MAAOsD,GAClChN,GAAiBpJ,EAAQkW,UAAWE,MAI7BT,QAAhBA,EAAA3V,EAAQoT,gBAAQuC,IAAAA,IAAhB3V,EAAQoT,SAAa,WAErBiD,MAAMrW,EACP,ECrDc,SAASsW,GAAQvG,EAAOrB,GAGtC,OAFAqB,EAAQsC,GAAStC,IAEZrB,GAASqB,EAAMrB,MAAM8E,OAAO9E,GAEzBqB,EAAMnB,OAAO9Y,SAGrB4Y,EAAQyC,GAAW7c,IAAIoa,IACV1C,KAAK+D,EACnB,CCfe,SAASzb,GAAKyb,EAAOwG,GACnCxG,EAAQsC,GAAStC,GAEjB,IAAIrB,MAACA,EAAKrN,MAAEA,GAAS8P,GAAWuD,aAAa6B,EAAMxG,EAAMrB,OAEzD,OADa4H,GAAOvG,EAAOrB,GACbrN,EACf,CCPe,SAASmV,GAAQzG,EAAOrB,EAAOE,GAK7C,OAJAmB,EAAQsC,GAAStC,GAEjBrB,EAAQyC,GAAW7c,IAAIoa,GACvBqB,EAAMnB,OAASF,EAAMzC,GAAG8D,EAAMrB,MAAOE,GAC9BmB,CACR,CCDe,SAAS3R,GAAK2R,EAAOwG,EAAMphB,GAGzC,GAFA4a,EAAQsC,GAAStC,GAEQ,IAArB/a,UAAUuC,QAAuC,WAAvB2H,GAAKlK,UAAU,IAAkB,CAE9D,IAAI8I,EAAS9I,UAAU,GACvB,IAAK,IAAIyU,KAAK3L,EACbM,GAAI2R,EAAOtG,EAAG3L,EAAO2L,GAEvB,KACK,CACiB,mBAAVtU,IACVA,EAAQA,EAAMb,GAAIyb,EAAOwG,KAG1B,IAAI7H,MAACA,EAAKrN,MAAEA,GAAS8P,GAAWuD,aAAa6B,EAAMxG,EAAMrB,OACrDE,EAAS0H,GAAOvG,EAAOrB,GAC3BE,EAAOvN,GAASlM,EAChBqhB,GAAOzG,EAAOrB,EAAOE,EACtB,CAEA,OAAOmB,CACR,CDnBAyG,GAAOC,QAAU,QCqBjBrY,GAAIqY,QAAU,QC5Bd,IAAeC,GAAA,IAAIvF,GAAW,CAC7BpW,GAAI,UACJU,KAAM,UACNqX,MAAO,MACPxG,KAAM0J,GACNpD,SAAUhE,GAAUrB,GAAMyI,GAAQlD,MAAO,MAAOlE,GAChDiE,OAAQjE,GAAUrB,GAAM,MAAOyI,GAAQlD,MAAOlE,KCL/C,MACM+H,GAAK,GAAK,IACVC,GAAI,MAAQ,GAElB,IAAI9D,GAAQ3F,GAAOC,IAEnB,IAAeyJ,GAAA,IAAI1F,GAAW,CAC7BpW,GAAI,MACJU,KAAM,MACNmT,OAAQ,CACPkI,EAAG,CACFrH,SAAU,CAAC,EAAG,KACdhU,KAAM,aAEPW,EAAG,CACFqT,SAAU,EAAE,IAAK,MAElBnH,EAAG,CACFmH,SAAU,EAAE,IAAK,aAMnBqD,GAEAxG,KAAMyK,GAGNnE,QAAAA,CAAUlF,GAET,IAGIhR,EAHMgR,EAAIlE,KAAI,CAACrU,EAAOkN,IAAMlN,EAAQ2d,GAAMzQ,KAGlCmH,KAAIrU,GAASA,EAlCjB,oBAkC6B3B,KAAKwjB,KAAK7hB,IAAUyhB,GAAIzhB,EAAQ,IAAM,MAE3E,MAAO,CACL,IAAMuH,EAAE,GAAM,GACf,KAAOA,EAAE,GAAKA,EAAE,IAChB,KAAOA,EAAE,GAAKA,EAAE,IAEjB,EAIDmW,MAAAA,CAAQoE,GAEP,IAAIva,EAAI,GAaR,OAZAA,EAAE,IAAMua,EAAI,GAAK,IAAM,IACvBva,EAAE,GAAKua,EAAI,GAAK,IAAMva,EAAE,GACxBA,EAAE,GAAKA,EAAE,GAAKua,EAAI,GAAK,IAGb,CACTva,EAAE,GAAOia,GAAKnjB,KAAK0jB,IAAIxa,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAMka,GACrEK,EAAI,GAAK,EAAKzjB,KAAK0jB,KAAKD,EAAI,GAAK,IAAM,IAAK,GAAKA,EAAI,GAAKL,GAC1Dla,EAAE,GAAOia,GAAKnjB,KAAK0jB,IAAIxa,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAMka,IAI3DpN,KAAI,CAACrU,EAAOkN,IAAMlN,EAAQ2d,GAAMzQ,IAC3C,EAED4P,QAAS,CACR4E,IAAO,CACNjI,OAAQ,CAAC,0BAA2B,gCAAiC,qCCtEjE,SAASuI,GAAWC,GAC1B,OAASA,EAAQ,IAAO,KAAO,GAChC,CCEA,IAAeC,GAAA,IAAIlG,GAAW,CAC7BpW,GAAI,MACJU,KAAM,MACNmT,OAAQ,CACPkI,EAAG,CACFrH,SAAU,CAAC,EAAG,KACdhU,KAAM,aAEPuO,EAAG,CACFyF,SAAU,CAAC,EAAG,KACdhU,KAAM,UAEP6b,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,QAIR6Q,KAAM2K,GACNrE,QAAAA,CAAUqE,GAET,IACIM,GADCC,EAAGpb,EAAGkM,GAAK2O,EAWhB,OANCM,EADG/jB,KAAKmX,IAAIvO,GAFH,KAEa5I,KAAKmX,IAAIrC,GAFtB,IAGHmI,IAGmB,IAAnBjd,KAAKikB,MAAMnP,EAAGlM,GAAW5I,KAAKkY,GAG9B,CACN8L,EACAhkB,KAAKkkB,KAAKtb,GAAK,EAAIkM,GAAK,GACxBqP,GAAeJ,GAEhB,EACD1E,MAAAA,CAAQ+E,GAEP,IAAKC,EAAWC,EAAQC,GAAOH,EAS/B,OAPIE,EAAS,IACZA,EAAS,GAGN/M,MAAMgN,KACTA,EAAM,GAEA,CACNF,EACAC,EAAStkB,KAAKwkB,IAAID,EAAMvkB,KAAKkY,GAAK,KAClCoM,EAAStkB,KAAKykB,IAAIF,EAAMvkB,KAAKkY,GAAK,KAEnC,EAEDuG,QAAS,CACRoF,IAAO,CACNzI,OAAQ,CAAC,0BAA2B,0BAA2B,0BClDlE,MAAMsJ,GAAU,IAAM,EAChBC,GAAI3kB,KAAKkY,GACT0M,GAAM,IAAMD,GACZE,GAAMF,GAAI,IAEhB,SAASG,GAAMxX,GAGd,MAAMyX,EAAKzX,EAAIA,EAGf,OAFWyX,EAAKA,EAAKA,EAAKzX,CAG3B,CAEe,SAAA0X,GAAUzI,EAAO0I,GAAuC,IAA/BC,GAACA,EAAK,EAACC,GAAEA,EAAK,EAACC,GAAEA,EAAK,GAAE5jB,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,IACjE+a,EAAO0I,GAAUpG,GAAS,CAACtC,EAAO0I,IAanC,IAAKI,EAAIC,EAAIC,GAAMlC,GAAI7K,KAAK+D,GACxBiJ,EAAK3B,GAAIrL,KAAK6K,GAAK,CAACgC,EAAIC,EAAIC,IAAK,IAChCE,EAAIC,EAAIC,GAAMtC,GAAI7K,KAAKyM,GACxBW,EAAK/B,GAAIrL,KAAK6K,GAAK,CAACoC,EAAIC,EAAIC,IAAK,GAMjCH,EAAK,IACRA,EAAK,GAEFI,EAAK,IACRA,EAAK,GAGN,IAIIC,EAAKf,IAJGU,EAAKI,GAAM,GAMnBE,EAAI,IAAO,EAAI9lB,KAAKkkB,KAAK2B,GAAMA,EAAKnB,MAIpCqB,GAAU,EAAID,GAAKR,EACnBU,GAAU,EAAIF,GAAKJ,EAGnBO,EAASjmB,KAAKkkB,KAAK6B,GAAU,EAAIR,GAAM,GACvCW,EAASlmB,KAAKkkB,KAAK8B,GAAU,EAAIL,GAAM,GAKvCQ,EAAiB,IAAXJ,GAAuB,IAAPR,EAAY,EAAIvlB,KAAKikB,MAAMsB,EAAIQ,GACrDK,EAAiB,IAAXJ,GAAuB,IAAPL,EAAY,EAAI3lB,KAAKikB,MAAM0B,EAAIK,GAErDG,EAAK,IACRA,GAAM,EAAIxB,IAEPyB,EAAK,IACRA,GAAM,EAAIzB,IAGXwB,GAAMvB,GACNwB,GAAMxB,GAGN,IAOIyB,EAPAC,EAAKb,EAAKJ,EACVkB,EAAKL,EAASD,EAGdO,EAAQJ,EAAKD,EACbM,EAAON,EAAKC,EACZM,EAAO1mB,KAAKmX,IAAIqP,GAGhBP,EAASC,GAAW,EACvBG,EAAK,EAEGK,GAAQ,IAChBL,EAAKG,EAEGA,EAAQ,IAChBH,EAAKG,EAAQ,IAELA,GAAS,IACjBH,EAAKG,EAAQ,IAGbpM,GAASM,KAAK,gCAIf,IAUIiM,EAVAC,EAAK,EAAI5mB,KAAKkkB,KAAKgC,EAASD,GAAUjmB,KAAKykB,IAAI4B,EAAKxB,GAAM,GAG1DgC,GAASxB,EAAKI,GAAM,EACpBqB,GAASb,EAASC,GAAU,EAC5Ba,EAASjC,GAAKgC,GAOjBH,EADGV,EAASC,GAAW,EACfO,EAEAC,GAAQ,IACRD,EAAO,EAEPA,EAAO,KACNA,EAAO,KAAO,GAGdA,EAAO,KAAO,EAQxB,IAAIO,GAAOH,EAAQ,KAAO,EACtBI,EAAK,EAAM,KAAQD,EAAOhnB,KAAKkkB,KAAK,GAAK8C,GAGzCE,EAAK,EAAI,KAAQJ,EAGjBK,EAAI,EACRA,GAAM,IAAOnnB,KAAKwkB,KAAUmC,EAAQ,IAAO9B,IAC3CsC,GAAM,IAAOnnB,KAAKwkB,IAAM,EAAImC,EAAe9B,IAC3CsC,GAAM,IAAOnnB,KAAKwkB,KAAM,EAAImC,EAAS,GAAM9B,IAC3CsC,GAAM,GAAOnnB,KAAKwkB,KAAM,EAAImC,EAAS,IAAM9B,IAI3C,IAAIuC,EAAK,EAAI,KAAQN,EAAQK,EAMzBE,EAAK,GAAKrnB,KAAK+Y,KAAK,IAAO4N,EAAQ,KAAO,KAAO,GACjDW,EAAK,EAAItnB,KAAKkkB,KAAK6C,GAAUA,EAASrC,KAItC6C,GAAMjB,GAAMpB,EAAK+B,KAAQ,EAI7B,OAHAM,IAAOhB,GAAMpB,EAAK+B,KAAQ,EAC1BK,IAAOX,GAAMxB,EAAKgC,KAAQ,EAC1BG,IANU,EAAIvnB,KAAKykB,IAAI,EAAI4C,EAAKxC,IAAOyC,GAM3Bf,GAAMpB,EAAK+B,KAAQN,GAAMxB,EAAKgC,IACnCpnB,KAAKkkB,KAAKqD,EAElB,CC5KA,MAAMC,GAAa,CAClB,CAAE,iBAAoB,mBAAqB,mBAC3C,CAAE,kBAAoB,kBAAqB,mBAC3C,CAAE,kBAAoB,kBAAqB,oBAGtCC,GAAa,CAClB,CAAG,oBAAqB,kBAAqB,mBAC7C,EAAG,kBAAqB,mBAAqB,mBAC7C,EAAG,mBAAqB,kBAAqB,qBAExCC,GAAa,CAClB,CAAE,iBAAqB,mBAAqB,mBAC5C,CAAE,oBAAqB,iBAAqB,kBAC5C,CAAE,kBAAqB,mBAAqB,oBAGvCC,GAAa,CAClB,CAAE,EAAqB,kBAAqB,mBAC5C,CAAE,GAAqB,mBAAqB,mBAC5C,CAAE,GAAqB,mBAAqB,qBAG7C,IAAeC,GAAA,IAAIjK,GAAW,CAC7BpW,GAAI,QACJU,KAAM,QACNmT,OAAQ,CACPkI,EAAG,CACFrH,SAAU,CAAC,EAAG,GACdhU,KAAM,aAEPW,EAAG,CACFqT,SAAU,EAAE,GAAK,KAElBnH,EAAG,CACFmH,SAAU,EAAE,GAAK,MAKnBqD,MAAO,MACPxG,KAAM0J,GACNpD,QAAAA,CAAUlF,GAET,IAGI2N,EAHMjS,GAAiB4R,GAAYtN,GAGxBlE,KAAI5P,GAAOpG,KAAKwjB,KAAKpd,KAEpC,OAAOwP,GAAiB8R,GAAYG,EAEpC,EACDxI,MAAAA,CAAQuI,GAEP,IAGIE,EAHOlS,GAAiB+R,GAAYC,GAGzB5R,KAAI5P,GAAOA,GAAO,IAEjC,OAAOwP,GAAiB6R,GAAYK,EACpC,EAEDrJ,QAAS,CACRsJ,MAAS,CACR3M,OAAQ,CAAC,0BAA2B,gCAAiC,qCChEzD,SAAA4M,GAAUzL,EAAO0I,IAC9B1I,EAAO0I,GAAUpG,GAAS,CAACtC,EAAO0I,IAKnC,IAAKI,EAAIC,EAAIC,GAAMwC,GAAMvP,KAAK+D,IACzBkJ,EAAIC,EAAIC,GAAMoC,GAAMvP,KAAKyM,GAC1BqB,EAAKjB,EAAKI,EACVwC,EAAK3C,EAAKI,EACVwC,EAAK3C,EAAKI,EACd,OAAO3lB,KAAKkkB,KAAKoC,GAAM,EAAI2B,GAAM,EAAIC,GAAM,EAC5C,CCfA,MAAMpJ,GAAI,MAMK,SAASa,GAASpD,EAAOrB,GAA2B,IAApB6E,QAACA,EAAUjB,IAAEtd,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAC9D+a,EAAQsC,GAAStC,GAEZrB,IACJA,EAAQqB,EAAMrB,OAGfA,EAAQyC,GAAW7c,IAAIoa,GACvB,IAAIE,EAASmB,EAAMnB,OAMnB,OAJIF,IAAUqB,EAAMrB,QACnBE,EAASF,EAAM1C,KAAK+D,IAGdrB,EAAMyE,QAAQvE,EAAQ,CAAC2E,WAC/B,CCxBe,SAASoI,GAAO5L,GAC9B,MAAO,CACNrB,MAAOqB,EAAMrB,MACbE,OAAQmB,EAAMnB,OAAO9Y,QACrB6a,MAAOZ,EAAMY,MAEf,CCDe,SAASiL,GAAUC,EAAQC,GAAuB,IAAfpN,EAAK1Z,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,MACzD0Z,EAAQyC,GAAW7c,IAAIoa,GAGvB,IAAIqN,EAAUrN,EAAM1C,KAAK6P,GACrBG,EAAUtN,EAAM1C,KAAK8P,GAEzB,OAAOtoB,KAAKkkB,KAAKqE,EAAQE,QAAO,CAACC,EAAKC,EAAI9Z,KACzC,IAAI+Z,EAAKJ,EAAQ3Z,GACjB,OAAI0I,MAAMoR,IAAOpR,MAAMqR,GACfF,EAGDA,GAAOE,EAAKD,IAAO,CAAC,GACzB,GACJ,CCjBe,SAASE,GAAUtM,EAAO0I,GAExC,OAAOmD,GAAS7L,EAAO0I,EAAQ,MAChC,CCMA,MACMJ,GADI7kB,KAAKkY,GACC,IAED,SAAA4Q,GAAUvM,EAAO0I,GAA6B,IAArB3B,EAACA,EAAI,EAAC9M,EAAEA,EAAI,GAAEhV,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,IACvD+a,EAAO0I,GAAUpG,GAAS,CAACtC,EAAO0I,IAUnC,IAAKI,EAAIC,EAAIC,GAAMlC,GAAI7K,KAAK+D,KACrBiJ,EAAIuD,GAAMlF,GAAIrL,KAAK6K,GAAK,CAACgC,EAAIC,EAAIC,KACnCE,EAAIC,EAAIC,GAAMtC,GAAI7K,KAAKyM,GACxBW,EAAK/B,GAAIrL,KAAK6K,GAAK,CAACoC,EAAIC,EAAIC,IAAK,GAYjCH,EAAK,IACRA,EAAK,GAEFI,EAAK,IACRA,EAAK,GAON,IAAIU,EAAKjB,EAAKI,EACVc,EAAKf,EAAKI,EAOVoD,GALK1D,EAAKI,IAKE,GAJPH,EAAKI,IAIc,EAAMY,GAAM,EAmBpCU,EAAK,KACL5B,GAAM,KACT4B,EAAM,QAAW5B,GAAO,EAAI,OAAUA,IAIvC,IAGI8B,EAHAD,EAAO,MAAS1B,GAAO,EAAI,MAASA,GAAO,KAI3ClO,OAAOC,MAAMwR,KAChBA,EAAK,GAIL5B,EADG4B,GAAM,KAAOA,GAAM,IAClB,IAAO/oB,KAAKmX,IAAI,GAAMnX,KAAKwkB,KAAKuE,EAAK,KAAOlE,KAG5C,IAAO7kB,KAAKmX,IAAI,GAAMnX,KAAKwkB,KAAKuE,EAAK,IAAMlE,KAKhD,IAAIoE,EAAKjpB,KAAK0jB,IAAI8B,EAAI,GAClB0D,EAAIlpB,KAAKkkB,KAAK+E,GAAMA,EAAK,OAIzB1B,GAAMjB,GAAMhD,EAAI2D,KAAQ,EAI5B,OAHAM,IAAOhB,GAAM/P,EAAI0Q,KAAQ,EACzBK,GAAOyB,GALE9B,GAAOgC,EAAI/B,EAAK,EAAI+B,KAKV,EAEZlpB,KAAKkkB,KAAKqD,EAElB,CC5GA,IAAe4B,GAAA,IAAIxL,GAAW,CAK7BpW,GAAI,cACJiX,MAAO,gBACPvW,KAAM,mBACNmT,OAAQ,CACP9N,EAAG,CACF2O,SAAU,CAAC,EAAG,QACdhU,KAAM,MAEP+Z,EAAG,CACF/F,SAAU,CAAC,EAAG,KACdhU,KAAM,MAEPga,EAAG,CACFhG,SAAU,CAAC,EAAG,SACdhU,KAAM,OAIR6Q,KAAM0J,GACNpD,SAAUlF,GAIFA,EAAIlE,KAAKoT,GAAKppB,KAAK0N,IA9BjB,IA8BqB0b,EAAQ,KAEvC/J,OAAQgK,GAEAA,EAAOrT,KAAIoT,GAAKppB,KAAK0N,IAAI0b,EAlCvB,IAkC+B,OCjC1C,MAAMtU,GAAI,KACJyN,GAAI,IACJhV,GAAI,KAAQ,MAEZob,GAAK,SACLC,GAAK,KAAQ,IACbU,GAAK,QAELC,GAAQ,IAAW,IAAM,MACzBtQ,IAAK,IACLuQ,GAAK,sBAELC,GAAc,CACnB,CAAG,UAAY,QAAW,SAC1B,EAAG,OAAY,SAAW,UAC1B,EAAG,SAAY,MAAW,WAGrBC,GAAc,CACnB,CAAG,oBAAsB,mBAAqB,kBAC9C,CAAG,mBAAsB,mBAAqB,oBAC9C,EAAG,oBAAsB,kBAAqB,qBAEzCC,GAAc,CACnB,CAAG,GAAW,GAAW,GACzB,CAAG,OAAW,SAAW,SACzB,CAAG,QAAW,UAAW,WAGpBC,GAAc,CACnB,CAAE,EAAqB,kBAAsB,oBAC7C,CAAE,mBAAqB,mBAAsB,oBAC7C,CAAE,mBAAqB,oBAAsB,oBAG9C,IAAeC,GAAA,IAAIlM,GAAW,CAC7BpW,GAAI,SACJU,KAAM,SACNmT,OAAQ,CACP0O,GAAI,CACH7N,SAAU,CAAC,EAAG,GACdhU,KAAM,MAEP8hB,GAAI,CACH9N,SAAU,EAAE,GAAK,KAElB+N,GAAI,CACH/N,SAAU,EAAE,GAAK,MAInBnD,KAAMqQ,GACN/J,QAAAA,CAAUlF,GAMT,IAAM+P,EAAIC,EAAIC,GAAOjQ,EAUjBkQ,EAHMxU,GAAiB6T,GAAa,CAJ9B3U,GAAImV,GAAQnV,GAAI,GAAKqV,EACrB5H,GAAI2H,GAAQ3H,GAAI,GAAK0H,EAGmBE,IAGlCnU,KAAK,SAAU5P,GAI9B,QAHUuiB,GAAMC,IAAOxiB,EAAM,MAAUmH,KAC3B,EAAK+b,IAAOljB,EAAM,MAAUmH,MA/DjC,kBAkER,KAGM8c,EAAIN,EAAIC,GAAMpU,GAAiB+T,GAAaS,GAIlD,MAAO,EADI,EAAInR,IAAKoR,GAAO,EAAKpR,GAAIoR,GAAOb,GAC/BO,EAAIC,EAChB,EACD3K,MAAAA,CAAQwK,GACP,IAAKS,EAAIP,EAAIC,GAAMH,EAOf/B,EAHQlS,GAAiBgU,GAAa,EAHhCU,EAAKd,KAAO,EAAIvQ,GAAIA,IAAKqR,EAAKd,KAGQO,EAAIC,IAGpChU,KAAI,SAAU5P,GAK7B,OAFQ,MAFGuiB,GAAMviB,GAAOmjB,KACXD,GAAMljB,GAAOmjB,GAASX,MAzFxB,iBA6FZ,KAGM2B,EAAIC,EAAIL,GAAOvU,GAAiB8T,GAAa5B,GAG/CmC,GAAMM,GAAOzV,GAAI,GAAKqV,GAAOrV,GAEjC,MAAO,CAAEmV,GADCO,GAAOjI,GAAI,GAAK0H,GAAO1H,GAChB4H,EACjB,EAED1L,QAAS,CAERlC,MAAS,CACRnB,OAAQ,CAAC,0BAA2B,gCAAiC,qCC9GzDqP,GAAA,IAAI9M,GAAW,CAC7BpW,GAAI,SACJU,KAAM,SACNmT,OAAQ,CACP0O,GAAI,CACH7N,SAAU,CAAC,EAAG,GACdhU,KAAM,MAEPyiB,GAAI,CACHzO,SAAU,CAAC,EAAG,GACdhU,KAAM,UAEP0iB,GAAI,CACH1O,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,QAIR6Q,KAAM+Q,GACNzK,QAAAA,CAAUwL,GAET,IACI7G,GADCuG,EAAIP,EAAIC,GAAMY,EAEnB,MAAM9L,EAAI,KASV,OANCiF,EADG/jB,KAAKmX,IAAI4S,GAAMjL,GAAK9e,KAAKmX,IAAI6S,GAAMlL,EAChC7B,IAGqB,IAArBjd,KAAKikB,MAAM+F,EAAID,GAAY/pB,KAAKkY,GAGhC,CACNoS,EACAtqB,KAAKkkB,KAAK6F,GAAM,EAAIC,GAAM,GAC1B7F,GAAeJ,GAEhB,EACD1E,OAAQoL,GAGA,CACNA,EAAO,GACPA,EAAO,GAAKzqB,KAAKwkB,IAAIiG,EAAO,GAAKzqB,KAAKkY,GAAK,KAC3CuS,EAAO,GAAKzqB,KAAKykB,IAAIgG,EAAO,GAAKzqB,KAAKkY,GAAK,QCvC/B,SAAA2S,GAAUtO,EAAO0I,IAC9B1I,EAAO0I,GAAUpG,GAAS,CAACtC,EAAO0I,IAKnC,IAAK6F,EAAKC,EAAKC,GAAOP,GAAOjS,KAAK+D,IAC7B0O,EAAKC,EAAKC,GAAOV,GAAOjS,KAAKyM,GAI9BmG,EAAKN,EAAMG,EACX1E,EAAKwE,EAAMG,EAGV5T,OAAOC,MAAMyT,IAAU1T,OAAOC,MAAM4T,IAExCH,EAAM,EACNG,EAAM,GAEE7T,OAAOC,MAAMyT,GAErBA,EAAMG,EAEE7T,OAAOC,MAAM4T,KACrBA,EAAMH,GAGP,IAAI3E,EAAK2E,EAAMG,EACXvE,EAAK,EAAI5mB,KAAKkkB,KAAK6G,EAAMG,GAAOlrB,KAAKykB,IAAK4B,EAAK,GAAMrmB,KAAKkY,GAAK,MAEnE,OAAOlY,KAAKkkB,KAAKkH,GAAM,EAAI7E,GAAM,EAAIK,GAAM,EAC5C,CCtCA,MAAM+B,GAAK,SACLC,GAAK,KAAO,IACZU,GAAK,QACL+B,GAAK,KAAO,MACZC,GAAK,KAAO,GACZC,GAAM,MAAQ,KACdC,GAAM,GAAK,KAIXhE,GAAa,CAClB,CAAG,kBAAqB,mBAAqB,kBAC7C,EAAG,kBAAqB,kBAAqB,mBAC7C,CAAG,kBAAqB,kBAAqB,oBAiBxCiE,GAAa,CAClB,CAAG,GAAe,GAAmB,GACrC,CAAG,KAAO,MAAO,MAAQ,KAAO,KAAO,MACvC,CAAE,MAAQ,MAAO,MAAQ,MAAQ,IAAM,OAIlCC,GAAa,CAClB,CAAE,kBAAqB,kBAAqB,kBAC5C,CAAE,mBAAqB,mBAAqB,mBAC5C,CAAE,kBAAqB,mBAAqB,oBASvCjE,GAAa,CAClB,CAAG,oBAAqB,mBAAqB,mBAC7C,CAAG,kBAAqB,mBAAqB,mBAC7C,EAAG,mBAAqB,kBAAqB,qBAU9C,IAAekE,GAAA,IAAIhO,GAAW,CAC7BpW,GAAI,QACJU,KAAM,QAUNmT,OAAQ,CACPvM,EAAG,CACFoN,SAAU,CAAC,EAAG,GACdhU,KAAM,KAEP2jB,GAAI,CACH3P,SAAU,EAAE,GAAK,IACjBhU,KAAM,MAEP4jB,GAAI,CACH5P,SAAU,EAAE,GAAK,IACjBhU,KAAM,OAIR6Q,KAAMqQ,GACN/J,SAAUlF,GAaX,SAAqB4N,GAGpB,IAAIsC,EAAQtC,EAAI9R,KAAK,SAAU5P,GAI9B,QAHUuiB,GAAMC,IAAOxiB,EAAM,MAAUilB,KAC3B,EAAK/B,IAAOljB,EAAM,MAAUilB,MAEfC,EAC1B,IAGA,OAAO1V,GAAiB6V,GAAYrB,EACrC,CArBS0B,CAFGlW,GAAiB4R,GAAYtN,IAIxCmF,MAAAA,CAAQ0M,GACP,IAAIjE,EAoBN,SAAqBiE,GACpB,IAAI3B,EAAQxU,GAAiB8V,GAAYK,GAGrCjE,EAAMsC,EAAMpU,KAAK,SAAU5P,GAG9B,OAAO,KAFIpG,KAAK0N,IAAKtH,GAAOolB,GAAO7C,GAAI,IAC1BC,GAAMU,GAAMljB,GAAOolB,MACCD,EAClC,IAEA,OAAOzD,CACR,CA/BYkE,CAAWD,GAErB,OAAOnW,GAAiB6R,GAAYK,EACrC,ICjGc,SAAAmE,GAAU1P,EAAO0I,IAC9B1I,EAAO0I,GAAUpG,GAAS,CAACtC,EAAO0I,IAOnC,IAAMiH,EAAIC,EAAIC,GAAOT,GAAMnT,KAAK+D,IAC1B8P,EAAIC,EAAIC,GAAOZ,GAAMnT,KAAKyM,GAMhC,OAAO,IAAMjlB,KAAKkkB,MAAMgI,EAAKG,IAAO,EAAK,KAAQF,EAAKG,IAAO,GAAMF,EAAKG,IAAO,EAChF,CCjBA,MAAMjN,GAAQ3F,GAAOE,IACf2S,GAAc,IACdC,GAAiB,EAAID,GACrBE,GAAM,EAAI1sB,KAAKkY,GAEfyU,GAAQ,CACb,CAAG,QAAW,SAAW,SACzB,EAAG,QAAW,SAAW,SACzB,EAAG,QAAW,QAAW,UAGpBC,GAAW,CAChB,CAAC,oBAAqB,mBAAoB,oBAC1C,CAAC,mBAAqB,mBAAqB,qBAC3C,EAAE,qBAAuB,mBAAqB,qBAGzCvB,GAAK,CACV,CAAC,IAAO,IAAO,KACf,CAAC,KAAQ,KAAQ,KACjB,CAAC,KAAQ,KAAQ,OAGZwB,GAAc,CACnBC,KAAM,CAAC,GAAK,KAAO,IACnBC,IAAK,CAAC,GAAK,IAAM,IACjBC,QAAS,CAAC,EAAG,IAAM,IAGdC,GAAa,CAElBnJ,EAAG,CAAC,MAAO,GAAO,OAAQ,OAAQ,QAClCoJ,EAAG,CAAC,GAAK,GAAK,EAAK,IAAK,IACxBC,EAAG,CAAC,EAAK,IAAO,IAAO,IAAO,MAGzBC,GAAU,IAAMptB,KAAKkY,GACrBmV,GAAUrtB,KAAKkY,GAAK,IAEnB,SAAS6B,GAAOqB,EAAQkS,GAC9B,MAAMC,EAAOnS,EAAOpF,KAAIQ,IACvB,MAAMlJ,EAAIuL,GAAKyU,EAAKttB,KAAKmX,IAAIX,GAAK,IAAMgW,IACxC,OAAO,IAAM7T,GAASrL,EAAGkJ,IAAMlJ,EAAI,MAAM,IAE1C,OAAOigB,CACR,CAsCO,SAASC,GACfC,EACAC,EACAC,EACAC,EACAC,GAGA,MAAMrU,EAAM,CAAA,EAEZA,EAAIqU,YAAcA,EAClBrU,EAAIiU,SAAWA,EACfjU,EAAIoU,SAAWA,EACf,MAAME,EAAOL,EAASzX,KAAIQ,GACd,IAAJA,IAIRgD,EAAIuU,GAAKL,EAETlU,EAAIwU,GAAKL,EAET,MAAMM,EAAKH,EAAK,GAGVI,EAAOtY,GAAiB+W,GAAOmB,GAI/B5kB,GADN0kB,EAAWf,GAAYrT,EAAIoU,WACR,GACnBpU,EAAIhD,EAAIoX,EAAS,GACjBpU,EAAI2U,GAAKP,EAAS,GAElB,MACMQ,GADI,GAAK,EAAI5U,EAAIuU,GAAK,KACZ,EAGhBvU,EAAI8T,GAAMc,EAAK5U,EAAIuU,GAAK,IAAO,EAAIK,IAAO,EAAIA,GAAMpuB,KAAKwjB,KAAK,EAAIhK,EAAIuU,IACtEvU,EAAI6U,OAAS7U,EAAI8T,IAAM,IAEvB9T,EAAIjM,EAAIiM,EAAIwU,GAAKC,EACjBzU,EAAIyI,EAAI,KAAOjiB,KAAKkkB,KAAK1K,EAAIjM,GAC7BiM,EAAI8U,IAAM,KAAS9U,EAAIjM,IAAM,GAC7BiM,EAAI+U,IAAM/U,EAAI8U,IAId,MAAMrV,EAAK4U,EACV,EACA7tB,KAAK0N,IACJ1N,KAAK2N,IAAIzE,GAAK,EAAI,EAAI,IAAMlJ,KAAK+Y,MAAMS,EAAIuU,GAAK,IAAM,KAAM,GAC5D,GAEFvU,EAAIgV,KAAON,EAAKlY,KAAIQ,GACZ4B,GAAY,EAAG6V,EAAKzX,EAAGyC,KAE/BO,EAAIiV,QAAUjV,EAAIgV,KAAKxY,KAAIQ,GACnB,EAAIA,IAIZ,MAAMkY,EAAQR,EAAKlY,KAAI,CAACQ,EAAG3H,IACnB2H,EAAIgD,EAAIgV,KAAK3f,KAEf8f,EAAQ5U,GAAM2U,EAAOlV,EAAI8T,IAK/B,OAJA9T,EAAIoV,GAAKpV,EAAI8U,KAAO,EAAIK,EAAM,GAAKA,EAAM,GAAK,IAAOA,EAAM,IAIpDnV,CACR,CAGA,MAAMqV,GAAoBrB,GACzBlO,GACA,GAAKtf,KAAKkY,GAAK,GAAK,GACpB,WACA,GAGM,SAAS4W,GAAWC,EAAOvV,GAIjC,UAAmB/V,IAAZsrB,EAAMC,OAAgCvrB,IAAZsrB,EAAME,GACtC,MAAM,IAAInc,MAAM,oDAGjB,UAAmBrP,IAAZsrB,EAAMrb,OAAgCjQ,IAAZsrB,EAAM5U,OAAgC1W,IAAZsrB,EAAMtN,GAChE,MAAM,IAAI3O,MAAM,yDAIjB,UAAmBrP,IAAZsrB,EAAMjL,OAAgCrgB,IAAZsrB,EAAM5B,GACtC,MAAM,IAAIra,MAAM,oDAIjB,GAAgB,IAAZic,EAAMC,GAAyB,IAAZD,EAAME,EAC5B,MAAO,CAAC,EAAK,EAAK,GAInB,IAAIC,EAAO,EAEVA,OADezrB,IAAZsrB,EAAMjL,EACFH,GAAUoL,EAAMjL,GAAKuJ,GAtHvB,SAA2BF,GACjC,IAAIgC,GAAOhC,EAAI,IAAM,KAAO,IAC5B,MAAMte,EAAI7O,KAAKoN,MAAM,IAAO+hB,GAC5BA,GAAU,IACV,MAAOC,EAAIC,GAAOpC,GAAWnJ,EAAExhB,MAAMuM,EAAGA,EAAI,IACrCygB,EAAIC,GAAOtC,GAAWC,EAAE5qB,MAAMuM,EAAGA,EAAI,GAE5C,OAAO8U,IACLwL,GAAMI,EAAMH,EAAKE,EAAKD,GAAO,IAAMD,EAAKG,IACxCJ,GAAMI,EAAMD,GAAM,IAAMC,GAE3B,CA8GSC,CAAiBT,EAAM5B,GAAKE,GAGpC,MAAMoC,EAAOzvB,KAAKwkB,IAAI0K,GAChBQ,EAAO1vB,KAAKykB,IAAIyK,GAGtB,IAAIS,EAAQ,OACIlsB,IAAZsrB,EAAMC,EACTW,EAA+B,GAAvB9W,GAAKkW,EAAMC,EAAG,SAEFvrB,IAAZsrB,EAAME,IACdU,EAAQ,IAAOnW,EAAIhD,EAAIuY,EAAME,IAAMzV,EAAIoV,GAAK,GAAKpV,EAAI6U,SAItD,IAAIlR,EAAQ,OACI1Z,IAAZsrB,EAAMrb,EACTyJ,EAAQ4R,EAAMrb,EAAIic,OAEElsB,IAAZsrB,EAAM5U,EACdgD,EAAS4R,EAAM5U,EAAIX,EAAI6U,OAAUsB,OAEblsB,IAAZsrB,EAAMtN,IACdtE,EAAQ,KAAU4R,EAAMtN,GAAK,GAAMjI,EAAIoV,GAAK,GAAKpV,EAAIhD,GAEtD,MAAMoZ,EAAI/W,GACTsE,EAAQnd,KAAK0jB,IAAI,KAAO1jB,KAAK0jB,IAAI,IAAMlK,EAAIjM,IAAK,KAChD,GAAK,GAIAsiB,EAAK,KAAQ7vB,KAAKwkB,IAAI0K,EAAO,GAAK,KAGlCrZ,EAAI2D,EAAIoV,GAAK/V,GAAK8W,EAAO,EAAInW,EAAIhD,EAAIgD,EAAIyI,GAGzC6N,EAAK,IAAM,GAAKtW,EAAI2U,GAAK3U,EAAI+U,IAAMsB,EACnCE,EAAKla,EAAI2D,EAAI8U,IACbhM,EACL,IAAMyN,EAAK,MACX/W,GAAK4W,EAAG,GAAKE,EAAKF,GAAK,GAAKH,EAAO,IAAMC,IAMpCM,EAhMA,SAAkBC,EAAS3C,GACjC,MAAM4C,EAAW,IAAM5C,EAAM,OAASb,GACtC,OAAOwD,EAAQja,KAAIQ,IAClB,MAAM2Z,EAAOnwB,KAAKmX,IAAIX,GACtB,OAAOmC,GAASuX,EAAWrX,GAAKsX,GAAQ,IAAMA,GAAO1D,IAAiBjW,EAAE,GAE1E,CA0Le4Z,CACbxa,GAAiByV,GAAI,CAAC0E,EALbzN,EAAImN,EACJnN,EAAIoN,IAIoB1Z,KAAIQ,GACzB,EAAJA,EAAQ,OAEhBgD,EAAI8T,IAEL,OAAO1X,GACNgX,GACAoD,EAAMha,KAAI,CAACQ,EAAG3H,IACN2H,EAAIgD,EAAIiV,QAAQ5f,MAEvBmH,KAAIQ,GACEA,EAAI,KAEb,CAGO,SAAS6Z,GAASC,EAAQ9W,GAEhC,MAAM+W,EAASD,EAAOta,KAAIQ,GACd,IAAJA,IAEFga,EAAOzW,GACZnE,GAAiB+W,GAAO4D,GAAQva,KAAI,CAACQ,EAAG3H,IAChC2H,EAAIgD,EAAIgV,KAAK3f,KAErB2K,EAAI8T,IAIC1kB,EAAI4nB,EAAK,KAAO,GAAKA,EAAK,GAAKA,EAAK,IAAM,GAC1C1b,GAAK0b,EAAK,GAAKA,EAAK,GAAK,EAAIA,EAAK,IAAM,EACxCtB,GAASlvB,KAAKikB,MAAMnP,EAAGlM,GAAK8jB,GAAOA,IAAOA,GAG1CmD,EAAK,KAAQ7vB,KAAKwkB,IAAI0K,EAAO,GAAK,KASlC/R,EAAQtE,GANb,IAAM,GAAKW,EAAI2U,GAAK3U,EAAI+U,IACxBvV,GACC6W,EAAK7vB,KAAKkkB,KAAKtb,GAAK,EAAIkM,GAAK,GAC7B0b,EAAK,GAAKA,EAAK,GAAK,KAAOA,EAAK,GAAK,MAGjB,IAAOxwB,KAAK0jB,IAAI,KAAO1jB,KAAK0jB,IAAI,IAAMlK,EAAIjM,GAAI,KAK9DoiB,EAAQ9W,GAFJW,EAAI8U,KAAO,EAAIkC,EAAK,GAAKA,EAAK,GAAK,IAAOA,EAAK,IAElChX,EAAIoV,GAAI,GAAMpV,EAAIhD,EAAIgD,EAAIyI,GAG3C+M,EAAI,IAAMnW,GAAK8W,EAAO,GAGtBV,EAAK,EAAIzV,EAAIhD,EAAImZ,GAASnW,EAAIoV,GAAK,GAAKpV,EAAI6U,OAG5C3a,EAAIyJ,EAAQwS,EAGZxV,EAAIzG,EAAI8F,EAAI6U,OAGZvK,EAAIH,GAAUuL,EAAO9B,IAGrBD,EA3PA,SAAwBrJ,GAC9B,IAAI2M,EAAK9M,GAAUG,GACf2M,GAAMxD,GAAWnJ,EAAE,KACtB2M,GAAM,KAGP,MAAM5hB,EhC+KA,SAAqB8P,EAAKhd,GAAgC,IAAzB+uB,EAAElvB,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,EAAG4tB,EAAE5tB,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAGmd,GAAAA,EAAI5a,OACxD,KAAO2sB,EAAKtB,GAAI,CACf,MAAMuB,EAAOD,EAAKtB,GAAO,EACrBzQ,EAAIgS,GAAOhvB,EACd+uB,EAAKC,EAAM,EAGXvB,EAAKuB,CAEP,CACA,OAAOD,CACR,CgC1LWE,CAAW3D,GAAWnJ,EAAG2M,GAAM,GAClCrB,EAAIC,GAAOpC,GAAWnJ,EAAExhB,MAAMuM,EAAGA,EAAI,IACrCygB,EAAIC,GAAOtC,GAAWC,EAAE5qB,MAAMuM,EAAGA,EAAI,GAGtC+gB,GAAKa,EAAKrB,GAAME,EACtB,OAHWrC,GAAWE,EAAEte,GAGX,IAAM+gB,GAAMA,GAAKP,EAAMoB,GAAMlB,EAC3C,CA8OWsB,CAAc/M,GAOxB,MAAO,CAACkL,EAAGA,EAAGtb,EAAGA,EAAGoQ,EAAGA,EAAGrC,EAJhB,GAAK5I,GAAKW,EAAIhD,EAAI2G,GAAS3D,EAAIoV,GAAK,GAAI,IAIlBK,EAAGA,EAAG9U,EAAGA,EAAGgT,EAAGA,EAChD,CASA,IAAe4B,GAAA,IAAIpR,GAAW,CAC7BpW,GAAI,YACJiX,MAAO,cACPvW,KAAM,YACNmT,OAAQ,CACP0V,EAAG,CACF7U,SAAU,CAAC,EAAG,KACdhU,KAAM,KAEP8N,EAAG,CACFkG,SAAU,CAAC,EAAG,KACdhU,KAAM,gBAEP6b,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,QAIR6Q,KAAMiJ,GAEN3C,QAAAA,CAAUwD,GACT,MAAMmM,EAAQsB,GAAQzN,EAAKiM,IAC3B,MAAO,CAACE,EAAMC,EAAGD,EAAM5U,EAAG4U,EAAMjL,EAChC,EACDzE,OAAQ0P,GACAD,GACN,CAACE,EAAGD,EAAM,GAAI5U,EAAG4U,EAAM,GAAIjL,EAAGiL,EAAM,IACpCF,MChWH,MAAMvP,GAAQ3F,GAAOE,IACfiF,GAAI,IAAM,MACVsE,GAAI,MAAQ,GASlB,SAAS2N,GAAWC,GAGnB,OAAQA,EAAQ,EAAMhxB,KAAK0jB,KAAKsN,EAAQ,IAAM,IAAK,GAAKA,EAAQ5N,EACjE,CA0EA,SAAS6N,GAAOrO,EAAKpJ,GAGpB,MAAMoW,EApFE,MAJS5N,EAwFCY,EAAI,IArFN9D,GAAK9e,KAAKwjB,KAAKxB,IAAMoB,GAAIpB,EAAI,IAAM,KAC7B,GAJvB,IAAkBA,EAyFjB,GAAU,IAAN4N,EACH,MAAO,CAAC,EAAK,EAAK,GAEnB,MAAMb,EAAQsB,GAAQzN,EAAKiM,IAC3B,MAAO,CAAClL,GAAUoL,EAAMjL,GAAIiL,EAAMrb,EAAGkc,EACtC,CAGO,MAAMf,GAAoBrB,GAChClO,GAAO,IAAMtf,KAAKkY,GAAK6Y,GAAU,IACf,IAAlBA,GAAU,IACV,WACA,GAYD,IAAeG,GAAA,IAAIvT,GAAW,CAC7BpW,GAAI,MACJU,KAAM,MACNmT,OAAQ,CACP0I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,OAEPuO,EAAG,CACFyF,SAAU,CAAC,EAAG,KACdhU,KAAM,gBAEP2nB,EAAG,CACF3T,SAAU,CAAC,EAAG,KACdhU,KAAM,SAIR6Q,KAAMiJ,GAEN3C,SAAUwD,GACFqO,GAAMrO,GAEdvD,OAAQ6R,GA5HT,SAAkB9V,EAAQ5B,GASzB,IAAKsK,EAAGtN,EAAGoZ,GAAKxU,EACZwH,EAAM,GACNkO,EAAI,EAGR,GAAU,IAANlB,EACH,MAAO,CAAC,EAAK,EAAK,GAInB,IAAI5N,EAAI+O,GAAUnB,GAKjBkB,EADGlB,EAAI,EACH,mBAAsBA,GAAK,EAAI,iBAAoBA,EAAI,kBAGvD,qBAAwBA,GAAK,EAAI,mBAAsBA,EAAI,mBAWhE,IAAIuB,EAAU,EACVC,EAAOC,IAIX,KAAOF,GAPc,IAOW,CAC/BvO,EAAMkM,GAAU,CAACE,EAAG8B,EAAGpd,EAAG8C,EAAGsN,EAAGA,GAAItK,GAIpC,MAAM8X,EAAQtxB,KAAKmX,IAAIyL,EAAI,GAAKZ,GAChC,GAAIsP,EAAQF,EAAM,CACjB,GAAIE,GAfY,MAgBf,OAAO1O,EAGRwO,EAAOE,CACR,CAOAR,IAASlO,EAAI,GAAKZ,GAAK8O,GAAK,EAAIlO,EAAI,IAEpCuO,GAAW,CACZ,CAIA,OAAOrC,GAAU,CAACE,EAAG8B,EAAGpd,EAAG8C,EAAGsN,EAAGA,GAAItK,EACtC,CAuDS+X,CAAQL,EAAKrC,IAErBpQ,QAAS,CACRlC,MAAO,CACNhV,GAAI,QACJ6T,OAAQ,CAAC,qBAAsB,0BAA2B,+BCpJ7D,MAAMiS,GAAUrtB,KAAKkY,GAAK,IACpBsZ,GAAW,CAAC,EAAM,KAAO,OAO/B,SAASC,GAAcrW,GAMlBA,EAAO,GAAK,IACfA,EAAS8V,GAAI9R,SAAS8R,GAAI7R,OAAOjE,KAMlC,MAAMjB,EAAIna,KAAK0xB,IAAI1xB,KAAK0N,IAAI,EAAI8jB,GAAS,GAAKpW,EAAO,GAAKyT,GAAkBR,OAAQ,IAAQmD,GAAS,GAC/FG,EAAOvW,EAAO,GAAKiS,GACnBzkB,EAAIuR,EAAIna,KAAKwkB,IAAImN,GACjB7c,EAAIqF,EAAIna,KAAKykB,IAAIkN,GAEvB,MAAO,CAACvW,EAAO,GAAIxS,EAAGkM,EACvB,CASe,SAAA8c,GAAUrV,EAAO0I,IAC9B1I,EAAO0I,GAAUpG,GAAS,CAACtC,EAAO0I,IAEnC,IAAM4M,EAAIvM,EAAIC,GAAOkM,GAAaP,GAAI1Y,KAAK+D,KACrCuV,EAAIpM,EAAIC,GAAO8L,GAAaP,GAAI1Y,KAAKyM,IAI3C,OAAOjlB,KAAKkkB,MAAM2N,EAAKC,IAAO,GAAKxM,EAAKI,IAAO,GAAKH,EAAKI,IAAO,EACjE,CChCA,IAAeoM,GAAA,CACdlJ,YACAC,aACA9D,cACA6F,YACAoB,aACAjE,YACA4J,cCGD,MAAMI,GAAa,CAClBd,IAAO,CACNptB,OAAQ,QACRmuB,IAAK,EACLC,aAAc,MACdC,gBAAiB,CAAC,GAEnB,YAAa,CACZruB,OAAQ,QACRmuB,IAAK,EACLC,aAAc,MACdC,gBAAiB,CAAEC,QAAS,QAASzkB,IAAK,EAAGD,IAAK,OAwBrC,SAAS2kB,GACvB9V,GAQC,IAqBG+V,GA5BJxuB,OACCA,EAASsW,GAASC,cAAaa,MAC/BA,EAAiBgX,aACjBA,EAAe,GAAED,IACjBA,EAAM,EAACE,gBACPA,EAAkB,CAAC,GACnB3wB,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAkBJ,GAhBA+a,EAAQsC,GAAStC,GAEbL,GAAc1a,UAAU,IAC3B0Z,EAAQ1Z,UAAU,GAET0Z,IACTA,EAAQqB,EAAMrB,OAGfA,EAAQyC,GAAW7c,IAAIoa,GAOnByE,GAAQpD,EAAOrB,EAAO,CAAE6E,QAAS,IACpC,OAAOxD,EAIR,GAAe,QAAXzY,EACHwuB,EAAaC,GAAWhW,EAAO,CAAErB,cAE7B,CACJ,GAAe,SAAXpX,GAAsB6b,GAAQpD,EAAOrB,GA2ExCoX,EAAa7Z,GAAG8D,EAAOrB,OA3EyB,CAE5Cta,OAAOS,UAAUH,eAAeE,KAAK4wB,GAAYluB,MAClDA,SAAQmuB,MAAKC,eAAcC,mBAAmBH,GAAWluB,IAI5D,IAAI0uB,EAAKxN,GACT,GAAqB,KAAjBkN,EACH,IAAK,IAAInc,KAAKgc,GACb,GAAI,SAAWG,EAAa9hB,gBAAkB2F,EAAE3F,cAAe,CAC9DoiB,EAAKT,GAAchc,GACnB,KACD,CAIF,IAAI0c,EAAUJ,GAAQ5Z,GAAG8D,EAAOrB,GAAQ,CAAEpX,OAAQ,OAAQoX,UAC1D,GAAIsX,EAAGjW,EAAOkW,GAAWR,EAAK,CAG7B,GAA4C,IAAxCrxB,OAAO6J,KAAK0nB,GAAiBpuB,OAAc,CAC9C,IAAI2uB,EAAc/U,GAAWuD,aAAaiR,EAAgBC,SACtDA,EAAUtxB,GAAI2X,GAAG8D,EAAOmW,EAAYxX,OAAQwX,EAAYnrB,IAI5D,GAHI2U,GAAYkW,KACfA,EAAU,GAEPA,GAAWD,EAAgBzkB,IAC9B,OAAO+K,GAAG,CAAEyC,MAAO,UAAWE,OAAQzB,GAAY,KAAK4C,EAAMrB,OAEzD,GAAIkX,GAAWD,EAAgBxkB,IACnC,OAAO8K,GAAG,CAAEyC,MAAO,UAAWE,OAAQ,CAAC,EAAG,EAAG,IAAMmB,EAAMrB,MAE3D,CAGA,IAAIK,EAAYoC,GAAWuD,aAAapd,GACpC6uB,EAAWpX,EAAUL,MACrBoG,EAAU/F,EAAUhU,GAEpBqrB,EAAcna,GAAG8D,EAAOoW,GAE5BC,EAAYxX,OAAO9B,SAAQ,CAAC9C,EAAG3H,KAC1BqN,GAAY1F,KACfoc,EAAYxX,OAAOvM,GAAK,EACzB,IAED,IACIlB,GADS4N,EAAUQ,OAASR,EAAUU,UACzB,GACb6C,EA/HR,SAAsBmT,GAGrB,MAAMY,EAAUZ,EAAWjyB,KAAKoN,MAAMpN,KAAKkX,MAAMlX,KAAKmX,IAAI8a,KAAnC,EAEvB,OAAOjyB,KAAK0N,IAAIolB,WAAY,MAAID,EAAQ,IAAM,KAC/C,CAyHYE,CAAYd,GAChBe,EAAMrlB,EACNslB,EAAOnyB,GAAI8xB,EAAatR,GAE5B,KAAO2R,EAAOD,EAAMlU,GAAG,CACtB,IAAI2T,EAAUtK,GAAMyK,GACpBH,EAAUJ,GAAQI,EAAS,CAAEvX,QAAOpX,OAAQ,SAC/B0uB,EAAGI,EAAaH,GAEhBR,EAAMnT,EAClBkU,EAAMlyB,GAAI8xB,EAAatR,GAGvB2R,EAAOnyB,GAAI8xB,EAAatR,GAGzB1W,GAAIgoB,EAAatR,GAAU0R,EAAMC,GAAQ,EAC1C,CAEAX,EAAa7Z,GAAGma,EAAa1X,EAC9B,MAECoX,EAAaG,CAEf,CAKA,GAAe,SAAX3uB,IAEC6b,GAAQ2S,EAAYpX,EAAO,CAAE6E,QAAS,IACzC,CACD,IAAImT,EAAStyB,OAAOqf,OAAO/E,EAAME,QAAQpF,KAAIQ,GAAKA,EAAEuF,OAAS,KAE7DuW,EAAWlX,OAASkX,EAAWlX,OAAOpF,KAAI,CAACQ,EAAG3H,KAC7C,IAAKlB,EAAKD,GAAOwlB,EAAOrkB,GAUxB,YARYpL,IAARkK,IACH6I,EAAIxW,KAAK0N,IAAIC,EAAK6I,SAGP/S,IAARiK,IACH8I,EAAIxW,KAAK2N,IAAI6I,EAAG9I,IAGV8I,CAAC,GAEV,CACD,CAOA,OALI0E,IAAUqB,EAAMrB,QACnBoX,EAAa7Z,GAAG6Z,EAAY/V,EAAMrB,QAGnCqB,EAAMnB,OAASkX,EAAWlX,OACnBmB,CACR,CAEA8V,GAAQpP,QAAU,QAKlB,MAAMkQ,GAAS,CACdC,MAAO,CAAElY,MAAO6M,GAAO3M,OAAQ,CAAC,EAAG,EAAG,IACtCiY,MAAO,CAAEnY,MAAO6M,GAAO3M,OAAQ,CAAC,EAAG,EAAG,KAahC,SAASmX,GAAYe,GAAsB,IAAdpY,MAACA,GAAM1Z,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAC7C,MAAM+xB,EAAM,IACNzU,EAAI,KAEVwU,EAASzU,GAASyU,GAEbpY,IACJA,EAAQoY,EAAOpY,OAGhBA,EAAQyC,GAAW7c,IAAIoa,GACvB,MAAMsY,EAAa7V,GAAW7c,IAAI,SAElC,GAAIoa,EAAMwE,YACT,OAAOjH,GAAG6a,EAAQpY,GAGnB,MAAMuY,EAAehb,GAAG6a,EAAQE,GAChC,IAAIxP,EAAIyP,EAAarY,OAAO,GAG5B,GAAI4I,GAAK,EAAG,CACX,MAAM1E,EAAQ7G,GAAG0a,GAAOC,MAAOlY,GAE/B,OADAoE,EAAMnC,MAAQmW,EAAOnW,MACd1E,GAAG6G,EAAOpE,EAClB,CACA,GAAI8I,GAAK,EAAG,CACX,MAAM0P,EAAQjb,GAAG0a,GAAOE,MAAOnY,GAE/B,OADAwY,EAAMvW,MAAQmW,EAAOnW,MACd1E,GAAGib,EAAOxY,EAClB,CAEA,GAAIyE,GAAQ8T,EAAcvY,EAAO,CAAC6E,QAAS,IAC1C,OAAOtH,GAAGgb,EAAcvY,GAGzB,SAASyY,EAAMC,GACd,MAAMC,EAAYpb,GAAGmb,EAAQ1Y,GACvB4Y,EAAclzB,OAAOqf,OAAO/E,EAAME,QAQxC,OAPAyY,EAAUzY,OAASyY,EAAUzY,OAAOpF,KAAI,CAACmK,EAAOtS,KAC/C,GAAI,UAAWimB,EAAYjmB,GAAQ,CAClC,MAAOF,EAAKD,GAAQomB,EAAYjmB,GAAOkO,MACvC,OpCrEG,SAAgBpO,EAAKvH,EAAKsH,GAChC,OAAO1N,KAAK0N,IAAI1N,KAAK2N,IAAID,EAAKtH,GAAMuH,EACrC,CoCmEWuO,CAAWvO,EAAKwS,EAAOzS,EAC/B,CACA,OAAOyS,CAAK,IAEN0T,CACR,CACA,IAAIlmB,EAAM,EACND,EAAM+lB,EAAarY,OAAO,GAC1B2Y,GAAc,EACd5pB,EAAUge,GAAMsL,GAChBhB,EAAUkB,EAAKxpB,GAEf6pB,EAAIhM,GAASyK,EAAStoB,GAC1B,GAAI6pB,EAAIT,EACP,OAAOd,EAGR,KAAQ/kB,EAAMC,EAAOmR,GAAG,CACvB,MAAMmV,GAAUtmB,EAAMD,GAAO,EAE7B,GADAvD,EAAQiR,OAAO,GAAK6Y,EAChBF,GAAepU,GAAQxV,EAAS+Q,EAAO,CAAC6E,QAAS,IACpDpS,EAAMsmB,OAKN,GAFAxB,EAAUkB,EAAKxpB,GACf6pB,EAAIhM,GAASyK,EAAStoB,GAClB6pB,EAAIT,EAAK,CACZ,GAAKA,EAAMS,EAAIlV,EACd,MAGAiV,GAAc,EACdpmB,EAAMsmB,CAER,MAECvmB,EAAMumB,CAGT,CACA,OAAOxB,CACR,CC1Se,SAASha,GAAI8D,EAAOrB,GAAuB,IAAhByE,QAACA,GAAQne,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GACrD+a,EAAQsC,GAAStC,GAGjB,IAAInB,GAFJF,EAAQyC,GAAW7c,IAAIoa,IAEJ1C,KAAK+D,GACpBhG,EAAM,CAAC2E,QAAOE,SAAQ+B,MAAOZ,EAAMY,OAMvC,OAJIwC,IACHpJ,EAAM8b,GAAQ9b,GAAiB,IAAZoJ,OAAmBlc,EAAYkc,IAG5CpJ,CACR,CAEAkC,GAAGwK,QAAU,8DCxBb,IAAItd,EAAcjF,KAEd+B,EAAaC,iBAEjBwxB,GAAiB,SAAUzqB,EAAG1D,GAC5B,WAAY0D,EAAE1D,GAAI,MAAM,IAAItD,EAAW,0BAA4BkD,EAAYI,GAAK,OAASJ,EAAY8D,KCQ5F,SAAS0qB,GAAW5X,GAK3B,IAAA1F,EAAAud,EAAA,IACH7d,GANqCO,UACzCA,EAAYsD,GAAStD,UAASqE,OAC9BA,EAAS,UACTwE,QAAAA,GAAU,KACP0U,GACH7yB,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAKC0c,EAAW/C,EACfA,EAC0C,QADpCtE,UAAAud,GAHN7X,EAAQsC,GAAStC,IAGFrB,MAAM2C,UAAU1C,UAAO,IAAAiZ,EAAAA,EAC5B7X,EAAMrB,MAAM2C,UAAU,kBAAUhH,IAAAA,EAAAA,EAChC8G,GAAW2W,eAMrB,IAAIlZ,EAASmB,EAAMnB,OAAO9Y,QAS1B,GAPAqd,IAAAA,EAAYxE,EAAOkX,SAEf1S,IAAY4U,GAAahY,KAE5BnB,EAASiX,GAAQlK,GAAM5L,IAAoB,IAAZoD,OAAmBlc,EAAYkc,GAASvE,QAGpD,WAAhBD,EAAOzP,KAAmB,CAG7B,GAFA2oB,EAAcvd,UAAYA,GAEtBqE,EAAOgZ,UAIV,MAAM,IAAIzxB,UAAW,UAASwb,6DAH9B3H,EAAM4E,EAAOgZ,UAAU/Y,EAAQmB,EAAMY,MAAOkX,EAK9C,KACK,CAEJ,IAAIpsB,EAAOkT,EAAOlT,MAAQ,QAEtBkT,EAAO2G,gBACV1G,EAASD,EAAO2G,gBAAgB1G,EAAQtE,GAGtB,OAAdA,IACHsE,EAASA,EAAOpF,KAAIQ,GACZ0F,GAAqB1F,EAAG,CAACM,iBAKnC,IAAI+F,EAAO,IAAIzB,GAEf,GAAa,UAATnT,EAAkB,CAAA,IAAAusB,EAErB,IAAIhW,EAAQrD,EAAO5T,aAAEitB,EAAIrZ,EAAOsC,WAAG,IAAA+W,OAAA,EAAVA,EAAa,KAAMjY,EAAMrB,MAAM3T,GACxDsV,EAAK4X,QAAQjW,EACd,CAEA,IAAIrB,EAAQZ,EAAMY,MACA,OAAdrG,IACHqG,EAAQjB,GAAqBiB,EAAO,CAACrG,eAGtC,IAAI4d,EAAWnY,EAAMY,OAAS,GAAKhC,EAAOwZ,QAAU,GAAM,GAAExZ,EAAOyZ,OAAS,IAAM,QAAQzX,IAC1F5G,EAAO,GAAEtO,KAAQ4U,EAAK1Q,KAAKgP,EAAOyZ,OAAS,KAAO,OAAOF,IAC1D,CAEA,OAAOne,CACR,kCCpFA,IAAIvB,EAAItU,KACJ0G,EAAWvE,KACXoL,EAAoBlL,KACpB2U,EAAiBlS,KACjB0uB,EAAwBvsB,KACxBgO,EAA2B/N,KAmB/BoN,EAAE,CAAEvF,OAAQ,QAAS8B,OAAO,EAAM5E,MAAO,EAAGmE,OAhBH,IAAlB,GAAG2jB,QAAQ,KAGG,WACnC,IAEE7zB,OAAOC,eAAe,GAAI,SAAU,CAAEiB,UAAU,IAAS2yB,SAC1D,CAAC,MAAOh0B,GACP,OAAOA,aAAiBiC,SACzB,CACH,CAEkCiV,IAI4B,CAE5D8c,QAAS,SAAiB7c,GACxB,IAAInO,EAAIrC,EAAS/G,MACb2N,EAAMC,EAAkBxE,GACxBoO,EAAWrW,UAAUuC,OACzB,GAAI8T,EAAU,CACZlC,EAAyB3H,EAAM6J,GAE/B,IADA,IAAIgd,EAAI7mB,EACD6mB,KAAK,CACV,IAAIpc,EAAKoc,EAAIhd,EACTgd,KAAKprB,EAAGA,EAAEgP,GAAMhP,EAAEorB,GACjBX,EAAsBzqB,EAAGgP,EAC/B,CACD,IAAK,IAAIqY,EAAI,EAAGA,EAAIjZ,EAAUiZ,IAC5BrnB,EAAEqnB,GAAKtvB,UAAUsvB,EAEpB,CAAC,OAAOpZ,EAAejO,EAAGuE,EAAM6J,EAClC,OCxBH,IAAeid,GAAA,IAAI5S,GAAc,CAChC3a,GAAI,iBACJiX,MAAO,mBACPvW,KAAM,kBACNqX,MAAO,cAjBQ,CACf,CAAE,kBAAoB,mBAAsB,mBAC5C,CAAE,kBAAoB,kBAAsB,oBAC5C,CAAE,EAAoB,oBAAsB,oBAgB5CoD,UAZiB,CACjB,CAAG,mBAAqB,kBAAoB,iBAC5C,EAAG,iBAAqB,kBAAoB,mBAC5C,CAAG,kBAAqB,iBAAoB,qBCZ7C,MAAMqS,GAAI,iBACJC,GAAI,iBAEV,IAAeC,GAAA,IAAI/S,GAAc,CAChC3a,GAAI,UACJU,KAAM,WACN6Q,KAAMgc,GAENzV,OAAQ6V,GACAA,EAAIlf,KAAI,SAAU5P,GACxB,OAAIA,EAAU,IAAJ4uB,GACF5uB,EAAM,IAGPpG,KAAK0jB,KAAKtd,EAAM2uB,GAAI,GAAKA,GAAG,EAAI,IACxC,IAED3V,SAAU8V,GACFA,EAAIlf,KAAI,SAAU5P,GACxB,OAAIA,GAAO4uB,GACHD,GAAI/0B,KAAK0jB,IAAItd,EAAK,MAAS2uB,GAAI,GAGhC,IAAM3uB,CACd,MCdF,IAAe+uB,GAAA,IAAIjT,GAAc,CAChC3a,GAAI,YACJiX,MAAO,sBACPvW,KAAM,YACNqX,MAAO,cAhBQ,CACf,CAAC,kBAAoB,mBAAqB,mBAC1C,CAAC,kBAAoB,kBAAqB,kBAC1C,CAAC,EAAoB,mBAAqB,oBAe1CoD,UAZiB,CACjB,CAAE,mBAAsB,mBAAqB,oBAC7C,EAAE,kBAAsB,mBAAqB,qBAC7C,CAAE,oBAAsB,mBAAqB,sBCF9C,MAQaA,GAAY,CACxB,CAAG,oBAAsB,mBAAsB,mBAC/C,EAAG,kBAAsB,mBAAsB,oBAC/C,CAAG,oBAAsB,mBAAsB,qBAGhD,IAAe0S,GAAA,IAAIlT,GAAc,CAChC3a,GAAI,cACJU,KAAM,cACNqX,MAAO,cAjBQ,CACf,CAAE,mBAAqB,iBAAqB,mBAC5C,CAAE,mBAAqB,iBAAqB,oBAC5C,CAAE,mBAAqB,mBAAqB,oBAgB5CoD,UAAAA,KCpBc2S,GAAA,CACdC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GACpCC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,KAAQ,CAAC,EAAG,EAAG,GACfC,WAAc,CAAC,IAAM,IAAK,EAAG,IAAM,KACnCC,MAAS,CAAC,IAAM,IAAK,EAAG,GACxBC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,OAAU,CAAC,EAAG,IAAM,IAAK,IAAM,KAC/BlC,MAAS,CAAC,EAAG,EAAG,GAChBmC,eAAkB,CAAC,EAAG,IAAM,IAAK,IAAM,KACvCC,KAAQ,CAAC,EAAG,EAAG,GACfC,WAAc,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC1CC,MAAS,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACpCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,WAAc,CAAC,IAAM,IAAK,EAAG,GAC7BC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,MAAS,CAAC,EAAG,IAAM,IAAK,GAAK,KAC7BC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC/CC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,QAAW,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACtCC,KAAQ,CAAC,EAAG,EAAG,GACfC,SAAY,CAAC,EAAG,EAAG,IAAM,KACzBC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KAC7CC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,UAAa,CAAC,EAAG,IAAM,IAAK,GAC5BC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,YAAe,CAAC,IAAM,IAAK,EAAG,IAAM,KACpCC,eAAkB,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KAC7CC,WAAc,CAAC,EAAG,IAAM,IAAK,GAC7BC,WAAc,CAAC,GAAW,GAAK,IAAK,IACpCC,QAAW,CAAC,IAAM,IAAK,EAAG,GAC1BC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,IAAM,KAC5CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,GAAK,KAC3CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,GAAK,KAC3CC,cAAiB,CAAC,EAAG,IAAM,IAAK,IAAM,KACtCC,WAAc,CAAC,IAAM,IAAK,EAAG,IAAM,KACnCC,SAAY,CAAC,EAAG,GAAK,IAAK,IAAM,KAChCC,YAAe,CAAC,EAAG,IAAM,IAAK,GAC9BC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,WAAc,CAAC,GAAK,IAAK,IAAM,IAAK,GACpCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACxCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,YAAe,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KAC1CC,QAAW,CAAC,EAAG,EAAG,GAClBC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,GACrCC,KAAQ,CAAC,EAAG,IAAM,IAAK,GACvBC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,MAAS,CAAC,EAAG,IAAM,IAAK,GACxBC,YAAe,CAAC,IAAM,IAAK,EAAG,GAAK,KACnCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,SAAY,CAAC,IAAM,IAAK,EAAG,IAAM,KACjCC,QAAW,CAAC,EAAG,IAAM,IAAK,IAAM,KAChCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACxCC,OAAU,CAAC,GAAK,IAAK,EAAG,IAAM,KAC9BC,MAAS,CAAC,EAAG,EAAG,IAAM,KACtBC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,cAAiB,CAAC,EAAG,IAAM,IAAK,IAAM,KACtCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GACpCC,aAAgB,CAAC,EAAG,IAAM,IAAK,IAAM,KACrCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,EAAG,GAC5BC,qBAAwB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrDC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,cAAiB,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KAC7CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IACzCC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IACzCC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC/CC,YAAe,CAAC,EAAG,EAAG,IAAM,KAC5BC,KAAQ,CAAC,EAAG,EAAG,GACfC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KACxCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,QAAW,CAAC,EAAG,EAAG,GAClBC,OAAU,CAAC,IAAM,IAAK,EAAG,GACzBC,iBAAoB,CAAC,GAAW,IAAM,IAAK,IAAM,KACjDC,WAAc,CAAC,EAAG,EAAG,IAAM,KAC3BC,aAAgB,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC5CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,eAAkB,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KAC9CC,gBAAmB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAChDC,kBAAqB,CAAC,EAAG,IAAM,IAAK,IAAM,KAC1CC,gBAAmB,CAAC,GAAK,IAAK,IAAM,IAAK,IACzCC,gBAAmB,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC/CC,aAAgB,CAAC,GAAK,IAAK,GAAK,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,EAAG,IAAM,KAClCC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,KAAQ,CAAC,EAAG,EAAG,IAAM,KACrBC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,GAChCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,OAAU,CAAC,EAAG,IAAM,IAAK,GACzBC,UAAa,CAAC,EAAG,GAAK,IAAK,GAC3BC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,WAAc,CAAC,EAAG,IAAM,IAAK,IAAM,KACnCC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACpCC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,OAAU,CAAC,IAAM,IAAK,EAAG,IAAM,KAC/BC,cAAiB,CAAC,GAAW,GAAU,IACvCC,IAAO,CAAC,EAAG,EAAG,GACdC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,YAAe,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KAC1CC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KAC1CC,SAAY,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KACvCC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,OAAU,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACrCC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KACzCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,YAAe,CAAC,EAAG,EAAG,IAAM,KAC5BC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,IAAO,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACpCC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,OAAU,CAAC,EAAG,GAAK,IAAK,GAAK,KAC7BC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtC9e,MAAS,CAAC,EAAG,EAAG,GAChB+e,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,OAAU,CAAC,EAAG,EAAG,GACjBC,YAAe,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,MCxJ5C,IAAI/iB,GAAelK,MAAM,GAAGktB,KAAK,mCAC7BC,GAAqBntB,MAAM,GAAGktB,KAAK,oBAEvC,IAAeE,GAAA,IAAIxc,GAAc,CAChC3a,GAAI,OACJU,KAAM,OACN6Q,KAAMsc,GACNhW,SAAUuD,GAIFA,EAAI3M,KAAI5P,IACd,IAAIwS,EAAOxS,EAAM,GAAK,EAAI,EACtB+Q,EAAM/Q,EAAMwS,EAEhB,OAAIzB,EAAM,SACFyB,GAAQ,MAASzB,IAAQ,EAAI,KAAQ,MAGtC,MAAQ/Q,CAAG,IAGpBiZ,OAAQsD,GAIAA,EAAI3M,KAAI5P,IACd,IAAIwS,EAAOxS,EAAM,GAAK,EAAI,EACtB+Q,EAAM/Q,EAAMwS,EAEhB,OAAIzB,GAAO,OACH/Q,EAAM,MAGPwS,IAAUzB,EAAM,MAAS,QAAU,GAAI,IAGhDsH,QAAS,CACRkE,IAAO,CACNvH,OAAQI,IAETmjB,WAAc,CACb12B,KAAM,MACN2sB,QAAQ,EACRxZ,OAAQqjB,GACR9J,SAAS,GAEVpY,MAAS,CAAsB,EAC/BqiB,KAAQ,CACPxjB,OAAQI,GACRoZ,QAAQ,EACRlW,WAAW,GAEZmgB,YAAe,CACd52B,KAAM,OACN2sB,QAAQ,EACRxZ,OAAQqjB,IAETK,IAAO,CACNpzB,KAAM,SACN2mB,SAAS,EACTrxB,KAAM0V,GAAO,2BAA2B1V,KAAK0V,GAC7CyF,KAAAA,CAAOzF,GACFA,EAAI3S,QAAU,IAEjB2S,EAAMA,EAAIxK,QAAQ,aAAc,SAGjC,IAAI0yB,EAAO,GAKX,OAJAloB,EAAIxK,QAAQ,iBAAiB6yB,IAC5BH,EAAK93B,KAAKk4B,SAASD,EAAW,IAAM,IAAI,IAGlC,CACN5gB,QAAS,OACT/C,OAAQwjB,EAAKt8B,MAAM,EAAG,GACtB6a,MAAOyhB,EAAKt8B,MAAM,GAAG,GAEtB,EACD6xB,UAAW,SAAC/Y,EAAQ+B,GAET,IAFgB8hB,SAC1BA,GAAW,GACXz9B,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GACC2b,EAAQ,GACX/B,EAAOtU,KAAKqW,GAGb/B,EAASA,EAAOpF,KAAIQ,GAAKxW,KAAKk/B,MAAU,IAAJ1oB,KAEpC,IAAI2oB,EAAcF,GAAY7jB,EAAO8E,OAAM1J,GAAKA,EAAI,IAAO,IAEvDsoB,EAAM1jB,EAAOpF,KAAIQ,GAChB2oB,GACK3oB,EAAI,IAAIpU,SAAS,IAGnBoU,EAAEpU,SAAS,IAAIg9B,SAAS,EAAG,OAChCjzB,KAAK,IAER,MAAO,IAAM2yB,CACd,GAEDO,QAAW,CACV3zB,KAAM,SACN1K,KAAM0V,GAAO,YAAY1V,KAAK0V,GAC9ByF,KAAAA,CAAOzF,GAEN,IAAIH,EAAM,CAAC4H,QAAS,OAAQ/C,OAAQ,KAAM+B,MAAO,GAUjD,GARY,iBAHZzG,EAAMA,EAAItG,gBAITmG,EAAI6E,OAASia,GAAS3B,MACtBnd,EAAI4G,MAAQ,GAGZ5G,EAAI6E,OAASia,GAAS3e,GAGnBH,EAAI6E,OACP,OAAO7E,CAET,MCvHY+oB,GAAA,IAAIpd,GAAc,CAChC3a,GAAI,KACJiX,MAAO,aACPvW,KAAM,KACN6Q,KAAMqc,GAEN/V,SAAUsf,GAAKtf,SACfC,OAAQqf,GAAKrf,SCEd,IAAIkgB,GAEJ,GAJAnlB,GAASolB,cAAgBd,GAIN,oBAARe,KAAuBA,IAAIC,SAErC,IAAK,IAAIxkB,IAAS,CAACuI,GAAKwR,GAASqK,IAAK,CACrC,IAAIlkB,EAASF,EAAMwF,eAEfhK,EAAMyd,GADE,CAACjZ,QAAOE,SAAQ+B,MAAO,IAGnC,GAAIsiB,IAAIC,SAAS,QAAShpB,GAAM,CAC/B0D,GAASolB,cAAgBtkB,EACzB,KACD,CACD,CCnBM,SAASykB,GAAcpjB,GAE7B,OAAOzb,GAAIyb,EAAO,CAACwF,GAAS,KAC7B,CCHe,SAAS6d,GAAgBvX,EAAQC,GAC/CD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIuX,EAAK7/B,KAAK0N,IAAIiyB,GAAatX,GAAS,GACpCyX,EAAK9/B,KAAK0N,IAAIiyB,GAAarX,GAAS,GAMxC,OAJIwX,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,KAGTA,EAAK,MAAQC,EAAK,IAC3B,CCXA,MAMMC,GAAU,KACVC,GAAU,MAWhB,SAASC,GAAQC,GAChB,OAAIA,GAAKH,GACDG,EAEDA,GAAKH,GAAUG,IAAMF,EAC7B,CAEA,SAASG,GAAW/5B,GACnB,IAAIwS,EAAOxS,EAAM,GAAK,EAAI,EACtB+Q,EAAMnX,KAAKmX,IAAI/Q,GACnB,OAAOwS,EAAO5Y,KAAK0jB,IAAIvM,EAAK,IAC7B,CAGe,SAASipB,GAAcC,EAAYC,GAIjD,IAAIC,EACA7sB,EACA8sB,EAGAC,EAAG3a,EAAGhQ,EARVwqB,EAAazhB,GAASyhB,GACtBD,EAAaxhB,GAASwhB,GAStBC,EAAa7nB,GAAG6nB,EAAY,SAK3BG,EAAG3a,EAAGhQ,GAAKwqB,EAAWllB,OACvB,IAAIslB,EAAwB,SAAfP,GAAUM,GAAgC,SAAfN,GAAUra,GAAgC,QAAfqa,GAAUrqB,GAE7EuqB,EAAa5nB,GAAG4nB,EAAY,SAC3BI,EAAG3a,EAAGhQ,GAAKuqB,EAAWjlB,OACvB,IAAIulB,EAAuB,SAAfR,GAAUM,GAAgC,SAAfN,GAAUra,GAAgC,QAAfqa,GAAUrqB,GAGxE8qB,EAAOX,GAAOS,GACdG,EAAMZ,GAAOU,GAGbG,EAAMD,EAAMD,EAgChB,OA3BI5gC,KAAKmX,IAAI0pB,EAAMD,GAxDF,KAyDhBltB,EAAI,EAGAotB,GAEHP,EAAIM,GAvEQ,IAuEQD,GAtEP,IAuEbltB,EA3Dc,KA2DV6sB,IAIJA,EAAIM,GAzEO,IAyEQD,GA1EP,IA2EZltB,EA9Dc,KA8DV6sB,GAILC,EADGxgC,KAAKmX,IAAIzD,GAxEC,GAyEN,EAECA,EAAI,EAGLA,EAxEW,KA2EXA,EA3EW,KA8EL,IAAP8sB,CACR,CC7Fe,SAASO,GAAmB1Y,EAAQC,GAClDD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIuX,EAAK7/B,KAAK0N,IAAIiyB,GAAatX,GAAS,GACpCyX,EAAK9/B,KAAK0N,IAAIiyB,GAAarX,GAAS,GAEpCwX,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,IAGjB,IAAImB,EAASnB,EAAKC,EAClB,OAAiB,IAAVkB,EAAc,GAAKnB,EAAKC,GAAMkB,CACtC,CCPe,SAASC,GAAe5Y,EAAQC,GAC9CD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIuX,EAAK7/B,KAAK0N,IAAIiyB,GAAatX,GAAS,GACpCyX,EAAK9/B,KAAK0N,IAAIiyB,GAAarX,GAAS,GAMxC,OAJIwX,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,IAGH,IAAPC,EAbI,KAacD,EAAKC,GAAMA,CACrC,CClBe,SAASoB,GAAe7Y,EAAQC,GAC9CD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIjD,EAAKvkB,GAAIunB,EAAQ,CAAChF,GAAK,MACvBoC,EAAK3kB,GAAIwnB,EAAQ,CAACjF,GAAK,MAE3B,OAAOrjB,KAAKmX,IAAIkO,EAAKI,EACtB,CCXA,MACMtC,GAAK,GAAK,IACVC,GAAI,MAAQ,GAElB,IAAI9D,GAAQ3F,GAAOE,IAEnB,IAAesnB,GAAA,IAAIxjB,GAAW,CAC7BpW,GAAI,UACJU,KAAM,UACNmT,OAAQ,CACPkI,EAAG,CACFrH,SAAU,CAAC,EAAG,KACdhU,KAAM,aAEPW,EAAG,CACFqT,SAAU,EAAE,IAAK,MAElBnH,EAAG,CACFmH,SAAU,EAAE,IAAK,aAMnBqD,GAEAxG,KAAMiJ,GAGN3C,QAAAA,CAAUlF,GAET,IAGIhR,EAHMgR,EAAIlE,KAAI,CAACrU,EAAOkN,IAAMlN,EAAQ2d,GAAMzQ,KAGlCmH,KAAIrU,GAASA,EAlCjB,oBAkC6B3B,KAAKwjB,KAAK7hB,IAAUyhB,GAAIzhB,EAAQ,IAAM,MAE3E,MAAO,CACL,IAAMuH,EAAE,GAAM,GACf,KAAOA,EAAE,GAAKA,EAAE,IAChB,KAAOA,EAAE,GAAKA,EAAE,IAEjB,EAIDmW,MAAAA,CAAQoE,GAEP,IAAIva,EAAI,GAaR,OAZAA,EAAE,IAAMua,EAAI,GAAK,IAAM,IACvBva,EAAE,GAAKua,EAAI,GAAK,IAAMva,EAAE,GACxBA,EAAE,GAAKA,EAAE,GAAKua,EAAI,GAAK,IAGb,CACTva,EAAE,GAAOia,GAAKnjB,KAAK0jB,IAAIxa,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAMka,GACrEK,EAAI,GAAK,EAAKzjB,KAAK0jB,KAAKD,EAAI,GAAK,IAAM,IAAK,GAAKA,EAAI,GAAKL,GAC1Dla,EAAE,GAAOia,GAAKnjB,KAAK0jB,IAAIxa,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAMka,IAI3DpN,KAAI,CAACrU,EAAOkN,IAAMlN,EAAQ2d,GAAMzQ,IAC3C,EAED4P,QAAS,CACR,UAAW,CACVrD,OAAQ,CAAC,0BAA2B,gCAAiC,qCC5DxE,MAAMgmB,GAAyB,GAAnBphC,KAAK0jB,IAAI,EAAG,IAAa,GAEtB,SAAS2d,GAAkBhZ,EAAQC,GACjDD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIgZ,EAAQxgC,GAAIunB,EAAQ,CAAC8Y,GAAS,MAC9BI,EAAQzgC,GAAIwnB,EAAQ,CAAC6Y,GAAS,MAE9BK,EAAexhC,KAAKmX,IAAInX,KAAK0jB,IAAI4d,EAAOF,IAAOphC,KAAK0jB,IAAI6d,EAAOH,KAE/DK,EAAWzhC,KAAK0jB,IAAI8d,EAAe,EAAIJ,IAAQphC,KAAK0hC,MAAQ,GAEhE,OAAQD,EAAW,IAAO,EAAMA,CACjC,qJCpBO,SAASE,GAAIplB,GAEnB,IAAKqlB,EAAG1B,EAAG2B,GAAK/e,GAAOvG,EAAOwF,IAC1Bif,EAAQY,EAAI,GAAK1B,EAAI,EAAI2B,EAC7B,MAAO,CAAC,EAAID,EAAIZ,EAAO,EAAId,EAAIc,EAChC,CCLe,SAAS1mB,GAAQqO,EAAIC,GAAY,IAARjS,EAACnV,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EACvCiV,GAASE,KACZA,EAAI,CAAC7S,OAAQ6S,IAGd,IAAI7S,OAACA,EAASsW,GAASE,UAAWwnB,GAAQnrB,EAE1C,IAAK,IAAIZ,KAAKgc,GACb,GAAI,SAAWjuB,EAAOsM,gBAAkB2F,EAAE3F,cACzC,OAAO2hB,GAAchc,GAAG4S,EAAIC,EAAIkZ,GAIlC,MAAM,IAAIp/B,UAAW,0BAAyBoB,IAC/C,CC0GO,SAASiY,GAAOsM,EAAQC,GAAsB,IAAd9b,EAAOhL,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAChD,GAAIugC,GAAQ1Z,GAAS,CAEpB,IAAK/F,EAAG9V,GAAW,CAAC6b,EAAQC,GAE5B,OAAOvM,MAASuG,EAAE0f,UAAUC,OAAQ,IAAI3f,EAAE0f,UAAUx1B,WAAYA,GACjE,CAEA,IAAI0O,MAACA,EAAKgnB,YAAEA,EAAWC,YAAEA,EAAWC,cAAEA,GAAiB51B,EAEvD6b,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAGlBD,EAASF,GAAME,GACfC,EAASH,GAAMG,GAEf,IAAI0Z,EAAY,CAACC,OAAQ,CAAC5Z,EAAQC,GAAS9b,WAoB3C,GAjBC0O,EADGA,EACKyC,GAAW7c,IAAIoa,GAGfyC,GAAWW,SAASlE,GAASioB,qBAAuBha,EAAOnN,MAGpEgnB,EAAcA,EAAcvkB,GAAW7c,IAAIohC,GAAehnB,EAE1DmN,EAAS5P,GAAG4P,EAAQnN,GACpBoN,EAAS7P,GAAG6P,EAAQpN,GAGpBmN,EAASgK,GAAQhK,GACjBC,EAAS+J,GAAQ/J,GAIbpN,EAAME,OAAO0I,GAA6B,UAAxB5I,EAAME,OAAO0I,EAAEpY,KAAkB,CACtD,IAAI42B,EAAM91B,EAAQuX,IAAMvX,EAAQuX,KAAO,UAEnCA,EAAM,CAAC7I,EAAO,MACbqnB,EAAIC,GAAM,CAAC1hC,GAAIunB,EAAQtE,GAAMjjB,GAAIwnB,EAAQvE,IAI1CxM,MAAMgrB,KAAQhrB,MAAMirB,GACvBD,EAAKC,EAEGjrB,MAAMirB,KAAQjrB,MAAMgrB,KAC5BC,EAAKD,IAELA,EAAIC,G3C3KA,SAAiBF,EAAKG,GAC5B,GAAY,QAARH,EACH,OAAOG,EAGR,IAAKnd,EAAII,GAAM+c,EAAOzsB,IAAI2N,IAEtB+e,EAAYhd,EAAKJ,EA+BrB,MA7BY,eAARgd,EACCI,EAAY,IACfhd,GAAM,KAGS,eAAR4c,EACJI,EAAY,IACfpd,GAAM,KAGS,WAARgd,GACH,IAAMI,GAAaA,EAAY,MAC/BA,EAAY,EACfpd,GAAM,IAGNI,GAAM,KAIQ,YAAR4c,IACJI,EAAY,IACfpd,GAAM,IAEEod,GAAa,MACrBhd,GAAM,MAID,CAACJ,EAAII,EACb,C2CoIa+c,CAAcH,EAAK,CAACC,EAAIC,IACnC53B,GAAIyd,EAAQtE,EAAKwe,GACjB33B,GAAI0d,EAAQvE,EAAKye,EAClB,CAQA,OANIJ,IAEH/Z,EAAOjN,OAASiN,EAAOjN,OAAOpF,KAAIQ,GAAKA,EAAI6R,EAAOlL,QAClDmL,EAAOlN,OAASkN,EAAOlN,OAAOpF,KAAIQ,GAAKA,EAAI8R,EAAOnL,SAG5Cvc,OAAOqd,QAAOhI,IACpBA,EAAIksB,EAAcA,EAAYlsB,GAAKA,EACnC,IAAImF,EAASiN,EAAOjN,OAAOpF,KAAI,CAACqC,EAAOxJ,IAE/BuJ,GAAYC,EADTiQ,EAAOlN,OAAOvM,GACOoH,KAG5BkH,EAAQ/E,GAAYiQ,EAAOlL,MAAOmL,EAAOnL,MAAOlH,GAChDM,EAAM,CAAC2E,QAAOE,SAAQ+B,SAW1B,OATIilB,IAEH7rB,EAAI6E,OAAS7E,EAAI6E,OAAOpF,KAAIQ,GAAKA,EAAI2G,KAGlC+kB,IAAgBhnB,IACnB3E,EAAMkC,GAAGlC,EAAK2rB,IAGR3rB,CAAG,GACR,CACFyrB,aAEF,CAEO,SAASD,GAAS37B,GACxB,MAAqB,aAAdsF,GAAKtF,MAAyBA,EAAI47B,SAC1C,CAEA5nB,GAASioB,mBAAqB,MCpN9B,IAAeM,GAAA,IAAIhlB,GAAW,CAC7BpW,GAAI,MACJU,KAAM,MACNmT,OAAQ,CACP0I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,OAEPwZ,EAAG,CACF1F,MAAO,CAAC,EAAG,KACX9T,KAAM,cAEPqb,EAAG,CACFvH,MAAO,CAAC,EAAG,KACX9T,KAAM,cAIR6Q,KAAM4lB,GAGNtf,SAAUuD,IACT,IAAIjV,EAAM1N,KAAK0N,OAAOiV,GAClBhV,EAAM3N,KAAK2N,OAAOgV,IACjBL,EAAGC,EAAGzN,GAAK6N,GACXmB,EAAGrC,EAAG6B,GAAK,CAACrG,IAAK,GAAItP,EAAMD,GAAO,GACnCuL,EAAIvL,EAAMC,EAEd,GAAU,IAANsL,EAAS,CAGZ,OAFAwI,EAAW,IAAN6B,GAAiB,IAANA,EAAW,GAAK5V,EAAM4V,GAAKtjB,KAAK2N,IAAI2V,EAAG,EAAIA,GAEnD5V,GACP,KAAK4U,EAAGwB,GAAKvB,EAAIzN,GAAKmE,GAAKsJ,EAAIzN,EAAI,EAAI,GAAI,MAC3C,KAAKyN,EAAGuB,GAAKhP,EAAIwN,GAAKrJ,EAAI,EAAG,MAC7B,KAAKnE,EAAGgP,GAAKxB,EAAIC,GAAKtJ,EAAI,EAG3B6K,GAAQ,EACT,CAcA,OATIrC,EAAI,IACPqC,GAAK,IACLrC,EAAIzhB,KAAKmX,IAAIsK,IAGVqC,GAAK,MACRA,GAAK,KAGC,CAACA,EAAO,IAAJrC,EAAa,IAAJ6B,EAAQ,EAI7BjE,OAAQujB,IACP,IAAK9e,EAAGrC,EAAG6B,GAAKsf,EAUhB,SAAS15B,EAAGqE,GACX,IAAIsnB,GAAKtnB,EAAIuW,EAAI,IAAM,GACnBlb,EAAI6Y,EAAIzhB,KAAK2N,IAAI2V,EAAG,EAAIA,GAC5B,OAAOA,EAAI1a,EAAI5I,KAAK0N,KAAK,EAAG1N,KAAK2N,IAAIknB,EAAI,EAAG,EAAIA,EAAG,GACpD,CAEA,OAfA/Q,GAAQ,IAEJA,EAAI,IACPA,GAAK,KAGNrC,GAAK,IACL6B,GAAK,IAQE,CAACpa,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAG,EAG1BuV,QAAS,CACRmkB,IAAO,CACNxnB,OAAQ,CAAC,qBAAsB,eAAgB,iBAEhDynB,KAAQ,CACPznB,OAAQ,CAAC,qBAAsB,eAAgB,gBAC/CwZ,QAAQ,EACRlW,WAAW,MC/ECokB,GAAA,IAAInlB,GAAW,CAC7BpW,GAAI,MACJU,KAAM,MACNmT,OAAQ,CACP0I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,OAEPwZ,EAAG,CACF1F,MAAO,CAAC,EAAG,KACX9T,KAAM,cAEPmhB,EAAG,CACFrN,MAAO,CAAC,EAAG,KACX9T,KAAM,UAIR6Q,KAAM6pB,GAENvjB,QAAAA,CAAUwjB,GACT,IAAK9e,EAAGrC,EAAG6B,GAAKsf,EAChBnhB,GAAK,IACL6B,GAAK,IAEL,IAAI8F,EAAI9F,EAAI7B,EAAIzhB,KAAK2N,IAAI2V,EAAG,EAAIA,GAEhC,MAAO,CACNQ,EACM,IAANsF,EAAU,EAAI,KAAO,EAAI9F,EAAI8F,GAC7B,IAAMA,EAEP,EAED/J,MAAAA,CAAQ0jB,GACP,IAAKjf,EAAGrC,EAAG2H,GAAK2Z,EAEhBthB,GAAK,IACL2H,GAAK,IAEL,IAAI9F,EAAI8F,GAAK,EAAI3H,EAAI,GAErB,MAAO,CACNqC,EACO,IAANR,GAAiB,IAANA,EAAW,GAAM8F,EAAI9F,GAAKtjB,KAAK2N,IAAI2V,EAAG,EAAIA,GAAM,IACxD,IAAJA,EAED,EAED7E,QAAS,CACRlC,MAAO,CACNhV,GAAI,QACJ6T,OAAQ,CAAC,qBAAsB,0BAA2B,+BCrD9C4nB,GAAA,IAAIrlB,GAAW,CAC7BpW,GAAI,MACJU,KAAM,MACNmT,OAAQ,CACP0I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,OAEPg7B,EAAG,CACFlnB,MAAO,CAAC,EAAG,KACX9T,KAAM,aAEP6M,EAAG,CACFiH,MAAO,CAAC,EAAG,KACX9T,KAAM,cAIR6Q,KAAMgqB,GACN1jB,QAAAA,CAAU2jB,GACT,IAAKjf,EAAGrC,EAAG2H,GAAK2Z,EAEhB,MAAO,CAACjf,EAAGsF,GAAK,IAAM3H,GAAK,IAAK,IAAM2H,EACtC,EACD/J,MAAAA,CAAQ2jB,GACP,IAAKlf,EAAGmf,EAAGnuB,GAAKkuB,EAGhBC,GAAK,IACLnuB,GAAK,IAGL,IAAIouB,EAAMD,EAAInuB,EACd,GAAIouB,GAAO,EAAG,CAEb,MAAO,CAACpf,EAAG,EAAU,KADVmf,EAAIC,GAEhB,CAEA,IAAI9Z,EAAK,EAAItU,EAEb,MAAO,CAACgP,EAAO,KADA,IAANsF,EAAW,EAAI,EAAI6Z,EAAI7Z,GACR,IAAJA,EACpB,EAED3K,QAAS,CACRukB,IAAO,CACN5nB,OAAQ,CAAC,qBAAsB,0BAA2B,+BClC7D,IAAe+nB,GAAA,IAAIjhB,GAAc,CAChC3a,GAAI,gBACJiX,MAAO,mBACPvW,KAAM,kCACNqX,MAAO,cAhBQ,CACf,CAAE,kBAAsB,kBAAsB,mBAC9C,CAAE,mBAAsB,kBAAsB,oBAC9C,CAAE,mBAAsB,mBAAsB,oBAe9CoD,UAZiB,CACjB,CAAG,oBAAwB,mBAAuB,oBAClD,EAAG,kBAAwB,mBAAuB,oBAClD,CAAG,qBAAwB,mBAAuB,uBCdpC0gB,GAAA,IAAIlhB,GAAc,CAChC3a,GAAI,SACJiX,MAAO,UACPvW,KAAM,2BACN6Q,KAAMqqB,GACN9jB,OAAQ6V,GAAOA,EAAIlf,KAAI5P,GAAOpG,KAAK0jB,IAAI1jB,KAAKmX,IAAI/Q,GAAM,IAAM,KAAOpG,KAAK4Y,KAAKxS,KAC7EgZ,SAAU8V,GAAOA,EAAIlf,KAAI5P,GAAOpG,KAAK0jB,IAAI1jB,KAAKmX,IAAI/Q,GAAM,IAAM,KAAOpG,KAAK4Y,KAAKxS,OCUhF,IAAei9B,GAAA,IAAInhB,GAAc,CAChC3a,GAAI,kBACJiX,MAAO,wBACPvW,KAAM,kBACNqX,MAAO,MACPxG,KAAMoK,WAjBS,CACf,CAAE,kBAAsB,mBAAsB,mBAC9C,CAAE,kBAAsB,iBAAsB,mBAC9C,CAAE,EAAsB,EAAsB,oBAgB9CR,UAbiB,CACjB,CAAG,oBAAsB,oBAAsB,oBAC/C,EAAG,kBAAsB,mBAAsB,oBAC/C,CAAG,EAAsB,EAAsB,uBCVhD,IAAe4gB,GAAA,IAAIphB,GAAc,CAChC3a,GAAI,WACJiX,MAAO,eACPvW,KAAM,WACN6Q,KAAMuqB,GACNhkB,OAAQ6V,GAEAA,EAAIlf,KAAIoT,GAAKA,EATV,OASoBA,EAAI,GAAKA,GAAK,MAE7ChK,SAAU8V,GACFA,EAAIlf,KAAIoT,GAAKA,GAbX,WAaqBA,IAAM,EAAI,KAAO,GAAKA,MCZvCma,GAAA,IAAI5lB,GAAW,CAC7BpW,GAAI,QACJU,KAAM,QACNmT,OAAQ,CACPkI,EAAG,CACFrH,SAAU,CAAC,EAAG,GACdhU,KAAM,aAEPuO,EAAG,CACFyF,SAAU,CAAC,EAAG,IACdhU,KAAM,UAEP6b,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,QAGRqX,MAAO,MAEPxG,KAAM8O,GACNxI,QAAAA,CAAU2I,GAET,IACIjE,GADCE,EAAGpb,EAAGkM,GAAKiT,EAEhB,MAAMjJ,EAAI,KASV,OANCgF,EADG9jB,KAAKmX,IAAIvO,GAAKkW,GAAK9e,KAAKmX,IAAIrC,GAAKgK,EAChC7B,IAGmB,IAAnBjd,KAAKikB,MAAMnP,EAAGlM,GAAW5I,KAAKkY,GAG5B,CACN8L,EACAhkB,KAAKkkB,KAAKtb,GAAK,EAAIkM,GAAK,GACxBqP,GAAeL,GAEhB,EAEDzE,MAAAA,CAAQkkB,GACP,IACI36B,EAAGkM,GADFkP,EAAGtQ,EAAGoQ,GAAKyf,EAahB,OATIhsB,MAAMuM,IACTlb,EAAI,EACJkM,EAAI,IAGJlM,EAAI8K,EAAI1T,KAAKwkB,IAAIV,EAAI9jB,KAAKkY,GAAK,KAC/BpD,EAAIpB,EAAI1T,KAAKykB,IAAIX,EAAI9jB,KAAKkY,GAAK,MAGzB,CAAE8L,EAAGpb,EAAGkM,EACf,EAED2J,QAAS,CACR8kB,MAAS,CACRnoB,OAAQ,CAAC,0BAA2B,+BAAgC,0BC1DvE,IAAIkE,GAAQ3F,GAAOE,IAEnB,MACMuJ,GAAI,MAAQ,IACXogB,GAAeC,IAAiB9B,GAAG,CAACzmB,MAAO6G,GAAS3G,OAAQkE,KAEnE,IAAeokB,GAAA,IAAI/lB,GAAW,CAC7BpW,GAAI,MACJU,KAAM,MACNmT,OAAQ,CACPkI,EAAG,CACFrH,SAAU,CAAC,EAAG,KACdhU,KAAM,aAGP07B,EAAG,CACF1nB,SAAU,EAAE,IAAK,MAElBmN,EAAG,CACFnN,SAAU,EAAE,IAAK,OAInBqD,MAAOA,GACPxG,KAAMiJ,GAIN3C,QAAAA,CAAUlF,GACT,IAAI0I,EAAM,CAACnL,GAASyC,EAAI,IAAKzC,GAASyC,EAAI,IAAKzC,GAASyC,EAAI,KACxD8H,EAAIY,EAAI,IAEPghB,EAAIC,GAAMlC,GAAG,CAACzmB,MAAO6G,GAAS3G,OAAQwH,IAG3C,IAAKtL,OAAOwsB,SAASF,KAAQtsB,OAAOwsB,SAASD,GAC5C,MAAO,CAAC,EAAG,EAAG,GAGf,IAAI7f,EAAIhC,GArCA,oBAqCSoB,GAAIpB,EAAI,IAAMhiB,KAAKwjB,KAAKxB,GAAK,GAC9C,MAAO,CACNgC,EACA,GAAKA,GAAK4f,EAAKJ,IACf,GAAKxf,GAAK6f,EAAKJ,IAEhB,EAIDpkB,MAAAA,CAAQqkB,GACP,IAAK1f,EAAG2f,EAAGva,GAAKsa,EAGhB,GAAU,IAAN1f,GAAWhN,GAAOgN,GACrB,MAAO,CAAC,EAAG,EAAG,GAGf2f,EAAIlsB,GAASksB,GACbva,EAAI3R,GAAS2R,GAEb,IAAIwa,EAAMD,GAAK,GAAK3f,GAAMwf,GACtBK,EAAMza,GAAK,GAAKpF,GAAMyf,GAEtBzhB,EAAIgC,GAAK,EAAIA,EAAIZ,GAAIpjB,KAAK0jB,KAAKM,EAAI,IAAM,IAAK,GAElD,MAAO,CACNhC,GAAM,EAAI4hB,GAAO,EAAIC,IACrB7hB,EACAA,IAAM,GAAK,EAAI4hB,EAAK,GAAKC,IAAO,EAAIA,IAErC,EAEDplB,QAAS,CACRlC,MAAO,CACNhV,GAAI,QACJ6T,OAAQ,CAAC,0BAA2B,gCAAiC,qCC7EzD2oB,GAAA,IAAIpmB,GAAW,CAC7BpW,GAAI,QACJU,KAAM,QACNmT,OAAQ,CACPkI,EAAG,CACFrH,SAAU,CAAC,EAAG,KACdhU,KAAM,aAEPuO,EAAG,CACFyF,SAAU,CAAC,EAAG,KACdhU,KAAM,UAEP6b,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,QAIR6Q,KAAM4qB,GACNtkB,QAAAA,CAAUskB,GAET,IACI3f,GADCC,EAAG2f,EAAGva,GAAKsa,EAWhB,OANC3f,EADG/jB,KAAKmX,IAAIwsB,GAFH,KAEa3jC,KAAKmX,IAAIiS,GAFtB,IAGHnM,IAGmB,IAAnBjd,KAAKikB,MAAMmF,EAAGua,GAAW3jC,KAAKkY,GAG9B,CACN8L,EACAhkB,KAAKkkB,KAAKyf,GAAK,EAAIva,GAAK,GACxBjF,GAAeJ,GAEhB,EACD1E,MAAAA,CAAQ+E,GAEP,IAAKC,EAAWC,EAAQC,GAAOH,EAS/B,OAPIE,EAAS,IACZA,EAAS,GAGN/M,MAAMgN,KACTA,EAAM,GAEA,CACNF,EACAC,EAAStkB,KAAKwkB,IAAID,EAAMvkB,KAAKkY,GAAK,KAClCoM,EAAStkB,KAAKykB,IAAIF,EAAMvkB,KAAKkY,GAAK,KAEnC,EAEDuG,QAAS,CACRlC,MAAO,CACNhV,GAAI,UACJ6T,OAAQ,CAAC,0BAA2B,0BAA2B,0BClClE,MAGM4oB,GAAOthB,GAAU,GAAG,GACpBuhB,GAAOvhB,GAAU,GAAG,GACpBwhB,GAAOxhB,GAAU,GAAG,GACpByhB,GAAOzhB,GAAU,GAAG,GACpB0hB,GAAO1hB,GAAU,GAAG,GACpB2hB,GAAO3hB,GAAU,GAAG,GACpB4hB,GAAO5hB,GAAU,GAAG,GACpB6hB,GAAO7hB,GAAU,GAAG,GACpB8hB,GAAO9hB,GAAU,GAAG,GAE1B,SAAS+hB,GAAyBC,EAAOC,EAAW/gB,GACnD,MAAM3K,EAAI0rB,GAAa3kC,KAAKykB,IAAIb,GAAS8gB,EAAQ1kC,KAAKwkB,IAAIZ,IAC1D,OAAO3K,EAAI,EAAIoY,IAAWpY,CAC3B,CAEO,SAAS2rB,GAAwBthB,GACvC,MAAMuhB,EAAO7kC,KAAK0jB,IAAIJ,EAAI,GAAI,GAAK,QAC7BwhB,EAAOD,EApBJ,oBAoBeA,EAAOvhB,EAnBtB,kBAoBHyhB,EAAMD,GAAQ,OAASd,GAAO,MAAQE,IACtCc,EAAMF,GAAQ,OAASZ,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMH,GAAQ,OAASZ,GAAO,OAASD,IACvCiB,EAAMJ,GAAQ,OAASX,GAAO,MAAQE,IACtCc,EAAML,GAAQ,OAAST,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMN,GAAQ,OAAST,GAAO,OAASD,IACvCiB,EAAMP,GAAQ,OAASR,GAAO,MAAQE,IACtCc,EAAMR,GAAQ,OAASN,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMT,GAAQ,OAASN,GAAO,OAASD,IAE7C,MAAO,CACNiB,IAAKT,EAAME,EACXQ,IAAKT,EAAM1hB,EAAI2hB,EACfS,IAAKX,GAAOE,EAAM,QAClBU,KAAMX,EAAM,QAAU1hB,GAAK2hB,EAAM,QACjCW,IAAKV,EAAME,EACXS,IAAKV,EAAM7hB,EAAI8hB,EACfU,IAAKZ,GAAOE,EAAM,QAClBW,KAAMZ,EAAM,QAAU7hB,GAAK8hB,EAAM,QACjCY,IAAKX,EAAME,EACXU,IAAKX,EAAMhiB,EAAIiiB,EACfW,IAAKb,GAAOE,EAAM,QAClBY,KAAMb,EAAM,QAAUhiB,GAAKiiB,EAAM,QAEnC,CAEA,SAASa,GAAoBC,EAAOviB,GACnC,MAAMwiB,EAASxiB,EAAI,IAAM9jB,KAAKkY,GAAK,EAC7BquB,EAAK9B,GAAwB4B,EAAMb,IAAKa,EAAMZ,IAAKa,GACnDE,EAAK/B,GAAwB4B,EAAMX,IAAKW,EAAMV,IAAKW,GACnDG,EAAKhC,GAAwB4B,EAAMT,IAAKS,EAAMR,IAAKS,GACnDI,EAAKjC,GAAwB4B,EAAMP,IAAKO,EAAMN,IAAKO,GACnDK,EAAKlC,GAAwB4B,EAAML,IAAKK,EAAMJ,IAAKK,GACnD/gB,EAAKkf,GAAwB4B,EAAMH,IAAKG,EAAMF,IAAKG,GAEzD,OAAOtmC,KAAK2N,IAAI44B,EAAIC,EAAIC,EAAIC,EAAIC,EAAIphB,EACrC,CAEA,IAAeqhB,GAAA,IAAIjpB,GAAW,CAC7BpW,GAAI,QACJU,KAAM,QACNmT,OAAQ,CACP0I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,OAEPwZ,EAAG,CACF1F,MAAO,CAAC,EAAG,KACX9T,KAAM,cAEPqb,EAAG,CACFvH,MAAO,CAAC,EAAG,KACX9T,KAAM,cAIR6Q,KAAMirB,GACNvkB,WAAYkf,GAGZtf,QAAAA,CAAUyE,GACT,IACIpC,GADC6B,EAAG9M,EAAGsN,GAAK,CAACrM,GAASoM,EAAI,IAAKpM,GAASoM,EAAI,IAAKpM,GAASoM,EAAI,KAGlE,GAAIP,EAAI,WACP7B,EAAI,EACJ6B,EAAI,SAEA,GAAIA,EAAI,KACZ7B,EAAI,EACJ6B,EAAI,MAEA,CAGJ7B,EAAIjL,EADM4vB,GADExB,GAAuBthB,GACCQ,GACtB,GACf,CAEA,MAAO,CAACA,EAAGrC,EAAG6B,EACd,EAGDjE,MAAAA,CAAQujB,GACP,IACIpsB,GADCsN,EAAGrC,EAAG6B,GAAK,CAAC7L,GAASmrB,EAAI,IAAKnrB,GAASmrB,EAAI,IAAKnrB,GAASmrB,EAAI,KAGlE,GAAItf,EAAI,WACPA,EAAI,IACJ9M,EAAI,OAEA,GAAI8M,EAAI,KACZA,EAAI,EACJ9M,EAAI,MAEA,CAGJA,EADU4vB,GADExB,GAAuBthB,GACCQ,GAC1B,IAAMrC,CACjB,CAEA,MAAO,CAAC6B,EAAG9M,EAAGsN,EACd,EAEDrF,QAAS,CACRlC,MAAO,CACNhV,GAAI,UACJ6T,OAAQ,CAAC,qBAAsB,0BAA2B,+BCnH7D,SAASyrB,GAAoBnC,EAAOC,GACnC,OAAO3kC,KAAKmX,IAAIwtB,GAAa3kC,KAAKkkB,KAAKlkB,KAAK0jB,IAAIghB,EAAO,GAAK,EAC7D,CAEA,SAASoC,GAAoBT,GAC5B,IAAIE,EAAKM,GAAmBR,EAAMb,IAAKa,EAAMZ,KACzCe,EAAKK,GAAmBR,EAAMX,IAAKW,EAAMV,KACzCc,EAAKI,GAAmBR,EAAMT,IAAKS,EAAMR,KACzCa,EAAKG,GAAmBR,EAAMP,IAAKO,EAAMN,KACzCY,EAAKE,GAAmBR,EAAML,IAAKK,EAAMJ,KACzC1gB,EAAKshB,GAAmBR,EAAMH,IAAKG,EAAMF,KAE7C,OAAOnmC,KAAK2N,IAAI44B,EAAIC,EAAIC,EAAIC,EAAIC,EAAIphB,EACrC,CAvBa7C,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GAiB1B,IAAeqkB,GAAA,IAAIppB,GAAW,CAC7BpW,GAAI,QACJU,KAAM,QACNmT,OAAQ,CACP0I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdvQ,KAAM,QACNzD,KAAM,OAEPwZ,EAAG,CACF1F,MAAO,CAAC,EAAG,KACX9T,KAAM,cAEPqb,EAAG,CACFvH,MAAO,CAAC,EAAG,KACX9T,KAAM,cAIR6Q,KAAMirB,GACNvkB,WAAY,OAGZJ,QAAAA,CAAUyE,GACT,IACIpC,GADC6B,EAAG9M,EAAGsN,GAAK,CAACrM,GAASoM,EAAI,IAAKpM,GAASoM,EAAI,IAAKpM,GAASoM,EAAI,KAGlE,GAAIP,EAAI,WACP7B,EAAI,EACJ6B,EAAI,SAEA,GAAIA,EAAI,KACZ7B,EAAI,EACJ6B,EAAI,MAEA,CAGJ7B,EAAIjL,EADMswB,GADElC,GAAuBthB,IAErB,GACf,CACA,MAAO,CAACQ,EAAGrC,EAAG6B,EACd,EAGDjE,MAAAA,CAAQujB,GACP,IACIpsB,GADCsN,EAAGrC,EAAG6B,GAAK,CAAC7L,GAASmrB,EAAI,IAAKnrB,GAASmrB,EAAI,IAAKnrB,GAASmrB,EAAI,KAGlE,GAAItf,EAAI,WACPA,EAAI,IACJ9M,EAAI,OAEA,GAAI8M,EAAI,KACZA,EAAI,EACJ9M,EAAI,MAEA,CAGJA,EADUswB,GADElC,GAAuBthB,IAEzB,IAAM7B,CACjB,CAEA,MAAO,CAAC6B,EAAG9M,EAAGsN,EACd,EAEDrF,QAAS,CACRlC,MAAO,CACNhV,GAAI,UACJ6T,OAAQ,CAAC,qBAAsB,0BAA2B,+BC3H7D,MACM7N,GAAI,KAAQ,MAGZy5B,GAAQ,GAAU,KAClBre,GAAK,SACLC,GAAK,KAAQ,IACbU,GAAK,QAEX,IAAe2d,GAAA,IAAI/kB,GAAc,CAChC3a,GAAI,YACJiX,MAAO,aACPvW,KAAM,cACN6Q,KAAMgc,GACNzV,OAAQ6V,GAGAA,EAAIlf,KAAI,SAAU5P,GAExB,OAAY,KADFpG,KAAK0N,IAAMtH,GAAO4gC,GAAQre,GAAK,IAAMC,GAAMU,GAAMljB,GAAO4gC,MAhBvD,kBAFH,GAoBT,IAED5nB,SAAU8V,GAGFA,EAAIlf,KAAI,SAAU5P,GACxB,IAAIkH,EAAItN,KAAK0N,IA1BL,IA0BStH,EAAW,IAAO,GAInC,QAHWuiB,GAAMC,GAAMtb,GAAKC,KACf,EAAK+b,GAAMhc,GAAKC,MAzBtB,QA4BR,MC7BF,MAAM3E,GAAI,UACJkM,GAAI,UACJ0B,GAAI,UAEJ0wB,GAAQ,OAEd,IAAeC,GAAA,IAAIjlB,GAAc,CAChC3a,GAAI,aACJiX,MAAO,cACPvW,KAAM,eACN2X,SAAU,QAEV9G,KAAMgc,GACNzV,OAAQ6V,GAGAA,EAAIlf,KAAI,SAAU5P,GAKxB,OAAIA,GAAO,GACFA,GAAO,EAAK,EAAI8gC,IAEhBlnC,KAAK+Y,KAAK3S,EAAMoQ,IAAK5N,IAAKkM,IAAK,GAAMoyB,EAC/C,IAED9nB,SAAU8V,GAIFA,EAAIlf,KAAI,SAAU5P,GAMxB,OAJAA,GAAO8gC,KAII,EAAI,GACPlnC,KAAKkkB,KAAK,EAAI9d,GAEfwC,GAAI5I,KAAK0xB,IAAI,GAAKtrB,EAAM0O,IAAK0B,EACrC,MC1CK,MAAM4wB,GAAO,CAAA,EAcb,SAASC,GAASxwB,GAA8B,IAA5BtP,GAACA,EAAE+/B,SAAEA,EAAQC,WAAEA,GAAW1wB,EAEpDuwB,GAAK7/B,GAAM/F,UAAU,EACtB,CAEO,SAASuY,GAAOC,EAAIC,GAAqB,IAK3CnW,EAASsjC,GALmB5lC,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,aAO9BgmC,EAAIC,EAAIC,GAAM9xB,GAAiB9R,EAAOwjC,SAAUttB,IAChD2tB,EAAIC,EAAIC,GAAMjyB,GAAiB9R,EAAOwjC,SAAUrtB,GAUjD6tB,EAAgBlyB,GAPR,CACX,CAAC+xB,EAAKH,EAAK,EAAU,GACrB,CAAC,EAAUI,EAAKH,EAAK,GACrB,CAAC,EAAU,EAAUI,EAAKH,IAIiB5jC,EAAOwjC,UAGnD,OAFc1xB,GAAiB9R,EAAOyjC,WAAYO,EAGnD,CAvCApuB,GAAMP,IAAI,8BAA8BK,IACnCA,EAAIhN,QAAQ1I,SACf0V,EAAIW,EAAIJ,GAAMP,EAAIQ,GAAIR,EAAIS,GAAIT,EAAIhN,QAAQ1I,QAC3C,IAGD4V,GAAMP,IAAI,4BAA4BK,IAChCA,EAAIW,IACRX,EAAIW,EAAIJ,GAAMP,EAAIQ,GAAIR,EAAIS,GAAIT,EAAIhN,QAAQ1I,QAC3C,IAgCDujC,GAAU,CACT9/B,GAAI,YACJ+/B,SAAU,CACT,CAAG,OAAY,OAAY,QAC3B,EAAG,MAAY,QAAY,OAC3B,CAAG,EAAY,EAAY,SAE5BC,WAAY,CACX,CAAE,oBAAqB,mBAAsB,oBAC7C,CAAE,kBAAqB,mBAAsB,sBAC7C,CAAE,EAAqB,EAAsB,uBAI/CF,GAAU,CACT9/B,GAAI,WAGJ+/B,SAAU,CACT,CAAG,MAAY,OAAY,OAC3B,EAAG,MAAY,OAAY,OAC3B,CAAG,OAAY,MAAY,SAG5BC,WAAY,CACX,CAAG,mBAAqB,mBAAqB,oBAC7C,CAAG,kBAAqB,kBAAqB,qBAC7C,EAAG,mBAAqB,mBAAqB,oBAI/CF,GAAU,CACT9/B,GAAI,QAEJ+/B,SAAU,CACT,CAAG,MAAY,OAAY,OAC3B,EAAG,MAAY,OAAY,OAC3B,CAAG,KAAY,MAAY,QAE5BC,WAAY,CACX,CAAG,oBAAuB,mBAAqB,oBAC/C,CAAG,kBAAuB,kBAAqB,oBAC/C,EAAG,qBAAuB,mBAAqB,uBAIjDF,GAAU,CACT9/B,GAAI,QACJ+/B,SAAU,CACT,CAAG,QAAW,SAAW,SACzB,EAAG,QAAW,SAAW,SACzB,EAAG,QAAW,QAAW,UAG1BC,WAAY,CACX,CAAG,mBAAsB,mBAAqB,oBAC9C,CAAG,kBAAsB,mBAAqB,qBAC9C,EAAG,oBAAsB,mBAAqB,uBAIhD3mC,OAAOqd,OAAOtE,GAAQ,CAIrB9D,EAAK,CAAC,OAAS,EAAS,QAGxBnC,EAAK,CAAC,OAAS,EAAU,SAKzBq0B,IAAK,CAAC,OAAS,EAAS,QACxBC,IAAK,CAAC,OAAS,EAAS,SAGxBhU,EAAK,CAAC,EAAS,EAAS,GAGxBiU,GAAK,CAAC,OAAS,EAAS,QACxBC,GAAK,CAAC,OAAS,EAAS,SACxBC,IAAK,CAAC,QAAS,EAAS,SCzHzBxuB,GAAOyuB,KAAO,CAAC,OAAU,OAAS,EAAS,OAAgC,QAc3E,IAAeC,GAAA,IAAInmB,GAAc,CAChC3a,GAAI,SACJiX,MAAO,WACPvW,KAAM,SAKNmT,OAAQ,CACPkH,EAAG,CACFvG,MAAO,CAAC,EAAG,OACX9T,KAAM,OAEPsa,EAAG,CACFxG,MAAO,CAAC,EAAG,OACX9T,KAAM,SAEP6M,EAAG,CACFiH,MAAO,CAAC,EAAG,OACX9T,KAAM,SAIR2X,SAAU,QAEVN,MAAO3F,GAAOyuB,KAEd3lB,QAtCe,CACf,CAAG,kBAAsB,mBAAsB,mBAC/C,CAAG,mBAAsB,kBAAsB,oBAC/C,EAAG,oBAAsB,oBAAsB,qBAoC/CC,UAlCiB,CACjB,CAAG,oBAAuB,iBAAsB,oBAChD,EAAG,kBAAuB,mBAAsB,qBAChD,CAAG,qBAAuB,oBAAsB,sBCfjD,MAAM5D,GAAI,IAAM,GAIVwpB,IAAoB,UAGpBC,IAAevoC,KAAKwoC,KAAK,OAAS,MAAQ,MAEhD,IAAeC,GAAA,IAAIvmB,GAAc,CAChC3a,GAAI,SACJiX,MAAO,WACPvW,KAAM,SASNmT,OAAQ,CACPkH,EAAG,CACFvG,MAAO,CAACusB,GAAkBC,IAC1BtgC,KAAM,OAEPsa,EAAG,CACFxG,MAAO,CAACusB,GAAkBC,IAC1BtgC,KAAM,SAEP6M,EAAG,CACFiH,MAAO,CAACusB,GAAkBC,IAC1BtgC,KAAM,SAGR2X,SAAU,QAEV9G,KAAMuvB,GAENhpB,OAAQ6V,GAGAA,EAAIlf,KAAI,SAAU5P,GACxB,OAAIA,IAHO,kBAIiC,GAAnC,IAAa,MAANA,EAAe,MAAQ0Y,IAE9B1Y,EAAMmiC,GACP,IAAa,MAANniC,EAAe,MAGtB,KAET,IAIDgZ,SAAU8V,GACFA,EAAIlf,KAAI,SAAU5P,GACxB,OAAIA,GAAO,GACFpG,KAAKwoC,KAAK1pB,IAAK,MAAQ,MAEvB1Y,EAAM0Y,IACL9e,KAAKwoC,KAAK1pB,GAAU,GAAN1Y,GAAa,MAAQ,OAGnCpG,KAAKwoC,KAAKpiC,GAAO,MAAQ,KAEpC,+rBClEa,SAAmBi6B,EAAYC,GAAoB,IAAR3pB,EAACnV,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EACzDiV,GAASE,KACZA,EAAI,CAAC+xB,UAAW/xB,IAGjB,IAAI+xB,UAACA,KAAc5G,GAAQnrB,EAE3B,IAAK+xB,EAAW,CACf,IAAIC,EAAa/nC,OAAO6J,KAAKm+B,IAAoB5yB,KAAIpN,GAAKA,EAAEsD,QAAQ,YAAa,MAAKC,KAAK,MAC3F,MAAM,IAAIzJ,UAAW,0EAAyEimC,IAC/F,CAEAtI,EAAaxhB,GAASwhB,GACtBC,EAAazhB,GAASyhB,GAEtB,IAAK,IAAI13B,KAAKggC,GACb,GAAI,WAAaF,EAAUt4B,gBAAkBxH,EAAEwH,cAC9C,OAAOw4B,GAAmBhgC,GAAGy3B,EAAYC,EAAYwB,GAIvD,MAAM,IAAIp/B,UAAW,+BAA8BgmC,IACpD,8KClBO,SAAiBnsB,GAAqB,IAAdssB,EAAMrnC,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,IAGvC,OAAOoJ,GAAI2R,EADK,CADJoB,GAAW7c,IAAI,QAAS,OACZ,MACKwiB,GAAKA,GAAK,EAAIulB,IAC5C,kO9B4Be,SAAkBtsB,GAA0D,IAAnDrB,MAACA,EAAQd,GAASolB,iBAAkBhzB,GAAQhL,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAClF+U,EAAM4d,GAAU5X,EAAO/P,GAE3B,GAAmB,oBAARizB,KAAuBA,IAAIC,SAAS,QAASnpB,KAAS6D,GAASolB,cACzEjpB,EAAM,IAAIhS,OAAOgS,GACjBA,EAAIgG,MAAQA,MAER,CAEJ,IAAIusB,EAAgBvsB,EAKP,IAAAwsB,EAAb,GAFcxsB,EAAMnB,OAAO4tB,KAAKhyB,KAAWA,GAAOuF,EAAMY,OAIvD,aAAI4rB,EAAExJ,UAAY,IAAAwJ,EAAAA,EAAZxJ,GAAiBE,IAAIC,SAAS,QAAS,wBAE5CoJ,EAAgB3gB,GAAM5L,GACtBusB,EAAc1tB,OAAS0tB,EAAc1tB,OAAOpF,IAAIyB,IAChDqxB,EAAc3rB,MAAQ1F,GAASqxB,EAAc3rB,OAE7C5G,EAAM4d,GAAU2U,EAAet8B,GAE3BizB,IAAIC,SAAS,QAASnpB,IAIzB,OAFAA,EAAM,IAAIhS,OAAOgS,GACjBA,EAAIgG,MAAQusB,EACLvyB,EAOVuyB,EAAgBrwB,GAAGqwB,EAAe5tB,GAClC3E,EAAM,IAAIhS,OAAO4vB,GAAU2U,EAAet8B,IAC1C+J,EAAIgG,MAAQusB,CACb,CAEA,OAAOvyB,CACR,qC+BhFe,SAAiB8R,EAAQC,GAIvC,OAHAD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAEXD,EAAOnN,QAAUoN,EAAOpN,OACrBmN,EAAOlL,QAAUmL,EAAOnL,OACxBkL,EAAOjN,OAAO8E,OAAM,CAAC1J,EAAG3H,IAAM2H,IAAM8R,EAAOlN,OAAOvM,IAC7D,sJDNO,SAAkB0N,GAAqB,IAAdssB,EAAMrnC,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,IAGxC,OAAOoJ,GAAI2R,EADK,CADJoB,GAAW7c,IAAI,QAAS,OACZ,MACKwiB,GAAKA,GAAK,EAAIulB,IAC5C,cnBmBO,SAAclgB,EAAIC,GAAoB,IAAhB3S,EAACzU,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,GAAImV,EAACnV,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAQxC,OAPCmnB,EAAIC,GAAM,CAAC/J,GAAS8J,GAAK9J,GAAS+J,IAEnB,WAAZld,GAAKuK,MACPA,EAAGU,GAAK,CAAC,GAAIV,IAGP8F,GAAM4M,EAAIC,EAAIjS,EACf2L,CAAErM,EACV,sJVvBO,SAAuBsG,EAAO5a,GAEpCiJ,GAAI2R,EAAO,CAACwF,GAAS,KAAMpgB,EAC5B,gBU6BO,SAAgBgnB,EAAIC,GAAkB,IACxCqgB,EAD0Bz8B,EAAOhL,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAGpCugC,GAAQpZ,MAEVsgB,EAAYz8B,GAAW,CAACmc,EAAIC,IAC5BD,EAAIC,GAAMqgB,EAAWjH,UAAUC,QAGjC,IAAIiH,UACHA,EAAShX,aAAEA,EAAYiX,MACvBA,EAAQ,EAACC,SAAEA,EAAW,OACnBC,GACA78B,EAECy8B,KACHtgB,EAAIC,GAAM,CAAC/J,GAAS8J,GAAK9J,GAAS+J,IACnCqgB,EAAaltB,GAAM4M,EAAIC,EAAIygB,IAG5B,IAAIC,EAAahvB,GAAOqO,EAAIC,GACxB2gB,EAAcL,EAAY,EAAIlpC,KAAK0N,IAAIy7B,EAAOnpC,KAAKmN,KAAKm8B,EAAaJ,GAAa,GAAKC,EACvF5yB,EAAM,GAMV,QAJiB9S,IAAb2lC,IACHG,EAAcvpC,KAAK2N,IAAI47B,EAAaH,IAGjB,IAAhBG,EACHhzB,EAAM,CAAC,CAACN,EAAG,GAAIsG,MAAO0sB,EAAW,UAE7B,CACJ,IAAIO,EAAO,GAAKD,EAAc,GAC9BhzB,EAAMjF,MAAMkH,KAAK,CAACzU,OAAQwlC,IAAc,CAACpzB,EAAGtH,KAC3C,IAAIoH,EAAIpH,EAAI26B,EACZ,MAAO,CAACvzB,IAAGsG,MAAO0sB,EAAWhzB,GAAG,GAElC,CAEA,GAAIizB,EAAY,EAAG,CAElB,IAAIO,EAAWlzB,EAAIkS,QAAO,CAACC,EAAKghB,EAAK76B,KACpC,GAAU,IAANA,EACH,OAAO,EAGR,IAAI86B,EAAKrvB,GAAOovB,EAAIntB,MAAOhG,EAAI1H,EAAI,GAAG0N,MAAO2V,GAC7C,OAAOlyB,KAAK0N,IAAIgb,EAAKihB,EAAG,GACtB,GAEH,KAAOF,EAAWP,GAAW,CAG5BO,EAAW,EAEX,IAAK,IAAI56B,EAAI,EAAIA,EAAI0H,EAAIxS,QAAYwS,EAAIxS,OAASqlC,EAAWv6B,IAAK,CACjE,IAAI+6B,EAAOrzB,EAAI1H,EAAI,GACf66B,EAAMnzB,EAAI1H,GAEVoH,GAAKyzB,EAAIzzB,EAAI2zB,EAAK3zB,GAAK,EACvBsG,EAAQ0sB,EAAWhzB,GACvBwzB,EAAWzpC,KAAK0N,IAAI+7B,EAAUnvB,GAAOiC,EAAOqtB,EAAKrtB,OAAQjC,GAAOiC,EAAOmtB,EAAIntB,QAC3EhG,EAAIszB,OAAOh7B,EAAG,EAAG,CAACoH,IAAGsG,MAAO0sB,EAAWhzB,KACvCpH,GACD,CACD,CACD,CAIA,OAFA0H,EAAMA,EAAIP,KAAIpN,GAAKA,EAAE2T,QAEdhG,CACR,kFFxGO,SAAagG,GAEnB,IAAKqlB,EAAG1B,EAAG2B,GAAK/e,GAAOvG,EAAOwF,IACzBmhB,EAAMtB,EAAI1B,EAAI2B,EACnB,MAAO,CAACD,EAAIsB,EAAKhD,EAAIgD,EACtB", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 87, 124, 126]}