var Color=function(){"use strict";function e(e,t){let r=e.length;Array.isArray(e[0])||(e=[e]),Array.isArray(t[0])||(t=t.map((e=>[e])));let a=t[0].length,n=t[0].map(((e,r)=>t.map((e=>e[r])))),o=e.map((e=>n.map((t=>{let r=0;if(!Array.isArray(e)){for(let a of t)r+=e*a;return r}for(let a=0;a<e.length;a++)r+=e[a]*(t[a]||0);return r}))));return 1===r&&(o=o[0]),1===a?o.map((e=>e[0])):o}function t(e){return"string"===r(e)}function r(e){return(Object.prototype.toString.call(e).match(/^\[object\s+(.*?)\]$/)[1]||"").toLowerCase()}function a(e,{precision:t,unit:r}){return n(e)?"none":s(e,t)+(r??"")}function n(e){return Number.isNaN(e)||e instanceof Number&&e?.none}function o(e){return n(e)?0:e}function s(e,t){if(0===e)return 0;let r=~~e,a=0;r&&t&&(a=1+~~Math.log10(Math.abs(r)));const n=10**(t-a);return Math.floor(e*n+.5)/n}const i={deg:1,grad:.9,rad:180/Math.PI,turn:360};function c(e){if(!e)return;e=e.trim();const t=/^-?[\d.]+$/,r=/%|deg|g?rad|turn$/,a=/\/?\s*(none|[-\w.]+(?:%|deg|g?rad|turn)?)/g;let n=e.match(/^([a-z]+)\((.+?)\)$/i);if(n){let e=[];return n[2].replace(a,((a,n)=>{let o=n.match(r),s=n;if(o){let e=o[0],t=s.slice(0,-e.length);"%"===e?(s=new Number(t/100),s.type="<percentage>"):(s=new Number(t*i[e]),s.type="<angle>",s.unit=e)}else t.test(s)?(s=new Number(s),s.type="<number>"):"none"===s&&(s=new Number(NaN),s.none=!0);a.startsWith("/")&&(s=s instanceof Number?s:new Number(s),s.alpha=!0),"object"==typeof s&&s instanceof Number&&(s.raw=n),e.push(s)})),{name:n[1].toLowerCase(),rawName:n[1],rawArgs:n[2],args:e}}}function l(e){return e[e.length-1]}function u(e,t,r){return isNaN(e)?t:isNaN(t)?e:e+(t-e)*r}function h(e,t,r){return(r-e)/(t-e)}function d(e,t,r){return u(t[0],t[1],h(e[0],e[1],r))}function m(e){return e.map((e=>e.split("|").map((e=>{let t=(e=e.trim()).match(/^(<[a-z]+>)\[(-?[.\d]+),\s*(-?[.\d]+)\]?$/);if(t){let e=new String(t[1]);return e.range=[+t[2],+t[3]],e}return e}))))}function f(e,t,r){return Math.max(Math.min(r,t),e)}function p(e,t){return Math.sign(e)===Math.sign(t)?e:-e}function g(e,t){return p(Math.abs(e)**t,e)}function b(e,t){return 0===t?0:e/t}function M(e,t,r=0,a=e.length){for(;r<a;){const n=r+a>>1;e[n]<t?r=n+1:a=n}return r}var w=Object.freeze({__proto__:null,bisectLeft:M,clamp:f,copySign:p,interpolate:u,interpolateInv:h,isNone:n,isString:t,last:l,mapRange:d,multiplyMatrices:e,parseCoordGrammar:m,parseFunction:c,serializeNumber:a,skipNone:o,spow:g,toPrecision:s,type:r,zdiv:b});const y=new class{add(e,t,r){if("string"==typeof arguments[0])(Array.isArray(e)?e:[e]).forEach((function(e){this[e]=this[e]||[],t&&this[e][r?"unshift":"push"](t)}),this);else for(var e in arguments[0])this.add(e,arguments[0][e],arguments[1])}run(e,t){this[e]=this[e]||[],this[e].forEach((function(e){e.call(t&&t.context?t.context:t,t)}))}};var v={gamut_mapping:"css",precision:5,deltaE:"76",verbose:"test"!==globalThis?.process?.env?.NODE_ENV?.toLowerCase(),warn:function(e){this.verbose&&globalThis?.console?.warn?.(e)}};const C={D50:[.3457/.3585,1,.2958/.3585],D65:[.3127/.329,1,.3583/.329]};function R(e){return Array.isArray(e)?e:C[e]}function _(t,r,a,n={}){if(t=R(t),r=R(r),!t||!r)throw new TypeError(`Missing white point to convert ${t?"":"from"}${t||r?"":"/"}${r?"":"to"}`);if(t===r)return a;let o={W1:t,W2:r,XYZ:a,options:n};if(y.run("chromatic-adaptation-start",o),o.M||(o.W1===C.D65&&o.W2===C.D50?o.M=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]]:o.W1===C.D50&&o.W2===C.D65&&(o.M=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]])),y.run("chromatic-adaptation-end",o),o.M)return e(o.M,o.XYZ);throw new TypeError("Only Bradford CAT with white points D50 and D65 supported for now.")}const B=new Set(["<number>","<percentage>","<angle>"]);function N(e,t,r,a){let n=Object.entries(e.coords).map((([e,n],o)=>{let s,i=t.coordGrammar[o],c=a[o],l=c?.type;if(s=c.none?i.find((e=>B.has(e))):i.find((e=>e==l)),!s){let t=n.name||e;throw new TypeError(`${l??c.raw} not allowed for ${t} in ${r}()`)}let u=s.range;"<percentage>"===l&&(u||=[0,1]);let h=n.range||n.refRange;return u&&h&&(a[o]=d(u,h,a[o])),s}));return n}function k(e,{meta:t}={}){let r={str:String(e)?.trim()};if(y.run("parse-start",r),r.color)return r.color;if(r.parsed=c(r.str),r.parsed){let e=r.parsed.name;if("color"===e){let e=r.parsed.args.shift(),a=e.startsWith("--")?e.substring(2):`--${e}`,n=[e,a],o=r.parsed.rawArgs.indexOf("/")>0?r.parsed.args.pop():1;for(let a of x.all){let s=a.getFormat("color");if(s&&(n.includes(s.id)||s.ids?.filter((e=>n.includes(e))).length)){const n=Object.keys(a.coords).map(((e,t)=>r.parsed.args[t]||0));let i;return s.coordGrammar&&(i=N(a,s,"color",n)),t&&Object.assign(t,{formatId:"color",types:i}),s.id.startsWith("--")&&!e.startsWith("--")&&v.warn(`${a.name} is a non-standard space and not currently supported in the CSS spec. Use prefixed color(${s.id}) instead of color(${e}).`),e.startsWith("--")&&!s.id.startsWith("--")&&v.warn(`${a.name} is a standard space and supported in the CSS spec. Use color(${s.id}) instead of prefixed color(${e}).`),{spaceId:a.id,coords:n,alpha:o}}}let s="",i=e in x.registry?e:a;if(i in x.registry){let e=x.registry[i].formats?.color?.id;e&&(s=`Did you mean color(${e})?`)}throw new TypeError(`Cannot parse color(${e}). `+(s||"Missing a plugin?"))}for(let a of x.all){let n=a.getFormat(e);if(n&&"function"===n.type){let o=1;(n.lastAlpha||l(r.parsed.args).alpha)&&(o=r.parsed.args.pop());let s,i=r.parsed.args;return n.coordGrammar&&(s=N(a,n,e,i)),t&&Object.assign(t,{formatId:n.name,types:s}),{spaceId:a.id,coords:i,alpha:o}}}}else for(let e of x.all)for(let a in e.formats){let n=e.formats[a];if("custom"!==n.type)continue;if(n.test&&!n.test(r.str))continue;let o=n.parse(r.str);if(o)return o.alpha??=1,t&&(t.formatId=a),o}throw new TypeError(`Could not parse ${e} as a color. Missing a plugin?`)}function S(e){if(Array.isArray(e))return e.map(S);if(!e)throw new TypeError("Empty color reference");t(e)&&(e=k(e));let r=e.space||e.spaceId;return r instanceof x||(e.space=x.get(r)),void 0===e.alpha&&(e.alpha=1),e}class x{constructor(e){this.id=e.id,this.name=e.name,this.base=e.base?x.get(e.base):null,this.aliases=e.aliases,this.base&&(this.fromBase=e.fromBase,this.toBase=e.toBase);let t=e.coords??this.base.coords;for(let e in t)"name"in t[e]||(t[e].name=e);this.coords=t;let r=e.white??this.base.white??"D65";this.white=R(r),this.formats=e.formats??{};for(let e in this.formats){let t=this.formats[e];t.type||="function",t.name||=e}this.formats.color?.id||(this.formats.color={...this.formats.color??{},id:e.cssId||this.id}),e.gamutSpace?this.gamutSpace="self"===e.gamutSpace?this:x.get(e.gamutSpace):this.isPolar?this.gamutSpace=this.base:this.gamutSpace=this,this.gamutSpace.isUnbounded&&(this.inGamut=(e,t)=>!0),this.referred=e.referred,Object.defineProperty(this,"path",{value:E(this).reverse(),writable:!1,enumerable:!0,configurable:!0}),y.run("colorspace-init-end",this)}inGamut(e,{epsilon:t=75e-6}={}){if(!this.equals(this.gamutSpace))return e=this.to(this.gamutSpace,e),this.gamutSpace.inGamut(e,{epsilon:t});let r=Object.values(this.coords);return e.every(((e,a)=>{let n=r[a];if("angle"!==n.type&&n.range){if(Number.isNaN(e))return!0;let[r,a]=n.range;return(void 0===r||e>=r-t)&&(void 0===a||e<=a+t)}return!0}))}get isUnbounded(){return Object.values(this.coords).every((e=>!("range"in e)))}get cssId(){return this.formats?.color?.id||this.id}get isPolar(){for(let e in this.coords)if("angle"===this.coords[e].type)return!0;return!1}getFormat(e){if("object"==typeof e)return e=I(e,this);let t;return t="default"===e?Object.values(this.formats)[0]:this.formats[e],t?(t=I(t,this),t):null}equals(e){return!!e&&(this===e||this.id===e||this.id===e.id)}to(e,t){if(1===arguments.length){const r=S(e);[e,t]=[r.space,r.coords]}if(e=x.get(e),this.equals(e))return t;t=t.map((e=>Number.isNaN(e)?0:e));let r,a,n=this.path,o=e.path;for(let e=0;e<n.length&&n[e].equals(o[e]);e++)r=n[e],a=e;if(!r)throw new Error(`Cannot convert between color spaces ${this} and ${e}: no connection space was found`);for(let e=n.length-1;e>a;e--)t=n[e].toBase(t);for(let e=a+1;e<o.length;e++)t=o[e].fromBase(t);return t}from(e,t){if(1===arguments.length){const r=S(e);[e,t]=[r.space,r.coords]}return(e=x.get(e)).to(this,t)}toString(){return`${this.name} (${this.id})`}getMinCoords(){let e=[];for(let t in this.coords){let r=this.coords[t],a=r.range||r.refRange;e.push(a?.min??0)}return e}static registry={};static get all(){return[...new Set(Object.values(x.registry))]}static register(e,t){if(1===arguments.length&&(e=(t=arguments[0]).id),t=this.get(t),this.registry[e]&&this.registry[e]!==t)throw new Error(`Duplicate color space registration: '${e}'`);if(this.registry[e]=t,1===arguments.length&&t.aliases)for(let e of t.aliases)this.register(e,t);return t}static get(e,...t){if(!e||e instanceof x)return e;if("string"===r(e)){let t=x.registry[e.toLowerCase()];if(!t)throw new TypeError(`No color space found with id = "${e}"`);return t}if(t.length)return x.get(...t);throw new TypeError(`${e} is not a valid color space`)}static resolveCoord(e,t){let a,n,o=r(e);if("string"===o?e.includes(".")?[a,n]=e.split("."):[a,n]=[,e]:Array.isArray(e)?[a,n]=e:(a=e.space,n=e.coordId),a=x.get(a),a||(a=t),!a)throw new TypeError(`Cannot resolve coordinate reference ${e}: No color space specified and relative references are not allowed here`);if(o=r(n),"number"===o||"string"===o&&n>=0){let e=Object.entries(a.coords)[n];if(e)return{space:a,id:e[0],index:n,...e[1]}}a=x.get(a);let s=n.toLowerCase(),i=0;for(let e in a.coords){let t=a.coords[e];if(e.toLowerCase()===s||t.name?.toLowerCase()===s)return{space:a,id:e,index:i,...t};i++}throw new TypeError(`No "${n}" coordinate found in ${a.name}. Its coordinates are: ${Object.keys(a.coords).join(", ")}`)}static DEFAULT_FORMAT={type:"functions",name:"color"}}function E(e){let t=[e];for(let r=e;r=r.base;)t.push(r);return t}function I(e,{coords:t}={}){if(e.coords&&!e.coordGrammar){e.type||="function",e.name||="color",e.coordGrammar=m(e.coords);let r=Object.entries(t).map((([t,r],a)=>{let n=e.coordGrammar[a][0],o=r.range||r.refRange,s=n.range,i="";return"<percentage>"==n?(s=[0,100],i="%"):"<angle>"==n&&(i="deg"),{fromRange:o,toRange:s,suffix:i}}));e.serializeCoords=(e,t)=>e.map(((e,n)=>{let{fromRange:o,toRange:s,suffix:i}=r[n];return o&&s&&(e=d(o,s,e)),e=a(e,{precision:t,unit:i})}))}return e}var L=new x({id:"xyz-d65",name:"XYZ D65",coords:{x:{name:"X"},y:{name:"Y"},z:{name:"Z"}},white:"D65",formats:{color:{ids:["xyz-d65","xyz"]}},aliases:["xyz"]});class z extends x{constructor(t){t.coords||(t.coords={r:{range:[0,1],name:"Red"},g:{range:[0,1],name:"Green"},b:{range:[0,1],name:"Blue"}}),t.base||(t.base=L),t.toXYZ_M&&t.fromXYZ_M&&(t.toBase??=r=>{let a=e(t.toXYZ_M,r);return this.white!==this.base.white&&(a=_(this.white,this.base.white,a)),a},t.fromBase??=r=>(r=_(this.base.white,this.white,r),e(t.fromXYZ_M,r))),t.referred??="display",super(t)}}function A(e,t){return e=S(e),!t||e.space.equals(t)?e.coords.slice():(t=x.get(t)).from(e)}function P(e,t){e=S(e);let{space:r,index:a}=x.resolveCoord(t,e.space);return A(e,r)[a]}function j(e,t,r){return e=S(e),t=x.get(t),e.coords=t.to(e.space,r),e}function O(e,t,a){if(e=S(e),2===arguments.length&&"object"===r(arguments[1])){let t=arguments[1];for(let r in t)O(e,r,t[r])}else{"function"==typeof a&&(a=a(P(e,t)));let{space:r,index:n}=x.resolveCoord(t,e.space),o=A(e,r);o[n]=a,j(e,r,o)}return e}j.returns="color",O.returns="color";var $=new x({id:"xyz-d50",name:"XYZ D50",white:"D50",base:L,fromBase:e=>_(L.white,"D50",e),toBase:e=>_("D50",L.white,e)});const q=24/116,D=24389/27;let H=C.D50;var W=new x({id:"lab",name:"Lab",coords:{l:{refRange:[0,100],name:"Lightness"},a:{refRange:[-125,125]},b:{refRange:[-125,125]}},white:H,base:$,fromBase(e){let t=e.map(((e,t)=>e/H[t])).map((e=>e>.008856451679035631?Math.cbrt(e):(D*e+16)/116));return[116*t[1]-16,500*(t[0]-t[1]),200*(t[1]-t[2])]},toBase(e){let t=[];return t[1]=(e[0]+16)/116,t[0]=e[1]/500+t[1],t[2]=t[1]-e[2]/200,[t[0]>q?Math.pow(t[0],3):(116*t[0]-16)/D,e[0]>8?Math.pow((e[0]+16)/116,3):e[0]/D,t[2]>q?Math.pow(t[2],3):(116*t[2]-16)/D].map(((e,t)=>e*H[t]))},formats:{lab:{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});function T(e){return(e%360+360)%360}var G=new x({id:"lch",name:"LCH",coords:{l:{refRange:[0,100],name:"Lightness"},c:{refRange:[0,150],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:W,fromBase(e){let t,[r,a,n]=e;return t=Math.abs(a)<.02&&Math.abs(n)<.02?NaN:180*Math.atan2(n,a)/Math.PI,[r,Math.sqrt(a**2+n**2),T(t)]},toBase(e){let[t,r,a]=e;return r<0&&(r=0),isNaN(a)&&(a=0),[t,r*Math.cos(a*Math.PI/180),r*Math.sin(a*Math.PI/180)]},formats:{lch:{coords:["<number> | <percentage>","<number> | <percentage>","<number> | <angle>"]}}});const X=25**7,Y=Math.PI,Z=180/Y,F=Y/180;function J(e){const t=e*e;return t*t*t*e}function Q(e,t,{kL:r=1,kC:a=1,kH:n=1}={}){[e,t]=S([e,t]);let[o,s,i]=W.from(e),c=G.from(W,[o,s,i])[1],[l,u,h]=W.from(t),d=G.from(W,[l,u,h])[1];c<0&&(c=0),d<0&&(d=0);let m=J((c+d)/2),f=.5*(1-Math.sqrt(m/(m+X))),p=(1+f)*s,g=(1+f)*u,b=Math.sqrt(p**2+i**2),M=Math.sqrt(g**2+h**2),w=0===p&&0===i?0:Math.atan2(i,p),y=0===g&&0===h?0:Math.atan2(h,g);w<0&&(w+=2*Y),y<0&&(y+=2*Y),w*=Z,y*=Z;let C,R=l-o,_=M-b,B=y-w,N=w+y,k=Math.abs(B);b*M==0?C=0:k<=180?C=B:B>180?C=B-360:B<-180?C=B+360:v.warn("the unthinkable has happened");let x,E=2*Math.sqrt(M*b)*Math.sin(C*F/2),I=(o+l)/2,L=(b+M)/2,z=J(L);x=b*M==0?N:k<=180?N/2:N<360?(N+360)/2:(N-360)/2;let A=(I-50)**2,P=1+.015*A/Math.sqrt(20+A),j=1+.045*L,O=1;O-=.17*Math.cos((x-30)*F),O+=.24*Math.cos(2*x*F),O+=.32*Math.cos((3*x+6)*F),O-=.2*Math.cos((4*x-63)*F);let $=1+.015*L*O,q=30*Math.exp(-1*((x-275)/25)**2),D=2*Math.sqrt(z/(z+X)),H=(R/(r*P))**2;return H+=(_/(a*j))**2,H+=(E/(n*$))**2,H+=-1*Math.sin(2*q*F)*D*(_/(a*j))*(E/(n*$)),Math.sqrt(H)}const U=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],K=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],V=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],ee=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]];var te=new x({id:"oklab",name:"Oklab",coords:{l:{refRange:[0,1],name:"Lightness"},a:{refRange:[-.4,.4]},b:{refRange:[-.4,.4]}},white:"D65",base:L,fromBase(t){let r=e(U,t).map((e=>Math.cbrt(e)));return e(V,r)},toBase(t){let r=e(ee,t).map((e=>e**3));return e(K,r)},formats:{oklab:{coords:["<percentage> | <number>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});function re(e,t){[e,t]=S([e,t]);let[r,a,n]=te.from(e),[o,s,i]=te.from(t),c=r-o,l=a-s,u=n-i;return Math.sqrt(c**2+l**2+u**2)}const ae=75e-6;function ne(e,t,{epsilon:r=ae}={}){e=S(e),t||(t=e.space),t=x.get(t);let a=e.coords;return t!==e.space&&(a=t.from(e)),t.inGamut(a,{epsilon:r})}function oe(e){return{space:e.space,coords:e.coords.slice(),alpha:e.alpha}}function se(e,t,r="lab"){let a=(r=x.get(r)).from(e),n=r.from(t);return Math.sqrt(a.reduce(((e,t,r)=>{let a=n[r];return isNaN(t)||isNaN(a)?e:e+(a-t)**2}),0))}const ie=Math.PI/180;var ce=new x({id:"xyz-abs-d65",cssId:"--xyz-abs-d65",name:"Absolute XYZ D65",coords:{x:{refRange:[0,9504.7],name:"Xa"},y:{refRange:[0,1e4],name:"Ya"},z:{refRange:[0,10888.3],name:"Za"}},base:L,fromBase:e=>e.map((e=>Math.max(203*e,0))),toBase:e=>e.map((e=>Math.max(e/203,0)))});const le=1.15,ue=.66,he=2610/16384,de=.8359375,me=2413/128,fe=18.6875,pe=32/(1.7*2523),ge=-.56,be=16295499532821565e-27,Me=[[.41478972,.579999,.014648],[-.20151,1.120649,.0531008],[-.0166008,.2648,.6684799]],we=[[1.9242264357876067,-1.0047923125953657,.037651404030618],[.35031676209499907,.7264811939316552,-.06538442294808501],[-.09098281098284752,-.3127282905230739,1.5227665613052603]],ye=[[.5,.5,0],[3.524,-4.066708,.542708],[.199076,1.096799,-1.295875]],ve=[[1,.1386050432715393,.05804731615611886],[.9999999999999999,-.1386050432715393,-.05804731615611886],[.9999999999999998,-.09601924202631895,-.8118918960560388]];var Ce=new x({id:"jzazbz",name:"Jzazbz",coords:{jz:{refRange:[0,1],name:"Jz"},az:{refRange:[-.5,.5]},bz:{refRange:[-.5,.5]}},base:ce,fromBase(t){let[r,a,n]=t,o=e(Me,[le*r-(le-1)*n,ue*a-(ue-1)*r,n]).map((function(e){return((de+me*(e/1e4)**he)/(1+fe*(e/1e4)**he))**134.03437499999998})),[s,i,c]=e(ye,o);return[(1+ge)*s/(1+ge*s)-be,i,c]},toBase(t){let[r,a,n]=t,o=e(ve,[(r+be)/(1+ge-ge*(r+be)),a,n]).map((function(e){return 1e4*((de-e**pe)/(fe*e**pe-me))**6.277394636015326})),[s,i,c]=e(we,o),l=(s+(le-1)*c)/le;return[l,(i+(ue-1)*l)/ue,c]},formats:{color:{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}}),Re=new x({id:"jzczhz",name:"JzCzHz",coords:{jz:{refRange:[0,1],name:"Jz"},cz:{refRange:[0,1],name:"Chroma"},hz:{refRange:[0,360],type:"angle",name:"Hue"}},base:Ce,fromBase(e){let t,[r,a,n]=e;const o=2e-4;return t=Math.abs(a)<o&&Math.abs(n)<o?NaN:180*Math.atan2(n,a)/Math.PI,[r,Math.sqrt(a**2+n**2),T(t)]},toBase:e=>[e[0],e[1]*Math.cos(e[2]*Math.PI/180),e[1]*Math.sin(e[2]*Math.PI/180)]});const _e=.8359375,Be=2413/128,Ne=18.6875,ke=2610/16384,Se=2523/32,xe=16384/2610,Ee=32/2523,Ie=[[.3592832590121217,.6976051147779502,-.035891593232029],[-.1920808463704993,1.100476797037432,.0753748658519118],[.0070797844607479,.0748396662186362,.8433265453898765]],Le=[[.5,.5,0],[6610/4096,-13613/4096,7003/4096],[17933/4096,-17390/4096,-543/4096]],ze=[[.9999999999999998,.0086090370379328,.111029625003026],[.9999999999999998,-.0086090370379328,-.1110296250030259],[.9999999999999998,.5600313357106791,-.3206271749873188]],Ae=[[2.0701522183894223,-1.3263473389671563,.2066510476294053],[.3647385209748072,.6805660249472273,-.0453045459220347],[-.0497472075358123,-.0492609666966131,1.1880659249923042]];var Pe=new x({id:"ictcp",name:"ICTCP",coords:{i:{refRange:[0,1],name:"I"},ct:{refRange:[-.5,.5],name:"CT"},cp:{refRange:[-.5,.5],name:"CP"}},base:ce,fromBase:t=>function(t){let r=t.map((function(e){return((_e+Be*(e/1e4)**ke)/(1+Ne*(e/1e4)**ke))**Se}));return e(Le,r)}(e(Ie,t)),toBase(t){let r=function(t){let r=e(ze,t),a=r.map((function(e){return 1e4*(Math.max(e**Ee-_e,0)/(Be-Ne*e**Ee))**xe}));return a}(t);return e(Ae,r)}});const je=C.D65,Oe=.42,$e=1/Oe,qe=2*Math.PI,De=[[.401288,.650173,-.051461],[-.250268,1.204414,.045854],[-.002079,.048952,.953127]],He=[[1.8620678550872327,-1.0112546305316843,.14918677544445175],[.38752654323613717,.6214474419314753,-.008973985167612518],[-.015841498849333856,-.03412293802851557,1.0499644368778496]],We=[[460,451,288],[460,-891,-261],[460,-220,-6300]],Te={dark:[.8,.525,.8],dim:[.9,.59,.9],average:[1,.69,1]},Ge={h:[20.14,90,164.25,237.53,380.14],e:[.8,.7,1,1.2,.8],H:[0,100,200,300,400]},Xe=180/Math.PI,Ye=Math.PI/180;function Ze(e,t){const r=e.map((e=>{const r=g(t*Math.abs(e)*.01,Oe);return 400*p(r,e)/(r+27.13)}));return r}function Fe(t,r,a,n,o){const s={};s.discounting=o,s.refWhite=t,s.surround=n;const i=t.map((e=>100*e));s.la=r,s.yb=a;const c=i[1],l=e(De,i),h=(n=Te[s.surround])[0];s.c=n[1],s.nc=n[2];const d=(1/(5*s.la+1))**4;s.fl=d*s.la+.1*(1-d)*(1-d)*Math.cbrt(5*s.la),s.flRoot=s.fl**.25,s.n=s.yb/c,s.z=1.48+Math.sqrt(s.n),s.nbb=.725*s.n**-.2,s.ncb=s.nbb;const m=o?1:Math.max(Math.min(h*(1-1/3.6*Math.exp((-s.la-42)/92)),1),0);s.dRgb=l.map((e=>u(1,c/e,m))),s.dRgbInv=s.dRgb.map((e=>1/e));const f=l.map(((e,t)=>e*s.dRgb[t])),p=Ze(f,s.fl);return s.aW=s.nbb*(2*p[0]+p[1]+.05*p[2]),s}const Je=Fe(je,64/Math.PI*.2,20,"average",!1);function Qe(t,r){if(!(void 0!==t.J^void 0!==t.Q))throw new Error("Conversion requires one and only one: 'J' or 'Q'");if(!(void 0!==t.C^void 0!==t.M^void 0!==t.s))throw new Error("Conversion requires one and only one: 'C', 'M' or 's'");if(!(void 0!==t.h^void 0!==t.H))throw new Error("Conversion requires one and only one: 'h' or 'H'");if(0===t.J||0===t.Q)return[0,0,0];let a=0;a=void 0!==t.h?T(t.h)*Ye:function(e){let t=(e%400+400)%400;const r=Math.floor(.01*t);t%=100;const[a,n]=Ge.h.slice(r,r+2),[o,s]=Ge.e.slice(r,r+2);return T((t*(s*a-o*n)-100*a*s)/(t*(s-o)-100*s))}(t.H)*Ye;const n=Math.cos(a),o=Math.sin(a);let s=0;void 0!==t.J?s=.1*g(t.J,.5):void 0!==t.Q&&(s=.25*r.c*t.Q/((r.aW+4)*r.flRoot));let i=0;void 0!==t.C?i=t.C/s:void 0!==t.M?i=t.M/r.flRoot/s:void 0!==t.s&&(i=4e-4*t.s**2*(r.aW+4)/r.c);const c=g(i*Math.pow(1.64-Math.pow(.29,r.n),-.73),10/9),l=.25*(Math.cos(a+2)+3.8),u=r.aW*g(s,2/r.c/r.z),h=5e4/13*r.nc*r.ncb*l,d=u/r.nbb,m=23*(d+.305)*b(c,23*h+c*(11*n+108*o)),f=function(e,t){const r=100/t*27.13**$e;return e.map((e=>{const t=Math.abs(e);return p(r*g(t/(400-t),$e),e)}))}(e(We,[d,m*n,m*o]).map((e=>1*e/1403)),r.fl);return e(He,f.map(((e,t)=>e*r.dRgbInv[t]))).map((e=>e/100))}function Ue(t,r){const a=t.map((e=>100*e)),n=Ze(e(De,a).map(((e,t)=>e*r.dRgb[t])),r.fl),o=n[0]+(-12*n[1]+n[2])/11,s=(n[0]+n[1]-2*n[2])/9,i=(Math.atan2(s,o)%qe+qe)%qe,c=.25*(Math.cos(i+2)+3.8),l=g(5e4/13*r.nc*r.ncb*b(c*Math.sqrt(o**2+s**2),n[0]+n[1]+1.05*n[2]+.305),.9)*Math.pow(1.64-Math.pow(.29,r.n),.73),u=g(r.nbb*(2*n[0]+n[1]+.05*n[2])/r.aW,.5*r.c*r.z),h=100*g(u,2),d=4/r.c*u*(r.aW+4)*r.flRoot,m=l*u,f=m*r.flRoot,p=T(i*Xe),w=function(e){let t=T(e);t<=Ge.h[0]&&(t+=360);const r=M(Ge.h,t)-1,[a,n]=Ge.h.slice(r,r+2),[o,s]=Ge.e.slice(r,r+2),i=(t-a)/o;return Ge.H[r]+100*i/(i+(n-t)/s)}(p);return{J:h,C:m,h:p,s:50*g(r.c*l/(r.aW+4),.5),Q:d,M:f,H:w}}var Ke=new x({id:"cam16-jmh",cssId:"--cam16-jmh",name:"CAM16-JMh",coords:{j:{refRange:[0,100],name:"J"},m:{refRange:[0,105],name:"Colorfulness"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:L,fromBase(e){const t=Ue(e,Je);return[t.J,t.M,t.h]},toBase:e=>Qe({J:e[0],M:e[1],h:e[2]},Je)});const Ve=C.D65,et=216/24389,tt=24389/27;function rt(e){return e>8?Math.pow((e+16)/116,3):e/tt}function at(e,t){const r=116*((a=e[1])>et?Math.cbrt(a):(tt*a+16)/116)-16;var a;if(0===r)return[0,0,0];const n=Ue(e,nt);return[T(n.h),n.C,r]}const nt=Fe(Ve,200/Math.PI*rt(50),100*rt(50),"average",!1);var ot=new x({id:"hct",name:"HCT",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},c:{refRange:[0,145],name:"Colorfulness"},t:{refRange:[0,100],name:"Tone"}},base:L,fromBase:e=>at(e),toBase:e=>function(e,t){let[r,a,n]=e,o=[],s=0;if(0===n)return[0,0,0];let i=rt(n);s=n>0?.00379058511492914*n**2+.608983189401032*n+.9155088574762233:9514440756550361e-21*n**2+.08693057439788597*n-21.928975842194614;let c=0,l=1/0;for(;c<=15;){o=Qe({J:s,C:a,h:r},t);const e=Math.abs(o[1]-i);if(e<l){if(e<=2e-12)return o;l=e}s-=(o[1]-i)*s/(2*o[1]),c+=1}return Qe({J:s,C:a,h:r},t)}(e,nt),formats:{color:{id:"--hct",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const st=Math.PI/180,it=[1,.007,.0228];function ct(e){e[1]<0&&(e=ot.fromBase(ot.toBase(e)));const t=Math.log(Math.max(1+it[2]*e[1]*nt.flRoot,1))/it[2],r=e[0]*st,a=t*Math.cos(r),n=t*Math.sin(r);return[e[2],a,n]}var lt={deltaE76:function(e,t){return se(e,t,"lab")},deltaECMC:function(e,t,{l:r=2,c:a=1}={}){[e,t]=S([e,t]);let[n,o,s]=W.from(e),[,i,c]=G.from(W,[n,o,s]),[l,u,h]=W.from(t),d=G.from(W,[l,u,h])[1];i<0&&(i=0),d<0&&(d=0);let m=n-l,f=i-d,p=(o-u)**2+(s-h)**2-f**2,g=.511;n>=16&&(g=.040975*n/(1+.01765*n));let b,M=.0638*i/(1+.0131*i)+.638;Number.isNaN(c)&&(c=0),b=c>=164&&c<=345?.56+Math.abs(.2*Math.cos((c+168)*ie)):.36+Math.abs(.4*Math.cos((c+35)*ie));let w=Math.pow(i,4),y=Math.sqrt(w/(w+1900)),v=(m/(r*g))**2;return v+=(f/(a*M))**2,v+=p/(M*(y*b+1-y))**2,Math.sqrt(v)},deltaE2000:Q,deltaEJz:function(e,t){[e,t]=S([e,t]);let[r,a,n]=Re.from(e),[o,s,i]=Re.from(t),c=r-o,l=a-s;Number.isNaN(n)&&Number.isNaN(i)?(n=0,i=0):Number.isNaN(n)?n=i:Number.isNaN(i)&&(i=n);let u=n-i,h=2*Math.sqrt(a*s)*Math.sin(u/2*(Math.PI/180));return Math.sqrt(c**2+l**2+h**2)},deltaEITP:function(e,t){[e,t]=S([e,t]);let[r,a,n]=Pe.from(e),[o,s,i]=Pe.from(t);return 720*Math.sqrt((r-o)**2+.25*(a-s)**2+(n-i)**2)},deltaEOK:re,deltaEHCT:function(e,t){[e,t]=S([e,t]);let[r,a,n]=ct(ot.from(e)),[o,s,i]=ct(ot.from(t));return Math.sqrt((r-o)**2+(a-s)**2+(n-i)**2)}};const ut={hct:{method:"hct.c",jnd:2,deltaEMethod:"hct",blackWhiteClamp:{}},"hct-tonal":{method:"hct.c",jnd:0,deltaEMethod:"hct",blackWhiteClamp:{channel:"hct.t",min:0,max:100}}};function ht(e,{method:r=v.gamut_mapping,space:a,deltaEMethod:o="",jnd:s=2,blackWhiteClamp:i={}}={}){if(e=S(e),t(arguments[1])?a=arguments[1]:a||(a=e.space),ne(e,a=x.get(a),{epsilon:0}))return e;let c;if("css"===r)c=function(e,{space:t}={}){const r=.02,a=1e-4;e=S(e),t||(t=e.space);t=x.get(t);const n=x.get("oklch");if(t.isUnbounded)return mt(e,t);const o=mt(e,n);let s=o.coords[0];if(s>=1){const r=mt(dt.WHITE,t);return r.alpha=e.alpha,mt(r,t)}if(s<=0){const r=mt(dt.BLACK,t);return r.alpha=e.alpha,mt(r,t)}if(ne(o,t,{epsilon:0}))return mt(o,t);function i(e){const r=mt(e,t),a=Object.values(t.coords);return r.coords=r.coords.map(((e,t)=>{if("range"in a[t]){const[r,n]=a[t].range;return f(r,e,n)}return e})),r}let c=0,l=o.coords[1],u=!0,h=oe(o),d=i(h),m=re(d,h);if(m<r)return d;for(;l-c>a;){const e=(c+l)/2;if(h.coords[1]=e,u&&ne(h,t,{epsilon:0}))c=e;else if(d=i(h),m=re(d,h),m<r){if(r-m<a)break;u=!1,c=e}else l=e}return d}(e,{space:a});else{if("clip"===r||ne(e,a))c=mt(e,a);else{Object.prototype.hasOwnProperty.call(ut,r)&&({method:r,jnd:s,deltaEMethod:o,blackWhiteClamp:i}=ut[r]);let t=Q;if(""!==o)for(let e in lt)if("deltae"+o.toLowerCase()===e.toLowerCase()){t=lt[e];break}let l=ht(mt(e,a),{method:"clip",space:a});if(t(e,l)>s){if(3===Object.keys(i).length){let t=x.resolveCoord(i.channel),r=P(mt(e,t.space),t.id);if(n(r)&&(r=0),r>=i.max)return mt({space:"xyz-d65",coords:C.D65},e.space);if(r<=i.min)return mt({space:"xyz-d65",coords:[0,0,0]},e.space)}let o=x.resolveCoord(r),l=o.space,u=o.id,h=mt(e,l);h.coords.forEach(((e,t)=>{n(e)&&(h.coords[t]=0)}));let d=(o.range||o.refRange)[0],m=function(e){const t=e?Math.floor(Math.log10(Math.abs(e))):0;return Math.max(parseFloat("1e"+(t-2)),1e-6)}(s),f=d,p=P(h,u);for(;p-f>m;){let e=oe(h);e=ht(e,{space:a,method:"clip"}),t(h,e)-s<m?f=P(h,u):p=P(h,u),O(h,u,(f+p)/2)}c=mt(h,a)}else c=l}if("clip"===r||!ne(c,a,{epsilon:0})){let e=Object.values(a.coords).map((e=>e.range||[]));c.coords=c.coords.map(((t,r)=>{let[a,n]=e[r];return void 0!==a&&(t=Math.max(a,t)),void 0!==n&&(t=Math.min(t,n)),t}))}}return a!==e.space&&(c=mt(c,e.space)),e.coords=c.coords,e}ht.returns="color";const dt={WHITE:{space:te,coords:[1,0,0]},BLACK:{space:te,coords:[0,0,0]}};function mt(e,t,{inGamut:r}={}){e=S(e);let a=(t=x.get(t)).from(e),n={space:t,coords:a,alpha:e.alpha};return r&&(n=ht(n,!0===r?void 0:r)),n}function ft(e,{precision:t=v.precision,format:r="default",inGamut:n=!0,...o}={}){let s,i=r;r=(e=S(e)).space.getFormat(r)??e.space.getFormat("default")??x.DEFAULT_FORMAT;let c=e.coords.slice();if(n||=r.toGamut,n&&!ne(e)&&(c=ht(oe(e),!0===n?void 0:n).coords),"custom"===r.type){if(o.precision=t,!r.serialize)throw new TypeError(`format ${i} can only be used to parse colors, not for serialization`);s=r.serialize(c,e.alpha,o)}else{let n=r.name||"color";r.serializeCoords?c=r.serializeCoords(c,t):null!==t&&(c=c.map((e=>a(e,{precision:t}))));let o=[...c];if("color"===n){let t=r.id||r.ids?.[0]||e.space.id;o.unshift(t)}let i=e.alpha;null!==t&&(i=a(i,{precision:t}));let l=e.alpha>=1||r.noAlpha?"":`${r.commas?",":" /"} ${i}`;s=`${n}(${o.join(r.commas?", ":" ")}${l})`}return s}mt.returns="color";var pt=new z({id:"rec2020-linear",cssId:"--rec2020-linear",name:"Linear REC.2020",white:"D65",toXYZ_M:[[.6369580483012914,.14461690358620832,.1688809751641721],[.2627002120112671,.6779980715188708,.05930171646986196],[0,.028072693049087428,1.060985057710791]],fromXYZ_M:[[1.716651187971268,-.355670783776392,-.25336628137366],[-.666684351832489,1.616481236634939,.0157685458139111],[.017639857445311,-.042770613257809,.942103121235474]]});const gt=1.09929682680944,bt=.018053968510807;var Mt=new z({id:"rec2020",name:"REC.2020",base:pt,toBase:e=>e.map((function(e){return e<4.5*bt?e/4.5:Math.pow((e+gt-1)/gt,1/.45)})),fromBase:e=>e.map((function(e){return e>=bt?gt*Math.pow(e,.45)-(gt-1):4.5*e}))});var wt=new z({id:"p3-linear",cssId:"--display-p3-linear",name:"Linear P3",white:"D65",toXYZ_M:[[.4865709486482162,.26566769316909306,.1982172852343625],[.2289745640697488,.6917385218365064,.079286914093745],[0,.04511338185890264,1.043944368900976]],fromXYZ_M:[[2.493496911941425,-.9313836179191239,-.40271078445071684],[-.8294889695615747,1.7626640603183463,.023624685841943577],[.03584583024378447,-.07617238926804182,.9568845240076872]]});const yt=[[3.2409699419045226,-1.537383177570094,-.4986107602930034],[-.9692436362808796,1.8759675015077202,.04155505740717559],[.05563007969699366,-.20397695888897652,1.0569715142428786]];var vt=new z({id:"srgb-linear",name:"Linear sRGB",white:"D65",toXYZ_M:[[.41239079926595934,.357584339383878,.1804807884018343],[.21263900587151027,.715168678767756,.07219231536073371],[.01933081871559182,.11919477979462598,.9505321522496607]],fromXYZ_M:yt}),Ct={aliceblue:[240/255,248/255,1],antiquewhite:[250/255,235/255,215/255],aqua:[0,1,1],aquamarine:[127/255,1,212/255],azure:[240/255,1,1],beige:[245/255,245/255,220/255],bisque:[1,228/255,196/255],black:[0,0,0],blanchedalmond:[1,235/255,205/255],blue:[0,0,1],blueviolet:[138/255,43/255,226/255],brown:[165/255,42/255,42/255],burlywood:[222/255,184/255,135/255],cadetblue:[95/255,158/255,160/255],chartreuse:[127/255,1,0],chocolate:[210/255,105/255,30/255],coral:[1,127/255,80/255],cornflowerblue:[100/255,149/255,237/255],cornsilk:[1,248/255,220/255],crimson:[220/255,20/255,60/255],cyan:[0,1,1],darkblue:[0,0,139/255],darkcyan:[0,139/255,139/255],darkgoldenrod:[184/255,134/255,11/255],darkgray:[169/255,169/255,169/255],darkgreen:[0,100/255,0],darkgrey:[169/255,169/255,169/255],darkkhaki:[189/255,183/255,107/255],darkmagenta:[139/255,0,139/255],darkolivegreen:[85/255,107/255,47/255],darkorange:[1,140/255,0],darkorchid:[.6,50/255,.8],darkred:[139/255,0,0],darksalmon:[233/255,150/255,122/255],darkseagreen:[143/255,188/255,143/255],darkslateblue:[72/255,61/255,139/255],darkslategray:[47/255,79/255,79/255],darkslategrey:[47/255,79/255,79/255],darkturquoise:[0,206/255,209/255],darkviolet:[148/255,0,211/255],deeppink:[1,20/255,147/255],deepskyblue:[0,191/255,1],dimgray:[105/255,105/255,105/255],dimgrey:[105/255,105/255,105/255],dodgerblue:[30/255,144/255,1],firebrick:[178/255,34/255,34/255],floralwhite:[1,250/255,240/255],forestgreen:[34/255,139/255,34/255],fuchsia:[1,0,1],gainsboro:[220/255,220/255,220/255],ghostwhite:[248/255,248/255,1],gold:[1,215/255,0],goldenrod:[218/255,165/255,32/255],gray:[128/255,128/255,128/255],green:[0,128/255,0],greenyellow:[173/255,1,47/255],grey:[128/255,128/255,128/255],honeydew:[240/255,1,240/255],hotpink:[1,105/255,180/255],indianred:[205/255,92/255,92/255],indigo:[75/255,0,130/255],ivory:[1,1,240/255],khaki:[240/255,230/255,140/255],lavender:[230/255,230/255,250/255],lavenderblush:[1,240/255,245/255],lawngreen:[124/255,252/255,0],lemonchiffon:[1,250/255,205/255],lightblue:[173/255,216/255,230/255],lightcoral:[240/255,128/255,128/255],lightcyan:[224/255,1,1],lightgoldenrodyellow:[250/255,250/255,210/255],lightgray:[211/255,211/255,211/255],lightgreen:[144/255,238/255,144/255],lightgrey:[211/255,211/255,211/255],lightpink:[1,182/255,193/255],lightsalmon:[1,160/255,122/255],lightseagreen:[32/255,178/255,170/255],lightskyblue:[135/255,206/255,250/255],lightslategray:[119/255,136/255,.6],lightslategrey:[119/255,136/255,.6],lightsteelblue:[176/255,196/255,222/255],lightyellow:[1,1,224/255],lime:[0,1,0],limegreen:[50/255,205/255,50/255],linen:[250/255,240/255,230/255],magenta:[1,0,1],maroon:[128/255,0,0],mediumaquamarine:[.4,205/255,170/255],mediumblue:[0,0,205/255],mediumorchid:[186/255,85/255,211/255],mediumpurple:[147/255,112/255,219/255],mediumseagreen:[60/255,179/255,113/255],mediumslateblue:[123/255,104/255,238/255],mediumspringgreen:[0,250/255,154/255],mediumturquoise:[72/255,209/255,.8],mediumvioletred:[199/255,21/255,133/255],midnightblue:[25/255,25/255,112/255],mintcream:[245/255,1,250/255],mistyrose:[1,228/255,225/255],moccasin:[1,228/255,181/255],navajowhite:[1,222/255,173/255],navy:[0,0,128/255],oldlace:[253/255,245/255,230/255],olive:[128/255,128/255,0],olivedrab:[107/255,142/255,35/255],orange:[1,165/255,0],orangered:[1,69/255,0],orchid:[218/255,112/255,214/255],palegoldenrod:[238/255,232/255,170/255],palegreen:[152/255,251/255,152/255],paleturquoise:[175/255,238/255,238/255],palevioletred:[219/255,112/255,147/255],papayawhip:[1,239/255,213/255],peachpuff:[1,218/255,185/255],peru:[205/255,133/255,63/255],pink:[1,192/255,203/255],plum:[221/255,160/255,221/255],powderblue:[176/255,224/255,230/255],purple:[128/255,0,128/255],rebeccapurple:[.4,.2,.6],red:[1,0,0],rosybrown:[188/255,143/255,143/255],royalblue:[65/255,105/255,225/255],saddlebrown:[139/255,69/255,19/255],salmon:[250/255,128/255,114/255],sandybrown:[244/255,164/255,96/255],seagreen:[46/255,139/255,87/255],seashell:[1,245/255,238/255],sienna:[160/255,82/255,45/255],silver:[192/255,192/255,192/255],skyblue:[135/255,206/255,235/255],slateblue:[106/255,90/255,205/255],slategray:[112/255,128/255,144/255],slategrey:[112/255,128/255,144/255],snow:[1,250/255,250/255],springgreen:[0,1,127/255],steelblue:[70/255,130/255,180/255],tan:[210/255,180/255,140/255],teal:[0,128/255,128/255],thistle:[216/255,191/255,216/255],tomato:[1,99/255,71/255],turquoise:[64/255,224/255,208/255],violet:[238/255,130/255,238/255],wheat:[245/255,222/255,179/255],white:[1,1,1],whitesmoke:[245/255,245/255,245/255],yellow:[1,1,0],yellowgreen:[154/255,205/255,50/255]};let Rt=Array(3).fill("<percentage> | <number>[0, 255]"),_t=Array(3).fill("<number>[0, 255]");var Bt=new z({id:"srgb",name:"sRGB",base:vt,fromBase:e=>e.map((e=>{let t=e<0?-1:1,r=e*t;return r>.0031308?t*(1.055*r**(1/2.4)-.055):12.92*e})),toBase:e=>e.map((e=>{let t=e<0?-1:1,r=e*t;return r<=.04045?e/12.92:t*((r+.055)/1.055)**2.4})),formats:{rgb:{coords:Rt},rgb_number:{name:"rgb",commas:!0,coords:_t,noAlpha:!0},color:{},rgba:{coords:Rt,commas:!0,lastAlpha:!0},rgba_number:{name:"rgba",commas:!0,coords:_t},hex:{type:"custom",toGamut:!0,test:e=>/^#([a-f0-9]{3,4}){1,2}$/i.test(e),parse(e){e.length<=5&&(e=e.replace(/[a-f0-9]/gi,"$&$&"));let t=[];return e.replace(/[a-f0-9]{2}/gi,(e=>{t.push(parseInt(e,16)/255)})),{spaceId:"srgb",coords:t.slice(0,3),alpha:t.slice(3)[0]}},serialize:(e,t,{collapse:r=!0}={})=>{t<1&&e.push(t),e=e.map((e=>Math.round(255*e)));let a=r&&e.every((e=>e%17==0)),n=e.map((e=>a?(e/17).toString(16):e.toString(16).padStart(2,"0"))).join("");return"#"+n}},keyword:{type:"custom",test:e=>/^[a-z]+$/i.test(e),parse(e){let t={spaceId:"srgb",coords:null,alpha:1};if("transparent"===(e=e.toLowerCase())?(t.coords=Ct.black,t.alpha=0):t.coords=Ct[e],t.coords)return t}}}}),Nt=new z({id:"p3",cssId:"display-p3",name:"P3",base:wt,fromBase:Bt.fromBase,toBase:Bt.toBase});let kt;if(v.display_space=Bt,"undefined"!=typeof CSS&&CSS.supports)for(let e of[W,Mt,Nt]){let t=e.getMinCoords(),r=ft({space:e,coords:t,alpha:1});if(CSS.supports("color",r)){v.display_space=e;break}}function St(e){return P(e,[L,"y"])}function xt(e,t){O(e,[L,"y"],t)}var Et=Object.freeze({__proto__:null,getLuminance:St,register:function(e){Object.defineProperty(e.prototype,"luminance",{get(){return St(this)},set(e){xt(this,e)}})},setLuminance:xt});const It=.022,Lt=1.414;function zt(e){return e>=It?e:e+(It-e)**Lt}function At(e){let t=e<0?-1:1,r=Math.abs(e);return t*Math.pow(r,2.4)}const Pt=24/116,jt=24389/27;let Ot=C.D65;var $t=new x({id:"lab-d65",name:"Lab D65",coords:{l:{refRange:[0,100],name:"Lightness"},a:{refRange:[-125,125]},b:{refRange:[-125,125]}},white:Ot,base:L,fromBase(e){let t=e.map(((e,t)=>e/Ot[t])).map((e=>e>.008856451679035631?Math.cbrt(e):(jt*e+16)/116));return[116*t[1]-16,500*(t[0]-t[1]),200*(t[1]-t[2])]},toBase(e){let t=[];return t[1]=(e[0]+16)/116,t[0]=e[1]/500+t[1],t[2]=t[1]-e[2]/200,[t[0]>Pt?Math.pow(t[0],3):(116*t[0]-16)/jt,e[0]>8?Math.pow((e[0]+16)/116,3):e[0]/jt,t[2]>Pt?Math.pow(t[2],3):(116*t[2]-16)/jt].map(((e,t)=>e*Ot[t]))},formats:{"lab-d65":{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});const qt=.5*Math.pow(5,.5)+.5;var Dt=Object.freeze({__proto__:null,contrastAPCA:function(e,t){let r,a,n,o,s,i;t=S(t),e=S(e),t=mt(t,"srgb"),[o,s,i]=t.coords;let c=.2126729*At(o)+.7151522*At(s)+.072175*At(i);e=mt(e,"srgb"),[o,s,i]=e.coords;let l=.2126729*At(o)+.7151522*At(s)+.072175*At(i),u=zt(c),h=zt(l),d=h>u;return Math.abs(h-u)<5e-4?a=0:d?(r=h**.56-u**.57,a=1.14*r):(r=h**.65-u**.62,a=1.14*r),n=Math.abs(a)<.1?0:a>0?a-.027:a+.027,100*n},contrastDeltaPhi:function(e,t){e=S(e),t=S(t);let r=P(e,[$t,"l"]),a=P(t,[$t,"l"]),n=Math.abs(Math.pow(r,qt)-Math.pow(a,qt)),o=Math.pow(n,1/qt)*Math.SQRT2-40;return o<7.5?0:o},contrastLstar:function(e,t){e=S(e),t=S(t);let r=P(e,[W,"l"]),a=P(t,[W,"l"]);return Math.abs(r-a)},contrastMichelson:function(e,t){e=S(e),t=S(t);let r=Math.max(St(e),0),a=Math.max(St(t),0);a>r&&([r,a]=[a,r]);let n=r+a;return 0===n?0:(r-a)/n},contrastWCAG21:function(e,t){e=S(e),t=S(t);let r=Math.max(St(e),0),a=Math.max(St(t),0);return a>r&&([r,a]=[a,r]),(r+.05)/(a+.05)},contrastWeber:function(e,t){e=S(e),t=S(t);let r=Math.max(St(e),0),a=Math.max(St(t),0);return a>r&&([r,a]=[a,r]),0===a?5e4:(r-a)/a}});function Ht(e){let[t,r,a]=A(e,L),n=t+15*r+3*a;return[4*t/n,9*r/n]}function Wt(e){let[t,r,a]=A(e,L),n=t+r+a;return[t/n,r/n]}var Tt=Object.freeze({__proto__:null,register:function(e){Object.defineProperty(e.prototype,"uv",{get(){return Ht(this)}}),Object.defineProperty(e.prototype,"xy",{get(){return Wt(this)}})},uv:Ht,xy:Wt});function Gt(e,r,a={}){t(a)&&(a={method:a});let{method:n=v.deltaE,...o}=a;for(let t in lt)if("deltae"+n.toLowerCase()===t.toLowerCase())return lt[t](e,r,o);throw new TypeError(`Unknown deltaE method: ${n}`)}var Xt=Object.freeze({__proto__:null,darken:function(e,t=.25){return O(e,[x.get("oklch","lch"),"l"],(e=>e*(1-t)))},lighten:function(e,t=.25){return O(e,[x.get("oklch","lch"),"l"],(e=>e*(1+t)))}});function Yt(e,t,a=.5,n={}){return[e,t]=[S(e),S(t)],"object"===r(a)&&([a,n]=[.5,a]),Ft(e,t,n)(a)}function Zt(e,t,r={}){let a;Jt(e)&&([a,r]=[e,t],[e,t]=a.rangeArgs.colors);let{maxDeltaE:n,deltaEMethod:o,steps:s=2,maxSteps:i=1e3,...c}=r;a||([e,t]=[S(e),S(t)],a=Ft(e,t,c));let l=Gt(e,t),u=n>0?Math.max(s,Math.ceil(l/n)+1):s,h=[];if(void 0!==i&&(u=Math.min(u,i)),1===u)h=[{p:.5,color:a(.5)}];else{let e=1/(u-1);h=Array.from({length:u},((t,r)=>{let n=r*e;return{p:n,color:a(n)}}))}if(n>0){let e=h.reduce(((e,t,r)=>{if(0===r)return 0;let a=Gt(t.color,h[r-1].color,o);return Math.max(e,a)}),0);for(;e>n;){e=0;for(let t=1;t<h.length&&h.length<i;t++){let r=h[t-1],n=h[t],o=(n.p+r.p)/2,s=a(o);e=Math.max(e,Gt(s,r.color),Gt(s,n.color)),h.splice(t,0,{p:o,color:a(o)}),t++}}}return h=h.map((e=>e.color)),h}function Ft(e,t,r={}){if(Jt(e)){let[r,a]=[e,t];return Ft(...r.rangeArgs.colors,{...r.rangeArgs.options,...a})}let{space:a,outputSpace:n,progression:o,premultiplied:s}=r;e=S(e),t=S(t),e=oe(e),t=oe(t);let i={colors:[e,t],options:r};if(a=a?x.get(a):x.registry[v.interpolationSpace]||e.space,n=n?x.get(n):a,e=mt(e,a),t=mt(t,a),e=ht(e),t=ht(t),a.coords.h&&"angle"===a.coords.h.type){let n=r.hue=r.hue||"shorter",o=[a,"h"],[s,i]=[P(e,o),P(t,o)];isNaN(s)&&!isNaN(i)?s=i:isNaN(i)&&!isNaN(s)&&(i=s),[s,i]=function(e,t){if("raw"===e)return t;let[r,a]=t.map(T),n=a-r;return"increasing"===e?n<0&&(a+=360):"decreasing"===e?n>0&&(r+=360):"longer"===e?-180<n&&n<180&&(n>0?r+=360:a+=360):"shorter"===e&&(n>180?r+=360:n<-180&&(a+=360)),[r,a]}(n,[s,i]),O(e,o,s),O(t,o,i)}return s&&(e.coords=e.coords.map((t=>t*e.alpha)),t.coords=t.coords.map((e=>e*t.alpha))),Object.assign((r=>{r=o?o(r):r;let i=e.coords.map(((e,a)=>u(e,t.coords[a],r))),c=u(e.alpha,t.alpha,r),l={space:a,coords:i,alpha:c};return s&&(l.coords=l.coords.map((e=>e/c))),n!==a&&(l=mt(l,n)),l}),{rangeArgs:i})}function Jt(e){return"function"===r(e)&&!!e.rangeArgs}v.interpolationSpace="lab";var Qt=Object.freeze({__proto__:null,isRange:Jt,mix:Yt,range:Ft,register:function(e){e.defineFunction("mix",Yt,{returns:"color"}),e.defineFunction("range",Ft,{returns:"function<color>"}),e.defineFunction("steps",Zt,{returns:"array<color>"})},steps:Zt}),Ut=new x({id:"hsl",name:"HSL",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:Bt,fromBase:e=>{let t=Math.max(...e),r=Math.min(...e),[a,n,o]=e,[s,i,c]=[NaN,0,(r+t)/2],l=t-r;if(0!==l){switch(i=0===c||1===c?0:(t-c)/Math.min(c,1-c),t){case a:s=(n-o)/l+(n<o?6:0);break;case n:s=(o-a)/l+2;break;case o:s=(a-n)/l+4}s*=60}return i<0&&(s+=180,i=Math.abs(i)),s>=360&&(s-=360),[s,100*i,100*c]},toBase:e=>{let[t,r,a]=e;function n(e){let n=(e+t/30)%12,o=r*Math.min(a,1-a);return a-o*Math.max(-1,Math.min(n-3,9-n,1))}return t%=360,t<0&&(t+=360),r/=100,a/=100,[n(0),n(8),n(4)]},formats:{hsl:{coords:["<number> | <angle>","<percentage>","<percentage>"]},hsla:{coords:["<number> | <angle>","<percentage>","<percentage>"],commas:!0,lastAlpha:!0}}}),Kt=new x({id:"hsv",name:"HSV",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},v:{range:[0,100],name:"Value"}},base:Ut,fromBase(e){let[t,r,a]=e;r/=100,a/=100;let n=a+r*Math.min(a,1-a);return[t,0===n?0:200*(1-a/n),100*n]},toBase(e){let[t,r,a]=e;r/=100,a/=100;let n=a*(1-r/2);return[t,0===n||1===n?0:(a-n)/Math.min(n,1-n)*100,100*n]},formats:{color:{id:"--hsv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}}),Vt=new x({id:"hwb",name:"HWB",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},w:{range:[0,100],name:"Whiteness"},b:{range:[0,100],name:"Blackness"}},base:Kt,fromBase(e){let[t,r,a]=e;return[t,a*(100-r)/100,100-a]},toBase(e){let[t,r,a]=e;r/=100,a/=100;let n=r+a;if(n>=1){return[t,0,100*(r/n)]}let o=1-a;return[t,100*(0===o?0:1-r/o),100*o]},formats:{hwb:{coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});var er=new z({id:"a98rgb-linear",cssId:"--a98-rgb-linear",name:"Linear Adobe® 98 RGB compatible",white:"D65",toXYZ_M:[[.5766690429101305,.1855582379065463,.1882286462349947],[.29734497525053605,.6273635662554661,.07529145849399788],[.02703136138641234,.07068885253582723,.9913375368376388]],fromXYZ_M:[[2.0415879038107465,-.5650069742788596,-.34473135077832956],[-.9692436362808795,1.8759675015077202,.04155505740717557],[.013444280632031142,-.11836239223101838,1.0151749943912054]]}),tr=new z({id:"a98rgb",cssId:"a98-rgb",name:"Adobe® 98 RGB compatible",base:er,toBase:e=>e.map((e=>Math.pow(Math.abs(e),563/256)*Math.sign(e))),fromBase:e=>e.map((e=>Math.pow(Math.abs(e),256/563)*Math.sign(e)))});var rr=new z({id:"prophoto-linear",cssId:"--prophoto-rgb-linear",name:"Linear ProPhoto",white:"D50",base:$,toXYZ_M:[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],fromXYZ_M:[[1.3457868816471583,-.25557208737979464,-.05110186497554526],[-.5446307051249019,1.5082477428451468,.02052744743642139],[0,0,1.2119675456389452]]});var ar=new z({id:"prophoto",cssId:"prophoto-rgb",name:"ProPhoto",base:rr,toBase:e=>e.map((e=>e<.03125?e/16:e**1.8)),fromBase:e=>e.map((e=>e>=.001953125?e**(1/1.8):16*e))}),nr=new x({id:"oklch",name:"Oklch",coords:{l:{refRange:[0,1],name:"Lightness"},c:{refRange:[0,.4],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},white:"D65",base:te,fromBase(e){let t,[r,a,n]=e;const o=2e-4;return t=Math.abs(a)<o&&Math.abs(n)<o?NaN:180*Math.atan2(n,a)/Math.PI,[r,Math.sqrt(a**2+n**2),T(t)]},toBase(e){let t,r,[a,n,o]=e;return isNaN(o)?(t=0,r=0):(t=n*Math.cos(o*Math.PI/180),r=n*Math.sin(o*Math.PI/180)),[a,t,r]},formats:{oklch:{coords:["<percentage> | <number>","<number> | <percentage>[0,1]","<number> | <angle>"]}}});let or=C.D65;const sr=24389/27,[ir,cr]=Ht({space:L,coords:or});var lr=new x({id:"luv",name:"Luv",coords:{l:{refRange:[0,100],name:"Lightness"},u:{refRange:[-215,215]},v:{refRange:[-215,215]}},white:or,base:L,fromBase(e){let t=[o(e[0]),o(e[1]),o(e[2])],r=t[1],[a,n]=Ht({space:L,coords:t});if(!Number.isFinite(a)||!Number.isFinite(n))return[0,0,0];let s=r<=.008856451679035631?sr*r:116*Math.cbrt(r)-16;return[s,13*s*(a-ir),13*s*(n-cr)]},toBase(e){let[t,r,a]=e;if(0===t||n(t))return[0,0,0];r=o(r),a=o(a);let s=r/(13*t)+ir,i=a/(13*t)+cr,c=t<=8?t/sr:Math.pow((t+16)/116,3);return[c*(9*s/(4*i)),c,c*((12-3*s-20*i)/(4*i))]},formats:{color:{id:"--luv",coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}}),ur=new x({id:"lchuv",name:"LChuv",coords:{l:{refRange:[0,100],name:"Lightness"},c:{refRange:[0,220],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:lr,fromBase(e){let t,[r,a,n]=e;return t=Math.abs(a)<.02&&Math.abs(n)<.02?NaN:180*Math.atan2(n,a)/Math.PI,[r,Math.sqrt(a**2+n**2),T(t)]},toBase(e){let[t,r,a]=e;return r<0&&(r=0),isNaN(a)&&(a=0),[t,r*Math.cos(a*Math.PI/180),r*Math.sin(a*Math.PI/180)]},formats:{color:{id:"--lchuv",coords:["<number> | <percentage>","<number> | <percentage>","<number> | <angle>"]}}});const hr=yt[0][0],dr=yt[0][1],mr=yt[0][2],fr=yt[1][0],pr=yt[1][1],gr=yt[1][2],br=yt[2][0],Mr=yt[2][1],wr=yt[2][2];function yr(e,t,r){const a=t/(Math.sin(r)-e*Math.cos(r));return a<0?1/0:a}function vr(e){const t=Math.pow(e+16,3)/1560896,r=t>.008856451679035631?t:e/903.2962962962963,a=r*(284517*hr-94839*mr),n=r*(838422*mr+769860*dr+731718*hr),o=r*(632260*mr-126452*dr),s=r*(284517*fr-94839*gr),i=r*(838422*gr+769860*pr+731718*fr),c=r*(632260*gr-126452*pr),l=r*(284517*br-94839*wr),u=r*(838422*wr+769860*Mr+731718*br),h=r*(632260*wr-126452*Mr);return{r0s:a/o,r0i:n*e/o,r1s:a/(o+126452),r1i:(n-769860)*e/(o+126452),g0s:s/c,g0i:i*e/c,g1s:s/(c+126452),g1i:(i-769860)*e/(c+126452),b0s:l/h,b0i:u*e/h,b1s:l/(h+126452),b1i:(u-769860)*e/(h+126452)}}function Cr(e,t){const r=t/360*Math.PI*2,a=yr(e.r0s,e.r0i,r),n=yr(e.r1s,e.r1i,r),o=yr(e.g0s,e.g0i,r),s=yr(e.g1s,e.g1i,r),i=yr(e.b0s,e.b0i,r),c=yr(e.b1s,e.b1i,r);return Math.min(a,n,o,s,i,c)}var Rr=new x({id:"hsluv",name:"HSLuv",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:ur,gamutSpace:Bt,fromBase(e){let t,[r,a,n]=[o(e[0]),o(e[1]),o(e[2])];if(r>99.9999999)t=0,r=100;else if(r<1e-8)t=0,r=0;else{t=a/Cr(vr(r),n)*100}return[n,t,r]},toBase(e){let t,[r,a,n]=[o(e[0]),o(e[1]),o(e[2])];if(n>99.9999999)n=100,t=0;else if(n<1e-8)n=0,t=0;else{t=Cr(vr(n),r)/100*a}return[n,t,r]},formats:{color:{id:"--hsluv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});function _r(e,t){return Math.abs(t)/Math.sqrt(Math.pow(e,2)+1)}function Br(e){let t=_r(e.r0s,e.r0i),r=_r(e.r1s,e.r1i),a=_r(e.g0s,e.g0i),n=_r(e.g1s,e.g1i),o=_r(e.b0s,e.b0i),s=_r(e.b1s,e.b1i);return Math.min(t,r,a,n,o,s)}yt[0][0],yt[0][1],yt[0][2],yt[1][0],yt[1][1],yt[1][2],yt[2][0],yt[2][1],yt[2][2];var Nr=new x({id:"hpluv",name:"HPLuv",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:ur,gamutSpace:"self",fromBase(e){let t,[r,a,n]=[o(e[0]),o(e[1]),o(e[2])];if(r>99.9999999)t=0,r=100;else if(r<1e-8)t=0,r=0;else{t=a/Br(vr(r))*100}return[n,t,r]},toBase(e){let t,[r,a,n]=[o(e[0]),o(e[1]),o(e[2])];if(n>99.9999999)n=100,t=0;else if(n<1e-8)n=0,t=0;else{t=Br(vr(n))/100*a}return[n,t,r]},formats:{color:{id:"--hpluv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const kr=2610/16384,Sr=32/2523,xr=.8359375,Er=2413/128,Ir=18.6875;var Lr=new z({id:"rec2100pq",cssId:"rec2100-pq",name:"REC.2100-PQ",base:pt,toBase:e=>e.map((function(e){return 1e4*(Math.max(e**Sr-xr,0)/(Er-Ir*e**Sr))**6.277394636015326/203})),fromBase:e=>e.map((function(e){let t=Math.max(203*e/1e4,0);return((xr+Er*t**kr)/(1+Ir*t**kr))**78.84375}))});const zr=.17883277,Ar=.28466892,Pr=.55991073,jr=3.7743;var Or=new z({id:"rec2100hlg",cssId:"rec2100-hlg",name:"REC.2100-HLG",referred:"scene",base:pt,toBase:e=>e.map((function(e){return e<=.5?e**2/3*jr:(Math.exp((e-Pr)/zr)+Ar)/12*jr})),fromBase:e=>e.map((function(e){return(e/=jr)<=1/12?Math.sqrt(3*e):zr*Math.log(12*e-Ar)+Pr}))});const $r={};function qr({id:e,toCone_M:t,fromCone_M:r}){$r[e]=arguments[0]}function Dr(t,r,a="Bradford"){let n=$r[a],[o,s,i]=e(n.toCone_M,t),[c,l,u]=e(n.toCone_M,r),h=e([[c/o,0,0],[0,l/s,0],[0,0,u/i]],n.toCone_M);return e(n.fromCone_M,h)}y.add("chromatic-adaptation-start",(e=>{e.options.method&&(e.M=Dr(e.W1,e.W2,e.options.method))})),y.add("chromatic-adaptation-end",(e=>{e.M||(e.M=Dr(e.W1,e.W2,e.options.method))})),qr({id:"von Kries",toCone_M:[[.40024,.7076,-.08081],[-.2263,1.16532,.0457],[0,0,.91822]],fromCone_M:[[1.8599363874558397,-1.1293816185800916,.21989740959619328],[.3611914362417676,.6388124632850422,-6370596838649899e-21],[0,0,1.0890636230968613]]}),qr({id:"Bradford",toCone_M:[[.8951,.2664,-.1614],[-.7502,1.7135,.0367],[.0389,-.0685,1.0296]],fromCone_M:[[.9869929054667121,-.14705425642099013,.15996265166373122],[.4323052697233945,.5183602715367774,.049291228212855594],[-.00852866457517732,.04004282165408486,.96848669578755]]}),qr({id:"CAT02",toCone_M:[[.7328,.4296,-.1624],[-.7036,1.6975,.0061],[.003,.0136,.9834]],fromCone_M:[[1.0961238208355142,-.27886900021828726,.18274517938277307],[.4543690419753592,.4735331543074117,.07209780371722911],[-.009627608738429355,-.00569803121611342,1.0153256399545427]]}),qr({id:"CAT16",toCone_M:[[.401288,.650173,-.051461],[-.250268,1.204414,.045854],[-.002079,.048952,.953127]],fromCone_M:[[1.862067855087233,-1.0112546305316845,.14918677544445172],[.3875265432361372,.6214474419314753,-.008973985167612521],[-.01584149884933386,-.03412293802851557,1.0499644368778496]]}),Object.assign(C,{A:[1.0985,1,.35585],C:[.98074,1,1.18232],D55:[.95682,1,.92149],D75:[.94972,1,1.22638],E:[1,1,1],F2:[.99186,1,.67393],F7:[.95041,1,1.08747],F11:[1.00962,1,.6435]}),C.ACES=[.32168/.33767,1,.34065/.33767];var Hr=new z({id:"acescg",cssId:"--acescg",name:"ACEScg",coords:{r:{range:[0,65504],name:"Red"},g:{range:[0,65504],name:"Green"},b:{range:[0,65504],name:"Blue"}},referred:"scene",white:C.ACES,toXYZ_M:[[.6624541811085053,.13400420645643313,.1561876870049078],[.27222871678091454,.6740817658111484,.05368951740793705],[-.005574649490394108,.004060733528982826,1.0103391003129971]],fromXYZ_M:[[1.6410233796943257,-.32480329418479,-.23642469523761225],[-.6636628587229829,1.6153315916573379,.016756347685530137],[.011721894328375376,-.008284441996237409,.9883948585390215]]});const Wr=2**-16,Tr=-.35828683,Gr=(Math.log2(65504)+9.72)/17.52;var Xr=new z({id:"acescc",cssId:"--acescc",name:"ACEScc",coords:{r:{range:[Tr,Gr],name:"Red"},g:{range:[Tr,Gr],name:"Green"},b:{range:[Tr,Gr],name:"Blue"}},referred:"scene",base:Hr,toBase:e=>e.map((function(e){return e<=-.3013698630136986?2*(2**(17.52*e-9.72)-Wr):e<Gr?2**(17.52*e-9.72):65504})),fromBase:e=>e.map((function(e){return e<=0?(Math.log2(Wr)+9.72)/17.52:e<Wr?(Math.log2(Wr+.5*e)+9.72)/17.52:(Math.log2(e)+9.72)/17.52}))}),Yr=Object.freeze({__proto__:null,A98RGB:tr,A98RGB_Linear:er,ACEScc:Xr,ACEScg:Hr,CAM16_JMh:Ke,HCT:ot,HPLuv:Nr,HSL:Ut,HSLuv:Rr,HSV:Kt,HWB:Vt,ICTCP:Pe,JzCzHz:Re,Jzazbz:Ce,LCH:G,LCHuv:ur,Lab:W,Lab_D65:$t,Luv:lr,OKLCH:nr,OKLab:te,P3:Nt,P3_Linear:wt,ProPhoto:ar,ProPhoto_Linear:rr,REC_2020:Mt,REC_2020_Linear:pt,REC_2100_HLG:Or,REC_2100_PQ:Lr,XYZ_ABS_D65:ce,XYZ_D50:$,XYZ_D65:L,sRGB:Bt,sRGB_Linear:vt});class Zr{constructor(...e){let t,r,a,n;1===e.length&&(t=S(e[0])),t?(r=t.space||t.spaceId,a=t.coords,n=t.alpha):[r,a,n]=e,Object.defineProperty(this,"space",{value:x.get(r),writable:!1,enumerable:!0,configurable:!0}),this.coords=a?a.slice():[0,0,0],this.alpha=n>1||void 0===n?1:n<0?0:n;for(let e=0;e<this.coords.length;e++)"NaN"===this.coords[e]&&(this.coords[e]=NaN);for(let e in this.space.coords)Object.defineProperty(this,e,{get:()=>this.get(e),set:t=>this.set(e,t)})}get spaceId(){return this.space.id}clone(){return new Zr(this.space,this.coords,this.alpha)}toJSON(){return{spaceId:this.spaceId,coords:this.coords,alpha:this.alpha}}display(...e){let t=function(e,{space:t=v.display_space,...r}={}){let a=ft(e,r);if("undefined"==typeof CSS||CSS.supports("color",a)||!v.display_space)a=new String(a),a.color=e;else{let s=e;if((e.coords.some(n)||n(e.alpha))&&!(kt??=CSS.supports("color","hsl(none 50% 50%)"))&&(s=oe(e),s.coords=s.coords.map(o),s.alpha=o(s.alpha),a=ft(s,r),CSS.supports("color",a)))return a=new String(a),a.color=s,a;s=mt(s,t),a=new String(ft(s,r)),a.color=s}return a}(this,...e);return t.color=new Zr(t.color),t}static get(e,...t){return e instanceof Zr?e:new Zr(e,...t)}static defineFunction(e,t,r=t){let{instance:a=!0,returns:n}=r,o=function(...e){let r=t(...e);if("color"===n)r=Zr.get(r);else if("function<color>"===n){let e=r;r=function(...t){let r=e(...t);return Zr.get(r)},Object.assign(r,e)}else"array<color>"===n&&(r=r.map((e=>Zr.get(e))));return r};e in Zr||(Zr[e]=o),a&&(Zr.prototype[e]=function(...e){return o(this,...e)})}static defineFunctions(e){for(let t in e)Zr.defineFunction(t,e[t],e[t])}static extend(e){if(e.register)e.register(Zr);else for(let t in e)Zr.defineFunction(t,e[t])}}Zr.defineFunctions({get:P,getAll:A,set:O,setAll:j,to:mt,equals:function(e,t){return e=S(e),t=S(t),e.space===t.space&&e.alpha===t.alpha&&e.coords.every(((e,r)=>e===t.coords[r]))},inGamut:ne,toGamut:ht,distance:se,toString:ft}),Object.assign(Zr,{util:w,hooks:y,WHITES:C,Space:x,spaces:x.registry,parse:k,defaults:v});for(let e of Object.keys(Yr))x.register(Yr[e]);for(let e in x.registry)Fr(e,x.registry[e]);function Fr(e,t){let r=e.replace(/-/g,"_");Object.defineProperty(Zr.prototype,r,{get(){let r=this.getAll(e);return"undefined"==typeof Proxy?r:new Proxy(r,{has:(e,r)=>{try{return x.resolveCoord([t,r]),!0}catch(e){}return Reflect.has(e,r)},get:(e,r,a)=>{if(r&&"symbol"!=typeof r&&!(r in e)){let{index:a}=x.resolveCoord([t,r]);if(a>=0)return e[a]}return Reflect.get(e,r,a)},set:(r,a,n,o)=>{if(a&&"symbol"!=typeof a&&!(a in r)||a>=0){let{index:o}=x.resolveCoord([t,a]);if(o>=0)return r[o]=n,this.setAll(e,r),!0}return Reflect.set(r,a,n,o)}})},set(t){this.setAll(e,t)},configurable:!0,enumerable:!0})}return y.add("colorspace-init-end",(e=>{Fr(e.id,e),e.aliases?.forEach((t=>{Fr(t,e)}))})),Zr.extend(lt),Zr.extend({deltaE:Gt}),Object.assign(Zr,{deltaEMethods:lt}),Zr.extend(Xt),Zr.extend({contrast:function(e,r,a={}){t(a)&&(a={algorithm:a});let{algorithm:n,...o}=a;if(!n){let e=Object.keys(Dt).map((e=>e.replace(/^contrast/,""))).join(", ");throw new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${e}`)}e=S(e),r=S(r);for(let t in Dt)if("contrast"+n.toLowerCase()===t.toLowerCase())return Dt[t](e,r,o);throw new TypeError(`Unknown contrast algorithm: ${n}`)}}),Zr.extend(Tt),Zr.extend(Et),Zr.extend(Qt),Zr.extend(Dt),Zr}();
//# sourceMappingURL=color.global.min.js.map
