"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e,r,t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n={};function o(){if(r)return e;r=1;var n=function(e){return e&&e.Math===Math&&e};return e=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof t&&t)||n("object"==typeof t&&t)||function(){return this}()||Function("return this")()}var a,i,s,u,c,l,f,h,p={};function d(){return i?a:(i=1,a=function(e){try{return!!e()}catch(e){return!0}})}function m(){if(u)return s;u=1;var e=d();return s=!e((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function g(){if(l)return c;l=1;var e=d();return c=!e((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))}function b(){if(h)return f;h=1;var e=g(),r=Function.prototype.call;return f=e?r.bind(r):function(){return r.apply(r,arguments)},f}var v,y,w,M,C,S,E,_,j,R,O,P,B,k,N,x,A,I,L,z,T,D,$,q,H,W,F,G,X,Y,Z,J,U,Q,K,V,ee,re,te,ne,oe,ae={};function ie(){return w?y:(w=1,y=function(e,r){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:r}})}function se(){if(C)return M;C=1;var e=g(),r=Function.prototype,t=r.call,n=e&&r.bind.bind(t,t);return M=e?n:function(e){return function(){return t.apply(e,arguments)}},M}function ue(){if(E)return S;E=1;var e=se(),r=e({}.toString),t=e("".slice);return S=function(e){return t(r(e),8,-1)}}function ce(){return O?R:(O=1,R=function(e){return null==e})}function le(){if(B)return P;B=1;var e=ce(),r=TypeError;return P=function(t){if(e(t))throw new r("Can't call method on "+t);return t}}function fe(){if(N)return k;N=1;var e=function(){if(j)return _;j=1;var e=se(),r=d(),t=ue(),n=Object,o=e("".split);return _=r((function(){return!n("z").propertyIsEnumerable(0)}))?function(e){return"String"===t(e)?o(e,""):n(e)}:n}(),r=le();return k=function(t){return e(r(t))}}function he(){if(A)return x;A=1;var e="object"==typeof document&&document.all;return x=void 0===e&&void 0!==e?function(r){return"function"==typeof r||r===e}:function(e){return"function"==typeof e}}function pe(){if(L)return I;L=1;var e=he();return I=function(r){return"object"==typeof r?null!==r:e(r)}}function de(){if(T)return z;T=1;var e=o(),r=he();return z=function(t,n){return arguments.length<2?(o=e[t],r(o)?o:void 0):e[t]&&e[t][n];var o},z}function me(){if($)return D;$=1;var e=se();return D=e({}.isPrototypeOf)}function ge(){if(F)return W;F=1;var e,r,t=o(),n=H?q:(H=1,q="undefined"!=typeof navigator&&String(navigator.userAgent)||""),a=t.process,i=t.Deno,s=a&&a.versions||i&&i.version,u=s&&s.v8;return u&&(r=(e=u.split("."))[0]>0&&e[0]<4?1:+(e[0]+e[1])),!r&&n&&(!(e=n.match(/Edge\/(\d+)/))||e[1]>=74)&&(e=n.match(/Chrome\/(\d+)/))&&(r=+e[1]),W=r}function be(){if(X)return G;X=1;var e=ge(),r=d(),t=o().String;return G=!!Object.getOwnPropertySymbols&&!r((function(){var r=Symbol("symbol detection");return!t(r)||!(Object(r)instanceof Symbol)||!Symbol.sham&&e&&e<41}))}function ve(){if(Z)return Y;Z=1;var e=be();return Y=e&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}function ye(){if(U)return J;U=1;var e=de(),r=he(),t=me(),n=ve(),o=Object;return J=n?function(e){return"symbol"==typeof e}:function(n){var a=e("Symbol");return r(a)&&t(a.prototype,o(n))}}function we(){if(K)return Q;K=1;var e=String;return Q=function(r){try{return e(r)}catch(e){return"Object"}}}function Me(){if(ee)return V;ee=1;var e=he(),r=we(),t=TypeError;return V=function(n){if(e(n))return n;throw new t(r(n)+" is not a function")}}function Ce(){if(te)return re;te=1;var e=Me(),r=ce();return re=function(t,n){var o=t[n];return r(o)?void 0:e(o)}}function Se(){if(oe)return ne;oe=1;var e=b(),r=he(),t=pe(),n=TypeError;return ne=function(o,a){var i,s;if("string"===a&&r(i=o.toString)&&!t(s=e(i,o)))return s;if(r(i=o.valueOf)&&!t(s=e(i,o)))return s;if("string"!==a&&r(i=o.toString)&&!t(s=e(i,o)))return s;throw new n("Can't convert object to primitive value")}}var Ee,_e,je,Re,Oe,Pe,Be,ke,Ne,xe,Ae,Ie,Le,ze,Te,De,$e,qe,He,We,Fe,Ge,Xe,Ye,Ze={exports:{}};function Je(){return _e?Ee:(_e=1,Ee=!1)}function Ue(){if(Re)return je;Re=1;var e=o(),r=Object.defineProperty;return je=function(t,n){try{r(e,t,{value:n,configurable:!0,writable:!0})}catch(r){e[t]=n}return n}}function Qe(){if(Oe)return Ze.exports;Oe=1;var e=Je(),r=o(),t=Ue(),n="__core-js_shared__",a=Ze.exports=r[n]||t(n,{});return(a.versions||(a.versions=[])).push({version:"3.36.0",mode:e?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.36.0/LICENSE",source:"https://github.com/zloirock/core-js"}),Ze.exports}function Ke(){if(Be)return Pe;Be=1;var e=Qe();return Pe=function(r,t){return e[r]||(e[r]=t||{})}}function Ve(){if(Ne)return ke;Ne=1;var e=le(),r=Object;return ke=function(t){return r(e(t))}}function er(){if(Ae)return xe;Ae=1;var e=se(),r=Ve(),t=e({}.hasOwnProperty);return xe=Object.hasOwn||function(e,n){return t(r(e),n)}}function rr(){if(Le)return Ie;Le=1;var e=se(),r=0,t=Math.random(),n=e(1..toString);return Ie=function(e){return"Symbol("+(void 0===e?"":e)+")_"+n(++r+t,36)}}function tr(){if(Te)return ze;Te=1;var e=o(),r=Ke(),t=er(),n=rr(),a=be(),i=ve(),s=e.Symbol,u=r("wks"),c=i?s.for||s:s&&s.withoutSetter||n;return ze=function(e){return t(u,e)||(u[e]=a&&t(s,e)?s[e]:c("Symbol."+e)),u[e]}}function nr(){if($e)return De;$e=1;var e=b(),r=pe(),t=ye(),n=Ce(),o=Se(),a=tr(),i=TypeError,s=a("toPrimitive");return De=function(a,u){if(!r(a)||t(a))return a;var c,l=n(a,s);if(l){if(void 0===u&&(u="default"),c=e(l,a,u),!r(c)||t(c))return c;throw new i("Can't convert object to primitive value")}return void 0===u&&(u="number"),o(a,u)}}function or(){if(He)return qe;He=1;var e=nr(),r=ye();return qe=function(t){var n=e(t,"string");return r(n)?n:n+""}}function ar(){if(Xe)return Ge;Xe=1;var e=m(),r=d(),t=function(){if(Fe)return We;Fe=1;var e=o(),r=pe(),t=e.document,n=r(t)&&r(t.createElement);return We=function(e){return n?t.createElement(e):{}}}();return Ge=!e&&!r((function(){return 7!==Object.defineProperty(t("div"),"a",{get:function(){return 7}}).a}))}function ir(){if(Ye)return p;Ye=1;var e=m(),r=b(),t=function(){if(v)return ae;v=1;var e={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,t=r&&!e.call({1:2},1);return ae.f=t?function(e){var t=r(this,e);return!!t&&t.enumerable}:e,ae}(),n=ie(),o=fe(),a=or(),i=er(),s=ar(),u=Object.getOwnPropertyDescriptor;return p.f=e?u:function(e,c){if(e=o(e),c=a(c),s)try{return u(e,c)}catch(e){}if(i(e,c))return n(!r(t.f,e,c),e[c])},p}var sr,ur,cr,lr,fr,hr,pr,dr={};function mr(){if(lr)return cr;lr=1;var e=pe(),r=String,t=TypeError;return cr=function(n){if(e(n))return n;throw new t(r(n)+" is not an object")}}function gr(){if(fr)return dr;fr=1;var e=m(),r=ar(),t=function(){if(ur)return sr;ur=1;var e=m(),r=d();return sr=e&&r((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))}(),n=mr(),o=or(),a=TypeError,i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,u="enumerable",c="configurable",l="writable";return dr.f=e?t?function(e,r,t){if(n(e),r=o(r),n(t),"function"==typeof e&&"prototype"===r&&"value"in t&&l in t&&!t[l]){var a=s(e,r);a&&a[l]&&(e[r]=t.value,t={configurable:c in t?t[c]:a[c],enumerable:u in t?t[u]:a[u],writable:!1})}return i(e,r,t)}:i:function(e,t,s){if(n(e),t=o(t),n(s),r)try{return i(e,t,s)}catch(e){}if("get"in s||"set"in s)throw new a("Accessors not supported");return"value"in s&&(e[t]=s.value),e},dr}function br(){if(pr)return hr;pr=1;var e=m(),r=gr(),t=ie();return hr=e?function(e,n,o){return r.f(e,n,t(1,o))}:function(e,r,t){return e[r]=t,e}}var vr,yr,wr,Mr,Cr,Sr,Er,_r,jr,Rr,Or,Pr,Br,kr,Nr,xr={exports:{}};function Ar(){if(Mr)return wr;Mr=1;var e=se(),r=he(),t=Qe(),n=e(Function.toString);return r(t.inspectSource)||(t.inspectSource=function(e){return n(e)}),wr=t.inspectSource}function Ir(){if(_r)return Er;_r=1;var e=Ke(),r=rr(),t=e("keys");return Er=function(e){return t[e]||(t[e]=r(e))}}function Lr(){return Rr?jr:(Rr=1,jr={})}function zr(){if(Pr)return Or;Pr=1;var e,r,t,n=function(){if(Sr)return Cr;Sr=1;var e=o(),r=he(),t=e.WeakMap;return Cr=r(t)&&/native code/.test(String(t))}(),a=o(),i=pe(),s=br(),u=er(),c=Qe(),l=Ir(),f=Lr(),h="Object already initialized",p=a.TypeError,d=a.WeakMap;if(n||c.state){var m=c.state||(c.state=new d);m.get=m.get,m.has=m.has,m.set=m.set,e=function(e,r){if(m.has(e))throw new p(h);return r.facade=e,m.set(e,r),r},r=function(e){return m.get(e)||{}},t=function(e){return m.has(e)}}else{var g=l("state");f[g]=!0,e=function(e,r){if(u(e,g))throw new p(h);return r.facade=e,s(e,g,r),r},r=function(e){return u(e,g)?e[g]:{}},t=function(e){return u(e,g)}}return Or={set:e,get:r,has:t,enforce:function(n){return t(n)?r(n):e(n,{})},getterFor:function(e){return function(t){var n;if(!i(t)||(n=r(t)).type!==e)throw new p("Incompatible receiver, "+e+" required");return n}}}}function Tr(){if(Br)return xr.exports;Br=1;var e=se(),r=d(),t=he(),n=er(),o=m(),a=function(){if(yr)return vr;yr=1;var e=m(),r=er(),t=Function.prototype,n=e&&Object.getOwnPropertyDescriptor,o=r(t,"name"),a=o&&"something"===function(){}.name,i=o&&(!e||e&&n(t,"name").configurable);return vr={EXISTS:o,PROPER:a,CONFIGURABLE:i}}().CONFIGURABLE,i=Ar(),s=zr(),u=s.enforce,c=s.get,l=String,f=Object.defineProperty,h=e("".slice),p=e("".replace),g=e([].join),b=o&&!r((function(){return 8!==f((function(){}),"length",{value:8}).length})),v=String(String).split("String"),y=xr.exports=function(e,r,t){"Symbol("===h(l(r),0,7)&&(r="["+p(l(r),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),t&&t.getter&&(r="get "+r),t&&t.setter&&(r="set "+r),(!n(e,"name")||a&&e.name!==r)&&(o?f(e,"name",{value:r,configurable:!0}):e.name=r),b&&t&&n(t,"arity")&&e.length!==t.arity&&f(e,"length",{value:t.arity});try{t&&n(t,"constructor")&&t.constructor?o&&f(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var i=u(e);return n(i,"source")||(i.source=g(v,"string"==typeof r?r:"")),e};return Function.prototype.toString=y((function(){return t(this)&&c(this).source||i(this)}),"toString"),xr.exports}function Dr(){if(Nr)return kr;Nr=1;var e=he(),r=gr(),t=Tr(),n=Ue();return kr=function(o,a,i,s){s||(s={});var u=s.enumerable,c=void 0!==s.name?s.name:a;if(e(i)&&t(i,c,s),s.global)u?o[a]=i:n(a,i);else{try{s.unsafe?o[a]&&(u=!0):delete o[a]}catch(e){}u?o[a]=i:r.f(o,a,{value:i,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return o}}var $r,qr,Hr,Wr,Fr,Gr,Xr,Yr,Zr,Jr,Ur,Qr,Kr,Vr,et,rt,tt,nt={};function ot(){if(Wr)return Hr;Wr=1;var e=function(){if(qr)return $r;qr=1;var e=Math.ceil,r=Math.floor;return $r=Math.trunc||function(t){var n=+t;return(n>0?r:e)(n)},$r}();return Hr=function(r){var t=+r;return t!=t||0===t?0:e(t)}}function at(){if(Gr)return Fr;Gr=1;var e=ot(),r=Math.max,t=Math.min;return Fr=function(n,o){var a=e(n);return a<0?r(a+o,0):t(a,o)}}function it(){if(Yr)return Xr;Yr=1;var e=ot(),r=Math.min;return Xr=function(t){var n=e(t);return n>0?r(n,9007199254740991):0}}function st(){if(Jr)return Zr;Jr=1;var e=it();return Zr=function(r){return e(r.length)}}function ut(){if(Vr)return Kr;Vr=1;var e=se(),r=er(),t=fe(),n=function(){if(Qr)return Ur;Qr=1;var e=fe(),r=at(),t=st(),n=function(n){return function(o,a,i){var s=e(o),u=t(s);if(0===u)return!n&&-1;var c,l=r(i,u);if(n&&a!=a){for(;u>l;)if((c=s[l++])!=c)return!0}else for(;u>l;l++)if((n||l in s)&&s[l]===a)return n||l||0;return!n&&-1}};return Ur={includes:n(!0),indexOf:n(!1)}}().indexOf,o=Lr(),a=e([].push);return Kr=function(e,i){var s,u=t(e),c=0,l=[];for(s in u)!r(o,s)&&r(u,s)&&a(l,s);for(;i.length>c;)r(u,s=i[c++])&&(~n(l,s)||a(l,s));return l}}var ct,lt,ft,ht,pt,dt,mt,gt,bt,vt,yt,wt,Mt,Ct,St,Et,_t={};function jt(){if(ft)return lt;ft=1;var e=de(),r=se(),t=function(){if(tt)return nt;tt=1;var e=ut(),r=(rt?et:(rt=1,et=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])).concat("length","prototype");return nt.f=Object.getOwnPropertyNames||function(t){return e(t,r)},nt}(),n=(ct||(ct=1,_t.f=Object.getOwnPropertySymbols),_t),o=mr(),a=r([].concat);return lt=e("Reflect","ownKeys")||function(e){var r=t.f(o(e)),i=n.f;return i?a(r,i(e)):r}}function Rt(){if(pt)return ht;pt=1;var e=er(),r=jt(),t=ir(),n=gr();return ht=function(o,a,i){for(var s=r(a),u=n.f,c=t.f,l=0;l<s.length;l++){var f=s[l];e(o,f)||i&&e(i,f)||u(o,f,c(a,f))}}}function Ot(){if(bt)return gt;bt=1;var e=o(),r=ir().f,t=br(),n=Dr(),a=Ue(),i=Rt(),s=function(){if(mt)return dt;mt=1;var e=d(),r=he(),t=/#|\.prototype\./,n=function(t,n){var u=a[o(t)];return u===s||u!==i&&(r(n)?e(n):!!n)},o=n.normalize=function(e){return String(e).replace(t,".").toLowerCase()},a=n.data={},i=n.NATIVE="N",s=n.POLYFILL="P";return dt=n}();return gt=function(o,u){var c,l,f,h,p,d=o.target,m=o.global,g=o.stat;if(c=m?e:g?e[d]||a(d,{}):e[d]&&e[d].prototype)for(l in u){if(h=u[l],f=o.dontCallGetSet?(p=r(c,l))&&p.value:c[l],!s(m?l:d+(g?".":"#")+l,o.forced)&&void 0!==f){if(typeof h==typeof f)continue;i(h,f)}(o.sham||f&&f.sham)&&t(h,"sham",!0),n(c,l,h,o)}}}function Pt(){if(yt)return vt;yt=1;var e=ue();return vt=Array.isArray||function(r){return"Array"===e(r)}}function Bt(){if(Mt)return wt;Mt=1;var e=m(),r=Pt(),t=TypeError,n=Object.getOwnPropertyDescriptor,o=e&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();return wt=o?function(e,o){if(r(e)&&!n(e,"length").writable)throw new t("Cannot set read only .length");return e.length=o}:function(e,r){return e.length=r}}function kt(){if(St)return Ct;St=1;var e=TypeError;return Ct=function(r){if(r>9007199254740991)throw e("Maximum allowed index exceeded");return r}}function Nt(e,r){let t=e.length;Array.isArray(e[0])||(e=[e]),Array.isArray(r[0])||(r=r.map((e=>[e])));let n=r[0].length,o=r[0].map(((e,t)=>r.map((e=>e[t])))),a=e.map((e=>o.map((r=>{let t=0;if(!Array.isArray(e)){for(let n of r)t+=e*n;return t}for(let n=0;n<e.length;n++)t+=e[n]*(r[n]||0);return t}))));return 1===t&&(a=a[0]),1===n?a.map((e=>e[0])):a}function xt(e){return"string"===At(e)}function At(e){return(Object.prototype.toString.call(e).match(/^\[object\s+(.*?)\]$/)[1]||"").toLowerCase()}function It(e,r){let{precision:t,unit:n}=r;return Lt(e)?"none":Tt(e,t)+(null!=n?n:"")}function Lt(e){return Number.isNaN(e)||e instanceof Number&&(null==e?void 0:e.none)}function zt(e){return Lt(e)?0:e}function Tt(e,r){if(0===e)return 0;let t=~~e,n=0;t&&r&&(n=1+~~Math.log10(Math.abs(t)));const o=10**(r-n);return Math.floor(e*o+.5)/o}!function(){if(Et)return n;Et=1;var e=Ot(),r=Ve(),t=st(),o=Bt(),a=kt();e({target:"Array",proto:!0,arity:1,forced:d()((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function(e){var n=r(this),i=t(n),s=arguments.length;a(i+s);for(var u=0;u<s;u++)n[i]=arguments[u],i++;return o(n,i),i}})}();const Dt={deg:1,grad:.9,rad:180/Math.PI,turn:360};function $t(e){if(!e)return;e=e.trim();const r=/^-?[\d.]+$/,t=/%|deg|g?rad|turn$/,n=/\/?\s*(none|[-\w.]+(?:%|deg|g?rad|turn)?)/g;let o=e.match(/^([a-z]+)\((.+?)\)$/i);if(o){let e=[];return o[2].replace(n,((n,o)=>{let a=o.match(t),i=o;if(a){let e=a[0],r=i.slice(0,-e.length);"%"===e?(i=new Number(r/100),i.type="<percentage>"):(i=new Number(r*Dt[e]),i.type="<angle>",i.unit=e)}else r.test(i)?(i=new Number(i),i.type="<number>"):"none"===i&&(i=new Number(NaN),i.none=!0);n.startsWith("/")&&(i=i instanceof Number?i:new Number(i),i.alpha=!0),"object"==typeof i&&i instanceof Number&&(i.raw=o),e.push(i)})),{name:o[1].toLowerCase(),rawName:o[1],rawArgs:o[2],args:e}}}function qt(e){return e[e.length-1]}function Ht(e,r,t){return isNaN(e)?r:isNaN(r)?e:e+(r-e)*t}function Wt(e,r,t){return(t-e)/(r-e)}function Ft(e,r,t){return Ht(r[0],r[1],Wt(e[0],e[1],t))}function Gt(e){return e.map((e=>e.split("|").map((e=>{let r=(e=e.trim()).match(/^(<[a-z]+>)\[(-?[.\d]+),\s*(-?[.\d]+)\]?$/);if(r){let e=new String(r[1]);return e.range=[+r[2],+r[3]],e}return e}))))}function Xt(e,r,t){return Math.max(Math.min(t,r),e)}function Yt(e,r){return Math.sign(e)===Math.sign(r)?e:-e}function Zt(e,r){return Yt(Math.abs(e)**r,e)}function Jt(e,r){return 0===r?0:e/r}function Ut(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.length;for(;t<n;){const o=t+n>>1;e[o]<r?t=o+1:n=o}return t}var Qt=Object.freeze({__proto__:null,bisectLeft:Ut,clamp:Xt,copySign:Yt,interpolate:Ht,interpolateInv:Wt,isNone:Lt,isString:xt,last:qt,mapRange:Ft,multiplyMatrices:Nt,parseCoordGrammar:Gt,parseFunction:$t,serializeNumber:It,skipNone:zt,spow:Zt,toPrecision:Tt,type:At,zdiv:Jt});const Kt=new class{add(e,r,t){if("string"==typeof arguments[0])(Array.isArray(e)?e:[e]).forEach((function(e){this[e]=this[e]||[],r&&this[e][t?"unshift":"push"](r)}),this);else for(var e in arguments[0])this.add(e,arguments[0][e],arguments[1])}run(e,r){this[e]=this[e]||[],this[e].forEach((function(e){e.call(r&&r.context?r.context:r,r)}))}};var Vt,en,rn,tn,nn,on,an,sn,un,cn,ln,fn,hn,pn,dn,mn,gn,bn,vn,yn,wn,Mn,Cn,Sn,En,_n,jn,Rn,On,Pn,Bn,kn,Nn,xn,An={gamut_mapping:"css",precision:5,deltaE:"76",verbose:"test"!==(null===globalThis||void 0===globalThis||null===(Vt=globalThis.process)||void 0===Vt||null===(Vt=Vt.env)||void 0===Vt||null===(Vt=Vt.NODE_ENV)||void 0===Vt?void 0:Vt.toLowerCase()),warn:function(e){var r,t;this.verbose&&(null===globalThis||void 0===globalThis||null===(r=globalThis.console)||void 0===r||null===(t=r.warn)||void 0===t||t.call(r,e))}},In={};function Ln(){if(an)return on;an=1;var e=pe();return on=function(r){return e(r)||null===r}}function zn(){if(un)return sn;un=1;var e=Ln(),r=String,t=TypeError;return sn=function(n){if(e(n))return n;throw new t("Can't set "+r(n)+" as a prototype")}}function Tn(){if(ln)return cn;ln=1;var e=function(){if(nn)return tn;nn=1;var e=se(),r=Me();return tn=function(t,n,o){try{return e(r(Object.getOwnPropertyDescriptor(t,n)[o]))}catch(e){}}}(),r=mr(),t=zn();return cn=Object.setPrototypeOf||("__proto__"in{}?function(){var n,o=!1,a={};try{(n=e(Object.prototype,"__proto__","set"))(a,[]),o=a instanceof Array}catch(e){}return function(e,a){return r(e),t(a),o?n(e,a):e.__proto__=a,e}}():void 0)}function Dn(){if(hn)return fn;hn=1;var e=gr().f;return fn=function(r,t,n){n in r||e(r,n,{configurable:!0,get:function(){return t[n]},set:function(e){t[n]=e}})}}function $n(){if(dn)return pn;dn=1;var e=he(),r=pe(),t=Tn();return pn=function(n,o,a){var i,s;return t&&e(i=o.constructor)&&i!==a&&r(s=i.prototype)&&s!==a.prototype&&t(n,s),n}}function qn(){if(vn)return bn;vn=1;var e=function(){if(gn)return mn;gn=1;var e={};return e[tr()("toStringTag")]="z",mn="[object z]"===String(e)}(),r=he(),t=ue(),n=tr()("toStringTag"),o=Object,a="Arguments"===t(function(){return arguments}());return bn=e?t:function(e){var i,s,u;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(s=function(e,r){try{return e[r]}catch(e){}}(i=o(e),n))?s:a?t(i):"Object"===(u=t(i))&&r(i.callee)?"Arguments":u}}function Hn(){if(wn)return yn;wn=1;var e=qn(),r=String;return yn=function(t){if("Symbol"===e(t))throw new TypeError("Cannot convert a Symbol value to a string");return r(t)}}function Wn(){if(Cn)return Mn;Cn=1;var e=Hn();return Mn=function(r,t){return void 0===r?arguments.length<2?"":t:e(r)},Mn}function Fn(){if(En)return Sn;En=1;var e=pe(),r=br();return Sn=function(t,n){e(n)&&"cause"in n&&r(t,"cause",n.cause)}}function Gn(){if(Bn)return Pn;Bn=1;var e=br(),r=function(){if(jn)return _n;jn=1;var e=se(),r=Error,t=e("".replace),n=String(new r("zxcasd").stack),o=/\n\s*at [^:]*:[^\n]*/,a=o.test(n);return _n=function(e,n){if(a&&"string"==typeof e&&!r.prepareStackTrace)for(;n--;)e=t(e,o,"");return e}}(),t=function(){if(On)return Rn;On=1;var e=d(),r=ie();return Rn=!e((function(){var e=new Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",r(1,7)),7!==e.stack)}))}(),n=Error.captureStackTrace;return Pn=function(o,a,i,s){t&&(n?n(o,a):e(o,"stack",r(i,s)))}}function Xn(){if(Nn)return kn;Nn=1;var e=de(),r=er(),t=br(),n=me(),o=Tn(),a=Rt(),i=Dn(),s=$n(),u=Wn(),c=Fn(),l=Gn(),f=m(),h=Je();return kn=function(p,d,m,g){var b="stackTraceLimit",v=g?2:1,y=p.split("."),w=y[y.length-1],M=e.apply(null,y);if(M){var C=M.prototype;if(!h&&r(C,"cause")&&delete C.cause,!m)return M;var S=e("Error"),E=d((function(e,r){var o=u(g?r:e,void 0),a=g?new M(e):new M;return void 0!==o&&t(a,"message",o),l(a,E,a.stack,2),this&&n(C,this)&&s(a,this,E),arguments.length>v&&c(a,arguments[v]),a}));if(E.prototype=C,"Error"!==w?o?o(E,S):a(E,S,{name:!0}):f&&b in M&&(i(E,M,b),i(E,M,"prepareStackTrace")),a(E,M),!h)try{C.name!==w&&t(C,"name",w),C.constructor=E}catch(e){}return E}},kn}!function(){if(xn)return In;xn=1;var e=Ot(),r=o(),t=function(){if(rn)return en;rn=1;var e=g(),r=Function.prototype,t=r.apply,n=r.call;return en="object"==typeof Reflect&&Reflect.apply||(e?n.bind(t):function(){return n.apply(t,arguments)}),en}(),n=Xn(),a="WebAssembly",i=r[a],s=7!==new Error("e",{cause:7}).cause,u=function(r,t){var o={};o[r]=n(r,t,s),e({global:!0,constructor:!0,arity:1,forced:s},o)},c=function(r,t){if(i&&i[r]){var o={};o[r]=n(a+"."+r,t,s),e({target:a,stat:!0,constructor:!0,arity:1,forced:s},o)}};u("Error",(function(e){return function(r){return t(e,this,arguments)}})),u("EvalError",(function(e){return function(r){return t(e,this,arguments)}})),u("RangeError",(function(e){return function(r){return t(e,this,arguments)}})),u("ReferenceError",(function(e){return function(r){return t(e,this,arguments)}})),u("SyntaxError",(function(e){return function(r){return t(e,this,arguments)}})),u("TypeError",(function(e){return function(r){return t(e,this,arguments)}})),u("URIError",(function(e){return function(r){return t(e,this,arguments)}})),c("CompileError",(function(e){return function(r){return t(e,this,arguments)}})),c("LinkError",(function(e){return function(r){return t(e,this,arguments)}})),c("RuntimeError",(function(e){return function(r){return t(e,this,arguments)}}))}();const Yn={D50:[.3457/.3585,1,.2958/.3585],D65:[.3127/.329,1,.3583/.329]};function Zn(e){return Array.isArray(e)?e:Yn[e]}function Jn(e,r,t){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(e=Zn(e),r=Zn(r),!e||!r)throw new TypeError(`Missing white point to convert ${e?"":"from"}${e||r?"":"/"}${r?"":"to"}`);if(e===r)return t;let o={W1:e,W2:r,XYZ:t,options:n};if(Kt.run("chromatic-adaptation-start",o),o.M||(o.W1===Yn.D65&&o.W2===Yn.D50?o.M=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]]:o.W1===Yn.D50&&o.W2===Yn.D65&&(o.M=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]])),Kt.run("chromatic-adaptation-end",o),o.M)return Nt(o.M,o.XYZ);throw new TypeError("Only Bradford CAT with white points D50 and D65 supported for now.")}const Un=new Set(["<number>","<percentage>","<angle>"]);function Qn(e,r,t,n){let o=Object.entries(e.coords).map(((e,o)=>{let a,[i,s]=e,u=r.coordGrammar[o],c=n[o],l=null==c?void 0:c.type;if(a=c.none?u.find((e=>Un.has(e))):u.find((e=>e==l)),!a){let e=s.name||i;throw new TypeError(`${null!=l?l:c.raw} not allowed for ${e} in ${t}()`)}let f=a.range;"<percentage>"===l&&(f||(f=[0,1]));let h=s.range||s.refRange;return f&&h&&(n[o]=Ft(f,h,n[o])),a}));return o}function Kn(e){var r;let{meta:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n={str:null===(r=String(e))||void 0===r?void 0:r.trim()};if(Kt.run("parse-start",n),n.color)return n.color;if(n.parsed=$t(n.str),n.parsed){let e=n.parsed.name;if("color"===e){let e=n.parsed.args.shift(),r=e.startsWith("--")?e.substring(2):`--${e}`,i=[e,r],s=n.parsed.rawArgs.indexOf("/")>0?n.parsed.args.pop():1;for(let r of ro.all){let a=r.getFormat("color");var o;if(a)if(i.includes(a.id)||null!==(o=a.ids)&&void 0!==o&&o.filter((e=>i.includes(e))).length){const o=Object.keys(r.coords).map(((e,r)=>n.parsed.args[r]||0));let i;return a.coordGrammar&&(i=Qn(r,a,"color",o)),t&&Object.assign(t,{formatId:"color",types:i}),a.id.startsWith("--")&&!e.startsWith("--")&&An.warn(`${r.name} is a non-standard space and not currently supported in the CSS spec. Use prefixed color(${a.id}) instead of color(${e}).`),e.startsWith("--")&&!a.id.startsWith("--")&&An.warn(`${r.name} is a standard space and supported in the CSS spec. Use color(${a.id}) instead of prefixed color(${e}).`),{spaceId:r.id,coords:o,alpha:s}}}let u="",c=e in ro.registry?e:r;if(c in ro.registry){var a;let e=null===(a=ro.registry[c].formats)||void 0===a||null===(a=a.color)||void 0===a?void 0:a.id;e&&(u=`Did you mean color(${e})?`)}throw new TypeError(`Cannot parse color(${e}). `+(u||"Missing a plugin?"))}for(let r of ro.all){let o=r.getFormat(e);if(o&&"function"===o.type){let a=1;(o.lastAlpha||qt(n.parsed.args).alpha)&&(a=n.parsed.args.pop());let i,s=n.parsed.args;return o.coordGrammar&&(i=Qn(r,o,e,s)),t&&Object.assign(t,{formatId:o.name,types:i}),{spaceId:r.id,coords:s,alpha:a}}}}else for(let e of ro.all)for(let r in e.formats){let o=e.formats[r];if("custom"!==o.type)continue;if(o.test&&!o.test(n.str))continue;let a=o.parse(n.str);var i;if(a)return null!==(i=a.alpha)&&void 0!==i||(a.alpha=1),t&&(t.formatId=r),a}throw new TypeError(`Could not parse ${e} as a color. Missing a plugin?`)}function Vn(e){if(Array.isArray(e))return e.map(Vn);if(!e)throw new TypeError("Empty color reference");xt(e)&&(e=Kn(e));let r=e.space||e.spaceId;return r instanceof ro||(e.space=ro.get(r)),void 0===e.alpha&&(e.alpha=1),e}const eo=75e-6;class ro{constructor(e){var r,t,n,o,a;this.id=e.id,this.name=e.name,this.base=e.base?ro.get(e.base):null,this.aliases=e.aliases,this.base&&(this.fromBase=e.fromBase,this.toBase=e.toBase);let i=null!==(r=e.coords)&&void 0!==r?r:this.base.coords;for(let e in i)"name"in i[e]||(i[e].name=e);this.coords=i;let s=null!==(t=null!==(n=e.white)&&void 0!==n?n:this.base.white)&&void 0!==t?t:"D65";this.white=Zn(s),this.formats=null!==(o=e.formats)&&void 0!==o?o:{};for(let e in this.formats){let r=this.formats[e];r.type||(r.type="function"),r.name||(r.name=e)}var u;null!==(a=this.formats.color)&&void 0!==a&&a.id||(this.formats.color={...null!==(u=this.formats.color)&&void 0!==u?u:{},id:e.cssId||this.id});e.gamutSpace?this.gamutSpace="self"===e.gamutSpace?this:ro.get(e.gamutSpace):this.isPolar?this.gamutSpace=this.base:this.gamutSpace=this,this.gamutSpace.isUnbounded&&(this.inGamut=(e,r)=>!0),this.referred=e.referred,Object.defineProperty(this,"path",{value:to(this).reverse(),writable:!1,enumerable:!0,configurable:!0}),Kt.run("colorspace-init-end",this)}inGamut(e){let{epsilon:r=eo}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.equals(this.gamutSpace))return e=this.to(this.gamutSpace,e),this.gamutSpace.inGamut(e,{epsilon:r});let t=Object.values(this.coords);return e.every(((e,n)=>{let o=t[n];if("angle"!==o.type&&o.range){if(Number.isNaN(e))return!0;let[t,n]=o.range;return(void 0===t||e>=t-r)&&(void 0===n||e<=n+r)}return!0}))}get isUnbounded(){return Object.values(this.coords).every((e=>!("range"in e)))}get cssId(){var e;return(null===(e=this.formats)||void 0===e||null===(e=e.color)||void 0===e?void 0:e.id)||this.id}get isPolar(){for(let e in this.coords)if("angle"===this.coords[e].type)return!0;return!1}getFormat(e){if("object"==typeof e)return e=no(e,this);let r;return r="default"===e?Object.values(this.formats)[0]:this.formats[e],r?(r=no(r,this),r):null}equals(e){return!!e&&(this===e||this.id===e||this.id===e.id)}to(e,r){if(1===arguments.length){const t=Vn(e);[e,r]=[t.space,t.coords]}if(e=ro.get(e),this.equals(e))return r;r=r.map((e=>Number.isNaN(e)?0:e));let t,n,o=this.path,a=e.path;for(let e=0;e<o.length&&o[e].equals(a[e]);e++)t=o[e],n=e;if(!t)throw new Error(`Cannot convert between color spaces ${this} and ${e}: no connection space was found`);for(let e=o.length-1;e>n;e--)r=o[e].toBase(r);for(let e=n+1;e<a.length;e++)r=a[e].fromBase(r);return r}from(e,r){if(1===arguments.length){const t=Vn(e);[e,r]=[t.space,t.coords]}return(e=ro.get(e)).to(this,r)}toString(){return`${this.name} (${this.id})`}getMinCoords(){let e=[];for(let t in this.coords){var r;let n=this.coords[t],o=n.range||n.refRange;e.push(null!==(r=null==o?void 0:o.min)&&void 0!==r?r:0)}return e}static registry={};static get all(){return[...new Set(Object.values(ro.registry))]}static register(e,r){if(1===arguments.length&&(e=(r=arguments[0]).id),r=this.get(r),this.registry[e]&&this.registry[e]!==r)throw new Error(`Duplicate color space registration: '${e}'`);if(this.registry[e]=r,1===arguments.length&&r.aliases)for(let e of r.aliases)this.register(e,r);return r}static get(e){if(!e||e instanceof ro)return e;if("string"===At(e)){let r=ro.registry[e.toLowerCase()];if(!r)throw new TypeError(`No color space found with id = "${e}"`);return r}for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];if(t.length)return ro.get(...t);throw new TypeError(`${e} is not a valid color space`)}static resolveCoord(e,r){let t,n,o=At(e);if("string"===o?e.includes(".")?[t,n]=e.split("."):[t,n]=[,e]:Array.isArray(e)?[t,n]=e:(t=e.space,n=e.coordId),t=ro.get(t),t||(t=r),!t)throw new TypeError(`Cannot resolve coordinate reference ${e}: No color space specified and relative references are not allowed here`);if(o=At(n),"number"===o||"string"===o&&n>=0){let e=Object.entries(t.coords)[n];if(e)return{space:t,id:e[0],index:n,...e[1]}}t=ro.get(t);let a=n.toLowerCase(),i=0;for(let e in t.coords){var s;let r=t.coords[e];if(e.toLowerCase()===a||(null===(s=r.name)||void 0===s?void 0:s.toLowerCase())===a)return{space:t,id:e,index:i,...r};i++}throw new TypeError(`No "${n}" coordinate found in ${t.name}. Its coordinates are: ${Object.keys(t.coords).join(", ")}`)}static DEFAULT_FORMAT={type:"functions",name:"color"}}function to(e){let r=[e];for(let t=e;t=t.base;)r.push(t);return r}function no(e){let{coords:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e.coords&&!e.coordGrammar){e.type||(e.type="function"),e.name||(e.name="color"),e.coordGrammar=Gt(e.coords);let t=Object.entries(r).map(((r,t)=>{let[n,o]=r,a=e.coordGrammar[t][0],i=o.range||o.refRange,s=a.range,u="";return"<percentage>"==a?(s=[0,100],u="%"):"<angle>"==a&&(u="deg"),{fromRange:i,toRange:s,suffix:u}}));e.serializeCoords=(e,r)=>e.map(((e,n)=>{let{fromRange:o,toRange:a,suffix:i}=t[n];return o&&a&&(e=Ft(o,a,e)),e=It(e,{precision:r,unit:i})}))}return e}var oo=new ro({id:"xyz-d65",name:"XYZ D65",coords:{x:{name:"X"},y:{name:"Y"},z:{name:"Z"}},white:"D65",formats:{color:{ids:["xyz-d65","xyz"]}},aliases:["xyz"]});class ao extends ro{constructor(e){var r,t,n;(e.coords||(e.coords={r:{range:[0,1],name:"Red"},g:{range:[0,1],name:"Green"},b:{range:[0,1],name:"Blue"}}),e.base||(e.base=oo),e.toXYZ_M&&e.fromXYZ_M)&&(null!==(t=e.toBase)&&void 0!==t||(e.toBase=r=>{let t=Nt(e.toXYZ_M,r);return this.white!==this.base.white&&(t=Jn(this.white,this.base.white,t)),t}),null!==(n=e.fromBase)&&void 0!==n||(e.fromBase=r=>(r=Jn(this.base.white,this.white,r),Nt(e.fromXYZ_M,r))));null!==(r=e.referred)&&void 0!==r||(e.referred="display"),super(e)}}function io(e,r){return e=Vn(e),!r||e.space.equals(r)?e.coords.slice():(r=ro.get(r)).from(e)}function so(e,r){e=Vn(e);let{space:t,index:n}=ro.resolveCoord(r,e.space);return io(e,t)[n]}function uo(e,r,t){return e=Vn(e),r=ro.get(r),e.coords=r.to(e.space,t),e}function co(e,r,t){if(e=Vn(e),2===arguments.length&&"object"===At(arguments[1])){let r=arguments[1];for(let t in r)co(e,t,r[t])}else{"function"==typeof t&&(t=t(so(e,r)));let{space:n,index:o}=ro.resolveCoord(r,e.space),a=io(e,n);a[o]=t,uo(e,n,a)}return e}uo.returns="color",co.returns="color";var lo=new ro({id:"xyz-d50",name:"XYZ D50",white:"D50",base:oo,fromBase:e=>Jn(oo.white,"D50",e),toBase:e=>Jn("D50",oo.white,e)});const fo=24/116,ho=24389/27;let po=Yn.D50;var mo=new ro({id:"lab",name:"Lab",coords:{l:{refRange:[0,100],name:"Lightness"},a:{refRange:[-125,125]},b:{refRange:[-125,125]}},white:po,base:lo,fromBase(e){let r=e.map(((e,r)=>e/po[r])).map((e=>e>.008856451679035631?Math.cbrt(e):(ho*e+16)/116));return[116*r[1]-16,500*(r[0]-r[1]),200*(r[1]-r[2])]},toBase(e){let r=[];return r[1]=(e[0]+16)/116,r[0]=e[1]/500+r[1],r[2]=r[1]-e[2]/200,[r[0]>fo?Math.pow(r[0],3):(116*r[0]-16)/ho,e[0]>8?Math.pow((e[0]+16)/116,3):e[0]/ho,r[2]>fo?Math.pow(r[2],3):(116*r[2]-16)/ho].map(((e,r)=>e*po[r]))},formats:{lab:{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});function go(e){return(e%360+360)%360}var bo=new ro({id:"lch",name:"LCH",coords:{l:{refRange:[0,100],name:"Lightness"},c:{refRange:[0,150],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:mo,fromBase(e){let r,[t,n,o]=e;return r=Math.abs(n)<.02&&Math.abs(o)<.02?NaN:180*Math.atan2(o,n)/Math.PI,[t,Math.sqrt(n**2+o**2),go(r)]},toBase(e){let[r,t,n]=e;return t<0&&(t=0),isNaN(n)&&(n=0),[r,t*Math.cos(n*Math.PI/180),t*Math.sin(n*Math.PI/180)]},formats:{lch:{coords:["<number> | <percentage>","<number> | <percentage>","<number> | <angle>"]}}});const vo=25**7,yo=Math.PI,wo=180/yo,Mo=yo/180;function Co(e){const r=e*e;return r*r*r*e}function So(e,r){let{kL:t=1,kC:n=1,kH:o=1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};[e,r]=Vn([e,r]);let[a,i,s]=mo.from(e),u=bo.from(mo,[a,i,s])[1],[c,l,f]=mo.from(r),h=bo.from(mo,[c,l,f])[1];u<0&&(u=0),h<0&&(h=0);let p=Co((u+h)/2),d=.5*(1-Math.sqrt(p/(p+vo))),m=(1+d)*i,g=(1+d)*l,b=Math.sqrt(m**2+s**2),v=Math.sqrt(g**2+f**2),y=0===m&&0===s?0:Math.atan2(s,m),w=0===g&&0===f?0:Math.atan2(f,g);y<0&&(y+=2*yo),w<0&&(w+=2*yo),y*=wo,w*=wo;let M,C=c-a,S=v-b,E=w-y,_=y+w,j=Math.abs(E);b*v==0?M=0:j<=180?M=E:E>180?M=E-360:E<-180?M=E+360:An.warn("the unthinkable has happened");let R,O=2*Math.sqrt(v*b)*Math.sin(M*Mo/2),P=(a+c)/2,B=(b+v)/2,k=Co(B);R=b*v==0?_:j<=180?_/2:_<360?(_+360)/2:(_-360)/2;let N=(P-50)**2,x=1+.015*N/Math.sqrt(20+N),A=1+.045*B,I=1;I-=.17*Math.cos((R-30)*Mo),I+=.24*Math.cos(2*R*Mo),I+=.32*Math.cos((3*R+6)*Mo),I-=.2*Math.cos((4*R-63)*Mo);let L=1+.015*B*I,z=30*Math.exp(-1*((R-275)/25)**2),T=2*Math.sqrt(k/(k+vo)),D=(C/(t*x))**2;return D+=(S/(n*A))**2,D+=(O/(o*L))**2,D+=-1*Math.sin(2*z*Mo)*T*(S/(n*A))*(O/(o*L)),Math.sqrt(D)}const Eo=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],_o=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],jo=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],Ro=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]];var Oo=new ro({id:"oklab",name:"Oklab",coords:{l:{refRange:[0,1],name:"Lightness"},a:{refRange:[-.4,.4]},b:{refRange:[-.4,.4]}},white:"D65",base:oo,fromBase(e){let r=Nt(Eo,e).map((e=>Math.cbrt(e)));return Nt(jo,r)},toBase(e){let r=Nt(Ro,e).map((e=>e**3));return Nt(_o,r)},formats:{oklab:{coords:["<percentage> | <number>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});function Po(e,r){[e,r]=Vn([e,r]);let[t,n,o]=Oo.from(e),[a,i,s]=Oo.from(r),u=t-a,c=n-i,l=o-s;return Math.sqrt(u**2+c**2+l**2)}const Bo=75e-6;function ko(e,r){let{epsilon:t=Bo}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e=Vn(e),r||(r=e.space),r=ro.get(r);let n=e.coords;return r!==e.space&&(n=r.from(e)),r.inGamut(n,{epsilon:t})}function No(e){return{space:e.space,coords:e.coords.slice(),alpha:e.alpha}}function xo(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"lab";t=ro.get(t);let n=t.from(e),o=t.from(r);return Math.sqrt(n.reduce(((e,r,t)=>{let n=o[t];return isNaN(r)||isNaN(n)?e:e+(n-r)**2}),0))}const Ao=Math.PI/180;var Io=new ro({id:"xyz-abs-d65",cssId:"--xyz-abs-d65",name:"Absolute XYZ D65",coords:{x:{refRange:[0,9504.7],name:"Xa"},y:{refRange:[0,1e4],name:"Ya"},z:{refRange:[0,10888.3],name:"Za"}},base:oo,fromBase:e=>e.map((e=>Math.max(203*e,0))),toBase:e=>e.map((e=>Math.max(e/203,0)))});const Lo=1.15,zo=.66,To=2610/16384,Do=.8359375,$o=2413/128,qo=18.6875,Ho=32/(1.7*2523),Wo=-.56,Fo=16295499532821565e-27,Go=[[.41478972,.579999,.014648],[-.20151,1.120649,.0531008],[-.0166008,.2648,.6684799]],Xo=[[1.9242264357876067,-1.0047923125953657,.037651404030618],[.35031676209499907,.7264811939316552,-.06538442294808501],[-.09098281098284752,-.3127282905230739,1.5227665613052603]],Yo=[[.5,.5,0],[3.524,-4.066708,.542708],[.199076,1.096799,-1.295875]],Zo=[[1,.1386050432715393,.05804731615611886],[.9999999999999999,-.1386050432715393,-.05804731615611886],[.9999999999999998,-.09601924202631895,-.8118918960560388]];var Jo=new ro({id:"jzazbz",name:"Jzazbz",coords:{jz:{refRange:[0,1],name:"Jz"},az:{refRange:[-.5,.5]},bz:{refRange:[-.5,.5]}},base:Io,fromBase(e){let[r,t,n]=e,o=Nt(Go,[Lo*r-(Lo-1)*n,zo*t-(zo-1)*r,n]).map((function(e){return((Do+$o*(e/1e4)**To)/(1+qo*(e/1e4)**To))**134.03437499999998})),[a,i,s]=Nt(Yo,o);return[(1+Wo)*a/(1+Wo*a)-Fo,i,s]},toBase(e){let[r,t,n]=e,o=Nt(Zo,[(r+Fo)/(1+Wo-Wo*(r+Fo)),t,n]).map((function(e){return 1e4*((Do-e**Ho)/(qo*e**Ho-$o))**6.277394636015326})),[a,i,s]=Nt(Xo,o),u=(a+(Lo-1)*s)/Lo;return[u,(i+(zo-1)*u)/zo,s]},formats:{color:{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}}),Uo=new ro({id:"jzczhz",name:"JzCzHz",coords:{jz:{refRange:[0,1],name:"Jz"},cz:{refRange:[0,1],name:"Chroma"},hz:{refRange:[0,360],type:"angle",name:"Hue"}},base:Jo,fromBase(e){let r,[t,n,o]=e;const a=2e-4;return r=Math.abs(n)<a&&Math.abs(o)<a?NaN:180*Math.atan2(o,n)/Math.PI,[t,Math.sqrt(n**2+o**2),go(r)]},toBase:e=>[e[0],e[1]*Math.cos(e[2]*Math.PI/180),e[1]*Math.sin(e[2]*Math.PI/180)]});const Qo=.8359375,Ko=2413/128,Vo=18.6875,ea=2610/16384,ra=2523/32,ta=16384/2610,na=32/2523,oa=[[.3592832590121217,.6976051147779502,-.035891593232029],[-.1920808463704993,1.100476797037432,.0753748658519118],[.0070797844607479,.0748396662186362,.8433265453898765]],aa=[[.5,.5,0],[6610/4096,-13613/4096,7003/4096],[17933/4096,-17390/4096,-543/4096]],ia=[[.9999999999999998,.0086090370379328,.111029625003026],[.9999999999999998,-.0086090370379328,-.1110296250030259],[.9999999999999998,.5600313357106791,-.3206271749873188]],sa=[[2.0701522183894223,-1.3263473389671563,.2066510476294053],[.3647385209748072,.6805660249472273,-.0453045459220347],[-.0497472075358123,-.0492609666966131,1.1880659249923042]];var ua=new ro({id:"ictcp",name:"ICTCP",coords:{i:{refRange:[0,1],name:"I"},ct:{refRange:[-.5,.5],name:"CT"},cp:{refRange:[-.5,.5],name:"CP"}},base:Io,fromBase:e=>function(e){let r=e.map((function(e){return((Qo+Ko*(e/1e4)**ea)/(1+Vo*(e/1e4)**ea))**ra}));return Nt(aa,r)}(Nt(oa,e)),toBase(e){let r=function(e){let r=Nt(ia,e),t=r.map((function(e){return 1e4*(Math.max(e**na-Qo,0)/(Ko-Vo*e**na))**ta}));return t}(e);return Nt(sa,r)}});const ca=Yn.D65,la=.42,fa=1/la,ha=2*Math.PI,pa=[[.401288,.650173,-.051461],[-.250268,1.204414,.045854],[-.002079,.048952,.953127]],da=[[1.8620678550872327,-1.0112546305316843,.14918677544445175],[.38752654323613717,.6214474419314753,-.008973985167612518],[-.015841498849333856,-.03412293802851557,1.0499644368778496]],ma=[[460,451,288],[460,-891,-261],[460,-220,-6300]],ga={dark:[.8,.525,.8],dim:[.9,.59,.9],average:[1,.69,1]},ba={h:[20.14,90,164.25,237.53,380.14],e:[.8,.7,1,1.2,.8],H:[0,100,200,300,400]},va=180/Math.PI,ya=Math.PI/180;function wa(e,r){const t=e.map((e=>{const t=Zt(r*Math.abs(e)*.01,la);return 400*Yt(t,e)/(t+27.13)}));return t}function Ma(e,r,t,n,o){const a={};a.discounting=o,a.refWhite=e,a.surround=n;const i=e.map((e=>100*e));a.la=r,a.yb=t;const s=i[1],u=Nt(pa,i),c=(n=ga[a.surround])[0];a.c=n[1],a.nc=n[2];const l=(1/(5*a.la+1))**4;a.fl=l*a.la+.1*(1-l)*(1-l)*Math.cbrt(5*a.la),a.flRoot=a.fl**.25,a.n=a.yb/s,a.z=1.48+Math.sqrt(a.n),a.nbb=.725*a.n**-.2,a.ncb=a.nbb;const f=o?1:Math.max(Math.min(c*(1-1/3.6*Math.exp((-a.la-42)/92)),1),0);a.dRgb=u.map((e=>Ht(1,s/e,f))),a.dRgbInv=a.dRgb.map((e=>1/e));const h=u.map(((e,r)=>e*a.dRgb[r])),p=wa(h,a.fl);return a.aW=a.nbb*(2*p[0]+p[1]+.05*p[2]),a}const Ca=Ma(ca,64/Math.PI*.2,20,"average",!1);function Sa(e,r){if(!(void 0!==e.J^void 0!==e.Q))throw new Error("Conversion requires one and only one: 'J' or 'Q'");if(!(void 0!==e.C^void 0!==e.M^void 0!==e.s))throw new Error("Conversion requires one and only one: 'C', 'M' or 's'");if(!(void 0!==e.h^void 0!==e.H))throw new Error("Conversion requires one and only one: 'h' or 'H'");if(0===e.J||0===e.Q)return[0,0,0];let t=0;t=void 0!==e.h?go(e.h)*ya:function(e){let r=(e%400+400)%400;const t=Math.floor(.01*r);r%=100;const[n,o]=ba.h.slice(t,t+2),[a,i]=ba.e.slice(t,t+2);return go((r*(i*n-a*o)-100*n*i)/(r*(i-a)-100*i))}(e.H)*ya;const n=Math.cos(t),o=Math.sin(t);let a=0;void 0!==e.J?a=.1*Zt(e.J,.5):void 0!==e.Q&&(a=.25*r.c*e.Q/((r.aW+4)*r.flRoot));let i=0;void 0!==e.C?i=e.C/a:void 0!==e.M?i=e.M/r.flRoot/a:void 0!==e.s&&(i=4e-4*e.s**2*(r.aW+4)/r.c);const s=Zt(i*Math.pow(1.64-Math.pow(.29,r.n),-.73),10/9),u=.25*(Math.cos(t+2)+3.8),c=r.aW*Zt(a,2/r.c/r.z),l=5e4/13*r.nc*r.ncb*u,f=c/r.nbb,h=23*(f+.305)*Jt(s,23*l+s*(11*n+108*o)),p=function(e,r){const t=100/r*27.13**fa;return e.map((e=>{const r=Math.abs(e);return Yt(t*Zt(r/(400-r),fa),e)}))}(Nt(ma,[f,h*n,h*o]).map((e=>1*e/1403)),r.fl);return Nt(da,p.map(((e,t)=>e*r.dRgbInv[t]))).map((e=>e/100))}function Ea(e,r){const t=e.map((e=>100*e)),n=wa(Nt(pa,t).map(((e,t)=>e*r.dRgb[t])),r.fl),o=n[0]+(-12*n[1]+n[2])/11,a=(n[0]+n[1]-2*n[2])/9,i=(Math.atan2(a,o)%ha+ha)%ha,s=.25*(Math.cos(i+2)+3.8),u=Zt(5e4/13*r.nc*r.ncb*Jt(s*Math.sqrt(o**2+a**2),n[0]+n[1]+1.05*n[2]+.305),.9)*Math.pow(1.64-Math.pow(.29,r.n),.73),c=Zt(r.nbb*(2*n[0]+n[1]+.05*n[2])/r.aW,.5*r.c*r.z),l=100*Zt(c,2),f=4/r.c*c*(r.aW+4)*r.flRoot,h=u*c,p=h*r.flRoot,d=go(i*va),m=function(e){let r=go(e);r<=ba.h[0]&&(r+=360);const t=Ut(ba.h,r)-1,[n,o]=ba.h.slice(t,t+2),[a,i]=ba.e.slice(t,t+2),s=(r-n)/a;return ba.H[t]+100*s/(s+(o-r)/i)}(d);return{J:l,C:h,h:d,s:50*Zt(r.c*u/(r.aW+4),.5),Q:f,M:p,H:m}}var _a=new ro({id:"cam16-jmh",cssId:"--cam16-jmh",name:"CAM16-JMh",coords:{j:{refRange:[0,100],name:"J"},m:{refRange:[0,105],name:"Colorfulness"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:oo,fromBase(e){const r=Ea(e,Ca);return[r.J,r.M,r.h]},toBase:e=>Sa({J:e[0],M:e[1],h:e[2]},Ca)});const ja=Yn.D65,Ra=216/24389,Oa=24389/27;function Pa(e){return e>8?Math.pow((e+16)/116,3):e/Oa}function Ba(e,r){const t=116*((n=e[1])>Ra?Math.cbrt(n):(Oa*n+16)/116)-16;var n;if(0===t)return[0,0,0];const o=Ea(e,ka);return[go(o.h),o.C,t]}const ka=Ma(ja,200/Math.PI*Pa(50),100*Pa(50),"average",!1);var Na=new ro({id:"hct",name:"HCT",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},c:{refRange:[0,145],name:"Colorfulness"},t:{refRange:[0,100],name:"Tone"}},base:oo,fromBase:e=>Ba(e),toBase:e=>function(e,r){let[t,n,o]=e,a=[],i=0;if(0===o)return[0,0,0];let s=Pa(o);i=o>0?.00379058511492914*o**2+.608983189401032*o+.9155088574762233:9514440756550361e-21*o**2+.08693057439788597*o-21.928975842194614;let u=0,c=1/0;for(;u<=15;){a=Sa({J:i,C:n,h:t},r);const e=Math.abs(a[1]-s);if(e<c){if(e<=2e-12)return a;c=e}i-=(a[1]-s)*i/(2*a[1]),u+=1}return Sa({J:i,C:n,h:t},r)}(e,ka),formats:{color:{id:"--hct",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const xa=Math.PI/180,Aa=[1,.007,.0228];function Ia(e){e[1]<0&&(e=Na.fromBase(Na.toBase(e)));const r=Math.log(Math.max(1+Aa[2]*e[1]*ka.flRoot,1))/Aa[2],t=e[0]*xa,n=r*Math.cos(t),o=r*Math.sin(t);return[e[2],n,o]}var La={deltaE76:function(e,r){return xo(e,r,"lab")},deltaECMC:function(e,r){let{l:t=2,c:n=1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};[e,r]=Vn([e,r]);let[o,a,i]=mo.from(e),[,s,u]=bo.from(mo,[o,a,i]),[c,l,f]=mo.from(r),h=bo.from(mo,[c,l,f])[1];s<0&&(s=0),h<0&&(h=0);let p=o-c,d=s-h,m=(a-l)**2+(i-f)**2-d**2,g=.511;o>=16&&(g=.040975*o/(1+.01765*o));let b,v=.0638*s/(1+.0131*s)+.638;Number.isNaN(u)&&(u=0),b=u>=164&&u<=345?.56+Math.abs(.2*Math.cos((u+168)*Ao)):.36+Math.abs(.4*Math.cos((u+35)*Ao));let y=Math.pow(s,4),w=Math.sqrt(y/(y+1900)),M=(p/(t*g))**2;return M+=(d/(n*v))**2,M+=m/(v*(w*b+1-w))**2,Math.sqrt(M)},deltaE2000:So,deltaEJz:function(e,r){[e,r]=Vn([e,r]);let[t,n,o]=Uo.from(e),[a,i,s]=Uo.from(r),u=t-a,c=n-i;Number.isNaN(o)&&Number.isNaN(s)?(o=0,s=0):Number.isNaN(o)?o=s:Number.isNaN(s)&&(s=o);let l=o-s,f=2*Math.sqrt(n*i)*Math.sin(l/2*(Math.PI/180));return Math.sqrt(u**2+c**2+f**2)},deltaEITP:function(e,r){[e,r]=Vn([e,r]);let[t,n,o]=ua.from(e),[a,i,s]=ua.from(r);return 720*Math.sqrt((t-a)**2+.25*(n-i)**2+(o-s)**2)},deltaEOK:Po,deltaEHCT:function(e,r){[e,r]=Vn([e,r]);let[t,n,o]=Ia(Na.from(e)),[a,i,s]=Ia(Na.from(r));return Math.sqrt((t-a)**2+(n-i)**2+(o-s)**2)}};const za={hct:{method:"hct.c",jnd:2,deltaEMethod:"hct",blackWhiteClamp:{}},"hct-tonal":{method:"hct.c",jnd:0,deltaEMethod:"hct",blackWhiteClamp:{channel:"hct.t",min:0,max:100}}};function Ta(e){let r,{method:t=An.gamut_mapping,space:n,deltaEMethod:o="",jnd:a=2,blackWhiteClamp:i={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e=Vn(e),xt(arguments[1])?n=arguments[1]:n||(n=e.space),n=ro.get(n),ko(e,n,{epsilon:0}))return e;if("css"===t)r=function(e){let{space:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const t=.02,n=1e-4;e=Vn(e),r||(r=e.space);r=ro.get(r);const o=ro.get("oklch");if(r.isUnbounded)return $a(e,r);const a=$a(e,o);let i=a.coords[0];if(i>=1){const t=$a(Da.WHITE,r);return t.alpha=e.alpha,$a(t,r)}if(i<=0){const t=$a(Da.BLACK,r);return t.alpha=e.alpha,$a(t,r)}if(ko(a,r,{epsilon:0}))return $a(a,r);function s(e){const t=$a(e,r),n=Object.values(r.coords);return t.coords=t.coords.map(((e,r)=>{if("range"in n[r]){const[t,o]=n[r].range;return Xt(t,e,o)}return e})),t}let u=0,c=a.coords[1],l=!0,f=No(a),h=s(f),p=Po(h,f);if(p<t)return h;for(;c-u>n;){const e=(u+c)/2;if(f.coords[1]=e,l&&ko(f,r,{epsilon:0}))u=e;else if(h=s(f),p=Po(h,f),p<t){if(t-p<n)break;l=!1,u=e}else c=e}return h}(e,{space:n});else{if("clip"===t||ko(e,n))r=$a(e,n);else{Object.prototype.hasOwnProperty.call(za,t)&&({method:t,jnd:a,deltaEMethod:o,blackWhiteClamp:i}=za[t]);let s=So;if(""!==o)for(let e in La)if("deltae"+o.toLowerCase()===e.toLowerCase()){s=La[e];break}let u=Ta($a(e,n),{method:"clip",space:n});if(s(e,u)>a){if(3===Object.keys(i).length){let r=ro.resolveCoord(i.channel),t=so($a(e,r.space),r.id);if(Lt(t)&&(t=0),t>=i.max)return $a({space:"xyz-d65",coords:Yn.D65},e.space);if(t<=i.min)return $a({space:"xyz-d65",coords:[0,0,0]},e.space)}let o=ro.resolveCoord(t),u=o.space,c=o.id,l=$a(e,u);l.coords.forEach(((e,r)=>{Lt(e)&&(l.coords[r]=0)}));let f=(o.range||o.refRange)[0],h=function(e){const r=e?Math.floor(Math.log10(Math.abs(e))):0;return Math.max(parseFloat("1e"+(r-2)),1e-6)}(a),p=f,d=so(l,c);for(;d-p>h;){let e=No(l);e=Ta(e,{space:n,method:"clip"}),s(l,e)-a<h?p=so(l,c):d=so(l,c),co(l,c,(p+d)/2)}r=$a(l,n)}else r=u}if("clip"===t||!ko(r,n,{epsilon:0})){let e=Object.values(n.coords).map((e=>e.range||[]));r.coords=r.coords.map(((r,t)=>{let[n,o]=e[t];return void 0!==n&&(r=Math.max(n,r)),void 0!==o&&(r=Math.min(r,o)),r}))}}return n!==e.space&&(r=$a(r,e.space)),e.coords=r.coords,e}Ta.returns="color";const Da={WHITE:{space:Oo,coords:[1,0,0]},BLACK:{space:Oo,coords:[0,0,0]}};function $a(e,r){let{inGamut:t}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e=Vn(e);let n=(r=ro.get(r)).from(e),o={space:r,coords:n,alpha:e.alpha};return t&&(o=Ta(o,!0===t?void 0:t)),o}$a.returns="color";var qa,Ha,Wa,Fa={};function Ga(){if(Ha)return qa;Ha=1;var e=we(),r=TypeError;return qa=function(t,n){if(!delete t[n])throw new r("Cannot delete property "+e(n)+" of "+e(t))}}function Xa(e){var r,t;let n,{precision:o=An.precision,format:a="default",inGamut:i=!0,...s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},u=a;a=null!==(r=null!==(t=(e=Vn(e)).space.getFormat(a))&&void 0!==t?t:e.space.getFormat("default"))&&void 0!==r?r:ro.DEFAULT_FORMAT;let c=e.coords.slice();if(i||(i=a.toGamut),i&&!ko(e)&&(c=Ta(No(e),!0===i?void 0:i).coords),"custom"===a.type){if(s.precision=o,!a.serialize)throw new TypeError(`format ${u} can only be used to parse colors, not for serialization`);n=a.serialize(c,e.alpha,s)}else{let r=a.name||"color";a.serializeCoords?c=a.serializeCoords(c,o):null!==o&&(c=c.map((e=>It(e,{precision:o}))));let t=[...c];if("color"===r){var l;let r=a.id||(null===(l=a.ids)||void 0===l?void 0:l[0])||e.space.id;t.unshift(r)}let i=e.alpha;null!==o&&(i=It(i,{precision:o}));let s=e.alpha>=1||a.noAlpha?"":`${a.commas?",":" /"} ${i}`;n=`${r}(${t.join(a.commas?", ":" ")}${s})`}return n}!function(){if(Wa)return Fa;Wa=1;var e=Ot(),r=Ve(),t=st(),n=Bt(),o=Ga(),a=kt();e({target:"Array",proto:!0,arity:1,forced:1!==[].unshift(0)||!function(){try{Object.defineProperty([],"length",{writable:!1}).unshift()}catch(e){return e instanceof TypeError}}()},{unshift:function(e){var i=r(this),s=t(i),u=arguments.length;if(u){a(s+u);for(var c=s;c--;){var l=c+u;c in i?i[l]=i[c]:o(i,l)}for(var f=0;f<u;f++)i[f]=arguments[f]}return n(i,s+u)}})}();var Ya=new ao({id:"rec2020-linear",cssId:"--rec2020-linear",name:"Linear REC.2020",white:"D65",toXYZ_M:[[.6369580483012914,.14461690358620832,.1688809751641721],[.2627002120112671,.6779980715188708,.05930171646986196],[0,.028072693049087428,1.060985057710791]],fromXYZ_M:[[1.716651187971268,-.355670783776392,-.25336628137366],[-.666684351832489,1.616481236634939,.0157685458139111],[.017639857445311,-.042770613257809,.942103121235474]]});const Za=1.09929682680944,Ja=.018053968510807;var Ua=new ao({id:"rec2020",name:"REC.2020",base:Ya,toBase:e=>e.map((function(e){return e<4.5*Ja?e/4.5:Math.pow((e+Za-1)/Za,1/.45)})),fromBase:e=>e.map((function(e){return e>=Ja?Za*Math.pow(e,.45)-(Za-1):4.5*e}))});var Qa=new ao({id:"p3-linear",cssId:"--display-p3-linear",name:"Linear P3",white:"D65",toXYZ_M:[[.4865709486482162,.26566769316909306,.1982172852343625],[.2289745640697488,.6917385218365064,.079286914093745],[0,.04511338185890264,1.043944368900976]],fromXYZ_M:[[2.493496911941425,-.9313836179191239,-.40271078445071684],[-.8294889695615747,1.7626640603183463,.023624685841943577],[.03584583024378447,-.07617238926804182,.9568845240076872]]});const Ka=[[3.2409699419045226,-1.537383177570094,-.4986107602930034],[-.9692436362808796,1.8759675015077202,.04155505740717559],[.05563007969699366,-.20397695888897652,1.0569715142428786]];var Va=new ao({id:"srgb-linear",name:"Linear sRGB",white:"D65",toXYZ_M:[[.41239079926595934,.357584339383878,.1804807884018343],[.21263900587151027,.715168678767756,.07219231536073371],[.01933081871559182,.11919477979462598,.9505321522496607]],fromXYZ_M:Ka}),ei={aliceblue:[240/255,248/255,1],antiquewhite:[250/255,235/255,215/255],aqua:[0,1,1],aquamarine:[127/255,1,212/255],azure:[240/255,1,1],beige:[245/255,245/255,220/255],bisque:[1,228/255,196/255],black:[0,0,0],blanchedalmond:[1,235/255,205/255],blue:[0,0,1],blueviolet:[138/255,43/255,226/255],brown:[165/255,42/255,42/255],burlywood:[222/255,184/255,135/255],cadetblue:[95/255,158/255,160/255],chartreuse:[127/255,1,0],chocolate:[210/255,105/255,30/255],coral:[1,127/255,80/255],cornflowerblue:[100/255,149/255,237/255],cornsilk:[1,248/255,220/255],crimson:[220/255,20/255,60/255],cyan:[0,1,1],darkblue:[0,0,139/255],darkcyan:[0,139/255,139/255],darkgoldenrod:[184/255,134/255,11/255],darkgray:[169/255,169/255,169/255],darkgreen:[0,100/255,0],darkgrey:[169/255,169/255,169/255],darkkhaki:[189/255,183/255,107/255],darkmagenta:[139/255,0,139/255],darkolivegreen:[85/255,107/255,47/255],darkorange:[1,140/255,0],darkorchid:[.6,50/255,.8],darkred:[139/255,0,0],darksalmon:[233/255,150/255,122/255],darkseagreen:[143/255,188/255,143/255],darkslateblue:[72/255,61/255,139/255],darkslategray:[47/255,79/255,79/255],darkslategrey:[47/255,79/255,79/255],darkturquoise:[0,206/255,209/255],darkviolet:[148/255,0,211/255],deeppink:[1,20/255,147/255],deepskyblue:[0,191/255,1],dimgray:[105/255,105/255,105/255],dimgrey:[105/255,105/255,105/255],dodgerblue:[30/255,144/255,1],firebrick:[178/255,34/255,34/255],floralwhite:[1,250/255,240/255],forestgreen:[34/255,139/255,34/255],fuchsia:[1,0,1],gainsboro:[220/255,220/255,220/255],ghostwhite:[248/255,248/255,1],gold:[1,215/255,0],goldenrod:[218/255,165/255,32/255],gray:[128/255,128/255,128/255],green:[0,128/255,0],greenyellow:[173/255,1,47/255],grey:[128/255,128/255,128/255],honeydew:[240/255,1,240/255],hotpink:[1,105/255,180/255],indianred:[205/255,92/255,92/255],indigo:[75/255,0,130/255],ivory:[1,1,240/255],khaki:[240/255,230/255,140/255],lavender:[230/255,230/255,250/255],lavenderblush:[1,240/255,245/255],lawngreen:[124/255,252/255,0],lemonchiffon:[1,250/255,205/255],lightblue:[173/255,216/255,230/255],lightcoral:[240/255,128/255,128/255],lightcyan:[224/255,1,1],lightgoldenrodyellow:[250/255,250/255,210/255],lightgray:[211/255,211/255,211/255],lightgreen:[144/255,238/255,144/255],lightgrey:[211/255,211/255,211/255],lightpink:[1,182/255,193/255],lightsalmon:[1,160/255,122/255],lightseagreen:[32/255,178/255,170/255],lightskyblue:[135/255,206/255,250/255],lightslategray:[119/255,136/255,.6],lightslategrey:[119/255,136/255,.6],lightsteelblue:[176/255,196/255,222/255],lightyellow:[1,1,224/255],lime:[0,1,0],limegreen:[50/255,205/255,50/255],linen:[250/255,240/255,230/255],magenta:[1,0,1],maroon:[128/255,0,0],mediumaquamarine:[.4,205/255,170/255],mediumblue:[0,0,205/255],mediumorchid:[186/255,85/255,211/255],mediumpurple:[147/255,112/255,219/255],mediumseagreen:[60/255,179/255,113/255],mediumslateblue:[123/255,104/255,238/255],mediumspringgreen:[0,250/255,154/255],mediumturquoise:[72/255,209/255,.8],mediumvioletred:[199/255,21/255,133/255],midnightblue:[25/255,25/255,112/255],mintcream:[245/255,1,250/255],mistyrose:[1,228/255,225/255],moccasin:[1,228/255,181/255],navajowhite:[1,222/255,173/255],navy:[0,0,128/255],oldlace:[253/255,245/255,230/255],olive:[128/255,128/255,0],olivedrab:[107/255,142/255,35/255],orange:[1,165/255,0],orangered:[1,69/255,0],orchid:[218/255,112/255,214/255],palegoldenrod:[238/255,232/255,170/255],palegreen:[152/255,251/255,152/255],paleturquoise:[175/255,238/255,238/255],palevioletred:[219/255,112/255,147/255],papayawhip:[1,239/255,213/255],peachpuff:[1,218/255,185/255],peru:[205/255,133/255,63/255],pink:[1,192/255,203/255],plum:[221/255,160/255,221/255],powderblue:[176/255,224/255,230/255],purple:[128/255,0,128/255],rebeccapurple:[.4,.2,.6],red:[1,0,0],rosybrown:[188/255,143/255,143/255],royalblue:[65/255,105/255,225/255],saddlebrown:[139/255,69/255,19/255],salmon:[250/255,128/255,114/255],sandybrown:[244/255,164/255,96/255],seagreen:[46/255,139/255,87/255],seashell:[1,245/255,238/255],sienna:[160/255,82/255,45/255],silver:[192/255,192/255,192/255],skyblue:[135/255,206/255,235/255],slateblue:[106/255,90/255,205/255],slategray:[112/255,128/255,144/255],slategrey:[112/255,128/255,144/255],snow:[1,250/255,250/255],springgreen:[0,1,127/255],steelblue:[70/255,130/255,180/255],tan:[210/255,180/255,140/255],teal:[0,128/255,128/255],thistle:[216/255,191/255,216/255],tomato:[1,99/255,71/255],turquoise:[64/255,224/255,208/255],violet:[238/255,130/255,238/255],wheat:[245/255,222/255,179/255],white:[1,1,1],whitesmoke:[245/255,245/255,245/255],yellow:[1,1,0],yellowgreen:[154/255,205/255,50/255]};let ri=Array(3).fill("<percentage> | <number>[0, 255]"),ti=Array(3).fill("<number>[0, 255]");var ni=new ao({id:"srgb",name:"sRGB",base:Va,fromBase:e=>e.map((e=>{let r=e<0?-1:1,t=e*r;return t>.0031308?r*(1.055*t**(1/2.4)-.055):12.92*e})),toBase:e=>e.map((e=>{let r=e<0?-1:1,t=e*r;return t<=.04045?e/12.92:r*((t+.055)/1.055)**2.4})),formats:{rgb:{coords:ri},rgb_number:{name:"rgb",commas:!0,coords:ti,noAlpha:!0},color:{},rgba:{coords:ri,commas:!0,lastAlpha:!0},rgba_number:{name:"rgba",commas:!0,coords:ti},hex:{type:"custom",toGamut:!0,test:e=>/^#([a-f0-9]{3,4}){1,2}$/i.test(e),parse(e){e.length<=5&&(e=e.replace(/[a-f0-9]/gi,"$&$&"));let r=[];return e.replace(/[a-f0-9]{2}/gi,(e=>{r.push(parseInt(e,16)/255)})),{spaceId:"srgb",coords:r.slice(0,3),alpha:r.slice(3)[0]}},serialize:function(e,r){let{collapse:t=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};r<1&&e.push(r),e=e.map((e=>Math.round(255*e)));let n=t&&e.every((e=>e%17==0)),o=e.map((e=>n?(e/17).toString(16):e.toString(16).padStart(2,"0"))).join("");return"#"+o}},keyword:{type:"custom",test:e=>/^[a-z]+$/i.test(e),parse(e){let r={spaceId:"srgb",coords:null,alpha:1};if("transparent"===(e=e.toLowerCase())?(r.coords=ei.black,r.alpha=0):r.coords=ei[e],r.coords)return r}}}}),oi=new ao({id:"p3",cssId:"display-p3",name:"P3",base:Qa,fromBase:ni.fromBase,toBase:ni.toBase});let ai;if(An.display_space=ni,"undefined"!=typeof CSS&&CSS.supports)for(let e of[mo,Ua,oi]){let r=e.getMinCoords(),t=Xa({space:e,coords:r,alpha:1});if(CSS.supports("color",t)){An.display_space=e;break}}function ii(e){return so(e,[oo,"y"])}function si(e,r){co(e,[oo,"y"],r)}var ui=Object.freeze({__proto__:null,getLuminance:ii,register:function(e){Object.defineProperty(e.prototype,"luminance",{get(){return ii(this)},set(e){si(this,e)}})},setLuminance:si});const ci=.022,li=1.414;function fi(e){return e>=ci?e:e+(ci-e)**li}function hi(e){let r=e<0?-1:1,t=Math.abs(e);return r*Math.pow(t,2.4)}const pi=24/116,di=24389/27;let mi=Yn.D65;var gi=new ro({id:"lab-d65",name:"Lab D65",coords:{l:{refRange:[0,100],name:"Lightness"},a:{refRange:[-125,125]},b:{refRange:[-125,125]}},white:mi,base:oo,fromBase(e){let r=e.map(((e,r)=>e/mi[r])).map((e=>e>.008856451679035631?Math.cbrt(e):(di*e+16)/116));return[116*r[1]-16,500*(r[0]-r[1]),200*(r[1]-r[2])]},toBase(e){let r=[];return r[1]=(e[0]+16)/116,r[0]=e[1]/500+r[1],r[2]=r[1]-e[2]/200,[r[0]>pi?Math.pow(r[0],3):(116*r[0]-16)/di,e[0]>8?Math.pow((e[0]+16)/116,3):e[0]/di,r[2]>pi?Math.pow(r[2],3):(116*r[2]-16)/di].map(((e,r)=>e*mi[r]))},formats:{"lab-d65":{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});const bi=.5*Math.pow(5,.5)+.5;var vi=Object.freeze({__proto__:null,contrastAPCA:function(e,r){let t,n,o,a,i,s;r=Vn(r),e=Vn(e),r=$a(r,"srgb"),[a,i,s]=r.coords;let u=.2126729*hi(a)+.7151522*hi(i)+.072175*hi(s);e=$a(e,"srgb"),[a,i,s]=e.coords;let c=.2126729*hi(a)+.7151522*hi(i)+.072175*hi(s),l=fi(u),f=fi(c),h=f>l;return Math.abs(f-l)<5e-4?n=0:h?(t=f**.56-l**.57,n=1.14*t):(t=f**.65-l**.62,n=1.14*t),o=Math.abs(n)<.1?0:n>0?n-.027:n+.027,100*o},contrastDeltaPhi:function(e,r){e=Vn(e),r=Vn(r);let t=so(e,[gi,"l"]),n=so(r,[gi,"l"]),o=Math.abs(Math.pow(t,bi)-Math.pow(n,bi)),a=Math.pow(o,1/bi)*Math.SQRT2-40;return a<7.5?0:a},contrastLstar:function(e,r){e=Vn(e),r=Vn(r);let t=so(e,[mo,"l"]),n=so(r,[mo,"l"]);return Math.abs(t-n)},contrastMichelson:function(e,r){e=Vn(e),r=Vn(r);let t=Math.max(ii(e),0),n=Math.max(ii(r),0);n>t&&([t,n]=[n,t]);let o=t+n;return 0===o?0:(t-n)/o},contrastWCAG21:function(e,r){e=Vn(e),r=Vn(r);let t=Math.max(ii(e),0),n=Math.max(ii(r),0);return n>t&&([t,n]=[n,t]),(t+.05)/(n+.05)},contrastWeber:function(e,r){e=Vn(e),r=Vn(r);let t=Math.max(ii(e),0),n=Math.max(ii(r),0);return n>t&&([t,n]=[n,t]),0===n?5e4:(t-n)/n}});function yi(e){let[r,t,n]=io(e,oo),o=r+15*t+3*n;return[4*r/o,9*t/o]}function wi(e){let[r,t,n]=io(e,oo),o=r+t+n;return[r/o,t/o]}var Mi=Object.freeze({__proto__:null,register:function(e){Object.defineProperty(e.prototype,"uv",{get(){return yi(this)}}),Object.defineProperty(e.prototype,"xy",{get(){return wi(this)}})},uv:yi,xy:wi});function Ci(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};xt(t)&&(t={method:t});let{method:n=An.deltaE,...o}=t;for(let t in La)if("deltae"+n.toLowerCase()===t.toLowerCase())return La[t](e,r,o);throw new TypeError(`Unknown deltaE method: ${n}`)}var Si=Object.freeze({__proto__:null,darken:function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.25;return co(e,[ro.get("oklch","lch"),"l"],(e=>e*(1-r)))},lighten:function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.25;return co(e,[ro.get("oklch","lch"),"l"],(e=>e*(1+r)))}});function Ei(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.5,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return[e,r]=[Vn(e),Vn(r)],"object"===At(t)&&([t,n]=[.5,t]),ji(e,r,n)(t)}function _i(e,r){let t,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Ri(e)&&([t,n]=[e,r],[e,r]=t.rangeArgs.colors);let{maxDeltaE:o,deltaEMethod:a,steps:i=2,maxSteps:s=1e3,...u}=n;t||([e,r]=[Vn(e),Vn(r)],t=ji(e,r,u));let c=Ci(e,r),l=o>0?Math.max(i,Math.ceil(c/o)+1):i,f=[];if(void 0!==s&&(l=Math.min(l,s)),1===l)f=[{p:.5,color:t(.5)}];else{let e=1/(l-1);f=Array.from({length:l},((r,n)=>{let o=n*e;return{p:o,color:t(o)}}))}if(o>0){let e=f.reduce(((e,r,t)=>{if(0===t)return 0;let n=Ci(r.color,f[t-1].color,a);return Math.max(e,n)}),0);for(;e>o;){e=0;for(let r=1;r<f.length&&f.length<s;r++){let n=f[r-1],o=f[r],a=(o.p+n.p)/2,i=t(a);e=Math.max(e,Ci(i,n.color),Ci(i,o.color)),f.splice(r,0,{p:a,color:t(a)}),r++}}}return f=f.map((e=>e.color)),f}function ji(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(Ri(e)){let[t,n]=[e,r];return ji(...t.rangeArgs.colors,{...t.rangeArgs.options,...n})}let{space:n,outputSpace:o,progression:a,premultiplied:i}=t;e=Vn(e),r=Vn(r),e=No(e),r=No(r);let s={colors:[e,r],options:t};if(n=n?ro.get(n):ro.registry[An.interpolationSpace]||e.space,o=o?ro.get(o):n,e=$a(e,n),r=$a(r,n),e=Ta(e),r=Ta(r),n.coords.h&&"angle"===n.coords.h.type){let o=t.hue=t.hue||"shorter",a=[n,"h"],[i,s]=[so(e,a),so(r,a)];isNaN(i)&&!isNaN(s)?i=s:isNaN(s)&&!isNaN(i)&&(s=i),[i,s]=function(e,r){if("raw"===e)return r;let[t,n]=r.map(go),o=n-t;return"increasing"===e?o<0&&(n+=360):"decreasing"===e?o>0&&(t+=360):"longer"===e?-180<o&&o<180&&(o>0?t+=360:n+=360):"shorter"===e&&(o>180?t+=360:o<-180&&(n+=360)),[t,n]}(o,[i,s]),co(e,a,i),co(r,a,s)}return i&&(e.coords=e.coords.map((r=>r*e.alpha)),r.coords=r.coords.map((e=>e*r.alpha))),Object.assign((t=>{t=a?a(t):t;let s=e.coords.map(((e,n)=>Ht(e,r.coords[n],t))),u=Ht(e.alpha,r.alpha,t),c={space:n,coords:s,alpha:u};return i&&(c.coords=c.coords.map((e=>e/u))),o!==n&&(c=$a(c,o)),c}),{rangeArgs:s})}function Ri(e){return"function"===At(e)&&!!e.rangeArgs}An.interpolationSpace="lab";var Oi=Object.freeze({__proto__:null,isRange:Ri,mix:Ei,range:ji,register:function(e){e.defineFunction("mix",Ei,{returns:"color"}),e.defineFunction("range",ji,{returns:"function<color>"}),e.defineFunction("steps",_i,{returns:"array<color>"})},steps:_i}),Pi=new ro({id:"hsl",name:"HSL",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:ni,fromBase:e=>{let r=Math.max(...e),t=Math.min(...e),[n,o,a]=e,[i,s,u]=[NaN,0,(t+r)/2],c=r-t;if(0!==c){switch(s=0===u||1===u?0:(r-u)/Math.min(u,1-u),r){case n:i=(o-a)/c+(o<a?6:0);break;case o:i=(a-n)/c+2;break;case a:i=(n-o)/c+4}i*=60}return s<0&&(i+=180,s=Math.abs(s)),i>=360&&(i-=360),[i,100*s,100*u]},toBase:e=>{let[r,t,n]=e;function o(e){let o=(e+r/30)%12,a=t*Math.min(n,1-n);return n-a*Math.max(-1,Math.min(o-3,9-o,1))}return r%=360,r<0&&(r+=360),t/=100,n/=100,[o(0),o(8),o(4)]},formats:{hsl:{coords:["<number> | <angle>","<percentage>","<percentage>"]},hsla:{coords:["<number> | <angle>","<percentage>","<percentage>"],commas:!0,lastAlpha:!0}}}),Bi=new ro({id:"hsv",name:"HSV",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},v:{range:[0,100],name:"Value"}},base:Pi,fromBase(e){let[r,t,n]=e;t/=100,n/=100;let o=n+t*Math.min(n,1-n);return[r,0===o?0:200*(1-n/o),100*o]},toBase(e){let[r,t,n]=e;t/=100,n/=100;let o=n*(1-t/2);return[r,0===o||1===o?0:(n-o)/Math.min(o,1-o)*100,100*o]},formats:{color:{id:"--hsv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}}),ki=new ro({id:"hwb",name:"HWB",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},w:{range:[0,100],name:"Whiteness"},b:{range:[0,100],name:"Blackness"}},base:Bi,fromBase(e){let[r,t,n]=e;return[r,n*(100-t)/100,100-n]},toBase(e){let[r,t,n]=e;t/=100,n/=100;let o=t+n;if(o>=1){return[r,0,100*(t/o)]}let a=1-n;return[r,100*(0===a?0:1-t/a),100*a]},formats:{hwb:{coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});var Ni=new ao({id:"a98rgb-linear",cssId:"--a98-rgb-linear",name:"Linear Adobe® 98 RGB compatible",white:"D65",toXYZ_M:[[.5766690429101305,.1855582379065463,.1882286462349947],[.29734497525053605,.6273635662554661,.07529145849399788],[.02703136138641234,.07068885253582723,.9913375368376388]],fromXYZ_M:[[2.0415879038107465,-.5650069742788596,-.34473135077832956],[-.9692436362808795,1.8759675015077202,.04155505740717557],[.013444280632031142,-.11836239223101838,1.0151749943912054]]}),xi=new ao({id:"a98rgb",cssId:"a98-rgb",name:"Adobe® 98 RGB compatible",base:Ni,toBase:e=>e.map((e=>Math.pow(Math.abs(e),563/256)*Math.sign(e))),fromBase:e=>e.map((e=>Math.pow(Math.abs(e),256/563)*Math.sign(e)))});var Ai=new ao({id:"prophoto-linear",cssId:"--prophoto-rgb-linear",name:"Linear ProPhoto",white:"D50",base:lo,toXYZ_M:[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],fromXYZ_M:[[1.3457868816471583,-.25557208737979464,-.05110186497554526],[-.5446307051249019,1.5082477428451468,.02052744743642139],[0,0,1.2119675456389452]]});var Ii=new ao({id:"prophoto",cssId:"prophoto-rgb",name:"ProPhoto",base:Ai,toBase:e=>e.map((e=>e<.03125?e/16:e**1.8)),fromBase:e=>e.map((e=>e>=.001953125?e**(1/1.8):16*e))}),Li=new ro({id:"oklch",name:"Oklch",coords:{l:{refRange:[0,1],name:"Lightness"},c:{refRange:[0,.4],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},white:"D65",base:Oo,fromBase(e){let r,[t,n,o]=e;const a=2e-4;return r=Math.abs(n)<a&&Math.abs(o)<a?NaN:180*Math.atan2(o,n)/Math.PI,[t,Math.sqrt(n**2+o**2),go(r)]},toBase(e){let r,t,[n,o,a]=e;return isNaN(a)?(r=0,t=0):(r=o*Math.cos(a*Math.PI/180),t=o*Math.sin(a*Math.PI/180)),[n,r,t]},formats:{oklch:{coords:["<percentage> | <number>","<number> | <percentage>[0,1]","<number> | <angle>"]}}});let zi=Yn.D65;const Ti=24389/27,[Di,$i]=yi({space:oo,coords:zi});var qi=new ro({id:"luv",name:"Luv",coords:{l:{refRange:[0,100],name:"Lightness"},u:{refRange:[-215,215]},v:{refRange:[-215,215]}},white:zi,base:oo,fromBase(e){let r=[zt(e[0]),zt(e[1]),zt(e[2])],t=r[1],[n,o]=yi({space:oo,coords:r});if(!Number.isFinite(n)||!Number.isFinite(o))return[0,0,0];let a=t<=.008856451679035631?Ti*t:116*Math.cbrt(t)-16;return[a,13*a*(n-Di),13*a*(o-$i)]},toBase(e){let[r,t,n]=e;if(0===r||Lt(r))return[0,0,0];t=zt(t),n=zt(n);let o=t/(13*r)+Di,a=n/(13*r)+$i,i=r<=8?r/Ti:Math.pow((r+16)/116,3);return[i*(9*o/(4*a)),i,i*((12-3*o-20*a)/(4*a))]},formats:{color:{id:"--luv",coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}}),Hi=new ro({id:"lchuv",name:"LChuv",coords:{l:{refRange:[0,100],name:"Lightness"},c:{refRange:[0,220],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:qi,fromBase(e){let r,[t,n,o]=e;return r=Math.abs(n)<.02&&Math.abs(o)<.02?NaN:180*Math.atan2(o,n)/Math.PI,[t,Math.sqrt(n**2+o**2),go(r)]},toBase(e){let[r,t,n]=e;return t<0&&(t=0),isNaN(n)&&(n=0),[r,t*Math.cos(n*Math.PI/180),t*Math.sin(n*Math.PI/180)]},formats:{color:{id:"--lchuv",coords:["<number> | <percentage>","<number> | <percentage>","<number> | <angle>"]}}});const Wi=Ka[0][0],Fi=Ka[0][1],Gi=Ka[0][2],Xi=Ka[1][0],Yi=Ka[1][1],Zi=Ka[1][2],Ji=Ka[2][0],Ui=Ka[2][1],Qi=Ka[2][2];function Ki(e,r,t){const n=r/(Math.sin(t)-e*Math.cos(t));return n<0?1/0:n}function Vi(e){const r=Math.pow(e+16,3)/1560896,t=r>.008856451679035631?r:e/903.2962962962963,n=t*(284517*Wi-94839*Gi),o=t*(838422*Gi+769860*Fi+731718*Wi),a=t*(632260*Gi-126452*Fi),i=t*(284517*Xi-94839*Zi),s=t*(838422*Zi+769860*Yi+731718*Xi),u=t*(632260*Zi-126452*Yi),c=t*(284517*Ji-94839*Qi),l=t*(838422*Qi+769860*Ui+731718*Ji),f=t*(632260*Qi-126452*Ui);return{r0s:n/a,r0i:o*e/a,r1s:n/(a+126452),r1i:(o-769860)*e/(a+126452),g0s:i/u,g0i:s*e/u,g1s:i/(u+126452),g1i:(s-769860)*e/(u+126452),b0s:c/f,b0i:l*e/f,b1s:c/(f+126452),b1i:(l-769860)*e/(f+126452)}}function es(e,r){const t=r/360*Math.PI*2,n=Ki(e.r0s,e.r0i,t),o=Ki(e.r1s,e.r1i,t),a=Ki(e.g0s,e.g0i,t),i=Ki(e.g1s,e.g1i,t),s=Ki(e.b0s,e.b0i,t),u=Ki(e.b1s,e.b1i,t);return Math.min(n,o,a,i,s,u)}var rs=new ro({id:"hsluv",name:"HSLuv",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:Hi,gamutSpace:ni,fromBase(e){let r,[t,n,o]=[zt(e[0]),zt(e[1]),zt(e[2])];if(t>99.9999999)r=0,t=100;else if(t<1e-8)r=0,t=0;else{r=n/es(Vi(t),o)*100}return[o,r,t]},toBase(e){let r,[t,n,o]=[zt(e[0]),zt(e[1]),zt(e[2])];if(o>99.9999999)o=100,r=0;else if(o<1e-8)o=0,r=0;else{r=es(Vi(o),t)/100*n}return[o,r,t]},formats:{color:{id:"--hsluv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});function ts(e,r){return Math.abs(r)/Math.sqrt(Math.pow(e,2)+1)}function ns(e){let r=ts(e.r0s,e.r0i),t=ts(e.r1s,e.r1i),n=ts(e.g0s,e.g0i),o=ts(e.g1s,e.g1i),a=ts(e.b0s,e.b0i),i=ts(e.b1s,e.b1i);return Math.min(r,t,n,o,a,i)}Ka[0][0],Ka[0][1],Ka[0][2],Ka[1][0],Ka[1][1],Ka[1][2],Ka[2][0],Ka[2][1],Ka[2][2];var os=new ro({id:"hpluv",name:"HPLuv",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:Hi,gamutSpace:"self",fromBase(e){let r,[t,n,o]=[zt(e[0]),zt(e[1]),zt(e[2])];if(t>99.9999999)r=0,t=100;else if(t<1e-8)r=0,t=0;else{r=n/ns(Vi(t))*100}return[o,r,t]},toBase(e){let r,[t,n,o]=[zt(e[0]),zt(e[1]),zt(e[2])];if(o>99.9999999)o=100,r=0;else if(o<1e-8)o=0,r=0;else{r=ns(Vi(o))/100*n}return[o,r,t]},formats:{color:{id:"--hpluv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const as=2610/16384,is=32/2523,ss=.8359375,us=2413/128,cs=18.6875;var ls=new ao({id:"rec2100pq",cssId:"rec2100-pq",name:"REC.2100-PQ",base:Ya,toBase:e=>e.map((function(e){return 1e4*(Math.max(e**is-ss,0)/(us-cs*e**is))**6.277394636015326/203})),fromBase:e=>e.map((function(e){let r=Math.max(203*e/1e4,0);return((ss+us*r**as)/(1+cs*r**as))**78.84375}))});const fs=.17883277,hs=.28466892,ps=.55991073,ds=3.7743;var ms=new ao({id:"rec2100hlg",cssId:"rec2100-hlg",name:"REC.2100-HLG",referred:"scene",base:Ya,toBase:e=>e.map((function(e){return e<=.5?e**2/3*ds:(Math.exp((e-ps)/fs)+hs)/12*ds})),fromBase:e=>e.map((function(e){return(e/=ds)<=1/12?Math.sqrt(3*e):fs*Math.log(12*e-hs)+ps}))});const gs={};function bs(e){let{id:r,toCone_M:t,fromCone_M:n}=e;gs[r]=arguments[0]}function vs(e,r){let t=gs[arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Bradford"],[n,o,a]=Nt(t.toCone_M,e),[i,s,u]=Nt(t.toCone_M,r),c=Nt([[i/n,0,0],[0,s/o,0],[0,0,u/a]],t.toCone_M);return Nt(t.fromCone_M,c)}Kt.add("chromatic-adaptation-start",(e=>{e.options.method&&(e.M=vs(e.W1,e.W2,e.options.method))})),Kt.add("chromatic-adaptation-end",(e=>{e.M||(e.M=vs(e.W1,e.W2,e.options.method))})),bs({id:"von Kries",toCone_M:[[.40024,.7076,-.08081],[-.2263,1.16532,.0457],[0,0,.91822]],fromCone_M:[[1.8599363874558397,-1.1293816185800916,.21989740959619328],[.3611914362417676,.6388124632850422,-6370596838649899e-21],[0,0,1.0890636230968613]]}),bs({id:"Bradford",toCone_M:[[.8951,.2664,-.1614],[-.7502,1.7135,.0367],[.0389,-.0685,1.0296]],fromCone_M:[[.9869929054667121,-.14705425642099013,.15996265166373122],[.4323052697233945,.5183602715367774,.049291228212855594],[-.00852866457517732,.04004282165408486,.96848669578755]]}),bs({id:"CAT02",toCone_M:[[.7328,.4296,-.1624],[-.7036,1.6975,.0061],[.003,.0136,.9834]],fromCone_M:[[1.0961238208355142,-.27886900021828726,.18274517938277307],[.4543690419753592,.4735331543074117,.07209780371722911],[-.009627608738429355,-.00569803121611342,1.0153256399545427]]}),bs({id:"CAT16",toCone_M:[[.401288,.650173,-.051461],[-.250268,1.204414,.045854],[-.002079,.048952,.953127]],fromCone_M:[[1.862067855087233,-1.0112546305316845,.14918677544445172],[.3875265432361372,.6214474419314753,-.008973985167612521],[-.01584149884933386,-.03412293802851557,1.0499644368778496]]}),Object.assign(Yn,{A:[1.0985,1,.35585],C:[.98074,1,1.18232],D55:[.95682,1,.92149],D75:[.94972,1,1.22638],E:[1,1,1],F2:[.99186,1,.67393],F7:[.95041,1,1.08747],F11:[1.00962,1,.6435]}),Yn.ACES=[.32168/.33767,1,.34065/.33767];var ys=new ao({id:"acescg",cssId:"--acescg",name:"ACEScg",coords:{r:{range:[0,65504],name:"Red"},g:{range:[0,65504],name:"Green"},b:{range:[0,65504],name:"Blue"}},referred:"scene",white:Yn.ACES,toXYZ_M:[[.6624541811085053,.13400420645643313,.1561876870049078],[.27222871678091454,.6740817658111484,.05368951740793705],[-.005574649490394108,.004060733528982826,1.0103391003129971]],fromXYZ_M:[[1.6410233796943257,-.32480329418479,-.23642469523761225],[-.6636628587229829,1.6153315916573379,.016756347685530137],[.011721894328375376,-.008284441996237409,.9883948585390215]]});const ws=2**-16,Ms=-.35828683,Cs=(Math.log2(65504)+9.72)/17.52;var Ss=new ao({id:"acescc",cssId:"--acescc",name:"ACEScc",coords:{r:{range:[Ms,Cs],name:"Red"},g:{range:[Ms,Cs],name:"Green"},b:{range:[Ms,Cs],name:"Blue"}},referred:"scene",base:ys,toBase:e=>e.map((function(e){return e<=-.3013698630136986?2*(2**(17.52*e-9.72)-ws):e<Cs?2**(17.52*e-9.72):65504})),fromBase:e=>e.map((function(e){return e<=0?(Math.log2(ws)+9.72)/17.52:e<ws?(Math.log2(ws+.5*e)+9.72)/17.52:(Math.log2(e)+9.72)/17.52}))}),Es=Object.freeze({__proto__:null,A98RGB:xi,A98RGB_Linear:Ni,ACEScc:Ss,ACEScg:ys,CAM16_JMh:_a,HCT:Na,HPLuv:os,HSL:Pi,HSLuv:rs,HSV:Bi,HWB:ki,ICTCP:ua,JzCzHz:Uo,Jzazbz:Jo,LCH:bo,LCHuv:Hi,Lab:mo,Lab_D65:gi,Luv:qi,OKLCH:Li,OKLab:Oo,P3:oi,P3_Linear:Qa,ProPhoto:Ii,ProPhoto_Linear:Ai,REC_2020:Ua,REC_2020_Linear:Ya,REC_2100_HLG:ms,REC_2100_PQ:ls,XYZ_ABS_D65:Io,XYZ_D50:lo,XYZ_D65:oo,sRGB:ni,sRGB_Linear:Va});class _s{constructor(){let e,r,t,n;for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];1===a.length&&(e=Vn(a[0])),e?(r=e.space||e.spaceId,t=e.coords,n=e.alpha):[r,t,n]=a,Object.defineProperty(this,"space",{value:ro.get(r),writable:!1,enumerable:!0,configurable:!0}),this.coords=t?t.slice():[0,0,0],this.alpha=n>1||void 0===n?1:n<0?0:n;for(let e=0;e<this.coords.length;e++)"NaN"===this.coords[e]&&(this.coords[e]=NaN);for(let e in this.space.coords)Object.defineProperty(this,e,{get:()=>this.get(e),set:r=>this.set(e,r)})}get spaceId(){return this.space.id}clone(){return new _s(this.space,this.coords,this.alpha)}toJSON(){return{spaceId:this.spaceId,coords:this.coords,alpha:this.alpha}}display(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];let n=function(e){let{space:r=An.display_space,...t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=Xa(e,t);if("undefined"==typeof CSS||CSS.supports("color",n)||!An.display_space)n=new String(n),n.color=e;else{let a=e;var o;if((e.coords.some(Lt)||Lt(e.alpha))&&!(null!==(o=ai)&&void 0!==o?o:ai=CSS.supports("color","hsl(none 50% 50%)"))&&(a=No(e),a.coords=a.coords.map(zt),a.alpha=zt(a.alpha),n=Xa(a,t),CSS.supports("color",n)))return n=new String(n),n.color=a,n;a=$a(a,r),n=new String(Xa(a,t)),n.color=a}return n}(this,...r);return n.color=new _s(n.color),n}static get(e){if(e instanceof _s)return e;for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];return new _s(e,...t)}static defineFunction(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,{instance:n=!0,returns:o}=t,a=function(){let e=r(...arguments);if("color"===o)e=_s.get(e);else if("function<color>"===o){let r=e;e=function(){let e=r(...arguments);return _s.get(e)},Object.assign(e,r)}else"array<color>"===o&&(e=e.map((e=>_s.get(e))));return e};e in _s||(_s[e]=a),n&&(_s.prototype[e]=function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return a(this,...r)})}static defineFunctions(e){for(let r in e)_s.defineFunction(r,e[r],e[r])}static extend(e){if(e.register)e.register(_s);else for(let r in e)_s.defineFunction(r,e[r])}}_s.defineFunctions({get:so,getAll:io,set:co,setAll:uo,to:$a,equals:function(e,r){return e=Vn(e),r=Vn(r),e.space===r.space&&e.alpha===r.alpha&&e.coords.every(((e,t)=>e===r.coords[t]))},inGamut:ko,toGamut:Ta,distance:xo,toString:Xa}),Object.assign(_s,{util:Qt,hooks:Kt,WHITES:Yn,Space:ro,spaces:ro.registry,parse:Kn,defaults:An});for(let e of Object.keys(Es))ro.register(Es[e]);var js,Rs,Os,Ps={};function Bs(){if(Rs)return js;Rs=1;var e=gr().f,r=er(),t=tr()("toStringTag");return js=function(n,o,a){n&&!a&&(n=n.prototype),n&&!r(n,t)&&e(n,t,{configurable:!0,value:o})}}!function(){if(Os)return Ps;Os=1;var e=Ot(),r=o(),t=Bs();e({global:!0},{Reflect:{}}),t(r.Reflect,"Reflect",!0)}();for(let e in ro.registry)ks(e,ro.registry[e]);function ks(e,r){let t=e.replace(/-/g,"_");Object.defineProperty(_s.prototype,t,{get(){let t=this.getAll(e);return"undefined"==typeof Proxy?t:new Proxy(t,{has:(e,t)=>{try{return ro.resolveCoord([r,t]),!0}catch(e){}return Reflect.has(e,t)},get:(e,t,n)=>{if(t&&"symbol"!=typeof t&&!(t in e)){let{index:n}=ro.resolveCoord([r,t]);if(n>=0)return e[n]}return Reflect.get(e,t,n)},set:(t,n,o,a)=>{if(n&&"symbol"!=typeof n&&!(n in t)||n>=0){let{index:a}=ro.resolveCoord([r,n]);if(a>=0)return t[a]=o,this.setAll(e,t),!0}return Reflect.set(t,n,o,a)}})},set(r){this.setAll(e,r)},configurable:!0,enumerable:!0})}Kt.add("colorspace-init-end",(e=>{var r;ks(e.id,e),null===(r=e.aliases)||void 0===r||r.forEach((r=>{ks(r,e)}))})),_s.extend(La),_s.extend({deltaE:Ci}),Object.assign(_s,{deltaEMethods:La}),_s.extend(Si),_s.extend({contrast:function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};xt(t)&&(t={algorithm:t});let{algorithm:n,...o}=t;if(!n){let e=Object.keys(vi).map((e=>e.replace(/^contrast/,""))).join(", ");throw new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${e}`)}e=Vn(e),r=Vn(r);for(let t in vi)if("contrast"+n.toLowerCase()===t.toLowerCase())return vi[t](e,r,o);throw new TypeError(`Unknown contrast algorithm: ${n}`)}}),_s.extend(Mi),_s.extend(ui),_s.extend(Oi),_s.extend(vi),exports.default=_s;
//# sourceMappingURL=color.legacy.min.cjs.map
