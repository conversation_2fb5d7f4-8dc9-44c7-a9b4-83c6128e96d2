{"version": 3, "file": "color.global.js", "sources": ["../src/multiply-matrices.js", "../src/util.js", "../src/hooks.js", "../src/defaults.js", "../src/adapt.js", "../src/parse.js", "../src/getColor.js", "../src/space.js", "../src/spaces/xyz-d65.js", "../src/rgbspace.js", "../src/getAll.js", "../src/get.js", "../src/setAll.js", "../src/set.js", "../src/spaces/xyz-d50.js", "../src/spaces/lab.js", "../src/angles.js", "../src/spaces/lch.js", "../src/deltaE/deltaE2000.js", "../src/spaces/oklab.js", "../src/deltaE/deltaEOK.js", "../src/inGamut.js", "../src/clone.js", "../src/distance.js", "../src/deltaE/deltaE76.js", "../src/deltaE/deltaECMC.js", "../src/spaces/xyz-abs-d65.js", "../src/spaces/jzazbz.js", "../src/spaces/jzczhz.js", "../src/deltaE/deltaEJz.js", "../src/spaces/ictcp.js", "../src/deltaE/deltaEITP.js", "../src/spaces/cam16.js", "../src/spaces/hct.js", "../src/deltaE/deltaEHCT.js", "../src/deltaE/index.js", "../src/toGamut.js", "../src/to.js", "../src/serialize.js", "../src/spaces/rec2020-linear.js", "../src/spaces/rec2020.js", "../src/spaces/p3-linear.js", "../src/spaces/srgb-linear.js", "../src/keywords.js", "../src/spaces/srgb.js", "../src/spaces/p3.js", "../src/display.js", "../src/equals.js", "../src/luminance.js", "../src/contrast/WCAG21.js", "../src/contrast/APCA.js", "../src/contrast/Michelson.js", "../src/contrast/Weber.js", "../src/contrast/Lstar.js", "../src/spaces/lab-d65.js", "../src/contrast/deltaPhi.js", "../src/contrast.js", "../src/chromaticity.js", "../src/deltaE.js", "../src/variations.js", "../src/interpolation.js", "../src/spaces/hsl.js", "../src/spaces/hsv.js", "../src/spaces/hwb.js", "../src/spaces/a98rgb-linear.js", "../src/spaces/a98rgb.js", "../src/spaces/prophoto-linear.js", "../src/spaces/prophoto.js", "../src/spaces/oklch.js", "../src/spaces/luv.js", "../src/spaces/lchuv.js", "../src/spaces/hsluv.js", "../src/spaces/hpluv.js", "../src/spaces/rec2100-pq.js", "../src/spaces/rec2100-hlg.js", "../src/CATs.js", "../src/spaces/acescg.js", "../src/spaces/acescc.js", "../src/color.js", "../src/spaces/index.js", "../src/space-accessors.js", "../src/index.js"], "sourcesContent": ["// A is m x n. B is n x p. product is m x p.\nexport default function multiplyMatrices (A, B) {\n\tlet m = A.length;\n\n\tif (!Array.isArray(A[0])) {\n\t\t// A is vector, convert to [[a, b, c, ...]]\n\t\tA = [A];\n\t}\n\n\tif (!Array.isArray(B[0])) {\n\t\t// B is vector, convert to [[a], [b], [c], ...]]\n\t\tB = B.map(x => [x]);\n\t}\n\n\tlet p = B[0].length;\n\tlet B_cols = B[0].map((_, i) => B.map(x => x[i])); // transpose B\n\tlet product = A.map(row => B_cols.map(col => {\n\t\tlet ret = 0;\n\n\t\tif (!Array.isArray(row)) {\n\t\t\tfor (let c of col) {\n\t\t\t\tret += row * c;\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tfor (let i = 0; i < row.length; i++) {\n\t\t\tret += row[i] * (col[i] || 0);\n\t\t}\n\n\t\treturn ret;\n\t}));\n\n\tif (m === 1) {\n\t\tproduct = product[0]; // Avoid [[a, b, c, ...]]\n\t}\n\n\tif (p === 1) {\n\t\treturn product.map(x => x[0]); // Avoid [[a], [b], [c], ...]]\n\t}\n\n\treturn product;\n}\n", "/**\n * Various utility functions\n */\n\nexport {default as multiplyMatrices} from \"./multiply-matrices.js\";\n\n/**\n * Check if a value is a string (including a String object)\n * @param {*} str - Value to check\n * @returns {boolean}\n */\nexport function isString (str) {\n\treturn type(str) === \"string\";\n}\n\n/**\n * Determine the internal JavaScript [[Class]] of an object.\n * @param {*} o - Value to check\n * @returns {string}\n */\nexport function type (o) {\n\tlet str = Object.prototype.toString.call(o);\n\n\treturn (str.match(/^\\[object\\s+(.*?)\\]$/)[1] || \"\").toLowerCase();\n}\n\nexport function serializeNumber (n, {precision, unit }) {\n\tif (isNone(n)) {\n\t\treturn \"none\";\n\t}\n\n\treturn toPrecision(n, precision) + (unit ?? \"\");\n}\n\n/**\n * Check if a value corresponds to a none argument\n * @param {*} n - Value to check\n * @returns {boolean}\n */\nexport function isNone (n) {\n\treturn Number.isNaN(n) || (n instanceof Number && n?.none);\n}\n\n/**\n * Replace none values with 0\n */\nexport function skipNone (n) {\n\treturn isNone(n) ? 0 : n;\n}\n\n/**\n * Round a number to a certain number of significant digits\n * @param {number} n - The number to round\n * @param {number} precision - Number of significant digits\n */\nexport function toPrecision (n, precision) {\n\tif (n === 0) {\n\t\treturn 0;\n\t}\n\tlet integer = ~~n;\n\tlet digits = 0;\n\tif (integer && precision) {\n\t\tdigits = ~~Math.log10(Math.abs(integer)) + 1;\n\t}\n\tconst multiplier = 10.0 ** (precision - digits);\n\treturn Math.floor(n * multiplier + 0.5) / multiplier;\n}\n\nconst angleFactor = {\n\tdeg: 1,\n\tgrad: 0.9,\n\trad: 180 / Math.PI,\n\tturn: 360,\n};\n\n/**\n* Parse a CSS function, regardless of its name and arguments\n* @param String str String to parse\n* @return {{name, args, rawArgs}}\n*/\nexport function parseFunction (str) {\n\tif (!str) {\n\t\treturn;\n\t}\n\n\tstr = str.trim();\n\n\tconst isFunctionRegex = /^([a-z]+)\\((.+?)\\)$/i;\n\tconst isNumberRegex = /^-?[\\d.]+$/;\n\tconst unitValueRegex = /%|deg|g?rad|turn$/;\n\tconst singleArgument = /\\/?\\s*(none|[-\\w.]+(?:%|deg|g?rad|turn)?)/g;\n\tlet parts = str.match(isFunctionRegex);\n\n\tif (parts) {\n\t\t// It is a function, parse args\n\t\tlet args = [];\n\t\tparts[2].replace(singleArgument, ($0, rawArg) => {\n\t\t\tlet match = rawArg.match(unitValueRegex);\n\t\t\tlet arg = rawArg;\n\n\t\t\tif (match) {\n\t\t\t\tlet unit = match[0];\n\t\t\t\t// Drop unit from value\n\t\t\t\tlet unitlessArg = arg.slice(0, -unit.length);\n\n\t\t\t\tif (unit === \"%\") {\n\t\t\t\t\t// Convert percentages to 0-1 numbers\n\t\t\t\t\targ = new Number(unitlessArg / 100);\n\t\t\t\t\targ.type = \"<percentage>\";\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// Multiply angle by appropriate factor for its unit\n\t\t\t\t\targ = new Number(unitlessArg * angleFactor[unit]);\n\t\t\t\t\targ.type = \"<angle>\";\n\t\t\t\t\targ.unit = unit;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (isNumberRegex.test(arg)) {\n\t\t\t\t// Convert numerical args to numbers\n\t\t\t\targ = new Number(arg);\n\t\t\t\targ.type = \"<number>\";\n\t\t\t}\n\t\t\telse if (arg === \"none\") {\n\t\t\t\targ = new Number(NaN);\n\t\t\t\targ.none = true;\n\t\t\t}\n\n\t\t\tif ($0.startsWith(\"/\")) {\n\t\t\t\t// It's alpha\n\t\t\t\targ = arg instanceof Number ? arg : new Number(arg);\n\t\t\t\targ.alpha = true;\n\t\t\t}\n\n\t\t\tif (typeof arg === \"object\" && arg instanceof Number) {\n\t\t\t\targ.raw = rawArg;\n\t\t\t}\n\n\t\t\targs.push(arg);\n\t\t});\n\n\t\treturn {\n\t\t\tname: parts[1].toLowerCase(),\n\t\t\trawName: parts[1],\n\t\t\trawArgs: parts[2],\n\t\t\t// An argument could be (as of css-color-4):\n\t\t\t// a number, percentage, degrees (hue), ident (in color())\n\t\t\targs,\n\t\t};\n\t}\n}\n\nexport function last (arr) {\n\treturn arr[arr.length - 1];\n}\n\nexport function interpolate (start, end, p) {\n\tif (isNaN(start)) {\n\t\treturn end;\n\t}\n\n\tif (isNaN(end)) {\n\t\treturn start;\n\t}\n\n\treturn start + (end - start) * p;\n}\n\nexport function interpolateInv (start, end, value) {\n\treturn (value - start) / (end - start);\n}\n\nexport function mapRange (from, to, value) {\n\treturn interpolate(to[0], to[1], interpolateInv(from[0], from[1], value));\n}\n\nexport function parseCoordGrammar (coordGrammars) {\n\treturn coordGrammars.map(coordGrammar => {\n\t\treturn coordGrammar.split(\"|\").map(type => {\n\t\t\ttype = type.trim();\n\t\t\tlet range = type.match(/^(<[a-z]+>)\\[(-?[.\\d]+),\\s*(-?[.\\d]+)\\]?$/);\n\n\t\t\tif (range) {\n\t\t\t\tlet ret = new String(range[1]);\n\t\t\t\tret.range = [+range[2], +range[3]];\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn type;\n\t\t});\n\t});\n}\n\n/**\n * Clamp value between the minimum and maximum\n * @param {number} min minimum value to return\n * @param {number} val the value to return if it is between min and max\n * @param {number} max maximum value to return\n * @returns number\n */\nexport function clamp (min, val, max) {\n\treturn Math.max(Math.min(max, val), min);\n}\n\n/**\n * Copy sign of one value to another.\n * @param {number} - to number to copy sign to\n * @param {number} - from number to copy sign from\n * @returns number\n */\nexport function copySign (to, from) {\n\treturn Math.sign(to) === Math.sign(from) ? to : -to;\n}\n\n/**\n * Perform pow on a signed number and copy sign to result\n * @param {number} - base the base number\n * @param {number} - exp the exponent\n * @returns number\n */\nexport function spow (base, exp) {\n\treturn copySign(Math.abs(base) ** exp, base);\n}\n\n/**\n * Perform a divide, but return zero if the numerator is zero\n * @param {number} n - the numerator\n * @param {number} d - the denominator\n * @returns number\n */\nexport function zdiv (n, d) {\n\treturn (d === 0) ? 0 : n / d;\n}\n\n/**\n * Perform a bisect on a sorted list and locate the insertion point for\n * a value in arr to maintain sorted order.\n * @param {number[]} arr - array of sorted numbers\n * @param {number} value - value to find insertion point for\n * @param {number} lo - used to specify a the low end of a subset of the list\n * @param {number} hi - used to specify a the high end of a subset of the list\n * @returns number\n */\nexport function bisectLeft (arr, value, lo = 0, hi = arr.length) {\n\twhile (lo < hi) {\n\t\tconst mid = (lo + hi) >> 1;\n\t\tif (arr[mid] < value) {\n\t\t\tlo = mid + 1;\n\t\t}\n\t\telse {\n\t\t\thi = mid;\n\t\t}\n\t}\n\treturn lo;\n}\n", "/**\n * A class for adding deep extensibility to any piece of JS code\n */\nexport class Hooks {\n\tadd (name, callback, first) {\n\t\tif (typeof arguments[0] != \"string\") {\n\t\t\t// Multiple hooks\n\t\t\tfor (var name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\tthis[name] = this[name] || [];\n\n\t\t\tif (callback) {\n\t\t\t\tthis[name][first ? \"unshift\" : \"push\"](callback);\n\t\t\t}\n\t\t}, this);\n\t}\n\n\trun (name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * The instance of {@link Hooks} used throughout Color.js\n */\nconst hooks = new Hooks();\n\nexport default hooks;\n", "// Global defaults one may want to configure\nexport default {\n\tgamut_mapping: \"css\",\n\tprecision: 5,\n\tdeltaE: \"76\", // Default deltaE method\n\tverbose: globalThis?.process?.env?.NODE_ENV?.toLowerCase() !== \"test\",\n\twarn: function warn (msg) {\n\t\tif (this.verbose) {\n\t\t\tglobalThis?.console?.warn?.(msg);\n\t\t}\n\t},\n};\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\n\nexport const WHITES = {\n\t// for compatibility, the four-digit chromaticity-derived ones everyone else uses\n\tD50: [0.3457 / 0.3585, 1.00000, (1.0 - 0.3457 - 0.3585) / 0.3585],\n\tD65: [0.3127 / 0.3290, 1.00000, (1.0 - 0.3127 - 0.3290) / 0.3290],\n};\n\nexport function getWhite (name) {\n\tif (Array.isArray(name)) {\n\t\treturn name;\n\t}\n\n\treturn WHITES[name];\n}\n\n// Adapt XYZ from white point W1 to W2\nexport default function adapt (W1, W2, XYZ, options = {}) {\n\tW1 = getWhite(W1);\n\tW2 = getWhite(W2);\n\n\tif (!W1 || !W2) {\n\t\tthrow new TypeError(`Missing white point to convert ${!W1 ? \"from\" : \"\"}${!W1 && !W2 ? \"/\" : \"\"}${!W2 ? \"to\" : \"\"}`);\n\t}\n\n\tif (W1 === W2) {\n\t\t// Same whitepoints, no conversion needed\n\t\treturn XYZ;\n\t}\n\n\tlet env = {W1, W2, XYZ, options};\n\n\thooks.run(\"chromatic-adaptation-start\", env);\n\n\tif (!env.M) {\n\t\tif (env.W1 === WHITES.D65 && env.W2 === WHITES.D50) {\n\t\t\tenv.M = [\n\t\t\t\t[ 1.0479297925449969, 0.022946870601609652, -0.05019226628920524 ],\n\t\t\t\t[ 0.02962780877005599, 0.9904344267538799, -0.017073799063418826 ],\n\t\t\t\t[ -0.009243040646204504, 0.015055191490298152, 0.7518742814281371 ],\n\t\t\t];\n\t\t}\n\t\telse if (env.W1 === WHITES.D50 && env.W2 === WHITES.D65) {\n\n\t\t\tenv.M = [\n\t\t\t\t[ 0.955473421488075, -0.02309845494876471, 0.06325924320057072 ],\n\t\t\t\t[ -0.0283697093338637, 1.0099953980813041, 0.021041441191917323 ],\n\t\t\t\t[ 0.012314014864481998, -0.020507649298898964, 1.330365926242124 ],\n\t\t\t];\n\t\t}\n\t}\n\n\thooks.run(\"chromatic-adaptation-end\", env);\n\n\tif (env.M) {\n\t\treturn multiplyMatrices(env.M, env.XYZ);\n\t}\n\telse {\n\t\tthrow new TypeError(\"Only Bradford CAT with white points D50 and D65 supported for now.\");\n\t}\n}\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\n\nconst noneTypes = new Set([\"<number>\", \"<percentage>\", \"<angle>\"]);\n\n/**\n * Validates the coordinates of a color against a format's coord grammar and\n * maps the coordinates to the range or refRange of the coordinates.\n * @param {ColorSpace} space - Colorspace the coords are in\n * @param {object} format - the format object to validate against\n * @param {string} name - the name of the color function. e.g. \"oklab\" or \"color\"\n * @returns {object[]} - an array of type metadata for each coordinate\n */\nfunction coerceCoords (space, format, name, coords) {\n\tlet types = Object.entries(space.coords).map(([id, coordMeta], i) => {\n\t\tlet coordGrammar = format.coordGrammar[i];\n\t\tlet arg = coords[i];\n\t\tlet providedType = arg?.type;\n\n\t\t// Find grammar alternative that matches the provided type\n\t\t// Non-strict equals is intentional because we are comparing w/ string objects\n\t\tlet type;\n\t\tif (arg.none) {\n\t\t\ttype = coordGrammar.find(c => noneTypes.has(c));\n\t\t}\n\t\telse {\n\t\t\ttype = coordGrammar.find(c => c == providedType);\n\t\t}\n\n\t\t// Check that each coord conforms to its grammar\n\t\tif (!type) {\n\t\t\t// Type does not exist in the grammar, throw\n\t\t\tlet coordName = coordMeta.name || id;\n\t\t\tthrow new TypeError(`${providedType ?? arg.raw} not allowed for ${coordName} in ${name}()`);\n\t\t}\n\n\t\tlet fromRange = type.range;\n\n\t\tif (providedType === \"<percentage>\") {\n\t\t\tfromRange ||= [0, 1];\n\t\t}\n\n\t\tlet toRange = coordMeta.range || coordMeta.refRange;\n\n\t\tif (fromRange && toRange) {\n\t\t\tcoords[i] = util.mapRange(fromRange, toRange, coords[i]);\n\t\t}\n\n\t\treturn type;\n\t});\n\n\treturn types;\n}\n\n\n/**\n * Convert a CSS Color string to a color object\n * @param {string} str\n * @param {object} [options]\n * @param {object} [options.meta] - Object for additional information about the parsing\n * @returns {Color}\n */\nexport default function parse (str, {meta} = {}) {\n\tlet env = {\"str\": String(str)?.trim()};\n\thooks.run(\"parse-start\", env);\n\n\tif (env.color) {\n\t\treturn env.color;\n\t}\n\n\tenv.parsed = util.parseFunction(env.str);\n\n\tif (env.parsed) {\n\t\t// Is a functional syntax\n\t\tlet name = env.parsed.name;\n\n\t\tif (name === \"color\") {\n\t\t\t// color() function\n\t\t\tlet id = env.parsed.args.shift();\n\t\t\t// Check against both <dashed-ident> and <ident> versions\n\t\t\tlet alternateId = id.startsWith(\"--\") ? id.substring(2) : `--${id}`;\n\t\t\tlet ids = [id, alternateId];\n\t\t\tlet alpha = env.parsed.rawArgs.indexOf(\"/\") > 0 ? env.parsed.args.pop() : 1;\n\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\tlet colorSpec = space.getFormat(\"color\");\n\n\t\t\t\tif (colorSpec) {\n\t\t\t\t\tif (ids.includes(colorSpec.id) || colorSpec.ids?.filter((specId) => ids.includes(specId)).length) {\n\t\t\t\t\t\t// From https://drafts.csswg.org/css-color-4/#color-function\n\t\t\t\t\t\t// If more <number>s or <percentage>s are provided than parameters that the colorspace takes, the excess <number>s at the end are ignored.\n\t\t\t\t\t\t// If less <number>s or <percentage>s are provided than parameters that the colorspace takes, the missing parameters default to 0. (This is particularly convenient for multichannel printers where the additional inks are spot colors or varnishes that most colors on the page won’t use.)\n\t\t\t\t\t\tconst coords = Object.keys(space.coords).map((_, i) => env.parsed.args[i] || 0);\n\n\t\t\t\t\t\tlet types;\n\n\t\t\t\t\t\tif (colorSpec.coordGrammar) {\n\t\t\t\t\t\t\ttypes = coerceCoords(space, colorSpec, \"color\", coords);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (meta) {\n\t\t\t\t\t\t\tObject.assign(meta, {formatId: \"color\", types});\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (colorSpec.id.startsWith(\"--\") && !id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a non-standard space and not currently supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use prefixed color(${colorSpec.id}) instead of color(${id}).`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (id.startsWith(\"--\") && !colorSpec.id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a standard space and supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use color(${colorSpec.id}) instead of prefixed color(${id}).`);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {spaceId: space.id, coords, alpha};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Not found\n\t\t\tlet didYouMean = \"\";\n\t\t\tlet registryId = id in ColorSpace.registry ? id : alternateId;\n\t\t\tif (registryId in ColorSpace.registry) {\n\t\t\t\t// Used color space id instead of color() id, these are often different\n\t\t\t\tlet cssId = ColorSpace.registry[registryId].formats?.color?.id;\n\n\t\t\t\tif (cssId) {\n\t\t\t\t\tdidYouMean = `Did you mean color(${cssId})?`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthrow new TypeError(`Cannot parse color(${id}). ` + (didYouMean || \"Missing a plugin?\"));\n\t\t}\n\t\telse {\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\t// color space specific function\n\t\t\t\tlet format = space.getFormat(name);\n\t\t\t\tif (format && format.type === \"function\") {\n\t\t\t\t\tlet alpha = 1;\n\n\t\t\t\t\tif (format.lastAlpha || util.last(env.parsed.args).alpha) {\n\t\t\t\t\t\talpha = env.parsed.args.pop();\n\t\t\t\t\t}\n\n\t\t\t\t\tlet coords = env.parsed.args;\n\n\t\t\t\t\tlet types;\n\n\t\t\t\t\tif (format.coordGrammar) {\n\t\t\t\t\t\ttypes = coerceCoords(space, format, name, coords);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tObject.assign(meta, {formatId: format.name, types});\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tspaceId: space.id,\n\t\t\t\t\t\tcoords, alpha,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\telse {\n\t\t// Custom, colorspace-specific format\n\t\tfor (let space of ColorSpace.all) {\n\t\t\tfor (let formatId in space.formats) {\n\t\t\t\tlet format = space.formats[formatId];\n\n\t\t\t\tif (format.type !== \"custom\") {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (format.test && !format.test(env.str)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tlet color = format.parse(env.str);\n\n\t\t\t\tif (color) {\n\t\t\t\t\tcolor.alpha ??= 1;\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tmeta.formatId = formatId;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t// If we're here, we couldn't parse\n\tthrow new TypeError(`Could not parse ${str} as a color. Missing a plugin?`);\n}\n", "import ColorSpace from \"./space.js\";\nimport {isString} from \"./util.js\";\nimport parse from \"./parse.js\";\n\n/**\n * Resolves a color reference (object or string) to a plain color object\n * @param {Color | {space, coords, alpha} | string | Array<Color | {space, coords, alpha} | string> } color\n * @returns {{space, coords, alpha} | Array<{space, coords, alpha}}>\n */\nexport default function getColor (color) {\n\tif (Array.isArray(color)) {\n\t\treturn color.map(getColor);\n\t}\n\n\tif (!color) {\n\t\tthrow new TypeError(\"Empty color reference\");\n\t}\n\n\tif (isString(color)) {\n\t\tcolor = parse(color);\n\t}\n\n\t// Object fixup\n\tlet space = color.space || color.spaceId;\n\n\tif (!(space instanceof ColorSpace)) {\n\t\t// Convert string id to color space object\n\t\tcolor.space = ColorSpace.get(space);\n\t}\n\n\tif (color.alpha === undefined) {\n\t\tcolor.alpha = 1;\n\t}\n\n\treturn color;\n}\n", "import {type, parseCoordGrammar, serialize<PERSON><PERSON>ber, mapRange} from \"./util.js\";\nimport {getWhite} from \"./adapt.js\";\nimport hooks from \"./hooks.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Class to represent a color space\n */\nexport default class ColorSpace {\n\tconstructor (options) {\n\t\tthis.id = options.id;\n\t\tthis.name = options.name;\n\t\tthis.base = options.base ? ColorSpace.get(options.base) : null;\n\t\tthis.aliases = options.aliases;\n\n\t\tif (this.base) {\n\t\t\tthis.fromBase = options.fromBase;\n\t\t\tthis.toBase = options.toBase;\n\t\t}\n\n\t\t// Coordinate metadata\n\n\t\tlet coords = options.coords ?? this.base.coords;\n\n\t\tfor (let name in coords) {\n\t\t\tif (!(\"name\" in coords[name])) {\n\t\t\t\tcoords[name].name = name;\n\t\t\t}\n\t\t}\n\t\tthis.coords = coords;\n\n\t\t// White point\n\n\t\tlet white = options.white ?? this.base.white ?? \"D65\";\n\t\tthis.white = getWhite(white);\n\n\t\t// Sort out formats\n\n\t\tthis.formats = options.formats ?? {};\n\n\t\tfor (let name in this.formats) {\n\t\t\tlet format = this.formats[name];\n\t\t\tformat.type ||= \"function\";\n\t\t\tformat.name ||= name;\n\t\t}\n\n\t\tif (!this.formats.color?.id) {\n\t\t\tthis.formats.color = {\n\t\t\t\t...this.formats.color ?? {},\n\t\t\t\tid: options.cssId || this.id,\n\t\t\t};\n\t\t}\n\n\t\t// Gamut space\n\n\t\tif (options.gamutSpace) {\n\t\t\t// Gamut space explicitly specified\n\t\t\tthis.gamutSpace = options.gamutSpace === \"self\" ? this : ColorSpace.get(options.gamutSpace);\n\t\t}\n\t\telse {\n\t\t\t// No gamut space specified, calculate a sensible default\n\t\t\tif (this.isPolar) {\n\t\t\t\t// Do not check gamut through polar coordinates\n\t\t\t\tthis.gamutSpace = this.base;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.gamutSpace =  this;\n\t\t\t}\n\t\t}\n\n\t\t// Optimize inGamut for unbounded spaces\n\t\tif (this.gamutSpace.isUnbounded) {\n\t\t\tthis.inGamut = (coords, options) => {\n\t\t\t\treturn true;\n\t\t\t};\n\t\t}\n\n\t\t// Other stuff\n\t\tthis.referred = options.referred;\n\n\t\t// Compute ancestors and store them, since they will never change\n\t\tObject.defineProperty(this, \"path\", {\n\t\t\tvalue: getPath(this).reverse(),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t});\n\n\t\thooks.run(\"colorspace-init-end\", this);\n\t}\n\n\tinGamut (coords, {epsilon = ε} = {}) {\n\t\tif (!this.equals(this.gamutSpace)) {\n\t\t\tcoords = this.to(this.gamutSpace, coords);\n\t\t\treturn this.gamutSpace.inGamut(coords, {epsilon});\n\t\t}\n\n\t\tlet coordMeta = Object.values(this.coords);\n\n\t\treturn coords.every((c, i) => {\n\t\t\tlet meta = coordMeta[i];\n\n\t\t\tif (meta.type !== \"angle\" && meta.range) {\n\t\t\t\tif (Number.isNaN(c)) {\n\t\t\t\t\t// NaN is always in gamut\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tlet [min, max] = meta.range;\n\t\t\t\treturn (min === undefined || c >= min - epsilon)\n\t\t\t\t    && (max === undefined || c <= max + epsilon);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t});\n\t}\n\n\tget isUnbounded () {\n\t\treturn Object.values(this.coords).every(coord => !(\"range\" in coord));\n\t}\n\n\tget cssId () {\n\t\treturn this.formats?.color?.id || this.id;\n\t}\n\n\tget isPolar () {\n\t\tfor (let id in this.coords) {\n\t\t\tif (this.coords[id].type === \"angle\") {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tgetFormat (format) {\n\t\tif (typeof format === \"object\") {\n\t\t\tformat = processFormat(format, this);\n\t\t\treturn format;\n\t\t}\n\n\t\tlet ret;\n\t\tif (format === \"default\") {\n\t\t\t// Get first format\n\t\t\tret = Object.values(this.formats)[0];\n\t\t}\n\t\telse {\n\t\t\tret = this.formats[format];\n\t\t}\n\n\t\tif (ret) {\n\t\t\tret = processFormat(ret, this);\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Check if this color space is the same as another color space reference.\n\t * Allows proxying color space objects and comparing color spaces with ids.\n\t * @param {string | ColorSpace} space ColorSpace object or id to compare to\n\t * @returns {boolean}\n\t */\n\tequals (space) {\n\t\tif (!space) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this === space || this.id === space || this.id === space.id;\n\t}\n\n\tto (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (this.equals(space)) {\n\t\t\t// Same space, no change needed\n\t\t\treturn coords;\n\t\t}\n\n\t\t// Convert NaN to 0, which seems to be valid in every coordinate of every color space\n\t\tcoords = coords.map(c => Number.isNaN(c) ? 0 : c);\n\n\t\t// Find connection space = lowest common ancestor in the base tree\n\t\tlet myPath = this.path;\n\t\tlet otherPath = space.path;\n\n\t\tlet connectionSpace, connectionSpaceIndex;\n\n\t\tfor (let i = 0; i < myPath.length; i++) {\n\t\t\tif (myPath[i].equals(otherPath[i])) {\n\t\t\t\tconnectionSpace = myPath[i];\n\t\t\t\tconnectionSpaceIndex = i;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (!connectionSpace) {\n\t\t\t// This should never happen\n\t\t\tthrow new Error(`Cannot convert between color spaces ${this} and ${space}: no connection space was found`);\n\t\t}\n\n\t\t// Go up from current space to connection space\n\t\tfor (let i = myPath.length - 1; i > connectionSpaceIndex; i--) {\n\t\t\tcoords = myPath[i].toBase(coords);\n\t\t}\n\n\t\t// Go down from connection space to target space\n\t\tfor (let i = connectionSpaceIndex + 1; i < otherPath.length; i++) {\n\t\t\tcoords = otherPath[i].fromBase(coords);\n\t\t}\n\n\t\treturn coords;\n\t}\n\n\tfrom (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\treturn space.to(this, coords);\n\t}\n\n\ttoString () {\n\t\treturn `${this.name} (${this.id})`;\n\t}\n\n\tgetMinCoords () {\n\t\tlet ret = [];\n\n\t\tfor (let id in this.coords) {\n\t\t\tlet meta = this.coords[id];\n\t\t\tlet range = meta.range || meta.refRange;\n\t\t\tret.push(range?.min ?? 0);\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tstatic registry = {};\n\n\t// Returns array of unique color spaces\n\tstatic get all () {\n\t\treturn [...new Set(Object.values(ColorSpace.registry))];\n\t}\n\n\tstatic register (id, space) {\n\t\tif (arguments.length === 1) {\n\t\t\tspace = arguments[0];\n\t\t\tid = space.id;\n\t\t}\n\n\t\tspace = this.get(space);\n\n\t\tif (this.registry[id] && this.registry[id] !== space) {\n\t\t\tthrow new Error(`Duplicate color space registration: '${id}'`);\n\t\t}\n\t\tthis.registry[id] = space;\n\n\t\t// Register aliases when called without an explicit ID.\n\t\tif (arguments.length === 1 && space.aliases) {\n\t\t\tfor (let alias of space.aliases) {\n\t\t\t\tthis.register(alias, space);\n\t\t\t}\n\t\t}\n\n\t\treturn space;\n\t}\n\n\t/**\n\t * Lookup ColorSpace object by name\n\t * @param {ColorSpace | string} name\n\t */\n\tstatic get (space, ...alternatives) {\n\t\tif (!space || space instanceof ColorSpace) {\n\t\t\treturn space;\n\t\t}\n\n\t\tlet argType = type(space);\n\n\t\tif (argType === \"string\") {\n\t\t\t// It's a color space id\n\t\t\tlet ret = ColorSpace.registry[space.toLowerCase()];\n\n\t\t\tif (!ret) {\n\t\t\t\tthrow new TypeError(`No color space found with id = \"${space}\"`);\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tif (alternatives.length) {\n\t\t\treturn ColorSpace.get(...alternatives);\n\t\t}\n\n\t\tthrow new TypeError(`${space} is not a valid color space`);\n\t}\n\n\t/**\n\t * Get metadata about a coordinate of a color space\n\t *\n\t * @static\n\t * @param {Array | string} ref\n\t * @param {ColorSpace | string} [workingSpace]\n\t * @return {Object}\n\t */\n\tstatic resolveCoord (ref, workingSpace) {\n\t\tlet coordType = type(ref);\n\t\tlet space, coord;\n\n\t\tif (coordType === \"string\") {\n\t\t\tif (ref.includes(\".\")) {\n\t\t\t\t// Absolute coordinate\n\t\t\t\t[space, coord] = ref.split(\".\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Relative coordinate\n\t\t\t\t[space, coord] = [, ref];\n\t\t\t}\n\t\t}\n\t\telse if (Array.isArray(ref)) {\n\t\t\t[space, coord] = ref;\n\t\t}\n\t\telse {\n\t\t\t// Object\n\t\t\tspace = ref.space;\n\t\t\tcoord = ref.coordId;\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (!space) {\n\t\t\tspace = workingSpace;\n\t\t}\n\n\t\tif (!space) {\n\t\t\tthrow new TypeError(`Cannot resolve coordinate reference ${ref}: No color space specified and relative references are not allowed here`);\n\t\t}\n\n\t\tcoordType = type(coord);\n\n\t\tif (coordType === \"number\" || coordType === \"string\" && coord >= 0) {\n\t\t\t// Resolve numerical coord\n\t\t\tlet meta = Object.entries(space.coords)[coord];\n\n\t\t\tif (meta) {\n\t\t\t\treturn {space, id: meta[0], index: coord, ...meta[1]};\n\t\t\t}\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tlet normalizedCoord = coord.toLowerCase();\n\n\t\tlet i = 0;\n\t\tfor (let id in space.coords) {\n\t\t\tlet meta = space.coords[id];\n\n\t\t\tif (id.toLowerCase() === normalizedCoord || meta.name?.toLowerCase() === normalizedCoord) {\n\t\t\t\treturn {space, id, index: i, ...meta};\n\t\t\t}\n\n\t\t\ti++;\n\t\t}\n\n\t\tthrow new TypeError(`No \"${coord}\" coordinate found in ${space.name}. Its coordinates are: ${Object.keys(space.coords).join(\", \")}`);\n\t}\n\n\tstatic DEFAULT_FORMAT = {\n\t\ttype: \"functions\",\n\t\tname: \"color\",\n\t};\n}\n\nfunction getPath (space) {\n\tlet ret = [space];\n\n\tfor (let s = space; s = s.base;) {\n\t\tret.push(s);\n\t}\n\n\treturn ret;\n}\n\nfunction processFormat (format, {coords} = {}) {\n\tif (format.coords && !format.coordGrammar) {\n\t\tformat.type ||= \"function\";\n\t\tformat.name ||= \"color\";\n\n\t\t// Format has not been processed\n\t\tformat.coordGrammar = parseCoordGrammar(format.coords);\n\n\t\tlet coordFormats = Object.entries(coords).map(([id, coordMeta], i) => {\n\t\t\t// Preferred format for each coord is the first one\n\t\t\tlet outputType = format.coordGrammar[i][0];\n\n\t\t\tlet fromRange = coordMeta.range || coordMeta.refRange;\n\t\t\tlet toRange = outputType.range, suffix = \"\";\n\n\t\t\t// Non-strict equals intentional since outputType could be a string object\n\t\t\tif (outputType == \"<percentage>\") {\n\t\t\t\ttoRange = [0, 100];\n\t\t\t\tsuffix = \"%\";\n\t\t\t}\n\t\t\telse if (outputType == \"<angle>\") {\n\t\t\t\tsuffix = \"deg\";\n\t\t\t}\n\n\t\t\treturn  {fromRange, toRange, suffix};\n\t\t});\n\n\t\tformat.serializeCoords = (coords, precision) => {\n\t\t\treturn coords.map((c, i) => {\n\t\t\t\tlet {fromRange, toRange, suffix} = coordFormats[i];\n\n\t\t\t\tif (fromRange && toRange) {\n\t\t\t\t\tc = mapRange(fromRange, toRange, c);\n\t\t\t\t}\n\n\t\t\t\tc = serializeNumber(c, {precision, unit: suffix});\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t};\n\t}\n\n\treturn format;\n}\n", "import ColorSpace from \"../space.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d65\",\n\tname: \"XYZ D65\",\n\tcoords: {\n\t\tx: {name: \"X\"},\n\t\ty: {name: \"Y\"},\n\t\tz: {name: \"Z\"},\n\t},\n\twhite: \"D65\",\n\tformats: {\n\t\tcolor: {\n\t\t\tids: [\"xyz-d65\", \"xyz\"],\n\t\t},\n\t},\n\taliases: [\"xyz\"],\n});\n", "import ColorSpace from \"./space.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport adapt from \"./adapt.js\";\nimport XYZ_D65 from \"./spaces/xyz-d65.js\";\n\n/**\n * Convenience class for RGB color spaces\n * @extends {ColorSpace}\n */\nexport default class RGBColorSpace extends ColorSpace {\n\t/**\n\t * Creates a new RGB ColorSpace.\n\t * If coords are not specified, they will use the default RGB coords.\n\t * Instead of `fromBase()` and `toBase()` functions,\n\t * you can specify to/from XYZ matrices and have `toBase()` and `fromBase()` automatically generated.\n\t * @param {*} options - Same options as {@link ColorSpace} plus:\n\t * @param {number[][]} options.toXYZ_M - Matrix to convert to XYZ\n\t * @param {number[][]} options.fromXYZ_M - Matrix to convert from XYZ\n\t */\n\tconstructor (options) {\n\t\tif (!options.coords) {\n\t\t\toptions.coords = {\n\t\t\t\tr: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"Red\",\n\t\t\t\t},\n\t\t\t\tg: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t\tb: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif (!options.base) {\n\t\t\toptions.base = XYZ_D65;\n\t\t}\n\n\t\tif (options.toXYZ_M && options.fromXYZ_M) {\n\t\t\toptions.toBase ??= rgb => {\n\t\t\t\tlet xyz = multiplyMatrices(options.toXYZ_M, rgb);\n\n\t\t\t\tif (this.white !== this.base.white) {\n\t\t\t\t\t// Perform chromatic adaptation\n\t\t\t\t\txyz = adapt(this.white, this.base.white, xyz);\n\t\t\t\t}\n\n\t\t\t\treturn xyz;\n\t\t\t};\n\n\t\t\toptions.fromBase ??= xyz => {\n\t\t\t\txyz = adapt(this.base.white, this.white, xyz);\n\t\t\t\treturn multiplyMatrices(options.fromXYZ_M, xyz);\n\t\t\t};\n\t\t}\n\n\t\toptions.referred ??= \"display\";\n\n\t\tsuper(options);\n\t}\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\n/**\n * Get the coordinates of a color in any color space\n * @param {Color} color\n * @param {string | ColorSpace} [space = color.space] The color space to convert to. Defaults to the color's current space\n * @returns {number[]} The color coordinates in the given color space\n */\nexport default function getAll (color, space) {\n\tcolor = getColor(color);\n\n\tif (!space || color.space.equals(space)) {\n\t\t// No conversion needed\n\t\treturn color.coords.slice();\n\t}\n\n\tspace = ColorSpace.get(space);\n\treturn space.from(color);\n}\n", "import ColorSpace from \"./space.js\";\nimport getAll from \"./getAll.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function get (color, prop) {\n\tcolor = getColor(color);\n\n\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\tlet coords = getAll(color, space);\n\treturn coords[index];\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function setAll (color, space, coords) {\n\tcolor = getColor(color);\n\n\tspace = ColorSpace.get(space);\n\tcolor.coords = space.to(color.space, coords);\n\treturn color;\n}\n\nsetAll.returns = \"color\";\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\nimport get from \"./get.js\";\nimport getAll from \"./getAll.js\";\nimport setAll from \"./setAll.js\";\nimport {type} from \"./util.js\";\n\n// Set properties and return current instance\nexport default function set (color, prop, value) {\n\tcolor = getColor(color);\n\n\tif (arguments.length === 2 && type(arguments[1]) === \"object\") {\n\t\t// Argument is an object literal\n\t\tlet object = arguments[1];\n\t\tfor (let p in object) {\n\t\t\tset(color, p, object[p]);\n\t\t}\n\t}\n\telse {\n\t\tif (typeof value === \"function\") {\n\t\t\tvalue = value(get(color, prop));\n\t\t}\n\n\t\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\t\tlet coords = getAll(color, space);\n\t\tcoords[index] = value;\n\t\tsetAll(color, space, coords);\n\t}\n\n\treturn color;\n}\n\nset.returns = \"color\";\n", "import ColorSpace from \"../space.js\";\nimport adapt from \"../adapt.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d50\",\n\tname: \"XYZ D50\",\n\twhite: \"D50\",\n\tbase: XYZ_D65,\n\tfromBase: coords => adapt(XYZ_D65.white, \"D50\", coords),\n\ttoBase: coords => adapt(\"D50\", XYZ_D65.white, coords),\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d50 from \"./xyz-d50.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D50;\n\nexport default new ColorSpace({\n\tid: \"lab\",\n\tname: \"Lab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D50, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d50,\n\t// Convert D50-adapted XYX to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D50-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "export function constrain (angle) {\n\treturn ((angle % 360) + 360) % 360;\n}\n\nexport function adjust (arc, angles) {\n\tif (arc === \"raw\") {\n\t\treturn angles;\n\t}\n\n\tlet [a1, a2] = angles.map(constrain);\n\n\tlet angleDiff = a2 - a1;\n\n\tif (arc === \"increasing\") {\n\t\tif (angleDiff < 0) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\telse if (arc === \"decreasing\") {\n\t\tif (angleDiff > 0) {\n\t\t\ta1 += 360;\n\t\t}\n\t}\n\telse if (arc === \"longer\") {\n\t\tif (-180 < angleDiff && angleDiff < 180) {\n\t\t\tif (angleDiff > 0) {\n\t\t\t\ta1 += 360;\n\t\t\t}\n\t\t\telse {\n\t\t\t\ta2 += 360;\n\t\t\t}\n\t\t}\n\t}\n\telse if (arc === \"shorter\") {\n\t\tif (angleDiff > 180) {\n\t\t\ta1 += 360;\n\t\t}\n\t\telse if (angleDiff < -180) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\n\treturn [a1, a2];\n}\n", "import ColorSpace from \"../space.js\";\nimport Lab from \"./lab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lch\",\n\tname: \"<PERSON><PERSON>\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 150],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Lab,\n\tfromBase (Lab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = Lab;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // a\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // b\n\t\t];\n\t},\n\n\tformats: {\n\t\t\"lch\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import defaults from \"../defaults.js\";\nimport lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// deltaE2000 is a statistically significant improvement\n// and is recommended by the CIE and Idealliance\n// especially for color differences less than 10 deltaE76\n// but is wicked complicated\n// and many implementations have small errors!\n// DeltaE2000 is also discontinuous; in case this\n// matters to you, use deltaECMC instead.\n\nconst Gfactor = 25 ** 7;\nconst π = Math.PI;\nconst r2d = 180 / π;\nconst d2r = π / 180;\n\nfunction pow7 (x) {\n\t// Faster than x ** 7 or Math.pow(x, 7)\n\n\tconst x2 = x * x;\n\tconst x7 = x2 * x2 * x2 * x;\n\n\treturn x7;\n}\n\nexport default function (color, sample, {kL = 1, kC = 1, kH = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and the function parameter as the sample,\n\t// calculate deltaE 2000.\n\n\t// This implementation assumes the parametric\n\t// weighting factors kL, kC and kH\n\t// for the influence of viewing conditions\n\t// are all 1, as sadly seems typical.\n\t// kL should be increased for lightness texture or noise\n\t// and kC increased for chroma noise\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet C1 = lch.from(lab, [L1, a1, b1])[1];\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\tlet Cbar = (C1 + C2) / 2; // mean Chroma\n\n\t// calculate a-axis asymmetry factor from mean Chroma\n\t// this turns JND ellipses for near-neutral colors back into circles\n\tlet C7 = pow7(Cbar);\n\n\tlet G = 0.5 * (1 - Math.sqrt(C7 / (C7 + Gfactor)));\n\n\t// scale a axes by asymmetry factor\n\t// this by the way is why there is no Lab2000 colorspace\n\tlet adash1 = (1 + G) * a1;\n\tlet adash2 = (1 + G) * a2;\n\n\t// calculate new Chroma from scaled a and original b axes\n\tlet Cdash1 = Math.sqrt(adash1 ** 2 + b1 ** 2);\n\tlet Cdash2 = Math.sqrt(adash2 ** 2 + b2 ** 2);\n\n\t// calculate new hues, with zero hue for true neutrals\n\t// and in degrees, not radians\n\n\tlet h1 = (adash1 === 0 && b1 === 0) ? 0 : Math.atan2(b1, adash1);\n\tlet h2 = (adash2 === 0 && b2 === 0) ? 0 : Math.atan2(b2, adash2);\n\n\tif (h1 < 0) {\n\t\th1 += 2 * π;\n\t}\n\tif (h2 < 0) {\n\t\th2 += 2 * π;\n\t}\n\n\th1 *= r2d;\n\th2 *= r2d;\n\n\t// Lightness and Chroma differences; sign matters\n\tlet ΔL = L2 - L1;\n\tlet ΔC = Cdash2 - Cdash1;\n\n\t// Hue difference, getting the sign correct\n\tlet hdiff = h2 - h1;\n\tlet hsum = h1 + h2;\n\tlet habs = Math.abs(hdiff);\n\tlet Δh;\n\n\tif (Cdash1 * Cdash2 === 0) {\n\t\tΔh = 0;\n\t}\n\telse if (habs <= 180) {\n\t\tΔh = hdiff;\n\t}\n\telse if (hdiff > 180) {\n\t\tΔh = hdiff - 360;\n\t}\n\telse if (hdiff < -180) {\n\t\tΔh = hdiff + 360;\n\t}\n\telse {\n\t\tdefaults.warn(\"the unthinkable has happened\");\n\t}\n\n\t// weighted Hue difference, more for larger Chroma\n\tlet ΔH = 2 * Math.sqrt(Cdash2 * Cdash1) * Math.sin(Δh * d2r / 2);\n\n\t// calculate mean Lightness and Chroma\n\tlet Ldash = (L1 + L2) / 2;\n\tlet Cdash = (Cdash1 + Cdash2) / 2;\n\tlet Cdash7 = pow7(Cdash);\n\n\t// Compensate for non-linearity in the blue region of Lab.\n\t// Four possibilities for hue weighting factor,\n\t// depending on the angles, to get the correct sign\n\tlet hdash;\n\tif (Cdash1 * Cdash2 === 0) {\n\t\thdash = hsum;   // which should be zero\n\t}\n\telse if (habs <= 180) {\n\t\thdash = hsum / 2;\n\t}\n\telse if (hsum < 360) {\n\t\thdash = (hsum + 360) / 2;\n\t}\n\telse {\n\t\thdash = (hsum - 360) / 2;\n\t}\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor\n\t// a background with L=50 is assumed\n\tlet lsq = (Ldash - 50) ** 2;\n\tlet SL = 1 + ((0.015 * lsq) / Math.sqrt(20 + lsq));\n\n\t// SC Chroma factor, similar to those in CMC and deltaE 94 formulae\n\tlet SC = 1 + 0.045 * Cdash;\n\n\t// Cross term T for blue non-linearity\n\tlet T = 1;\n\tT -= (0.17 * Math.cos((     hdash - 30)  * d2r));\n\tT += (0.24 * Math.cos(  2 * hdash        * d2r));\n\tT += (0.32 * Math.cos(((3 * hdash) + 6)  * d2r));\n\tT -= (0.20 * Math.cos(((4 * hdash) - 63) * d2r));\n\n\t// SH Hue factor depends on Chroma,\n\t// as well as adjusted hue angle like deltaE94.\n\tlet SH = 1 + 0.015 * Cdash * T;\n\n\t// RT Hue rotation term compensates for rotation of JND ellipses\n\t// and Munsell constant hue lines\n\t// in the medium-high Chroma blue region\n\t// (Hue 225 to 315)\n\tlet Δθ = 30 * Math.exp(-1 * (((hdash - 275) / 25) ** 2));\n\tlet RC = 2 * Math.sqrt(Cdash7 / (Cdash7 + Gfactor));\n\tlet RT = -1 * Math.sin(2 * Δθ * d2r) * RC;\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (kL * SL)) ** 2;\n\tdE += (ΔC / (kC * SC)) ** 2;\n\tdE += (ΔH / (kH * SH)) ** 2;\n\tdE += RT * (ΔC / (kC * SC)) * (ΔH / (kH * SH));\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\n// Recalculated for consistent reference white\n// see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484\nconst XY<PERSON>toLMS_M = [\n\t[ 0.8190224379967030, 0.3619062600528904, -0.1288737815209879 ],\n\t[ 0.0329836539323885, 0.9292868615863434,  0.0361446663506424 ],\n\t[ 0.0481771893596242, 0.2642395317527308,  0.6335478284694309 ],\n];\n// inverse of XYZtoLMS_M\nconst LMStoXYZ_M = [\n\t[  1.2268798758459243, -0.5578149944602171,  0.2813910456659647 ],\n\t[ -0.0405757452148008,  1.1122868032803170, -0.0717110580655164 ],\n\t[ -0.0763729366746601, -0.4214933324022432,  1.5869240198367816 ],\n];\nconst LMStoLab_M = [\n\t[ 0.2104542683093140,  0.7936177747023054, -0.0040720430116193 ],\n\t[ 1.9779985324311684, -2.4285922420485799,  0.4505937096174110 ],\n\t[ 0.0259040424655478,  0.7827717124575296, -0.8086757549230774 ],\n];\n// LMStoIab_M inverted\nconst LabtoLMS_M = [\n\t[ 1.0000000000000000,  0.3963377773761749,  0.2158037573099136 ],\n\t[ 1.0000000000000000, -0.1055613458156586, -0.0638541728258133 ],\n\t[ 1.0000000000000000, -0.0894841775298119, -1.2914855480194092 ],\n];\n\nexport default new ColorSpace({\n\tid: \"oklab\",\n\tname: \"Oklab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t},\n\n\t// Note that XYZ is relative to D65\n\twhite: \"D65\",\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\t// non-linearity\n\t\tlet LMSg = LMS.map(val => Math.cbrt(val));\n\n\t\treturn multiplyMatrices(LMStoLab_M, LMSg);\n\n\t},\n\ttoBase (OKLab) {\n\t\t// move to LMS cone domain\n\t\tlet LMSg = multiplyMatrices(LabtoLMS_M, OKLab);\n\n\t\t// restore linearity\n\t\tlet LMS = LMSg.map(val => val ** 3);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n\n\tformats: {\n\t\t\"oklab\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in CIE Lab\n\nimport oklab from \"../spaces/oklab.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaEOK, term by term as root sum of squares\n\tlet [L1, a1, b1] = oklab.from(color);\n\tlet [L2, a2, b2] = oklab.from(sample);\n\tlet ΔL = L1 - L2;\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\treturn Math.sqrt(ΔL ** 2 + Δa ** 2 + Δb ** 2);\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Check if a color is in gamut of either its own or another color space\n * @return {Boolean} Is the color in gamut?\n */\nexport default function inGamut (color, space, {epsilon = ε} = {}) {\n\tcolor = getColor(color);\n\n\tif (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tlet coords = color.coords;\n\n\tif (space !== color.space) {\n\t\tcoords = space.from(color);\n\t}\n\n\treturn space.inGamut(coords, {epsilon});\n}\n", "export default function clone (color) {\n\treturn {\n\t\tspace: color.space,\n\t\tcoords: color.coords.slice(),\n\t\talpha: color.alpha,\n\t};\n}\n", "import ColorSpace from \"./space.js\";\n\n/**\n * Euclidean distance of colors in an arbitrary color space\n */\nexport default function distance (color1, color2, space = \"lab\") {\n\tspace = ColorSpace.get(space);\n\n\t// Assume getColor() is called on color in space.from()\n\tlet coords1 = space.from(color1);\n\tlet coords2 = space.from(color2);\n\n\treturn Math.sqrt(coords1.reduce((acc, c1, i) => {\n\t\tlet c2 = coords2[i];\n\t\tif (isNaN(c1) || isNaN(c2)) {\n\t\t\treturn acc;\n\t\t}\n\n\t\treturn acc + (c2 - c1) ** 2;\n\t}, 0));\n}\n", "import distance from \"../distance.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function deltaE76 (color, sample) {\n\t// Assume getColor() is called in the distance function\n\treturn distance(color, sample, \"lab\");\n}\n", "import lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// CMC by the Color Measurement Committee of the\n// Bradford Society of Dyeists and Colorsts, 1994.\n// Uses LCH rather than Lab,\n// with different weights for L, C and H differences\n// A nice increase in accuracy for modest increase in complexity\nconst π = Math.PI;\nconst d2r = π / 180;\n\nexport default function (color, sample, {l = 2, c = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE CMC.\n\n\t// This implementation assumes the parametric\n\t// weighting factors l:c are 2:1\n\t// which is typical for non-textile uses.\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet [, C1, H1] = lch.from(lab, [L1, a1, b1]);\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// let [L1, a1, b1] = color.getAll(lab);\n\t// let C1 = color.get(\"lch.c\");\n\t// let H1 = color.get(\"lch.h\");\n\t// let [L2, a2, b2] = sample.getAll(lab);\n\t// let C2 = sample.get(\"lch.c\");\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\t// we don't need H2 as ΔH is calculated from Δa, Δb and ΔC\n\n\t// Lightness and Chroma differences\n\t// These are (color - sample), unlike deltaE2000\n\tlet ΔL = L1 - L2;\n\tlet ΔC = C1 - C2;\n\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\n\t// weighted Hue difference, less for larger Chroma difference\n\n\tlet H2 = (Δa ** 2) + (Δb ** 2) - (ΔC ** 2);\n\t// due to roundoff error it is possible that, for zero a and b,\n\t// ΔC > Δa + Δb is 0, resulting in attempting\n\t// to take the square root of a negative number\n\n\t// trying instead the equation from Industrial Color Physics\n\t// By Georg A. Klein\n\n\t// let ΔH = ((a1 * b2) - (a2 * b1)) / Math.sqrt(0.5 * ((C2 * C1) + (a2 * a1) + (b2 * b1)));\n\t// console.log({ΔH});\n\t// This gives the same result to 12 decimal places\n\t// except it sometimes NaNs when trying to root a negative number\n\n\t// let ΔH = Math.sqrt(H2); we never actually use the root, it gets squared again!!\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor, depends entirely on L1 not L2\n\tlet SL = 0.511;\t// linear portion of the Y to L transfer function\n\tif (L1 >= 16) {\t// cubic portion\n\t\tSL = (0.040975 * L1) / (1 + 0.01765 * L1);\n\t}\n\n\t// SC Chroma factor\n\tlet SC = ((0.0638 * C1) / (1 + 0.0131 * C1)) + 0.638;\n\n\t// Cross term T for blue non-linearity\n\tlet T;\n\tif (Number.isNaN(H1)) {\n\t\tH1 = 0;\n\t}\n\n\tif (H1 >= 164 && H1 <= 345) {\n\t\tT = 0.56 + Math.abs(0.2 * Math.cos((H1 + 168) * d2r));\n\t}\n\telse {\n\t\tT = 0.36 + Math.abs(0.4 * Math.cos((H1 + 35) * d2r));\n\t}\n\t// console.log({T});\n\n\t// SH Hue factor also depends on C1,\n\tlet C4 = Math.pow(C1, 4);\n\tlet F = Math.sqrt(C4 / (C4 + 1900));\n\tlet SH = SC * ((F * T) + 1 - F);\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (l * SL)) ** 2;\n\tdE += (ΔC / (c * SC)) ** 2;\n\tdE += (H2 / (SH ** 2));\n\t// dE += (ΔH / SH)  ** 2;\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nconst Yw = 203;\t// absolute luminance of media white\n\nexport default new ColorSpace({\n// Absolute CIE XYZ, with a D65 whitepoint,\n// as used in most HDR colorspaces as a starting point.\n// SDR spaces are converted per BT.2048\n// so that diffuse, media white is 203 cd/m²\n\tid: \"xyz-abs-d65\",\n\tcssId: \"--xyz-abs-d65\",\n\tname: \"Absolute XYZ D65\",\n\tcoords: {\n\t\tx: {\n\t\t\trefRange: [0, 9504.7],\n\t\t\tname: \"Xa\",\n\t\t},\n\t\ty: {\n\t\t\trefRange: [0, 10000],\n\t\t\tname: \"Ya\",\n\t\t},\n\t\tz: {\n\t\t\trefRange: [0, 10888.3],\n\t\t\tname: \"Za\",\n\t\t},\n\t},\n\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// Make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\treturn XYZ.map (v => Math.max(v * Yw, 0));\n\t},\n\ttoBase (AbsXYZ) {\n\t\t// Convert to media-white relative XYZ\n\t\treturn AbsXYZ.map(v => Math.max(v / Yw, 0));\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst b = 1.15;\nconst g = 0.66;\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\nconst p = 1.7 * 2523 / (2 ** 5);\nconst pinv = (2 ** 5) / (1.7 * 2523);\nconst d = -0.56;\nconst d0 = 1.6295499532821566E-11;\n\nconst XYZtoCone_M = [\n\t[  0.41478972, 0.579999,  0.0146480 ],\n\t[ -0.2015100,  1.120649,  0.0531008 ],\n\t[ -0.0166008,  0.264800,  0.6684799 ],\n];\n// XYZtoCone_M inverted\nconst Coneto<PERSON>Y<PERSON>_M = [\n\t[  1.9242264357876067,  -1.0047923125953657,  0.037651404030618   ],\n\t[  0.35031676209499907,  0.7264811939316552, -0.06538442294808501 ],\n\t[ -0.09098281098284752, -0.3127282905230739,  1.5227665613052603  ],\n];\nconst ConetoIab_M = [\n\t[  0.5,       0.5,       0        ],\n\t[  3.524000, -4.066708,  0.542708 ],\n\t[  0.199076,  1.096799, -1.295875 ],\n];\n// ConetoIab_M inverted\nconst IabtoCone_M = [\n\t[ 1,                   0.1386050432715393,   0.05804731615611886 ],\n\t[ 0.9999999999999999, -0.1386050432715393,  -0.05804731615611886 ],\n\t[ 0.9999999999999998, -0.09601924202631895, -0.8118918960560388  ],\n];\n\nexport default new ColorSpace({\n\tid: \"jzazbz\",\n\tname: \"Jzazbz\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Jz\",\n\t\t},\n\t\taz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t\tbz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// First make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\t// BT.2048 says media white Y=203 at PQ 58\n\n\t\tlet [ Xa, Ya, Za ] = XYZ;\n\n\t\t// modify X and Y\n\t\tlet Xm = (b * Xa) - ((b - 1) * Za);\n\t\tlet Ym = (g * Ya) - ((g - 1) * Xa);\n\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoCone_M, [ Xm, Ym, Za ]);\n\n\t\t// PQ-encode LMS\n\t\tlet PQLMS = LMS.map (function (val) {\n\t\t\tlet num = c1 + (c2 * ((val / 10000) ** n));\n\t\t\tlet denom = 1 + (c3 * ((val / 10000) ** n));\n\n\t\t\treturn (num / denom)  ** p;\n\t\t});\n\n\t\t// almost there, calculate Iz az bz\n\t\tlet [ Iz, az, bz] = multiplyMatrices(ConetoIab_M, PQLMS);\n\t\t// console.log({Iz, az, bz});\n\n\t\tlet Jz = ((1 + d) * Iz) / (1 + (d * Iz)) - d0;\n\t\treturn [Jz, az, bz];\n\t},\n\ttoBase (Jzazbz) {\n\t\tlet [Jz, az, bz] = Jzazbz;\n\t\tlet Iz = (Jz + d0) / (1 + d - d * (Jz + d0));\n\n\t\t// bring into LMS cone domain\n\t\tlet PQLMS = multiplyMatrices(IabtoCone_M, [ Iz, az, bz ]);\n\n\t\t// convert from PQ-coded to linear-light\n\t\tlet LMS = PQLMS.map(function (val) {\n\t\t\tlet num = (c1 - (val ** pinv));\n\t\t\tlet denom = (c3 * (val ** pinv)) - c2;\n\t\t\tlet x = 10000 * ((num / denom) ** ninv);\n\n\t\t\treturn (x); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\n\t\t// modified abs XYZ\n\t\tlet [ Xm, Ym, Za ] = multiplyMatrices(ConetoXYZ_M, LMS);\n\n\t\t// restore standard D50 relative XYZ, relative to media white\n\t\tlet Xa = (Xm + ((b - 1) * Za)) / b;\n\t\tlet Ya = (Ym + ((g - 1) * Xa)) / g;\n\t\treturn [ Xa, Ya, Za ];\n\t},\n\n\tformats: {\n\t\t// https://drafts.csswg.org/css-color-hdr/#Jzazbz\n\t\t\"color\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport <PERSON><PERSON><PERSON>b<PERSON> from \"./jzazbz.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"jzczhz\",\n\tname: \"J<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\tcz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\thz: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Jzazbz,\n\tfromBase (jzazbz) {\n\t\t// Convert to polar form\n\t\tlet [Jz, az, bz] = jzazbz;\n\t\tlet hue;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(az) < ε && Math.abs(bz) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(bz, az) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tJz, // Jz is still Jz\n\t\t\tMath.sqrt(az ** 2 + bz ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (jzczhz) {\n\t\t// Convert from polar form\n\t\t// debugger;\n\t\treturn [\n\t\t\tjzczhz[0], // Jz is still Jz\n\t\t\tjzczhz[1] * Math.cos(jzczhz[2] * Math.PI / 180), // az\n\t\t\tjzczhz[1] * Math.sin(jzczhz[2] * Math.PI / 180),  // bz\n\t\t];\n\t},\n});\n", "import jzczhz from \"../spaces/jzczhz.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// Uses JzCzHz, which has improved perceptual uniformity\n// and thus a simple Euclidean root-sum of ΔL² ΔC² ΔH²\n// gives good results.\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in JzCzHz.\n\tlet [Jz1, Cz1, Hz1] = jzczhz.from(color);\n\tlet [Jz2, Cz2, Hz2] = jzczhz.from(sample);\n\n\t// Lightness and Chroma differences\n\t// sign does not matter as they are squared.\n\tlet ΔJ = Jz1 - Jz2;\n\tlet ΔC = Cz1 - Cz2;\n\n\t// length of chord for ΔH\n\tif ((Number.isNaN(Hz1)) && (Number.isNaN(Hz2))) {\n\t\t// both undefined hues\n\t\tHz1 = 0;\n\t\tHz2 = 0;\n\t}\n\telse if (Number.isNaN(Hz1)) {\n\t\t// one undefined, set to the defined hue\n\t\tHz1 = Hz2;\n\t}\n\telse if (Number.isNaN(Hz2)) {\n\t\tHz2 = Hz1;\n\t}\n\n\tlet Δh = Hz1 - Hz2;\n\tlet ΔH = 2 * Math.sqrt(Cz1 * Cz2) * Math.sin((Δh / 2) * (Math.PI / 180));\n\n\treturn Math.sqrt(ΔJ ** 2 + ΔC ** 2 + ΔH ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst c1 = 3424 / 4096;\nconst c2 = 2413 / 128;\nconst c3 = 2392 / 128;\nconst m1 = 2610 / 16384;\nconst m2 = 2523 / 32;\nconst im1 = 16384 / 2610;\nconst im2 = 32 / 2523;\n\n// The matrix below includes the 4% crosstalk components\n// and is from the Dolby \"What is ICtCp\" paper\"\nconst XYZtoLMS_M = [\n\t[  0.3592832590121217,  0.6976051147779502, -0.0358915932320290 ],\n\t[ -0.1920808463704993,  1.1004767970374321,  0.0753748658519118 ],\n\t[  0.0070797844607479,  0.0748396662186362,  0.8433265453898765 ],\n];\n// linear-light Rec.2020 to LMS, again with crosstalk\n// rational terms from <PERSON>,\n// Encoding High Dynamic Range andWide Color Gamut Imagery, p.97\n// and ITU-R BT.2124-0 p.2\n/*\nconst Rec2020toLMS_M = [\n\t[ 1688 / 4096,  2146 / 4096,   262 / 4096 ],\n\t[  683 / 4096,  2951 / 4096,   462 / 4096 ],\n\t[   99 / 4096,   309 / 4096,  3688 / 4096 ]\n];\n*/\n// this includes the Ebner LMS coefficients,\n// the rotation, and the scaling to [-0.5,0.5] range\n// rational terms from Fröhlich p.97\n// and ITU-R BT.2124-0 pp.2-3\nconst LMStoIPT_M = [\n\t[  2048 / 4096,   2048 / 4096,       0      ],\n\t[  6610 / 4096, -13613 / 4096,  7003 / 4096 ],\n\t[ 17933 / 4096, -17390 / 4096,  -543 / 4096 ],\n];\n\n// inverted matrices, calculated from the above\nconst IPTtoLMS_M = [\n\t[ 0.9999999999999998,  0.0086090370379328,  0.1110296250030260 ],\n\t[ 0.9999999999999998, -0.0086090370379328, -0.1110296250030259 ],\n\t[ 0.9999999999999998,  0.5600313357106791, -0.3206271749873188 ],\n];\n/*\nconst LMStoRec2020_M = [\n\t[ 3.4375568932814012112,   -2.5072112125095058195,   0.069654319228104608382],\n\t[-0.79142868665644156125,   1.9838372198740089874,  -0.19240853321756742626 ],\n\t[-0.025646662911506476363, -0.099240248643945566751, 1.1248869115554520431  ]\n];\n*/\nconst LMStoXYZ_M = [\n\t[  2.0701522183894223, -1.3263473389671563,  0.2066510476294053 ],\n\t[  0.3647385209748072,  0.6805660249472273, -0.0453045459220347 ],\n\t[ -0.0497472075358123, -0.0492609666966131,  1.1880659249923042 ],\n];\n\n// Only the PQ form of ICtCp is implemented here. There is also an HLG form.\n// from Dolby, \"WHAT IS ICTCP?\"\n// https://professional.dolby.com/siteassets/pdfs/ictcp_dolbywhitepaper_v071.pdf\n// and\n// Dolby, \"Perceptual Color Volume\n// Measuring the Distinguishable Colors of HDR and WCG Displays\"\n// https://professional.dolby.com/siteassets/pdfs/dolby-vision-measuring-perceptual-color-volume-v7.1.pdf\nexport default new ColorSpace({\n\tid: \"ictcp\",\n\tname: \"ICTCP\",\n\t// From BT.2100-2 page 7:\n\t// During production, signal values are expected to exceed the\n\t// range E′ = [0.0 : 1.0]. This provides processing headroom and avoids\n\t// signal degradation during cascaded processing. Such values of E′,\n\t// below 0.0 or exceeding 1.0, should not be clipped during production\n\t// and exchange.\n\t// Values below 0.0 should not be clipped in reference displays (even\n\t// though they represent “negative” light) to allow the black level of\n\t// the signal (LB) to be properly set using test signals known as “PLUGE”\n\tcoords: {\n\t\ti: {\n\t\t\trefRange: [0, 1],\t// Constant luminance,\n\t\t\tname: \"I\",\n\t\t},\n\t\tct: {\n\t\t\trefRange: [-0.5, 0.5],\t// Full BT.2020 gamut in range [-0.5, 0.5]\n\t\t\tname: \"CT\",\n\t\t},\n\t\tcp: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t\tname: \"CP\",\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\treturn LMStoICtCp(LMS);\n\t},\n\ttoBase (ICtCp) {\n\t\tlet LMS = ICtCptoLMS(ICtCp);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n});\n\nfunction LMStoICtCp (LMS) {\n\t// apply the PQ EOTF\n\t// we can't ever be dividing by zero because of the \"1 +\" in the denominator\n\tlet PQLMS = LMS.map (function (val) {\n\t\tlet num = c1 + (c2 * ((val / 10000) ** m1));\n\t\tlet denom = 1 + (c3 * ((val / 10000) ** m1));\n\n\t\treturn (num / denom)  ** m2;\n\t});\n\n\t// LMS to IPT, with rotation for Y'C'bC'r compatibility\n\treturn multiplyMatrices(LMStoIPT_M, PQLMS);\n}\n\nfunction ICtCptoLMS (ICtCp) {\n\tlet PQLMS = multiplyMatrices(IPTtoLMS_M, ICtCp);\n\n\t// From BT.2124-0 Annex 2 Conversion 3\n\tlet LMS = PQLMS.map (function (val) {\n\t\tlet num  = Math.max((val ** im2) - c1, 0);\n\t\tlet denom = (c2 - (c3 * (val ** im2)));\n\t\treturn 10000 * ((num / denom) ** im1);\n\t});\n\n\treturn LMS;\n}\n", "import ictcp from \"../spaces/ictcp.js\";\nimport getColor from \"../getColor.js\";\n\n// Delta E in ICtCp space,\n// which the ITU calls Delta E ITP, which is shorter\n// formulae from ITU Rec. ITU-R BT.2124-0\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in ICtCp\n\t// which is simply the Euclidean distance\n\n\tlet [ I1, T1, P1 ] = ictcp.from(color);\n\tlet [ I2, T2, P2 ] = ictcp.from(sample);\n\n\t// the 0.25 factor is to undo the encoding scaling in Ct\n\t// the 720 is so that 1 deltaE = 1 JND\n\t// per  ITU-R BT.2124-0 p.3\n\n\treturn 720 * Math.sqrt((I1 - I2) ** 2 + (0.25 * (T1 - T2) ** 2) + (P1 - P2) ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices, interpolate, copySign, spow, zdiv, bisectLeft} from \"../util.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst adaptedCoef = 0.42;\nconst adaptedCoefInv = 1 / adaptedCoef;\nconst tau = 2 * Math.PI;\n\nconst cat16 = [\n\t[  0.401288,  0.650173, -0.051461 ],\n\t[ -0.250268,  1.204414,  0.045854 ],\n\t[ -0.002079,  0.048952,  0.953127 ],\n];\n\nconst cat16Inv = [\n\t[1.8620678550872327, -1.0112546305316843, 0.14918677544445175],\n\t[0.38752654323613717, 0.6214474419314753, -0.008973985167612518],\n\t[-0.015841498849333856, -0.03412293802851557, 1.0499644368778496],\n];\n\nconst m1 = [\n\t[460.0, 451.0, 288.0],\n\t[460.0, -891.0, -261.0],\n\t[460.0, -220.0, -6300.0],\n];\n\nconst surroundMap = {\n\tdark: [0.8, 0.525, 0.8],\n\tdim: [0.9, 0.59, 0.9],\n\taverage: [1, 0.69, 1],\n};\n\nconst hueQuadMap = {\n\t// Red, Yellow, Green, Blue, Red\n\th: [20.14, 90.00, 164.25, 237.53, 380.14],\n\te: [0.8, 0.7, 1.0, 1.2, 0.8],\n\tH: [0.0, 100.0, 200.0, 300.0, 400.0],\n};\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\n\nexport function adapt (coords, fl) {\n\tconst temp = coords.map(c => {\n\t\tconst x = spow(fl * Math.abs(c) * 0.01, adaptedCoef);\n\t\treturn 400 * copySign(x, c) / (x + 27.13);\n\t});\n\treturn temp;\n}\n\nexport function unadapt (adapted, fl) {\n\tconst constant = 100 / fl * (27.13 ** adaptedCoefInv);\n\treturn adapted.map(c => {\n\t\tconst cabs = Math.abs(c);\n\t\treturn copySign(constant * spow(cabs / (400 - cabs), adaptedCoefInv), c);\n\t});\n}\n\nexport function hueQuadrature (h) {\n\tlet hp = constrain(h);\n\tif (hp <= hueQuadMap.h[0]) {\n\t\thp += 360;\n\t}\n\n\tconst i = bisectLeft(hueQuadMap.h, hp) - 1;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\tconst Hi = hueQuadMap.H[i];\n\n\tconst t = (hp - hi) / ei;\n\treturn Hi + (100 * t) / (t + (hii - hp) / eii);\n}\n\nexport function invHueQuadrature (H) {\n\tlet Hp = ((H % 400 + 400) % 400);\n\tconst i = Math.floor(0.01 * Hp);\n\tHp = Hp % 100;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\n\treturn constrain(\n\t\t(Hp * (eii * hi - ei * hii) - 100 * hi * eii) /\n\t\t(Hp * (eii - ei) - 100 * eii),\n\t);\n}\n\nexport function environment (\n\trefWhite,\n\tadaptingLuminance,\n\tbackgroundLuminance,\n\tsurround,\n\tdiscounting,\n) {\n\n\tconst env = {};\n\n\tenv.discounting = discounting;\n\tenv.refWhite = refWhite;\n\tenv.surround = surround;\n\tconst xyzW = refWhite.map(c => {\n\t\treturn c * 100;\n\t});\n\n\t// The average luminance of the environment in `cd/m^2cd/m` (a.k.a. nits)\n\tenv.la = adaptingLuminance;\n\t// The relative luminance of the nearby background\n\tenv.yb = backgroundLuminance;\n\t// Absolute luminance of the reference white.\n\tconst yw = xyzW[1];\n\n\t// Cone response for reference white\n\tconst rgbW = multiplyMatrices(cat16, xyzW);\n\n\t// Surround: dark, dim, and average\n\tsurround = surroundMap[env.surround];\n\tconst f = surround[0];\n\tenv.c = surround[1];\n\tenv.nc = surround[2];\n\n\tconst k = 1 / (5 * env.la + 1);\n\tconst k4 = k ** 4;\n\n\t// Factor of luminance level adaptation\n\tenv.fl = (k4 * env.la + 0.1 * (1 - k4) * (1 - k4) * Math.cbrt(5 * env.la));\n\tenv.flRoot = env.fl ** 0.25;\n\n\tenv.n = env.yb / yw;\n\tenv.z = 1.48 + Math.sqrt(env.n);\n\tenv.nbb = 0.725 * (env.n ** -0.2);\n\tenv.ncb = env.nbb;\n\n\t// Degree of adaptation calculating if not discounting\n\t// illuminant (assumed eye is fully adapted)\n\tconst d = (discounting) ?\n\t\t1 :\n\t\tMath.max(\n\t\t\tMath.min(f * (1 - 1 / 3.6 * Math.exp((-env.la - 42) / 92)), 1),\n\t\t\t0,\n\t\t);\n\tenv.dRgb = rgbW.map(c => {\n\t\treturn interpolate(1, yw / c, d);\n\t});\n\tenv.dRgbInv = env.dRgb.map(c => {\n\t\treturn 1 / c;\n\t});\n\n\t// Achromatic response\n\tconst rgbCW = rgbW.map((c, i) => {\n\t\treturn c * env.dRgb[i];\n\t});\n\tconst rgbAW = adapt(rgbCW, env.fl);\n\tenv.aW = env.nbb * (2 * rgbAW[0] + rgbAW[1] + 0.05 * rgbAW[2]);\n\n\t// console.log(env);\n\n\treturn env;\n}\n\n// Pre-calculate everything we can with the viewing conditions\nconst viewingConditions = environment(\n\twhite,\n\t64 / Math.PI * 0.2, 20,\n\t\"average\",\n\tfalse,\n);\n\nexport function fromCam16 (cam16, env) {\n\n\t// These check ensure one, and only one attribute for a\n\t// given category is provided.\n\tif (!((cam16.J !== undefined) ^ (cam16.Q !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'J' or 'Q'\");\n\t}\n\n\tif (!((cam16.C !== undefined) ^ (cam16.M !== undefined) ^ (cam16.s !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'C', 'M' or 's'\");\n\t}\n\n\t// Hue is absolutely required\n\tif (!((cam16.h !== undefined) ^ (cam16.H !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'h' or 'H'\");\n\t}\n\n\t// Black\n\tif (cam16.J === 0.0 || cam16.Q === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Break hue into Cartesian components\n\tlet hRad = 0.0;\n\tif (cam16.h !== undefined) {\n\t\thRad = constrain(cam16.h) * deg2rad;\n\t}\n\telse {\n\t\thRad = invHueQuadrature(cam16.H) * deg2rad;\n\t}\n\n\tconst cosh = Math.cos(hRad);\n\tconst sinh = Math.sin(hRad);\n\n\t// Calculate `Jroot` from one of the lightness derived coordinates.\n\tlet Jroot = 0.0;\n\tif (cam16.J !== undefined) {\n\t\tJroot = spow(cam16.J, 1 / 2) * 0.1;\n\t}\n\telse if (cam16.Q !== undefined) {\n\t\tJroot = 0.25 * env.c * cam16.Q / ((env.aW + 4) * env.flRoot);\n\t}\n\n\t// Calculate the `t` value from one of the chroma derived coordinates\n\tlet alpha = 0.0;\n\tif (cam16.C !== undefined) {\n\t\talpha = cam16.C / Jroot;\n\t}\n\telse if (cam16.M !== undefined) {\n\t\talpha = (cam16.M / env.flRoot) / Jroot;\n\t}\n\telse if (cam16.s !== undefined) {\n\t\talpha = 0.0004 * (cam16.s ** 2) * (env.aW + 4) / env.c;\n\t}\n\tconst t = spow(\n\t\talpha * Math.pow(1.64 - Math.pow(0.29, env.n), -0.73),\n\t\t10 / 9,\n\t);\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\t// Achromatic response\n\tconst A = env.aW * spow(Jroot, 2 / env.c / env.z);\n\n\t// Calculate red-green and yellow-blue components\n\tconst p1 = 5e4 / 13 * env.nc * env.ncb * et;\n\tconst p2 = A / env.nbb;\n\tconst r = (\n\t\t23 * (p2 + 0.305) *\n\t\tzdiv(t, 23 * p1 + t * (11 * cosh + 108 * sinh))\n\t);\n\tconst a = r * cosh;\n\tconst b = r * sinh;\n\n\t// Calculate back from cone response to XYZ\n\tconst rgb_c = unadapt(\n\t\tmultiplyMatrices(m1, [p2, a, b]).map(c => {\n\t\t\treturn c * 1 / 1403;\n\t\t}),\n\t\tenv.fl,\n\t);\n\treturn multiplyMatrices(\n\t\tcat16Inv,\n\t\trgb_c.map((c, i) => {\n\t\t\treturn c * env.dRgbInv[i];\n\t\t}),\n\t).map(c => {\n\t\treturn c / 100;\n\t});\n}\n\n\nexport function toCam16 (xyzd65, env) {\n\t// Cone response\n\tconst xyz100 = xyzd65.map(c => {\n\t\treturn c * 100;\n\t});\n\tconst rgbA = adapt(\n\t\tmultiplyMatrices(cat16, xyz100).map((c, i) => {\n\t\t\treturn c * env.dRgb[i];\n\t\t}),\n\t\tenv.fl,\n\t);\n\n\t// Calculate hue from red-green and yellow-blue components\n\tconst a = rgbA[0] + (-12 * rgbA[1] + rgbA[2]) / 11;\n\tconst b = (rgbA[0] + rgbA[1] - 2 * rgbA[2]) / 9;\n\tconst hRad = ((Math.atan2(b, a) % tau) + tau) % tau;\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\tconst t = (\n\t\t5e4 / 13 * env.nc * env.ncb *\n\t\tzdiv(\n\t\t\tet * Math.sqrt(a ** 2 + b ** 2),\n\t\t\trgbA[0] + rgbA[1] + 1.05 * rgbA[2] + 0.305,\n\t\t)\n\t);\n\tconst alpha = spow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, env.n), 0.73);\n\n\t// Achromatic response\n\tconst A = env.nbb * (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]);\n\n\tconst Jroot = spow(A / env.aW, 0.5 * env.c * env.z);\n\n\t// Lightness\n\tconst J = 100 * spow(Jroot, 2);\n\n\t// Brightness\n\tconst Q = (4 / env.c * Jroot * (env.aW + 4) * env.flRoot);\n\n\t// Chroma\n\tconst C = alpha * Jroot;\n\n\t// Colorfulness\n\tconst M = C * env.flRoot;\n\n\t// Hue\n\tconst h = constrain(hRad * rad2deg);\n\n\t// Hue quadrature\n\tconst H = hueQuadrature(h);\n\n\t// Saturation\n\tconst s = 50 * spow(env.c * alpha / (env.aW + 4), 1 / 2);\n\n\t// console.log({J: J, C: C, h: h, s: s, Q: Q, M: M, H: H});\n\n\treturn {J: J, C: C, h: h, s: s, Q: Q, M: M, H: H};\n}\n\n\n// Provided as a way to directly evaluate the CAM16 model\n// https://observablehq.com/@jrus/cam16: reference implementation\n// https://arxiv.org/pdf/1802.06067.pdf: Nico Schlömer\n// https://onlinelibrary.wiley.com/doi/pdf/10.1002/col.22324: hue quadrature\n// https://www.researchgate.net/publication/318152296_Comprehensive_color_solutions_CAM16_CAT16_and_CAM16-UCS\n// Results compared against: https://github.com/colour-science/colour\nexport default new ColorSpace({\n\tid: \"cam16-jmh\",\n\tcssId: \"--cam16-jmh\",\n\tname: \"CAM16-JMh\",\n\tcoords: {\n\t\tj: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"J\",\n\t\t},\n\t\tm: {\n\t\t\trefRange: [0, 105.0],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\tconst cam16 = toCam16(xyz, viewingConditions);\n\t\treturn [cam16.J, cam16.M, cam16.h];\n\t},\n\ttoBase (cam16) {\n\t\treturn fromCam16(\n\t\t\t{J: cam16[0], M: cam16[1], h: cam16[2]},\n\t\t\tviewingConditions,\n\t\t);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {fromCam16, toCam16, environment} from \"./cam16.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nfunction toLstar (y) {\n\t// Convert XYZ Y to L*\n\n\tconst fy = (y > ε) ? Math.cbrt(y) : (κ * y + 16) / 116;\n\treturn (116.0 * fy) - 16.0;\n}\n\nfunction fromLstar (lstar) {\n\t// Convert L* back to XYZ Y\n\n\treturn (lstar > 8) ?  Math.pow((lstar + 16) / 116, 3) : lstar / κ;\n}\n\nfunction fromHct (coords, env) {\n\t// Use Newton's method to try and converge as quick as possible or\n\t// converge as close as we can. While the requested precision is achieved\n\t// most of the time, it may not always be achievable. Especially past the\n\t// visible spectrum, the algorithm will likely struggle to get the same\n\t// precision. If, for whatever reason, we cannot achieve the accuracy we\n\t// seek in the allotted iterations, just return the closest we were able to\n\t// get.\n\n\tlet [h, c, t] = coords;\n\tlet xyz = [];\n\tlet j = 0;\n\n\t// Shortcut out for black\n\tif (t === 0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Calculate the Y we need to target\n\tlet y = fromLstar(t);\n\n\t// A better initial guess yields better results. Polynomials come from\n\t// curve fitting the T vs J response.\n\tif (t > 0) {\n\t\tj = 0.00379058511492914 * t ** 2 + 0.608983189401032 * t + 0.9155088574762233;\n\t}\n\telse {\n\t\tj = 9.514440756550361e-06 * t ** 2 + 0.08693057439788597 * t - 21.928975842194614;\n\t}\n\n\t// Threshold of how close is close enough, and max number of attempts.\n\t// More precision and more attempts means more time spent iterating. Higher\n\t// required precision gives more accuracy but also increases the chance of\n\t// not hitting the goal. 2e-12 allows us to convert round trip with\n\t// reasonable accuracy of six decimal places or more.\n\tconst threshold = 2e-12;\n\tconst max_attempts = 15;\n\n\tlet attempt = 0;\n\tlet last = Infinity;\n\tlet best = j;\n\n\t// Try to find a J such that the returned y matches the returned y of the L*\n\twhile (attempt <= max_attempts) {\n\t\txyz = fromCam16({J: j, C: c, h: h}, env);\n\n\t\t// If we are within range, return XYZ\n\t\t// If we are closer than last time, save the values\n\t\tconst delta = Math.abs(xyz[1] - y);\n\t\tif (delta < last) {\n\t\t\tif (delta <= threshold) {\n\t\t\t\treturn xyz;\n\t\t\t}\n\t\t\tbest = j;\n\t\t\tlast = delta;\n\t\t}\n\n\t\t// f(j_root) = (j ** (1 / 2)) * 0.1\n\t\t// f(j) = ((f(j_root) * 100) ** 2) / j - 1 = 0\n\t\t// f(j_root) = Y = y / 100\n\t\t// f(j) = (y ** 2) / j - 1\n\t\t// f'(j) = (2 * y) / j\n\t\tj = j - (xyz[1] - y) * j / (2 * xyz[1]);\n\n\t\tattempt += 1;\n\t}\n\n\t// We could not acquire the precision we desired,\n\t// return our closest attempt.\n\treturn fromCam16({J: j, C: c, h: h}, env);\n}\n\nfunction toHct (xyz, env) {\n\t// Calculate HCT by taking the L* of CIE LCh D65 and CAM16 chroma and hue.\n\n\tconst t = toLstar(xyz[1]);\n\tif (t === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\tconst cam16 = toCam16(xyz, viewingConditions);\n\treturn [constrain(cam16.h), cam16.C, t];\n}\n\n// Pre-calculate everything we can with the viewing conditions\nexport const viewingConditions = environment(\n\twhite, 200 / Math.PI * fromLstar(50.0),\n\tfromLstar(50.0) * 100,\n\t\"average\",\n\tfalse,\n);\n\n// https://material.io/blog/science-of-color-design\n// This is not a port of the material-color-utilities,\n// but instead implements the full color space as described,\n// combining CAM16 JCh and Lab D65. This does not clamp conversion\n// to HCT to specific chroma bands and provides support for wider\n// gamuts than Google currently supports and does so at a greater\n// precision (> 8 bits back to sRGB).\n// This implementation comes from https://github.com/facelessuser/coloraide\n// which is licensed under MIT.\nexport default new ColorSpace({\n\tid: \"hct\",\n\tname: \"HCT\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 145],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\tt: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Tone\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\treturn toHct(xyz, viewingConditions);\n\t},\n\ttoBase (hct) {\n\t\treturn fromHct(hct, viewingConditions);\n\t},\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hct\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import hct from \"../spaces/hct.js\";\nimport {viewingConditions} from \"../spaces/hct.js\";\nimport getColor from \"../getColor.js\";\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\nconst ucsCoeff = [1.00, 0.007, 0.0228];\n\n/**\n* Convert HCT chroma and hue (CAM16 JMh colorfulness and hue) using UCS logic for a and b.\n* @param {number[]} coords - HCT coordinates.\n* @return {number[]}\n*/\nfunction convertUcsAb (coords) {\n\t// We want the distance between the actual color.\n\t// If chroma is negative, it will throw off our calculations.\n\t// Normally, converting back to the base and forward will correct it.\n\t// If we have a negative chroma after this, then we have a color that\n\t// cannot resolve to positive chroma.\n\tif (coords[1] < 0) {\n\t\tcoords = hct.fromBase(hct.toBase(coords));\n\t}\n\n\t// Only in extreme cases (usually outside the visible spectrum)\n\t// can the input value for log become negative.\n\t// Avoid domain error by forcing a zero result via \"max\" if necessary.\n\tconst M = Math.log(Math.max(1 + ucsCoeff[2] * coords[1] * viewingConditions.flRoot, 1.0)) / ucsCoeff[2];\n\tconst hrad = coords[0] * deg2rad;\n\tconst a = M * Math.cos(hrad);\n\tconst b = M * Math.sin(hrad);\n\n\treturn [coords[2], a, b];\n}\n\n\n/**\n* Color distance using HCT.\n* @param {Color} color - Color to compare.\n* @param {Color} sample - Color to compare.\n* @return {number[]}\n*/\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\tlet [ t1, a1, b1 ] = convertUcsAb(hct.from(color));\n\tlet [ t2, a2, b2 ] = convertUcsAb(hct.from(sample));\n\n\t// Use simple euclidean distance with a and b using UCS conversion\n\t// and LCh lightness (HCT tone).\n\treturn Math.sqrt((t1 - t2) ** 2 + (a1 - a2) ** 2 + (b1 - b2) ** 2);\n}\n", "import deltaE76 from \"./deltaE76.js\";\nimport deltaECMC from \"./deltaECMC.js\";\nimport deltaE2000 from \"./deltaE2000.js\";\nimport deltaEJz from \"./deltaEJz.js\";\nimport deltaEITP from \"./deltaEITP.js\";\nimport deltaE<PERSON> from \"./deltaEOK.js\";\nimport deltaEHCT from \"./deltaEHCT.js\";\n\nexport {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n\nexport default {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport deltaE2000 from \"./deltaE/deltaE2000.js\";\nimport deltaEOK from \"./deltaE/deltaEOK.js\";\nimport inGamut from \"./inGamut.js\";\nimport to from \"./to.js\";\nimport get from \"./get.js\";\nimport oklab from \"./spaces/oklab.js\";\nimport set from \"./set.js\";\nimport clone from \"./clone.js\";\nimport getColor from \"./getColor.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\nimport {WHITES} from \"./adapt.js\";\n\n/**\n * Calculate the epsilon to 2 degrees smaller than the specified JND.\n * @param {Number} jnd - The target \"just noticeable difference\".\n * @returns {Number}\n */\nfunction calcEpsilon (jnd) {\n\t// Calculate the epsilon to 2 degrees smaller than the specified JND.\n\n\tconst order = (!jnd) ? 0 : Math.floor(Math.log10(Math.abs(jnd)));\n\t// Limit to an arbitrary value to ensure value is never too small and causes infinite loops.\n\treturn Math.max(parseFloat(`1e${order - 2}`), 1e-6);\n}\n\nconst GMAPPRESET = {\n\t\"hct\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 2,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: {},\n\t},\n\t\"hct-tonal\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 0,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: { channel: \"hct.t\", min: 0, max: 100 },\n\t},\n};\n\n/**\n * Force coordinates to be in gamut of a certain color space.\n * Mutates the color it is passed.\n * @param {Object|string} options object or spaceId string\n * @param {string} options.method - How to force into gamut.\n *        If \"clip\", coordinates are just clipped to their reference range.\n *        If \"css\", coordinates are reduced according to the CSS 4 Gamut Mapping Algorithm.\n *        If in the form [colorSpaceId].[coordName], that coordinate is reduced\n *        until the color is in gamut. Please note that this may produce nonsensical\n *        results for certain coordinates (e.g. hue) or infinite loops if reducing the coordinate never brings the color in gamut.\n * @param {ColorSpace|string} options.space - The space whose gamut we want to map to\n * @param {string} options.deltaEMethod - The delta E method to use while performing gamut mapping.\n *        If no method is specified, delta E 2000 is used.\n * @param {Number} options.jnd - The \"just noticeable difference\" to target.\n * @param {Object} options.blackWhiteClamp - Used to configure SDR black and clamping.\n *        \"channel\" indicates the \"space.channel\" to use for determining when to clamp.\n *        \"min\" indicates the lower limit for black clamping and \"max\" indicates the upper\n *        limit for white clamping.\n */\n\nexport default function toGamut (\n\tcolor,\n\t{\n\t\tmethod = defaults.gamut_mapping,\n\t\tspace = undefined,\n\t\tdeltaEMethod = \"\",\n\t\tjnd = 2,\n\t\tblackWhiteClamp = {},\n\t} = {},\n) {\n\tcolor = getColor(color);\n\n\tif (util.isString(arguments[1])) {\n\t\tspace = arguments[1];\n\t}\n\telse if (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\n\t// 3 spaces:\n\t// color.space: current color space\n\t// space: space whose gamut we are mapping to\n\t// mapSpace: space with the coord we're reducing\n\n\tif (inGamut(color, space, { epsilon: 0 })) {\n\t\treturn color;\n\t}\n\n\tlet spaceColor;\n\tif (method === \"css\") {\n\t\tspaceColor = toGamutCSS(color, { space });\n\t}\n\telse {\n\t\tif (method !== \"clip\" && !inGamut(color, space)) {\n\n\t\t\tif (Object.prototype.hasOwnProperty.call(GMAPPRESET, method)) {\n\t\t\t\t({method, jnd, deltaEMethod, blackWhiteClamp} = GMAPPRESET[method]);\n\t\t\t}\n\n\t\t\t// Get the correct delta E method\n\t\t\tlet de = deltaE2000;\n\t\t\tif (deltaEMethod !== \"\") {\n\t\t\t\tfor (let m in deltaEMethods) {\n\t\t\t\t\tif (\"deltae\" + deltaEMethod.toLowerCase() === m.toLowerCase()) {\n\t\t\t\t\t\tde = deltaEMethods[m];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet clipped = toGamut(to(color, space), { method: \"clip\", space });\n\t\t\tif (de(color, clipped) > jnd) {\n\n\t\t\t\t// Clamp to SDR white and black if required\n\t\t\t\tif (Object.keys(blackWhiteClamp).length === 3) {\n\t\t\t\t\tlet channelMeta = ColorSpace.resolveCoord(blackWhiteClamp.channel);\n\t\t\t\t\tlet channel = get(to(color, channelMeta.space), channelMeta.id);\n\t\t\t\t\tif (util.isNone(channel)) {\n\t\t\t\t\t\tchannel = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (channel >= blackWhiteClamp.max) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: WHITES[\"D65\"] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t\telse if (channel <= blackWhiteClamp.min) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: [0, 0, 0] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Reduce a coordinate of a certain color space until the color is in gamut\n\t\t\t\tlet coordMeta = ColorSpace.resolveCoord(method);\n\t\t\t\tlet mapSpace = coordMeta.space;\n\t\t\t\tlet coordId = coordMeta.id;\n\n\t\t\t\tlet mappedColor = to(color, mapSpace);\n\t\t\t\t// If we were already in the mapped color space, we need to resolve undefined channels\n\t\t\t\tmappedColor.coords.forEach((c, i) => {\n\t\t\t\t\tif (util.isNone(c)) {\n\t\t\t\t\t\tmappedColor.coords[i] = 0;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tlet bounds = coordMeta.range || coordMeta.refRange;\n\t\t\t\tlet min = bounds[0];\n\t\t\t\tlet ε = calcEpsilon(jnd);\n\t\t\t\tlet low = min;\n\t\t\t\tlet high = get(mappedColor, coordId);\n\n\t\t\t\twhile (high - low > ε) {\n\t\t\t\t\tlet clipped = clone(mappedColor);\n\t\t\t\t\tclipped = toGamut(clipped, { space, method: \"clip\" });\n\t\t\t\t\tlet deltaE = de(mappedColor, clipped);\n\n\t\t\t\t\tif (deltaE - jnd < ε) {\n\t\t\t\t\t\tlow = get(mappedColor, coordId);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thigh = get(mappedColor, coordId);\n\t\t\t\t\t}\n\n\t\t\t\t\tset(mappedColor, coordId, (low + high) / 2);\n\t\t\t\t}\n\n\t\t\t\tspaceColor = to(mappedColor, space);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tspaceColor = clipped;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tspaceColor = to(color, space);\n\t\t}\n\n\t\tif (method === \"clip\" // Dumb coord clipping\n\t\t\t// finish off smarter gamut mapping with clip to get rid of ε, see #17\n\t\t\t|| !inGamut(spaceColor, space, { epsilon: 0 })\n\t\t) {\n\t\t\tlet bounds = Object.values(space.coords).map(c => c.range || []);\n\n\t\t\tspaceColor.coords = spaceColor.coords.map((c, i) => {\n\t\t\t\tlet [min, max] = bounds[i];\n\n\t\t\t\tif (min !== undefined) {\n\t\t\t\t\tc = Math.max(min, c);\n\t\t\t\t}\n\n\t\t\t\tif (max !== undefined) {\n\t\t\t\t\tc = Math.min(c, max);\n\t\t\t\t}\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t}\n\t}\n\n\tif (space !== color.space) {\n\t\tspaceColor = to(spaceColor, color.space);\n\t}\n\n\tcolor.coords = spaceColor.coords;\n\treturn color;\n}\n\ntoGamut.returns = \"color\";\n\n// The reference colors to be used if lightness is out of the range 0-1 in the\n// `Oklch` space. These are created in the `Oklab` space, as it is used by the\n// DeltaEOK calculation, so it is guaranteed to be imported.\nconst COLORS = {\n\tWHITE: { space: oklab, coords: [1, 0, 0] },\n\tBLACK: { space: oklab, coords: [0, 0, 0] },\n};\n\n/**\n * Given a color `origin`, returns a new color that is in gamut using\n * the CSS Gamut Mapping Algorithm. If `space` is specified, it will be in gamut\n * in `space`, and returned in `space`. Otherwise, it will be in gamut and\n * returned in the color space of `origin`.\n * @param {Object} origin\n * @param {Object} options\n * @param {ColorSpace|string} options.space\n * @returns {Color}\n */\nexport function toGamutCSS (origin, {space} = {}) {\n\tconst JND = 0.02;\n\tconst ε = 0.0001;\n\n\torigin = getColor(origin);\n\n\tif (!space) {\n\t\tspace = origin.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tconst oklchSpace = ColorSpace.get(\"oklch\");\n\n\tif (space.isUnbounded) {\n\t\treturn to(origin, space);\n\t}\n\n\tconst origin_OKLCH = to(origin, oklchSpace);\n\tlet L = origin_OKLCH.coords[0];\n\n\t// return media white or black, if lightness is out of range\n\tif (L >= 1) {\n\t\tconst white = to(COLORS.WHITE, space);\n\t\twhite.alpha = origin.alpha;\n\t\treturn to(white, space);\n\t}\n\tif (L <= 0) {\n\t\tconst black = to(COLORS.BLACK, space);\n\t\tblack.alpha = origin.alpha;\n\t\treturn to(black, space);\n\t}\n\n\tif (inGamut(origin_OKLCH, space, {epsilon: 0})) {\n\t\treturn to(origin_OKLCH, space);\n\t}\n\n\tfunction clip (_color) {\n\t\tconst destColor = to(_color, space);\n\t\tconst spaceCoords = Object.values(space.coords);\n\t\tdestColor.coords = destColor.coords.map((coord, index) => {\n\t\t\tif (\"range\" in spaceCoords[index]) {\n\t\t\t\tconst [min, max] =  spaceCoords[index].range;\n\t\t\t\treturn util.clamp(min, coord, max);\n\t\t\t}\n\t\t\treturn coord;\n\t\t});\n\t\treturn destColor;\n\t}\n\tlet min = 0;\n\tlet max = origin_OKLCH.coords[1];\n\tlet min_inGamut = true;\n\tlet current = clone(origin_OKLCH);\n\tlet clipped = clip(current);\n\n\tlet E = deltaEOK(clipped, current);\n\tif (E < JND) {\n\t\treturn clipped;\n\t}\n\n\twhile ((max - min) > ε) {\n\t\tconst chroma = (min + max) / 2;\n\t\tcurrent.coords[1] = chroma;\n\t\tif (min_inGamut && inGamut(current, space, {epsilon: 0})) {\n\t\t\tmin = chroma;\n\t\t}\n\t\telse {\n\t\t\tclipped = clip(current);\n\t\t\tE = deltaEOK(clipped, current);\n\t\t\tif (E < JND) {\n\t\t\t\tif ((JND - E < ε)) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tmin_inGamut = false;\n\t\t\t\t\tmin = chroma;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tmax = chroma;\n\t\t\t}\n\t\t}\n\t}\n\treturn clipped;\n}\n", "import getColor from \"./getColor.js\";\nimport ColorSpace from \"./space.js\";\nimport toGamut from \"./toGamut.js\";\n\n/**\n * Convert to color space and return a new color\n * @param {Object|string} space - Color space object or id\n * @param {Object} options\n * @param {boolean} options.inGamut - Whether to force resulting color in gamut\n * @returns {Color}\n */\nexport default function to (color, space, {inGamut} = {}) {\n\tcolor = getColor(color);\n\tspace = ColorSpace.get(space);\n\n\tlet coords = space.from(color);\n\tlet ret = {space, coords, alpha: color.alpha};\n\n\tif (inGamut) {\n\t\tret = toGamut(ret, inGamut === true ? undefined : inGamut);\n\t}\n\n\treturn ret;\n}\n\nto.returns = \"color\";\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport getColor from \"./getColor.js\";\nimport checkInGamut from \"./inGamut.js\";\nimport toGamut from \"./toGamut.js\";\nimport clone from \"./clone.js\";\n\n/**\n * Generic toString() method, outputs a color(spaceId ...coords) function, a functional syntax, or custom formats defined by the color space\n * @param {Object} options\n * @param {number} options.precision - Significant digits\n * @param {boolean} options.inGamut - Adjust coordinates to fit in gamut first? [default: false]\n */\nexport default function serialize (color, {\n\tprecision = defaults.precision,\n\tformat = \"default\",\n\tinGamut = true,\n\t...customOptions\n} = {}) {\n\tlet ret;\n\n\tcolor = getColor(color);\n\n\tlet formatId = format;\n\tformat = color.space.getFormat(format)\n\t       ?? color.space.getFormat(\"default\")\n\t       ?? ColorSpace.DEFAULT_FORMAT;\n\n\t// The assignment to coords and inGamut needs to stay in the order they are now\n\t// The order of the assignment was changed as a workaround for a bug in Next.js\n\t// See this issue for details: https://github.com/color-js/color.js/issues/260\n\n\tlet coords = color.coords.slice(); // clone so we can manipulate it\n\n\tinGamut ||= format.toGamut;\n\n\tif (inGamut && !checkInGamut(color)) {\n\t\t// FIXME what happens if the color contains NaNs?\n\t\tcoords = toGamut(clone(color), inGamut === true ? undefined : inGamut).coords;\n\t}\n\n\tif (format.type === \"custom\") {\n\t\tcustomOptions.precision = precision;\n\n\t\tif (format.serialize) {\n\t\t\tret = format.serialize(coords, color.alpha, customOptions);\n\t\t}\n\t\telse {\n\t\t\tthrow new TypeError(`format ${formatId} can only be used to parse colors, not for serialization`);\n\t\t}\n\t}\n\telse {\n\t\t// Functional syntax\n\t\tlet name = format.name || \"color\";\n\n\t\tif (format.serializeCoords) {\n\t\t\tcoords = format.serializeCoords(coords, precision);\n\t\t}\n\t\telse {\n\t\t\tif (precision !== null) {\n\t\t\t\tcoords = coords.map(c => {\n\t\t\t\t\treturn util.serializeNumber(c, {precision});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tlet args = [...coords];\n\n\t\tif (name === \"color\") {\n\t\t\t// If output is a color() function, add colorspace id as first argument\n\t\t\tlet cssId = format.id || format.ids?.[0] || color.space.id;\n\t\t\targs.unshift(cssId);\n\t\t}\n\n\t\tlet alpha = color.alpha;\n\t\tif (precision !== null) {\n\t\t\talpha = util.serializeNumber(alpha, {precision});\n\t\t}\n\n\t\tlet strAlpha = color.alpha >= 1 || format.noAlpha ? \"\" : `${format.commas ? \",\" : \" /\"} ${alpha}`;\n\t\tret = `${name}(${args.join(format.commas ? \", \" : \" \")}${strAlpha})`;\n\t}\n\n\treturn ret;\n}\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light rec2020 values to CIE XYZ\n// using  D65 (no chromatic adaptation)\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// 0 is actually calculated as  4.994106574466076e-17\nconst toXYZ_M = [\n\t[ 0.6369580483012914, 0.14461690358620832,  0.1688809751641721  ],\n\t[ 0.2627002120112671, 0.6779980715188708,   0.05930171646986196 ],\n\t[ 0.000000000000000,  0.028072693049087428, 1.060985057710791   ],\n];\n\n// from ITU-R BT.2124-0 Annex 2 p.3\nconst fromXYZ_M = [\n\t[  1.716651187971268,  -0.355670783776392, -0.253366281373660  ],\n\t[ -0.666684351832489,   1.616481236634939,  0.0157685458139111 ],\n\t[  0.017639857445311,  -0.042770613257809,  0.942103121235474  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"rec2020-linear\",\n\tcssId: \"--rec2020-linear\",\n\tname: \"Linear REC.2020\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n// import sRGB from \"./srgb.js\";\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\n\nexport default new RGBColorSpace({\n\tid: \"rec2020\",\n\tname: \"REC.2020\",\n\tbase: REC2020Linear,\n\t// Non-linear transfer function from Rec. ITU-R BT.2020-2 table 4\n\ttoBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val < β * 4.5) {\n\t\t\t\treturn val / 4.5;\n\t\t\t}\n\n\t\t\treturn Math.pow((val + α - 1) / α, 1 / 0.45);\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val >= β) {\n\t\t\t\treturn α * Math.pow(val, 0.45) - (α - 1);\n\t\t\t}\n\n\t\t\treturn 4.5 * val;\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\nconst toXYZ_M = [\n\t[0.4865709486482162, 0.26566769316909306, 0.1982172852343625],\n\t[0.2289745640697488, 0.6917385218365064,  0.079286914093745],\n\t[0.0000000000000000, 0.04511338185890264, 1.043944368900976],\n];\n\nconst fromXYZ_M = [\n\t[ 2.493496911941425,   -0.9313836179191239, -0.40271078445071684],\n\t[-0.8294889695615747,   1.7626640603183463,  0.023624685841943577],\n\t[ 0.03584583024378447, -0.07617238926804182, 0.9568845240076872],\n];\n\nexport default new RGBColorSpace({\n\tid: \"p3-linear\",\n\tcssId: \"--display-p3-linear\",\n\tname: \"Linear P3\",\n\twhite: \"D65\",\n\tto<PERSON><PERSON><PERSON>_<PERSON>,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// This is the linear-light version of sRGB\n// as used for example in SVG filters\n// or in Canvas\n\n// This matrix was calculated directly from the RGB and white chromaticities\n// when rounded to 8 decimal places, it agrees completely with the official matrix\n// see https://github.com/w3c/csswg-drafts/issues/5922\nconst toXYZ_M = [\n\t[ 0.41239079926595934, 0.357584339383878,   0.1804807884018343  ],\n\t[ 0.21263900587151027, 0.715168678767756,   0.07219231536073371 ],\n\t[ 0.01933081871559182, 0.11919477979462598, 0.9505321522496607  ],\n];\n\n// This matrix is the inverse of the above;\n// again it agrees with the official definition when rounded to 8 decimal places\nexport const fromXYZ_M = [\n\t[  3.2409699419045226,  -1.537383177570094,   -0.4986107602930034  ],\n\t[ -0.9692436362808796,   1.8759675015077202,   0.04155505740717559 ],\n\t[  0.05563007969699366, -0.20397695888897652,  1.0569715142428786  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"srgb-linear\",\n\tname: \"Linear sRGB\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "/* List of CSS color keywords\n * Note that this does not include currentColor, transparent,\n * or system colors\n */\n\n// To produce: Visit https://www.w3.org/TR/css-color-4/#named-colors\n// and run in the console:\n// copy($$(\"tr\", $(\".named-color-table tbody\")).map(tr => `\"${tr.cells[2].textContent.trim()}\": [${tr.cells[4].textContent.trim().split(/\\s+/).map(c => c === \"0\"? \"0\" : c === \"255\"? \"1\" : c + \" / 255\").join(\", \")}]`).join(\",\\n\"))\nexport default {\n\t\"aliceblue\": [240 / 255, 248 / 255, 1],\n\t\"antiquewhite\": [250 / 255, 235 / 255, 215 / 255],\n\t\"aqua\": [0, 1, 1],\n\t\"aquamarine\": [127 / 255, 1, 212 / 255],\n\t\"azure\": [240 / 255, 1, 1],\n\t\"beige\": [245 / 255, 245 / 255, 220 / 255],\n\t\"bisque\": [1, 228 / 255, 196 / 255],\n\t\"black\": [0, 0, 0],\n\t\"blanchedalmond\": [1, 235 / 255, 205 / 255],\n\t\"blue\": [0, 0, 1],\n\t\"blueviolet\": [138 / 255, 43 / 255, 226 / 255],\n\t\"brown\": [165 / 255, 42 / 255, 42 / 255],\n\t\"burlywood\": [222 / 255, 184 / 255, 135 / 255],\n\t\"cadetblue\": [95 / 255, 158 / 255, 160 / 255],\n\t\"chartreuse\": [127 / 255, 1, 0],\n\t\"chocolate\": [210 / 255, 105 / 255, 30 / 255],\n\t\"coral\": [1, 127 / 255, 80 / 255],\n\t\"cornflowerblue\": [100 / 255, 149 / 255, 237 / 255],\n\t\"cornsilk\": [1, 248 / 255, 220 / 255],\n\t\"crimson\": [220 / 255, 20 / 255, 60 / 255],\n\t\"cyan\": [0, 1, 1],\n\t\"darkblue\": [0, 0, 139 / 255],\n\t\"darkcyan\": [0, 139 / 255, 139 / 255],\n\t\"darkgoldenrod\": [184 / 255, 134 / 255, 11 / 255],\n\t\"darkgray\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkgreen\": [0, 100 / 255, 0],\n\t\"darkgrey\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkkhaki\": [189 / 255, 183 / 255, 107 / 255],\n\t\"darkmagenta\": [139 / 255, 0, 139 / 255],\n\t\"darkolivegreen\": [85 / 255, 107 / 255, 47 / 255],\n\t\"darkorange\": [1, 140 / 255, 0],\n\t\"darkorchid\": [153 / 255, 50 / 255, 204 / 255],\n\t\"darkred\": [139 / 255, 0, 0],\n\t\"darksalmon\": [233 / 255, 150 / 255, 122 / 255],\n\t\"darkseagreen\": [143 / 255, 188 / 255, 143 / 255],\n\t\"darkslateblue\": [72 / 255, 61 / 255, 139 / 255],\n\t\"darkslategray\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkslategrey\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkturquoise\": [0, 206 / 255, 209 / 255],\n\t\"darkviolet\": [148 / 255, 0, 211 / 255],\n\t\"deeppink\": [1, 20 / 255, 147 / 255],\n\t\"deepskyblue\": [0, 191 / 255, 1],\n\t\"dimgray\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dimgrey\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dodgerblue\": [30 / 255, 144 / 255, 1],\n\t\"firebrick\": [178 / 255, 34 / 255, 34 / 255],\n\t\"floralwhite\": [1, 250 / 255, 240 / 255],\n\t\"forestgreen\": [34 / 255, 139 / 255, 34 / 255],\n\t\"fuchsia\": [1, 0, 1],\n\t\"gainsboro\": [220 / 255, 220 / 255, 220 / 255],\n\t\"ghostwhite\": [248 / 255, 248 / 255, 1],\n\t\"gold\": [1, 215 / 255, 0],\n\t\"goldenrod\": [218 / 255, 165 / 255, 32 / 255],\n\t\"gray\": [128 / 255, 128 / 255, 128 / 255],\n\t\"green\": [0, 128 / 255, 0],\n\t\"greenyellow\": [173 / 255, 1, 47 / 255],\n\t\"grey\": [128 / 255, 128 / 255, 128 / 255],\n\t\"honeydew\": [240 / 255, 1, 240 / 255],\n\t\"hotpink\": [1, 105 / 255, 180 / 255],\n\t\"indianred\": [205 / 255, 92 / 255, 92 / 255],\n\t\"indigo\": [75 / 255, 0, 130 / 255],\n\t\"ivory\": [1, 1, 240 / 255],\n\t\"khaki\": [240 / 255, 230 / 255, 140 / 255],\n\t\"lavender\": [230 / 255, 230 / 255, 250 / 255],\n\t\"lavenderblush\": [1, 240 / 255, 245 / 255],\n\t\"lawngreen\": [124 / 255, 252 / 255, 0],\n\t\"lemonchiffon\": [1, 250 / 255, 205 / 255],\n\t\"lightblue\": [173 / 255, 216 / 255, 230 / 255],\n\t\"lightcoral\": [240 / 255, 128 / 255, 128 / 255],\n\t\"lightcyan\": [224 / 255, 1, 1],\n\t\"lightgoldenrodyellow\": [250 / 255, 250 / 255, 210 / 255],\n\t\"lightgray\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightgreen\": [144 / 255, 238 / 255, 144 / 255],\n\t\"lightgrey\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightpink\": [1, 182 / 255, 193 / 255],\n\t\"lightsalmon\": [1, 160 / 255, 122 / 255],\n\t\"lightseagreen\": [32 / 255, 178 / 255, 170 / 255],\n\t\"lightskyblue\": [135 / 255, 206 / 255, 250 / 255],\n\t\"lightslategray\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightslategrey\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightsteelblue\": [176 / 255, 196 / 255, 222 / 255],\n\t\"lightyellow\": [1, 1, 224 / 255],\n\t\"lime\": [0, 1, 0],\n\t\"limegreen\": [50 / 255, 205 / 255, 50 / 255],\n\t\"linen\": [250 / 255, 240 / 255, 230 / 255],\n\t\"magenta\": [1, 0, 1],\n\t\"maroon\": [128 / 255, 0, 0],\n\t\"mediumaquamarine\": [102 / 255, 205 / 255, 170 / 255],\n\t\"mediumblue\": [0, 0, 205 / 255],\n\t\"mediumorchid\": [186 / 255, 85 / 255, 211 / 255],\n\t\"mediumpurple\": [147 / 255, 112 / 255, 219 / 255],\n\t\"mediumseagreen\": [60 / 255, 179 / 255, 113 / 255],\n\t\"mediumslateblue\": [123 / 255, 104 / 255, 238 / 255],\n\t\"mediumspringgreen\": [0, 250 / 255, 154 / 255],\n\t\"mediumturquoise\": [72 / 255, 209 / 255, 204 / 255],\n\t\"mediumvioletred\": [199 / 255, 21 / 255, 133 / 255],\n\t\"midnightblue\": [25 / 255, 25 / 255, 112 / 255],\n\t\"mintcream\": [245 / 255, 1, 250 / 255],\n\t\"mistyrose\": [1, 228 / 255, 225 / 255],\n\t\"moccasin\": [1, 228 / 255, 181 / 255],\n\t\"navajowhite\": [1, 222 / 255, 173 / 255],\n\t\"navy\": [0, 0, 128 / 255],\n\t\"oldlace\": [253 / 255, 245 / 255, 230 / 255],\n\t\"olive\": [128 / 255, 128 / 255, 0],\n\t\"olivedrab\": [107 / 255, 142 / 255, 35 / 255],\n\t\"orange\": [1, 165 / 255, 0],\n\t\"orangered\": [1, 69 / 255, 0],\n\t\"orchid\": [218 / 255, 112 / 255, 214 / 255],\n\t\"palegoldenrod\": [238 / 255, 232 / 255, 170 / 255],\n\t\"palegreen\": [152 / 255, 251 / 255, 152 / 255],\n\t\"paleturquoise\": [175 / 255, 238 / 255, 238 / 255],\n\t\"palevioletred\": [219 / 255, 112 / 255, 147 / 255],\n\t\"papayawhip\": [1, 239 / 255, 213 / 255],\n\t\"peachpuff\": [1, 218 / 255, 185 / 255],\n\t\"peru\": [205 / 255, 133 / 255, 63 / 255],\n\t\"pink\": [1, 192 / 255, 203 / 255],\n\t\"plum\": [221 / 255, 160 / 255, 221 / 255],\n\t\"powderblue\": [176 / 255, 224 / 255, 230 / 255],\n\t\"purple\": [128 / 255, 0, 128 / 255],\n\t\"rebeccapurple\": [102 / 255, 51 / 255, 153 / 255],\n\t\"red\": [1, 0, 0],\n\t\"rosybrown\": [188 / 255, 143 / 255, 143 / 255],\n\t\"royalblue\": [65 / 255, 105 / 255, 225 / 255],\n\t\"saddlebrown\": [139 / 255, 69 / 255, 19 / 255],\n\t\"salmon\": [250 / 255, 128 / 255, 114 / 255],\n\t\"sandybrown\": [244 / 255, 164 / 255, 96 / 255],\n\t\"seagreen\": [46 / 255, 139 / 255, 87 / 255],\n\t\"seashell\": [1, 245 / 255, 238 / 255],\n\t\"sienna\": [160 / 255, 82 / 255, 45 / 255],\n\t\"silver\": [192 / 255, 192 / 255, 192 / 255],\n\t\"skyblue\": [135 / 255, 206 / 255, 235 / 255],\n\t\"slateblue\": [106 / 255, 90 / 255, 205 / 255],\n\t\"slategray\": [112 / 255, 128 / 255, 144 / 255],\n\t\"slategrey\": [112 / 255, 128 / 255, 144 / 255],\n\t\"snow\": [1, 250 / 255, 250 / 255],\n\t\"springgreen\": [0, 1, 127 / 255],\n\t\"steelblue\": [70 / 255, 130 / 255, 180 / 255],\n\t\"tan\": [210 / 255, 180 / 255, 140 / 255],\n\t\"teal\": [0, 128 / 255, 128 / 255],\n\t\"thistle\": [216 / 255, 191 / 255, 216 / 255],\n\t\"tomato\": [1, 99 / 255, 71 / 255],\n\t\"turquoise\": [64 / 255, 224 / 255, 208 / 255],\n\t\"violet\": [238 / 255, 130 / 255, 238 / 255],\n\t\"wheat\": [245 / 255, 222 / 255, 179 / 255],\n\t\"white\": [1, 1, 1],\n\t\"whitesmoke\": [245 / 255, 245 / 255, 245 / 255],\n\t\"yellow\": [1, 1, 0],\n\t\"yellowgreen\": [154 / 255, 205 / 255, 50 / 255],\n};\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport sRGBLinear from \"./srgb-linear.js\";\nimport KEYWORDS from \"../keywords.js\";\n\nlet coordGrammar = Array(3).fill(\"<percentage> | <number>[0, 255]\");\nlet coordGrammarNumber = Array(3).fill(\"<number>[0, 255]\");\n\nexport default new RGBColorSpace({\n\tid: \"srgb\",\n\tname: \"sRGB\",\n\tbase: sRGBLinear,\n\tfromBase: rgb => {\n\t\t// convert an array of linear-light sRGB values in the range 0.0-1.0\n\t\t// to gamma corrected form\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs > 0.0031308) {\n\t\t\t\treturn sign * (1.055 * (abs ** (1 / 2.4)) - 0.055);\n\t\t\t}\n\n\t\t\treturn 12.92 * val;\n\t\t});\n\t},\n\ttoBase: rgb => {\n\t\t// convert an array of sRGB values in the range 0.0 - 1.0\n\t\t// to linear light (un-companded) form.\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs <= 0.04045) {\n\t\t\t\treturn val / 12.92;\n\t\t\t}\n\n\t\t\treturn sign * (((abs + 0.055) / 1.055) ** 2.4);\n\t\t});\n\t},\n\tformats: {\n\t\t\"rgb\": {\n\t\t\tcoords: coordGrammar,\n\t\t},\n\t\t\"rgb_number\": {\n\t\t\tname: \"rgb\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t\tnoAlpha: true,\n\t\t},\n\t\t\"color\": { /* use defaults */ },\n\t\t\"rgba\": {\n\t\t\tcoords: coordGrammar,\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t\t\"rgba_number\": {\n\t\t\tname: \"rgba\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t},\n\t\t\"hex\": {\n\t\t\ttype: \"custom\",\n\t\t\ttoGamut: true,\n\t\t\ttest: str => /^#([a-f0-9]{3,4}){1,2}$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tif (str.length <= 5) {\n\t\t\t\t\t// #rgb or #rgba, duplicate digits\n\t\t\t\t\tstr = str.replace(/[a-f0-9]/gi, \"$&$&\");\n\t\t\t\t}\n\n\t\t\t\tlet rgba = [];\n\t\t\t\tstr.replace(/[a-f0-9]{2}/gi, component => {\n\t\t\t\t\trgba.push(parseInt(component, 16) / 255);\n\t\t\t\t});\n\n\t\t\t\treturn {\n\t\t\t\t\tspaceId: \"srgb\",\n\t\t\t\t\tcoords: rgba.slice(0, 3),\n\t\t\t\t\talpha: rgba.slice(3)[0],\n\t\t\t\t};\n\t\t\t},\n\t\t\tserialize: (coords, alpha, {\n\t\t\t\tcollapse = true, // collapse to 3-4 digit hex when possible?\n\t\t\t} = {}) => {\n\t\t\t\tif (alpha < 1) {\n\t\t\t\t\tcoords.push(alpha);\n\t\t\t\t}\n\n\t\t\t\tcoords = coords.map(c => Math.round(c * 255));\n\n\t\t\t\tlet collapsible = collapse && coords.every(c => c % 17 === 0);\n\n\t\t\t\tlet hex = coords.map(c => {\n\t\t\t\t\tif (collapsible) {\n\t\t\t\t\t\treturn (c / 17).toString(16);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn c.toString(16).padStart(2, \"0\");\n\t\t\t\t}).join(\"\");\n\n\t\t\t\treturn \"#\" + hex;\n\t\t\t},\n\t\t},\n\t\t\"keyword\": {\n\t\t\ttype: \"custom\",\n\t\t\ttest: str => /^[a-z]+$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tstr = str.toLowerCase();\n\t\t\t\tlet ret = {spaceId: \"srgb\", coords: null, alpha: 1};\n\n\t\t\t\tif (str === \"transparent\") {\n\t\t\t\t\tret.coords = KEYWORDS.black;\n\t\t\t\t\tret.alpha = 0;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tret.coords = KEYWORDS[str];\n\t\t\t\t}\n\n\t\t\t\tif (ret.coords) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport P3Linear from \"./p3-linear.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new RGBColorSpace({\n\tid: \"p3\",\n\tcssId: \"display-p3\",\n\tname: \"P3\",\n\tbase: P3Linear,\n\t// Gamma encoding/decoding is the same as sRGB\n\tfromBase: sRGB.fromBase,\n\ttoBase: sRGB.toBase,\n});\n", "import { isNone, skipNone } from \"./util.js\";\nimport defaults from \"./defaults.js\";\nimport to from \"./to.js\";\nimport serialize from \"./serialize.js\";\nimport clone from \"./clone.js\";\nimport REC2020 from \"./spaces/rec2020.js\";\nimport P3 from \"./spaces/p3.js\";\nimport Lab from \"./spaces/lab.js\";\nimport sRGB from \"./spaces/srgb.js\";\n\n// Default space for CSS output. Code in Color.js makes this wider if there's a DOM available\ndefaults.display_space = sRGB;\n\nlet supportsNone;\n\nif (typeof CSS !== \"undefined\" && CSS.supports) {\n\t// Find widest supported color space for CSS\n\tfor (let space of [Lab, REC2020, P3]) {\n\t\tlet coords = space.getMinCoords();\n\t\tlet color = {space, coords, alpha: 1};\n\t\tlet str = serialize(color);\n\n\t\tif (CSS.supports(\"color\", str)) {\n\t\t\tdefaults.display_space = space;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\n/**\n * Returns a serialization of the color that can actually be displayed in the browser.\n * If the default serialization can be displayed, it is returned.\n * Otherwise, the color is converted to Lab, REC2020, or P3, whichever is the widest supported.\n * In Node.js, this is basically equivalent to `serialize()` but returns a `String` object instead.\n *\n * @export\n * @param {{space, coords} | Color | string} color\n * @param {*} [options={}] Options to be passed to serialize()\n * @param {ColorSpace | string} [options.space = defaults.display_space] Color space to use for serialization if default is not supported\n * @returns {String} String object containing the serialized color with a color property containing the converted color (or the original, if no conversion was necessary)\n */\nexport default function display (color, {space = defaults.display_space, ...options} = {}) {\n\tlet ret = serialize(color, options);\n\n\tif (typeof CSS === \"undefined\" || CSS.supports(\"color\", ret) || !defaults.display_space) {\n\t\tret = new String(ret);\n\t\tret.color = color;\n\t}\n\telse {\n\t\t// If we're here, what we were about to output is not supported\n\t\tlet fallbackColor = color;\n\n\t\t// First, check if the culprit is none values\n\t\tlet hasNone = color.coords.some(isNone) || isNone(color.alpha);\n\n\t\tif (hasNone) {\n\t\t\t// Does the browser support none values?\n\t\t\tif (!(supportsNone ??= CSS.supports(\"color\", \"hsl(none 50% 50%)\"))) {\n\t\t\t\t// Nope, try again without none\n\t\t\t\tfallbackColor = clone(color);\n\t\t\t\tfallbackColor.coords = fallbackColor.coords.map(skipNone);\n\t\t\t\tfallbackColor.alpha = skipNone(fallbackColor.alpha);\n\n\t\t\t\tret = serialize(fallbackColor, options);\n\n\t\t\t\tif (CSS.supports(\"color\", ret)) {\n\t\t\t\t\t// We're done, now it's supported\n\t\t\t\t\tret = new String(ret);\n\t\t\t\t\tret.color = fallbackColor;\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// If we're here, the color function is not supported\n\t\t// Fall back to fallback space\n\t\tfallbackColor = to(fallbackColor, space);\n\t\tret = new String(serialize(fallbackColor, options));\n\t\tret.color = fallbackColor;\n\t}\n\n\treturn ret;\n}\n", "import getColor from \"./getColor.js\";\n\nexport default function equals (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\treturn color1.space === color2.space\n\t       && color1.alpha === color2.alpha\n\t       && color1.coords.every((c, i) => c === color2.coords[i]);\n}\n", "/**\n * Relative luminance\n */\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport xyz_d65 from \"./spaces/xyz-d65.js\";\n\nexport function getLuminance (color) {\n\t// Assume getColor() is called on color in get()\n\treturn get(color, [xyz_d65, \"y\"]);\n}\n\nexport function setLuminance (color, value) {\n\t// Assume getColor() is called on color in set()\n\tset(color, [xyz_d65, \"y\"], value);\n}\n\nexport function register (Color) {\n\tObject.defineProperty(Color.prototype, \"luminance\", {\n\t\tget () {\n\t\t\treturn getLuminance(this);\n\t\t},\n\t\tset (value) {\n\t\t\tsetLuminance(this, value);\n\t\t},\n\t});\n}\n", "// WCAG 2.0 contrast https://www.w3.org/TR/WCAG20-TECHS/G18.html\n// Simple contrast, with fixed 5% viewing flare contribution\n// Symmetric, does not matter which is foreground and which is background\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrastWCAG21 (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn (Y1 + .05) / (Y2 + .05);\n}\n", "// APCA 0.0.98G\n// https://github.com/Myndex/apca-w3\n// see also https://github.com/w3c/silver/issues/643\n\nimport getColor from \"../getColor.js\";\nimport to from \"../to.js\";\n\n// exponents\nconst normBG = 0.56;\nconst normTXT = 0.57;\nconst revTXT = 0.62;\nconst revBG = 0.65;\n\n// clamps\nconst blkThrs = 0.022;\nconst blkClmp = 1.414;\nconst loClip = 0.1;\nconst deltaYmin = 0.0005;\n\n// scalers\n// see https://github.com/w3c/silver/issues/645\nconst scaleBoW = 1.14;\nconst loBoWoffset = 0.027;\nconst scaleWoB = 1.14;\nconst loWoBoffset = 0.027;\n\nfunction fclamp (Y) {\n\tif (Y >= blkThrs) {\n\t\treturn Y;\n\t}\n\treturn Y + (blkThrs - Y) ** blkClmp;\n}\n\nfunction linearize (val) {\n\tlet sign = val < 0 ? -1 : 1;\n\tlet abs = Math.abs(val);\n\treturn sign * Math.pow(abs, 2.4);\n}\n\n// Not symmetric, requires a foreground (text) color, and a background color\nexport default function contrastAPCA (background, foreground) {\n\tforeground = getColor(foreground);\n\tbackground = getColor(background);\n\n\tlet S;\n\tlet C;\n\tlet Sapc;\n\n\t// Myndex as-published, assumes sRGB inputs\n\tlet R, G, B;\n\n\tforeground = to(foreground, \"srgb\");\n\t// Should these be clamped to in-gamut values?\n\n\t// Calculates \"screen luminance\" with non-standard simple gamma EOTF\n\t// weights should be from CSS Color 4, not the ones here which are via Myndex and copied from Lindbloom\n\t[R, G, B] = foreground.coords;\n\tlet lumTxt = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\tbackground = to(background, \"srgb\");\n\t[R, G, B] = background.coords;\n\tlet lumBg = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\t// toe clamping of very dark values to account for flare\n\tlet Ytxt = fclamp(lumTxt);\n\tlet Ybg = fclamp(lumBg);\n\n\t// are we \"Black on White\" (dark on light), or light on dark?\n\tlet BoW = Ybg > Ytxt;\n\n\t// why is this a delta, when Y is not perceptually uniform?\n\t// Answer: it is a noise gate, see\n\t// https://github.com/LeaVerou/color.js/issues/208\n\tif (Math.abs(Ybg - Ytxt) < deltaYmin) {\n\t\tC = 0;\n\t}\n\telse {\n\t\tif (BoW) {\n\t\t\t// dark text on light background\n\t\t\tS = Ybg ** normBG - Ytxt ** normTXT;\n\t\t\tC = S * scaleBoW;\n\t\t}\n\t\telse {\n\t\t\t// light text on dark background\n\t\t\tS = Ybg ** revBG - Ytxt ** revTXT;\n\t\t\tC = S * scaleWoB;\n\t\t}\n\t}\n\tif (Math.abs(C) < loClip) {\n\t\tSapc = 0;\n\t}\n\telse if (C > 0) {\n\t\t// not clear whether Woffset is loBoWoffset or loWoBoffset\n\t\t// but they have the same value\n\t\tSapc = C - loBoWoffset;\n\t}\n\telse {\n\t\tSapc = C + loBoWoffset;\n\t}\n\n\treturn Sapc * 100;\n}\n", "// Michelson  luminance contrast\n// the relation between the spread and the sum of the two luminances\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrast<PERSON><PERSON><PERSON> (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\tlet denom = (Y1 + Y2);\n\treturn denom === 0 ? 0 : (Y1 - Y2) / denom;\n}\n", "// Weber luminance contrast\n// The difference between the two luminances divided by the lower luminance\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\n// the darkest sRGB color above black is #000001 and this produces\n// a plain Weber contrast of ~45647.\n// So, setting the divide-by-zero result at 50000 is a reasonable\n// max clamp for the plain Weber\nconst max = 50000;\n\nexport default function contrastWeber (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn Y2 === 0 ? max : (Y1 - Y2) / Y2;\n}\n", "// CIE Lightness difference, as used by Google Material Design\n// Google HCT Tone is the same as CIE Lightness\n// https://material.io/blog/science-of-color-design\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab from \"../spaces/lab.js\";\n\nexport default function contrastLstar (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet L1 = get(color1, [lab, \"l\"]);\n\tlet L2 = get(color2, [lab, \"l\"]);\n\n\treturn Math.abs(L1 - L2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D65;\n\nexport default new ColorSpace({\n\tid: \"lab-d65\",\n\tname: \"Lab D65\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D65, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d65,\n\t// Convert D65-adapted XYZ to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D65-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab-d65\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// Delta Phi Star perceptual lightness contrast\n// See https://github.com/Myndex/deltaphistar\n// The (difference between two Lstars each raised to phi) raised to (1/phi)\n// Symmetric, does not matter which is foreground and which is background\n\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab_d65 from \"../spaces/lab-d65.js\";\n\nconst phi = Math.pow(5, 0.5) * 0.5 + 0.5; // Math.phi can be used if Math.js\n\nexport default function contrastDeltaPhi (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Lstr1 = get(color1, [lab_d65, \"l\"]);\n\tlet Lstr2 = get(color2, [lab_d65, \"l\"]);\n\n\tlet deltaPhiStar = Math.abs(Math.pow(Lstr1, phi) - Math.pow(Lstr2, phi));\n\n\tlet contrast = Math.pow(deltaPhiStar, (1 / phi)) * Math.SQRT2 - 40;\n\n\treturn (contrast < 7.5) ? 0.0 : contrast ;\n}\n", "import getColor from \"./getColor.js\";\n// import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport * as contrastAlgorithms from \"./contrast/index.js\";\n\nexport default function contrast (background, foreground, o = {}) {\n\tif (isString(o)) {\n\t\to = {algorithm: o};\n\t}\n\n\tlet {algorithm, ...rest} = o;\n\n\tif (!algorithm) {\n\t\tlet algorithms = Object.keys(contrastAlgorithms).map(a => a.replace(/^contrast/, \"\")).join(\", \");\n\t\tthrow new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${algorithms}`);\n\t}\n\n\tbackground = getColor(background);\n\tforeground = getColor(foreground);\n\n\tfor (let a in contrastAlgorithms) {\n\t\tif (\"contrast\" + algorithm.toLowerCase() === a.toLowerCase()) {\n\t\t\treturn contrastAlgorithms[a](background, foreground, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown contrast algorithm: ${algorithm}`);\n}\n", "import xyz_d65 from \"./spaces/xyz-d65.js\";\nimport getAll from \"./getAll.js\";\n\n// Chromaticity coordinates\nexport function uv (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet denom = X + 15 * Y + 3 * Z;\n\treturn [4 * X / denom, 9 * Y / denom];\n}\n\nexport function xy (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet  sum = X + Y + Z;\n\treturn [X / sum, Y / sum];\n}\n\nexport function register (Color) {\n\t// no setters, as lightness information is lost\n\t// when converting color to chromaticity\n\tObject.defineProperty(Color.prototype, \"uv\", {\n\t\tget () {\n\t\t\treturn uv(this);\n\t\t},\n\t});\n\n\tObject.defineProperty(Color.prototype, \"xy\", {\n\t\tget () {\n\t\t\treturn xy(this);\n\t\t},\n\t});\n}\n", "import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nexport default function deltaE (c1, c2, o = {}) {\n\tif (isString(o)) {\n\t\to = {method: o};\n\t}\n\n\tlet {method = defaults.deltaE, ...rest} = o;\n\n\tfor (let m in deltaEMethods) {\n\t\tif (\"deltae\" + method.toLowerCase() === m.toLowerCase()) {\n\t\t\treturn deltaEMethods[m](c1, c2, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown deltaE method: ${method}`);\n}\n", "import ColorSpace from \"./space.js\";\nimport set from \"./set.js\";\n\nexport function lighten (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 + amount));\n}\n\nexport function darken (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 - amount));\n}\n", "/**\n * Functions related to color interpolation\n */\nimport ColorSpace from \"./space.js\";\nimport {type, interpolate} from \"./util.js\";\nimport getColor from \"./getColor.js\";\nimport clone from \"./clone.js\";\nimport to from \"./to.js\";\nimport toGamut from \"./toGamut.js\";\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport defaults from \"./defaults.js\";\nimport * as angles from \"./angles.js\";\nimport deltaE from \"./deltaE.js\";\n\n/**\n * Return an intermediate color between two colors\n * Signatures: mix(c1, c2, p, options)\n *             mix(c1, c2, options)\n *             mix(color)\n * @param {Color | string} c1 The first color\n * @param {Color | string} [c2] The second color\n * @param {number} [p=.5] A 0-1 percentage where 0 is c1 and 1 is c2\n * @param {Object} [o={}]\n * @return {Color}\n */\nexport function mix (c1, c2, p = .5, o = {}) {\n\t[c1, c2] = [getColor(c1), getColor(c2)];\n\n\tif (type(p) === \"object\") {\n\t\t[p, o] = [.5, p];\n\t}\n\n\tlet r = range(c1, c2, o);\n\treturn r(p);\n}\n\n/**\n *\n * @param {Color | string | Function} c1 The first color or a range\n * @param {Color | string} [c2] The second color if c1 is not a range\n * @param {Object} [options={}]\n * @return {Color[]}\n */\nexport function steps (c1, c2, options = {}) {\n\tlet colorRange;\n\n\tif (isRange(c1)) {\n\t\t// Tweaking existing range\n\t\t[colorRange, options] = [c1, c2];\n\t\t[c1, c2] = colorRange.rangeArgs.colors;\n\t}\n\n\tlet {\n\t\tmaxDeltaE, deltaEMethod,\n\t\tsteps = 2, maxSteps = 1000,\n\t\t...rangeOptions\n\t} = options;\n\n\tif (!colorRange) {\n\t\t[c1, c2] = [getColor(c1), getColor(c2)];\n\t\tcolorRange = range(c1, c2, rangeOptions);\n\t}\n\n\tlet totalDelta = deltaE(c1, c2);\n\tlet actualSteps = maxDeltaE > 0 ? Math.max(steps, Math.ceil(totalDelta / maxDeltaE) + 1) : steps;\n\tlet ret = [];\n\n\tif (maxSteps !== undefined) {\n\t\tactualSteps = Math.min(actualSteps, maxSteps);\n\t}\n\n\tif (actualSteps === 1) {\n\t\tret = [{p: .5, color: colorRange(.5)}];\n\t}\n\telse {\n\t\tlet step = 1 / (actualSteps - 1);\n\t\tret = Array.from({length: actualSteps}, (_, i) => {\n\t\t\tlet p = i * step;\n\t\t\treturn {p, color: colorRange(p)};\n\t\t});\n\t}\n\n\tif (maxDeltaE > 0) {\n\t\t// Iterate over all stops and find max deltaE\n\t\tlet maxDelta = ret.reduce((acc, cur, i) => {\n\t\t\tif (i === 0) {\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tlet ΔΕ = deltaE(cur.color, ret[i - 1].color, deltaEMethod);\n\t\t\treturn Math.max(acc, ΔΕ);\n\t\t}, 0);\n\n\t\twhile (maxDelta > maxDeltaE) {\n\t\t\t// Insert intermediate stops and measure maxDelta again\n\t\t\t// We need to do this for all pairs, otherwise the midpoint shifts\n\t\t\tmaxDelta = 0;\n\n\t\t\tfor (let i = 1; (i < ret.length) && (ret.length < maxSteps); i++) {\n\t\t\t\tlet prev = ret[i - 1];\n\t\t\t\tlet cur = ret[i];\n\n\t\t\t\tlet p = (cur.p + prev.p) / 2;\n\t\t\t\tlet color = colorRange(p);\n\t\t\t\tmaxDelta = Math.max(maxDelta, deltaE(color, prev.color), deltaE(color, cur.color));\n\t\t\t\tret.splice(i, 0, {p, color: colorRange(p)});\n\t\t\t\ti++;\n\t\t\t}\n\t\t}\n\t}\n\n\tret = ret.map(a => a.color);\n\n\treturn ret;\n}\n\n/**\n * Interpolate to color2 and return a function that takes a 0-1 percentage\n * @param {Color | string | Function} color1 The first color or an existing range\n * @param {Color | string} [color2] If color1 is a color, this is the second color\n * @param {Object} [options={}]\n * @returns {Function} A function that takes a 0-1 percentage and returns a color\n */\nexport function range (color1, color2, options = {}) {\n\tif (isRange(color1)) {\n\t\t// Tweaking existing range\n\t\tlet [r, options] = [color1, color2];\n\n\t\treturn range(...r.rangeArgs.colors, {...r.rangeArgs.options, ...options});\n\t}\n\n\tlet {space, outputSpace, progression, premultiplied} = options;\n\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\t// Make sure we're working on copies of these colors\n\tcolor1 = clone(color1);\n\tcolor2 = clone(color2);\n\n\tlet rangeArgs = {colors: [color1, color2], options};\n\n\tif (space) {\n\t\tspace = ColorSpace.get(space);\n\t}\n\telse {\n\t\tspace = ColorSpace.registry[defaults.interpolationSpace] || color1.space;\n\t}\n\n\toutputSpace = outputSpace ? ColorSpace.get(outputSpace) : space;\n\n\tcolor1 = to(color1, space);\n\tcolor2 = to(color2, space);\n\n\t// Gamut map to avoid areas of flat color\n\tcolor1 = toGamut(color1);\n\tcolor2 = toGamut(color2);\n\n\t// Handle hue interpolation\n\t// See https://github.com/w3c/csswg-drafts/issues/4735#issuecomment-635741840\n\tif (space.coords.h && space.coords.h.type === \"angle\") {\n\t\tlet arc = options.hue = options.hue || \"shorter\";\n\n\t\tlet hue = [space, \"h\"];\n\t\tlet [θ1, θ2] = [get(color1, hue), get(color2, hue)];\n\t\t// Undefined hues must be evaluated before hue fix-up to properly\n\t\t// calculate hue arcs between undefined and defined hues.\n\t\t// See https://github.com/w3c/csswg-drafts/issues/9436#issuecomment-1746957545\n\t\tif (isNaN(θ1) && !isNaN(θ2)) {\n\t\t\tθ1 = θ2;\n\t\t}\n\t\telse if (isNaN(θ2) && !isNaN(θ1)) {\n\t\t\tθ2 = θ1;\n\t\t}\n\t\t[θ1, θ2] = angles.adjust(arc, [θ1, θ2]);\n\t\tset(color1, hue, θ1);\n\t\tset(color2, hue, θ2);\n\t}\n\n\tif (premultiplied) {\n\t\t// not coping with polar spaces yet\n\t\tcolor1.coords = color1.coords.map(c => c * color1.alpha);\n\t\tcolor2.coords = color2.coords.map(c => c * color2.alpha);\n\t}\n\n\treturn Object.assign(p => {\n\t\tp = progression ? progression(p) : p;\n\t\tlet coords = color1.coords.map((start, i) => {\n\t\t\tlet end = color2.coords[i];\n\t\t\treturn interpolate(start, end, p);\n\t\t});\n\n\t\tlet alpha = interpolate(color1.alpha, color2.alpha, p);\n\t\tlet ret = {space, coords, alpha};\n\n\t\tif (premultiplied) {\n\t\t\t// undo premultiplication\n\t\t\tret.coords = ret.coords.map(c => c / alpha);\n\t\t}\n\n\t\tif (outputSpace !== space) {\n\t\t\tret = to(ret, outputSpace);\n\t\t}\n\n\t\treturn ret;\n\t}, {\n\t\trangeArgs,\n\t});\n}\n\nexport function isRange (val) {\n\treturn type(val) === \"function\" && !!val.rangeArgs;\n}\n\ndefaults.interpolationSpace = \"lab\";\n\nexport function register (Color) {\n\tColor.defineFunction(\"mix\", mix, {returns: \"color\"});\n\tColor.defineFunction(\"range\", range, {returns: \"function<color>\"});\n\tColor.defineFunction(\"steps\", steps, {returns: \"array<color>\"});\n}\n", "import ColorSpace from \"../space.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new ColorSpace({\n\tid: \"hsl\",\n\tname: \"H<PERSON>\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: sRGB,\n\n\t// Adapted from https://drafts.csswg.org/css-color-4/better-rgbToHsl.js\n\tfromBase: rgb => {\n\t\tlet max = Math.max(...rgb);\n\t\tlet min = Math.min(...rgb);\n\t\tlet [r, g, b] = rgb;\n\t\tlet [h, s, l] = [NaN, 0, (min + max) / 2];\n\t\tlet d = max - min;\n\n\t\tif (d !== 0) {\n\t\t\ts = (l === 0 || l === 1) ? 0 : (max - l) / Math.min(l, 1 - l);\n\n\t\t\tswitch (max) {\n\t\t\t\tcase r: h = (g - b) / d + (g < b ? 6 : 0); break;\n\t\t\t\tcase g: h = (b - r) / d + 2; break;\n\t\t\t\tcase b: h = (r - g) / d + 4;\n\t\t\t}\n\n\t\t\th = h * 60;\n\t\t}\n\n\t\t// Very out of gamut colors can produce negative saturation\n\t\t// If so, just rotate the hue by 180 and use a positive saturation\n\t\t// see https://github.com/w3c/csswg-drafts/issues/9222\n\t\tif (s < 0) {\n\t\t\th += 180;\n\t\t\ts = Math.abs(s);\n\t\t}\n\n\t\tif (h >= 360) {\n\t\t\th -= 360;\n\t\t}\n\n\t\treturn [h, s * 100, l * 100];\n\t},\n\n\t// Adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_RGB_alternative\n\ttoBase: hsl => {\n\t\tlet [h, s, l] = hsl;\n\t\th = h % 360;\n\n\t\tif (h < 0) {\n\t\t\th += 360;\n\t\t}\n\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tfunction f (n) {\n\t\t\tlet k = (n + h / 30) % 12;\n\t\t\tlet a = s * Math.min(l, 1 - l);\n\t\t\treturn l - a * Math.max(-1, Math.min(k - 3, 9 - k, 1));\n\t\t}\n\n\t\treturn [f(0), f(8), f(4)];\n\t},\n\n\tformats: {\n\t\t\"hsl\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t},\n\t\t\"hsla\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSL from \"./hsl.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hsv\",\n\tname: \"HSV\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tv: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Value\",\n\t\t},\n\t},\n\n\tbase: HSL,\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\tfromBase (hsl) {\n\t\tlet [h, s, l] = hsl;\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tlet v = l + s * Math.min(l, 1 - l);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\tv === 0 ? 0 : 200 * (1 - l / v), // s\n\t\t\t100 * v,\n\t\t];\n\t},\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\ttoBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\ts /= 100;\n\t\tv /= 100;\n\n\t\tlet l = v * (1 - s / 2);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\t(l === 0 || l === 1) ? 0 : ((v - l) / Math.min(l, 1 - l)) * 100,\n\t\t\tl * 100,\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSV from \"./hsv.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hwb\",\n\tname: \"HWB\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\tw: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Whiteness\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Blackness\",\n\t\t},\n\t},\n\n\tbase: HSV,\n\tfromBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\treturn [h, v * (100 - s) / 100, 100 - v];\n\t},\n\ttoBase (hwb) {\n\t\tlet [h, w, b] = hwb;\n\n\t\t// Now convert percentages to [0..1]\n\t\tw /= 100;\n\t\tb /= 100;\n\n\t\t// Achromatic check (white plus black >= 1)\n\t\tlet sum = w + b;\n\t\tif (sum >= 1) {\n\t\t\tlet gray = w / sum;\n\t\t\treturn [h, 0, gray * 100];\n\t\t}\n\n\t\tlet v = (1 - b);\n\t\tlet s = (v === 0) ? 0 : 1 - w / v;\n\t\treturn [h, s * 100, v * 100];\n\t},\n\n\tformats: {\n\t\t\"hwb\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light a98-rgb values to CIE XYZ\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// has greater numerical precision than section ******* of\n// https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf\n// but the values below were calculated from first principles\n// from the chromaticity coordinates of R G B W\nconst toXYZ_M = [\n\t[ 0.5766690429101305,   0.1855582379065463,   0.1882286462349947  ],\n\t[ 0.29734497525053605,  0.6273635662554661,   0.07529145849399788 ],\n\t[ 0.02703136138641234,  0.07068885253582723,  0.9913375368376388  ],\n];\n\nconst fromXYZ_M = [\n\t[  2.0415879038107465,    -0.5650069742788596,   -0.34473135077832956 ],\n\t[ -0.9692436362808795,     1.8759675015077202,    0.04155505740717557 ],\n\t[  0.013444280632031142,  -0.11836239223101838,   1.0151749943912054  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb-linear\",\n\tcssId: \"--a98-rgb-linear\",\n\tname: \"Linear Adobe® 98 RGB compatible\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport A98Linear from \"./a98rgb-linear.js\";\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb\",\n\tcssId: \"a98-rgb\",\n\tname: \"Adobe® 98 RGB compatible\",\n\tbase: A98Linear,\n\ttoBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 563 / 256) * Math.sign(val)),\n\tfromBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 256 / 563) * Math.sign(val)),\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport XYZ_D50 from \"./xyz-d50.js\";\n\n// convert an array of  prophoto-rgb values to CIE XYZ\n// using  D50 (so no chromatic adaptation needed afterwards)\n// matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy\n// see https://github.com/w3c/csswg-drafts/issues/7675\nconst toXYZ_M = [\n\t[ 0.79776664490064230,  0.13518129740053308,  0.03134773412839220 ],\n\t[ 0.28807482881940130,  0.71183523424187300,  0.00008993693872564 ],\n\t[ 0.00000000000000000,  0.00000000000000000,  0.82510460251046020 ],\n];\n\nconst fromXYZ_M = [\n\t[  1.34578688164715830, -0.25557208737979464, -0.05110186497554526 ],\n\t[ -0.54463070512490190,  1.50824774284514680,  0.02052744743642139 ],\n\t[  0.00000000000000000,  0.00000000000000000,  1.21196754563894520 ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"prophoto-linear\",\n\tcssId: \"--prophoto-rgb-linear\",\n\tname: \"Linear ProPhoto\",\n\twhite: \"D50\",\n\tbase: XYZ_D50,\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport ProPhotoLinear from \"./prophoto-linear.js\";\n\nconst Et = 1 / 512;\nconst Et2 = 16 / 512;\n\nexport default new RGBColorSpace({\n\tid: \"prophoto\",\n\tcssId: \"prophoto-rgb\",\n\tname: \"ProPhoto\",\n\tbase: ProPhotoLinear,\n\ttoBase (RGB) {\n\t\t// Transfer curve is gamma 1.8 with a small linear portion\n\t\treturn RGB.map(v => v < Et2 ? v / 16 : v ** 1.8);\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(v => v >= Et ? v ** (1 / 1.8) : 16 * v);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport OKLab from \"./oklab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"oklch\",\n\tname: \"Oklch\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 0.4],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\twhite: \"D65\",\n\n\tbase: OKLab,\n\tfromBase (oklab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = oklab;\n\t\tlet h;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\th = NaN;\n\t\t}\n\t\telse {\n\t\t\th = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // OKLab L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(h), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\t// Convert from polar form\n\ttoBase (oklch) {\n\t\tlet [L, C, h] = oklch;\n\t\tlet a, b;\n\n\t\t// check for NaN hue\n\t\tif (isNaN(h)) {\n\t\t\ta = 0;\n\t\t\tb = 0;\n\t\t}\n\t\telse {\n\t\t\ta = C * Math.cos(h * Math.PI / 180);\n\t\t\tb = C * Math.sin(h * Math.PI / 180);\n\t\t}\n\n\t\treturn [ L, a, b ];\n\t},\n\n\tformats: {\n\t\t\"oklch\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[0,1]\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {uv} from \"../chromaticity.js\";\nimport {isNone, skipNone} from \"../util.js\";\n\nlet white = WHITES.D65;\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\nconst [U_PRIME_WHITE, V_PRIME_WHITE] = uv({space: xyz_d65, coords: white});\n\nexport default new ColorSpace({\n\tid: \"luv\",\n\tname: \"Luv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\t// Reference ranges from https://facelessuser.github.io/coloraide/colors/luv/\n\t\tu: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t\tv: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t},\n\n\twhite: white,\n\tbase: xyz_d65,\n\n\t// Convert D65-adapted XYZ to Luv\n\t// https://en.wikipedia.org/wiki/CIELUV#The_forward_transformation\n\tfromBase (XYZ) {\n\t\tlet xyz = [skipNone(XYZ[0]), skipNone(XYZ[1]), skipNone(XYZ[2])];\n\t\tlet y = xyz[1];\n\n\t\tlet [up, vp] = uv({space: xyz_d65, coords: xyz});\n\n\t\t// Protect against XYZ of [0, 0, 0]\n\t\tif (!Number.isFinite(up) || !Number.isFinite(vp)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tlet L = y <= ε ? κ * y : 116 * Math.cbrt(y) - 16;\n\t\treturn [\n\t\t\tL,\n\t\t\t13 * L * (up - U_PRIME_WHITE),\n\t\t\t13 * L * (vp - V_PRIME_WHITE),\n\t\t ];\n\t},\n\n\t// Convert Luv to D65-adapted XYZ\n\t// https://en.wikipedia.org/wiki/CIELUV#The_reverse_transformation\n\ttoBase (Luv) {\n\t\tlet [L, u, v] = Luv;\n\n\t\t// Protect against division by zero and NaN Lightness\n\t\tif (L === 0 || isNone(L)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tu = skipNone(u);\n\t\tv = skipNone(v);\n\n\t\tlet up = (u / (13 * L)) + U_PRIME_WHITE;\n\t\tlet vp = (v / (13 * L)) + V_PRIME_WHITE;\n\n\t\tlet y = L <= 8 ? L / κ : Math.pow((L + 16) / 116, 3);\n\n\t\treturn [\n\t\t\ty * ((9 * up) / (4 * vp)),\n\t\t\ty,\n\t\t\ty * ((12 - 3 * up - 20 * vp) / (4 * vp)),\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--luv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport Luv from \"./luv.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lchuv\",\n\tname: \"<PERSON>Chuv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 220],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Luv,\n\tfromBase (Luv) {\n\t\t// Convert to polar form\n\t\tlet [L, u, v] = Luv;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(u) < ε && Math.abs(v) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(v, u) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(u ** 2 + v ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // u\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // v\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--lchuv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport sRGB from \"./srgb.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOriginAngle (slope, intercept, angle) {\n\tconst d = intercept / (Math.sin(angle) - slope * Math.cos(angle));\n\treturn d < 0 ? Infinity : d;\n}\n\nexport function calculateBoundingLines (l) {\n\tconst sub1 = Math.pow(l + 16, 3) / 1560896;\n\tconst sub2 = sub1 > ε ? sub1 : l / κ;\n\tconst s1r = sub2 * (284517 * m_r0 - 94839 * m_r2);\n\tconst s2r = sub2 * (838422 * m_r2 + 769860 * m_r1 + 731718 * m_r0);\n\tconst s3r = sub2 * (632260 * m_r2 - 126452 * m_r1);\n\tconst s1g = sub2 * (284517 * m_g0 - 94839 * m_g2);\n\tconst s2g = sub2 * (838422 * m_g2 + 769860 * m_g1 + 731718 * m_g0);\n\tconst s3g = sub2 * (632260 * m_g2 - 126452 * m_g1);\n\tconst s1b = sub2 * (284517 * m_b0 - 94839 * m_b2);\n\tconst s2b = sub2 * (838422 * m_b2 + 769860 * m_b1 + 731718 * m_b0);\n\tconst s3b = sub2 * (632260 * m_b2 - 126452 * m_b1);\n\n\treturn {\n\t\tr0s: s1r / s3r,\n\t\tr0i: s2r * l / s3r,\n\t\tr1s: s1r / (s3r + 126452),\n\t\tr1i: (s2r - 769860) * l / (s3r + 126452),\n\t\tg0s: s1g / s3g,\n\t\tg0i: s2g * l / s3g,\n\t\tg1s: s1g / (s3g + 126452),\n\t\tg1i: (s2g - 769860) * l / (s3g + 126452),\n\t\tb0s: s1b / s3b,\n\t\tb0i: s2b * l / s3b,\n\t\tb1s: s1b / (s3b + 126452),\n\t\tb1i: (s2b - 769860) * l / (s3b + 126452),\n\t};\n}\n\nfunction calcMaxChromaHsluv (lines, h) {\n\tconst hueRad = h / 360 * Math.PI * 2;\n\tconst r0 = distanceFromOriginAngle(lines.r0s, lines.r0i, hueRad);\n\tconst r1 = distanceFromOriginAngle(lines.r1s, lines.r1i, hueRad);\n\tconst g0 = distanceFromOriginAngle(lines.g0s, lines.g0i, hueRad);\n\tconst g1 = distanceFromOriginAngle(lines.g1s, lines.g1i, hueRad);\n\tconst b0 = distanceFromOriginAngle(lines.b0s, lines.b0i, hueRad);\n\tconst b1 = distanceFromOriginAngle(lines.b1s, lines.b1i, hueRad);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hsluv\",\n\tname: \"HSLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: sRGB,\n\n\t// Convert LCHuv to HSLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\ts = c / max * 100;\n\t\t}\n\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HSLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\nimport {calculateBoundingLines} from \"./hsluv.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOrigin (slope, intercept) {\n\treturn Math.abs(intercept) / Math.sqrt(Math.pow(slope, 2) + 1);\n}\n\nfunction calcMaxChromaHpluv (lines) {\n\tlet r0 = distanceFromOrigin(lines.r0s, lines.r0i);\n\tlet r1 = distanceFromOrigin(lines.r1s, lines.r1i);\n\tlet g0 = distanceFromOrigin(lines.g0s, lines.g0i);\n\tlet g1 = distanceFromOrigin(lines.g1s, lines.g1i);\n\tlet b0 = distanceFromOrigin(lines.b0s, lines.b0i);\n\tlet b1 = distanceFromOrigin(lines.b1s, lines.b1i);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hpluv\",\n\tname: \"HPLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: \"self\",\n\n\t// Convert LCHuv to HPLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines);\n\t\t\ts = c / max * 100;\n\t\t}\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HPLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hpluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\nconst Yw = 203;\t// absolute luminance of media white, cd/m²\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst m = 2523 / (2 ** 5);\nconst minv = (2 ** 5) / 2523;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\n\nexport default new RGBColorSpace({\n\tid: \"rec2100pq\",\n\tcssId: \"rec2100-pq\",\n\tname: \"REC.2100-PQ\",\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given PQ encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = ((Math.max(((val ** minv) - c1), 0) / (c2 - (c3 * (val ** minv)))) ** ninv);\n\t\t\treturn (x * 10000 / Yw); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// returnPQ encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = Math.max(val * Yw / 10000, 0); \t// absolute luminance of peak white is 10,000 cd/m².\n\t\t\tlet num = (c1 + (c2 * (x ** n)));\n\t\t\tlet denom = (1 + (c3 * (x ** n)));\n\n\t\t\treturn ((num / denom)  ** m);\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\n// FIXME see https://github.com/LeaVerou/color.js/issues/190\n\nconst a = 0.17883277;\nconst b = 0.28466892; // 1 - (4 * a)\nconst c = 0.55991073; // 0.5 - a * Math.log(4 *a)\n\nconst scale = 3.7743;\t// Place 18% grey at HLG 0.38, so media white at 0.75\n\nexport default new RGBColorSpace({\n\tid: \"rec2100hlg\",\n\tcssId: \"rec2100-hlg\",\n\tname: \"REC.2100-HLG\",\n\treferred: \"scene\",\n\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given HLG encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\t// first the HLG EOTF\n\t\t\t// ITU-R BT.2390-10 p.30 section\n\t\t\t// 6.3 The hybrid log-gamma electro-optical transfer function (EOTF)\n\t\t\t// Then scale by 3 so media white is 1.0\n\t\t\tif (val <= 0.5) {\n\t\t\t\treturn (val ** 2) / 3 * scale;\n\t\t\t}\n\t\t\treturn ((Math.exp((val - c) / a) + b) / 12) * scale;\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// where diffuse white is 1.0,\n\t\t// return HLG encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\t// first scale to put linear-light media white at 1/3\n\t\t\tval /= scale;\n\t\t\t// now the HLG OETF\n\t\t\t// ITU-R BT.2390-10 p.23\n\t\t\t// 6.1 The hybrid log-gamma opto-electronic transfer function (OETF)\n\t\t\tif (val <= 1 / 12) {\n\t\t\t\treturn Math.sqrt(3 * val);\n\t\t\t}\n\t\t\treturn a * Math.log(12 * val - b) + c;\n\t\t});\n\t},\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport {WHITES} from \"./adapt.js\";\n\nexport const CATs = {};\n\nhooks.add(\"chromatic-adaptation-start\", env => {\n\tif (env.options.method) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nhooks.add(\"chromatic-adaptation-end\", env => {\n\tif (!env.M) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nexport function defineCAT ({id, toCone_M, fromCone_M}) {\n\t// Use id, toCone_M, fromCone_M like variables\n\tCATs[id] = arguments[0];\n}\n\nexport function adapt (W1, W2, id = \"Bradford\") {\n\t// adapt from a source whitepoint or illuminant W1\n\t// to a destination whitepoint or illuminant W2,\n\t// using the given chromatic adaptation transform (CAT)\n\t// debugger;\n\tlet method = CATs[id];\n\n\tlet [ρs, γs, βs] = multiplyMatrices(method.toCone_M, W1);\n\tlet [ρd, γd, βd] = multiplyMatrices(method.toCone_M, W2);\n\n\t// all practical illuminants have non-zero XYZ so no division by zero can occur below\n\tlet scale = [\n\t\t[ρd / ρs,  0,        0      ],\n\t\t[0,        γd / γs,  0      ],\n\t\t[0,        0,        βd / βs],\n\t];\n\t// console.log({scale});\n\n\tlet scaled_cone_M = multiplyMatrices(scale, method.toCone_M);\n\tlet adapt_M\t= multiplyMatrices(method.fromCone_M, scaled_cone_M);\n\t// console.log({scaled_cone_M, adapt_M});\n\treturn adapt_M;\n}\n\ndefineCAT({\n\tid: \"von Kries\",\n\ttoCone_M: [\n\t\t[  0.4002400,  0.7076000, -0.0808100 ],\n\t\t[ -0.2263000,  1.1653200,  0.0457000 ],\n\t\t[  0.0000000,  0.0000000,  0.9182200 ],\n\t],\n\tfromCone_M: [\n\t\t[ 1.8599363874558397, -1.1293816185800916,   0.21989740959619328     ],\n\t\t[ 0.3611914362417676,  0.6388124632850422,  -0.000006370596838649899 ],\n\t\t[ 0,                   0,                    1.0890636230968613      ],\n\t],\n});\n\ndefineCAT({\n\tid: \"Bradford\",\n\t// Convert an array of XYZ values in the range 0.0 - 1.0\n\t// to cone fundamentals\n\ttoCone_M: [\n\t\t[  0.8951000,  0.2664000, -0.1614000 ],\n\t\t[ -0.7502000,  1.7135000,  0.0367000 ],\n\t\t[  0.0389000, -0.0685000,  1.0296000 ],\n\t],\n\t// and back\n\tfromCone_M: [\n\t\t[  0.9869929054667121, -0.14705425642099013, 0.15996265166373122  ],\n\t\t[  0.4323052697233945,  0.5183602715367774,  0.049291228212855594 ],\n\t\t[ -0.00852866457517732, 0.04004282165408486, 0.96848669578755     ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT02\",\n\t// with complete chromatic adaptation to W2, so D = 1.0\n\ttoCone_M: [\n\t\t[  0.7328000,  0.4296000, -0.1624000 ],\n\t\t[ -0.7036000,  1.6975000,  0.0061000 ],\n\t\t[  0.0030000,  0.0136000,  0.9834000 ],\n\t],\n\tfromCone_M: [\n\t\t[  1.0961238208355142,   -0.27886900021828726, 0.18274517938277307 ],\n\t\t[  0.4543690419753592,    0.4735331543074117,  0.07209780371722911 ],\n\t\t[ -0.009627608738429355, -0.00569803121611342, 1.0153256399545427  ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT16\",\n\ttoCone_M: [\n\t\t[  0.401288,  0.650173, -0.051461 ],\n\t\t[ -0.250268,  1.204414,  0.045854 ],\n\t\t[ -0.002079,  0.048952,  0.953127 ],\n\t],\n\t// the extra precision is needed to avoid roundtripping errors\n\tfromCone_M: [\n\t\t[  1.862067855087233,   -1.0112546305316845,  0.14918677544445172  ],\n\t\t[  0.3875265432361372,   0.6214474419314753, -0.008973985167612521 ],\n\t\t[ -0.01584149884933386, -0.03412293802851557, 1.0499644368778496   ],\n\t],\n});\n\nObject.assign(WHITES, {\n\t// whitepoint values from ASTM E308-01 with 10nm spacing, 1931 2 degree observer\n\t// all normalized to Y (luminance) = 1.00000\n\t// Illuminant A is a tungsten electric light, giving a very warm, orange light.\n\tA:   [1.09850, 1.00000, 0.35585],\n\n\t// Illuminant C was an early approximation to daylight: illuminant A with a blue filter.\n\tC:   [0.98074, 1.000000, 1.18232],\n\n\t// The daylight series of illuminants simulate natural daylight.\n\t// The color temperature (in degrees Kelvin/100) ranges from\n\t// cool, overcast daylight (D50) to bright, direct sunlight (D65).\n\tD55: [0.95682, 1.00000, 0.92149],\n\tD75: [0.94972, 1.00000, 1.22638],\n\n\t// Equal-energy illuminant, used in two-stage CAT16\n\tE:   [1.00000, 1.00000, 1.00000],\n\n\t// The F series of illuminants represent fluorescent lights\n\tF2:  [0.99186, 1.00000, 0.67393],\n\tF7:  [0.95041, 1.00000, 1.08747],\n\tF11: [1.00962, 1.00000, 0.64350],\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport {WHITES} from \"../adapt.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\n\n// The ACES whitepoint\n// see TB-2018-001 Derivation of the ACES White Point CIE Chromaticity Coordinates\n// also https://github.com/ampas/aces-dev/blob/master/documents/python/TB-2018-001/aces_wp.py\n// Similar to D60\nWHITES.ACES = [0.32168 / 0.33767, 1.00000, (1.00000 - 0.32168 - 0.33767) / 0.33767];\n\n// convert an array of linear-light ACEScc values to CIE XYZ\nconst toXYZ_M = [\n\t[  0.6624541811085053,   0.13400420645643313,  0.1561876870049078  ],\n\t[  0.27222871678091454,  0.6740817658111484,   0.05368951740793705 ],\n\t[ -0.005574649490394108, 0.004060733528982826, 1.0103391003129971  ],\n];\nconst fromXYZ_M = [\n\t[  1.6410233796943257,   -0.32480329418479,    -0.23642469523761225  ],\n\t[ -0.6636628587229829,    1.6153315916573379,   0.016756347685530137 ],\n\t[  0.011721894328375376, -0.008284441996237409, 0.9883948585390215   ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"acescg\",\n\tcssId: \"--acescg\",\n\tname: \"ACEScg\",\n\n\t// ACEScg – A scene-referred, linear-light encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescg/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\tcoords: {\n\t\tr: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\n\treferred: \"scene\",\n\n\twhite: WHITES.ACES,\n\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n\n// export default Color;\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\nimport ACEScg from \"./acescg.js\";\n\nconst ε = 2 ** -16;\n\n// the smallest value which, in the 32bit IEEE 754 float encoding,\n// decodes as a non-negative value\nconst ACES_min_nonzero = -0.35828683;\n\n// brightest encoded value, decodes to 65504\nconst ACES_cc_max = (Math.log2(65504) + 9.72) / 17.52; // 1.468\n\nexport default new RGBColorSpace({\n\tid: \"acescc\",\n\tcssId: \"--acescc\",\n\tname: \"ACEScc\",\n\t// see S-2014-003 ACEScc – A Logarithmic Encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescc/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\n\t// Appendix A: \"Very small ACES scene referred values below 7 1/4 stops\n\t// below 18% middle gray are encoded as negative ACEScc values.\n\t// These values should be preserved per the encoding in Section 4.4\n\t// so that all positive ACES values are maintained.\"\n\tcoords: {\n\t\tr: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\treferred: \"scene\",\n\n\tbase: ACEScg,\n\t// from section 4.4.2 Decoding Function\n\ttoBase (RGB) {\n\t\tconst low = (9.72 - 15) / 17.52; // -0.3014\n\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= low) {\n\t\t\t\treturn (2 ** ((val * 17.52) - 9.72) - ε) * 2; // very low values, below -0.3014\n\t\t\t}\n\t\t\telse if (val < ACES_cc_max) {\n\t\t\t\treturn 2 ** ((val * 17.52) - 9.72);\n\t\t\t}\n\t\t\telse { // val >= ACES_cc_max\n\t\t\t\treturn 65504;\n\t\t\t}\n\t\t});\n\t},\n\n\t// Non-linear encoding function from S-2014-003, section 4.4.1 Encoding Function\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= 0) {\n\t\t\t\treturn (Math.log2(ε) + 9.72) / 17.52; // -0.3584\n\t\t\t}\n\t\t\telse if (val < ε) {\n\t\t\t\treturn  (Math.log2(ε + val * 0.5) + 9.72) / 17.52;\n\t\t\t}\n\t\t\telse { // val >= ε\n\t\t\t\treturn  (Math.log2(val) + 9.72) / 17.52;\n\t\t\t}\n\t\t});\n\t},\n\t// encoded media white (rgb 1,1,1) => linear  [ 222.861, 222.861, 222.861 ]\n\t// encoded media black (rgb 0,0,0) => linear [ 0.0011857, 0.0011857, 0.0011857]\n});\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport defaults from \"./defaults.js\";\nimport ColorSpace from \"./space.js\";\nimport {WHITES} from \"./adapt.js\";\nimport {\n\tgetColor,\n\tparse,\n\tto,\n\tserialize,\n\tinGamut,\n\ttoGamut,\n\tdistance,\n\tequals,\n\tget,\n\tgetAll,\n\tset,\n\tsetAll,\n\tdisplay,\n} from \"./index-fn.js\";\n\n\nimport \"./spaces/xyz-d50.js\";\nimport \"./spaces/srgb.js\";\n\n/**\n * Class that represents a color\n */\nexport default class Color {\n\t/**\n\t * Creates an instance of Color.\n\t * Signatures:\n\t * - `new Color(stringToParse)`\n\t * - `new Color(otherColor)`\n\t * - `new Color({space, coords, alpha})`\n\t * - `new Color(space, coords, alpha)`\n\t * - `new Color(spaceId, coords, alpha)`\n\t */\n\tconstructor (...args) {\n\t\tlet color;\n\n\t\tif (args.length === 1) {\n\t\t\tcolor = getColor(args[0]);\n\t\t}\n\n\t\tlet space, coords, alpha;\n\n\t\tif (color) {\n\t\t\tspace = color.space || color.spaceId;\n\t\t\tcoords = color.coords;\n\t\t\talpha = color.alpha;\n\t\t}\n\t\telse {\n\t\t\t// default signature new Color(ColorSpace, array [, alpha])\n\t\t\t[space, coords, alpha] = args;\n\t\t}\n\n\t\tObject.defineProperty(this, \"space\", {\n\t\t\tvalue: ColorSpace.get(space),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true, // see note in https://262.ecma-international.org/8.0/#sec-proxy-object-internal-methods-and-internal-slots-get-p-receiver\n\t\t});\n\n\t\tthis.coords = coords ? coords.slice() : [0, 0, 0];\n\n\t\t// Clamp alpha to [0, 1]\n\t\tthis.alpha = alpha > 1 || alpha === undefined ? 1 : (alpha < 0 ? 0 : alpha);\n\n\t\t// Convert \"NaN\" to NaN\n\t\tfor (let i = 0; i < this.coords.length; i++) {\n\t\t\tif (this.coords[i] === \"NaN\") {\n\t\t\t\tthis.coords[i] = NaN;\n\t\t\t}\n\t\t}\n\n\t\t// Define getters and setters for each coordinate\n\t\tfor (let id in this.space.coords) {\n\t\t\tObject.defineProperty(this, id, {\n\t\t\t\tget: () => this.get(id),\n\t\t\t\tset: value => this.set(id, value),\n\t\t\t});\n\t\t}\n\t}\n\n\tget spaceId () {\n\t\treturn this.space.id;\n\t}\n\n\tclone () {\n\t\treturn new Color(this.space, this.coords, this.alpha);\n\t}\n\n\ttoJSON () {\n\t\treturn {\n\t\t\tspaceId: this.spaceId,\n\t\t\tcoords: this.coords,\n\t\t\talpha: this.alpha,\n\t\t};\n\t}\n\n\tdisplay (...args) {\n\t\tlet ret = display(this, ...args);\n\n\t\t// Convert color object to Color instance\n\t\tret.color = new Color(ret.color);\n\n\t\treturn ret;\n\t}\n\n\t/**\n\t * Get a color from the argument passed\n\t * Basically gets us the same result as new Color(color) but doesn't clone an existing color object\n\t */\n\tstatic get (color, ...args) {\n\t\tif (color instanceof Color) {\n\t\t\treturn color;\n\t\t}\n\n\t\treturn new Color(color, ...args);\n\t}\n\n\tstatic defineFunction (name, code, o = code) {\n\t\tlet {instance = true, returns} = o;\n\n\t\tlet func = function (...args) {\n\t\t\tlet ret = code(...args);\n\n\t\t\tif (returns === \"color\") {\n\t\t\t\tret = Color.get(ret);\n\t\t\t}\n\t\t\telse if (returns === \"function<color>\") {\n\t\t\t\tlet f = ret;\n\t\t\t\tret = function (...args) {\n\t\t\t\t\tlet ret = f(...args);\n\t\t\t\t\treturn Color.get(ret);\n\t\t\t\t};\n\t\t\t\t// Copy any function metadata\n\t\t\t\tObject.assign(ret, f);\n\t\t\t}\n\t\t\telse if (returns === \"array<color>\") {\n\t\t\t\tret = ret.map(c => Color.get(c));\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t};\n\n\t\tif (!(name in Color)) {\n\t\t\tColor[name] = func;\n\t\t}\n\n\t\tif (instance) {\n\t\t\tColor.prototype[name] = function (...args) {\n\t\t\t\treturn func(this, ...args);\n\t\t\t};\n\t\t}\n\t}\n\n\tstatic defineFunctions (o) {\n\t\tfor (let name in o) {\n\t\t\tColor.defineFunction(name, o[name], o[name]);\n\t\t}\n\t}\n\n\tstatic extend (exports) {\n\t\tif (exports.register) {\n\t\t\texports.register(Color);\n\t\t}\n\t\telse {\n\t\t\t// No register method, just add the module's functions\n\t\t\tfor (let name in exports) {\n\t\t\t\tColor.defineFunction(name, exports[name]);\n\t\t\t}\n\t\t}\n\t}\n}\n\nColor.defineFunctions({\n\tget,\n\tgetAll,\n\tset,\n\tsetAll,\n\tto,\n\tequals,\n\tinGamut,\n\ttoGamut,\n\tdistance,\n\ttoString: serialize,\n});\n\nObject.assign(Color, {\n\tutil,\n\thooks,\n\tWHITES,\n\tSpace: ColorSpace,\n\tspaces: ColorSpace.registry,\n\tparse,\n\n\t// Global defaults one may want to configure\n\tdefaults,\n});\n", "import ColorSpace from \"../space.js\";\nimport * as spaces from \"./index-fn.js\";\n\nexport * as spaces from \"./index-fn.js\";\n\nfor (let key of Object.keys(spaces)) {\n\tColorSpace.register(spaces[key]);\n}\n", "/**\n * This plugin defines getters and setters for color[spaceId]\n * e.g. color.lch on *any* color gives us the lch coords\n */\nimport ColorSpace from \"./space.js\";\nimport Color from \"./color.js\";\nimport hooks from \"./hooks.js\";\n\n// Add space accessors to existing color spaces\nfor (let id in ColorSpace.registry) {\n\taddSpaceAccessors(id, ColorSpace.registry[id]);\n}\n\n// Add space accessors to color spaces not yet created\nhooks.add(\"colorspace-init-end\", space => {\n\taddSpaceAccessors(space.id, space);\n\tspace.aliases?.forEach(alias => {\n\t\taddSpaceAccessors(alias, space);\n\t});\n});\n\nfunction addSpaceAccessors (id, space) {\n\tlet propId = id.replace(/-/g, \"_\");\n\n\tObject.defineProperty(Color.prototype, propId, {\n\t\t// Convert coords to coords in another colorspace and return them\n\t\t// Source colorspace: this.spaceId\n\t\t// Target colorspace: id\n\t\tget () {\n\t\t\tlet ret = this.getAll(id);\n\n\t\t\tif (typeof Proxy === \"undefined\") {\n\t\t\t\t// If proxies are not supported, just return a static array\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\t// Enable color.spaceId.coordName syntax\n\t\t\treturn new Proxy(ret, {\n\t\t\t\thas: (obj, property) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tColorSpace.resolveCoord([space, property]);\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tcatch (e) {}\n\n\t\t\t\t\treturn Reflect.has(obj, property);\n\t\t\t\t},\n\t\t\t\tget: (obj, property, receiver) => {\n\t\t\t\t\tif (property && typeof property !== \"symbol\" && !(property in obj)) {\n\t\t\t\t\t\tlet {index} = ColorSpace.resolveCoord([space, property]);\n\n\t\t\t\t\t\tif (index >= 0) {\n\t\t\t\t\t\t\treturn obj[index];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Reflect.get(obj, property, receiver);\n\t\t\t\t},\n\t\t\t\tset: (obj, property, value, receiver) => {\n\t\t\t\t\tif (property && typeof property !== \"symbol\" && !(property in obj) || property >= 0) {\n\t\t\t\t\t\tlet {index} = ColorSpace.resolveCoord([space, property]);\n\n\t\t\t\t\t\tif (index >= 0) {\n\t\t\t\t\t\t\tobj[index] = value;\n\n\t\t\t\t\t\t\t// Update color.coords\n\t\t\t\t\t\t\tthis.setAll(id, obj);\n\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Reflect.set(obj, property, value, receiver);\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t\t// Convert coords in another colorspace to internal coords and set them\n\t\t// Target colorspace: this.spaceId\n\t\t// Source colorspace: id\n\t\tset (coords) {\n\t\t\tthis.setAll(id, coords);\n\t\t},\n\t\tconfigurable: true,\n\t\tenumerable: true,\n\t});\n}\n", "// Import all modules of Color.js\nimport Color from \"./color.js\";\n\n// Import all color spaces\nimport \"./spaces/index.js\";\n\n// Import all DeltaE methods\nimport deltaE from \"./deltaE.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nColor.extend(deltaEMethods);\nColor.extend({deltaE});\nObject.assign(Color, {deltaEMethods});\n\n// Import optional modules\nimport * as variations from \"./variations.js\";\nColor.extend(variations);\n\nimport contrast from \"./contrast.js\";\nColor.extend({contrast});\n\nimport * as chromaticity from \"./chromaticity.js\";\nColor.extend(chromaticity);\n\nimport * as luminance from \"./luminance.js\";\nColor.extend(luminance);\n\nimport * as interpolation from \"./interpolation.js\";\nColor.extend(interpolation);\n\nimport * as contrastMethods from \"./contrast/index.js\";\nColor.extend(contrastMethods);\n\nimport \"./CATs.js\";\nimport \"./space-accessors.js\";\n\n// Re-export everything\nexport default Color;\n"], "names": ["adapt", "util.mapRange", "util.parseFunction", "util.last", "ε", "XYZ_D65", "ε3", "κ", "white", "xyz_d50", "Lab", "constrainAngle", "π", "d2r", "XYZtoLMS_M", "LMStoXYZ_M", "oklab", "Yw", "b", "n", "ninv", "c1", "c2", "c3", "m1", "deg2rad", "viewingConditions", "util.isString", "util.isNone", "util.clamp", "inGamut", "checkInGamut", "util.serializeNumber", "toXYZ_M", "fromXYZ_M", "register", "contrastAlgorithms", "angles.adjust"], "mappings": ";;;CAAA;CACe,SAAS,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE;CAChD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAClB;CACA,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;CAC3B;CACA,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACV,EAAE;AACF;CACA,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;CAC3B;CACA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CACtB,EAAE;AACF;CACA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;CACrB,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACnD,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI;CAC9C,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACd;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;CAC3B,GAAG,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE;CACtB,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;CACnB,IAAI;AACJ;CACA,GAAG,OAAO,GAAG,CAAC;CACd,GAAG;AACH;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACvC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;CACjC,GAAG;AACH;CACA,EAAE,OAAO,GAAG,CAAC;CACb,EAAE,CAAC,CAAC,CAAC;AACL;CACA,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;CACd,EAAE,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CACvB,EAAE;AACF;CACA,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;CACd,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAChC,EAAE;AACF;CACA,CAAC,OAAO,OAAO,CAAC;CAChB;;CC3CA;CACA;CACA;AACA;AAEA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,QAAQ,EAAE,GAAG,EAAE;CAC/B,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC;CAC/B,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,IAAI,EAAE,CAAC,EAAE;CACzB,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7C;CACA,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC;CACnE,CAAC;AACD;CACO,SAAS,eAAe,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE;CACxD,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;CAChB,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;AACF;CACA,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;CACjD,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,MAAM,EAAE,CAAC,EAAE;CAC3B,CAAC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;CAC5D,CAAC;AACD;CACA;CACA;CACA;CACO,SAAS,QAAQ,EAAE,CAAC,EAAE;CAC7B,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CAC1B,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE;CAC3C,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;CACd,EAAE,OAAO,CAAC,CAAC;CACX,EAAE;CACF,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;CACnB,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;CAChB,CAAC,IAAI,OAAO,IAAI,SAAS,EAAE;CAC3B,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;CAC/C,EAAE;CACF,CAAC,MAAM,UAAU,GAAG,IAAI,KAAK,SAAS,GAAG,MAAM,CAAC,CAAC;CACjD,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,GAAG,CAAC,GAAG,UAAU,CAAC;CACtD,CAAC;AACD;CACA,MAAM,WAAW,GAAG;CACpB,CAAC,GAAG,EAAE,CAAC;CACP,CAAC,IAAI,EAAE,GAAG;CACV,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE;CACnB,CAAC,IAAI,EAAE,GAAG;CACV,CAAC,CAAC;AACF;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,aAAa,EAAE,GAAG,EAAE;CACpC,CAAC,IAAI,CAAC,GAAG,EAAE;CACX,EAAE,OAAO;CACT,EAAE;AACF;CACA,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AAClB;CACA,CAAC,MAAM,eAAe,GAAG,sBAAsB,CAAC;CAChD,CAAC,MAAM,aAAa,GAAG,YAAY,CAAC;CACpC,CAAC,MAAM,cAAc,GAAG,mBAAmB,CAAC;CAC5C,CAAC,MAAM,cAAc,GAAG,4CAA4C,CAAC;CACrE,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AACxC;CACA,CAAC,IAAI,KAAK,EAAE;CACZ;CACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;CAChB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,MAAM,KAAK;CACnD,GAAG,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;CAC5C,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC;AACpB;CACA,GAAG,IAAI,KAAK,EAAE;CACd,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;CACxB;CACA,IAAI,IAAI,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjD;CACA,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE;CACtB;CACA,KAAK,GAAG,GAAG,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;CACzC,KAAK,GAAG,CAAC,IAAI,GAAG,cAAc,CAAC;CAC/B,KAAK;CACL,SAAS;CACT;CACA,KAAK,GAAG,GAAG,IAAI,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;CACvD,KAAK,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC;CAC1B,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;CACrB,KAAK;CACL,IAAI;CACJ,QAAQ,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;CACrC;CACA,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;CAC1B,IAAI,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC;CAC1B,IAAI;CACJ,QAAQ,IAAI,GAAG,KAAK,MAAM,EAAE;CAC5B,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;CAC1B,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;CACpB,IAAI;AACJ;CACA,GAAG,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;CAC3B;CACA,IAAI,GAAG,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;CACxD,IAAI,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC;CACrB,IAAI;AACJ;CACA,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,MAAM,EAAE;CACzD,IAAI,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC;CACrB,IAAI;AACJ;CACA,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CAClB,GAAG,CAAC,CAAC;AACL;CACA,EAAE,OAAO;CACT,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;CAC/B,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;CACpB,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;CACpB;CACA;CACA,GAAG,IAAI;CACP,GAAG,CAAC;CACJ,EAAE;CACF,CAAC;AACD;CACO,SAAS,IAAI,EAAE,GAAG,EAAE;CAC3B,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;CAC5B,CAAC;AACD;CACO,SAAS,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;CAC5C,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;CACnB,EAAE,OAAO,GAAG,CAAC;CACb,EAAE;AACF;CACA,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;CACjB,EAAE,OAAO,KAAK,CAAC;CACf,EAAE;AACF;CACA,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC;CAClC,CAAC;AACD;CACO,SAAS,cAAc,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;CACnD,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;CACxC,CAAC;AACD;CACO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE;CAC3C,CAAC,OAAO,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;CAC3E,CAAC;AACD;CACO,SAAS,iBAAiB,EAAE,aAAa,EAAE;CAClD,CAAC,OAAO,aAAa,CAAC,GAAG,CAAC,YAAY,IAAI;CAC1C,EAAE,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI;CAC7C,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;CACtB,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;AACvE;CACA,GAAG,IAAI,KAAK,EAAE;CACd,IAAI,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACnC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACvC,IAAI,OAAO,GAAG,CAAC;CACf,IAAI;AACJ;CACA,GAAG,OAAO,IAAI,CAAC;CACf,GAAG,CAAC,CAAC;CACL,EAAE,CAAC,CAAC;CACJ,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;CACtC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;CAC1C,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE;CACpC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;CACrD,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE;CACjC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC;CAC9C,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;CAC5B,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CAC9B,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,UAAU,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE;CACjE,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;CACjB,EAAE,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;CAC7B,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE;CACxB,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;CAChB,GAAG;CACH,OAAO;CACP,GAAG,EAAE,GAAG,GAAG,CAAC;CACZ,GAAG;CACH,EAAE;CACF,CAAC,OAAO,EAAE,CAAC;CACX;;;;;;;;;;;;;;;;;;;;;;;;CC7PA;CACA;CACA;CACO,MAAM,KAAK,CAAC;CACnB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;CAC7B,EAAE,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;CACvC;CACA,GAAG,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;CAClC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;CACrD,IAAI;AACJ;CACA,GAAG,OAAO;CACV,GAAG;AACH;CACA,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,UAAU,IAAI,EAAE;CAChE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACjC;CACA,GAAG,IAAI,QAAQ,EAAE;CACjB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;CACrD,IAAI;CACJ,GAAG,EAAE,IAAI,CAAC,CAAC;CACX,EAAE;AACF;CACA,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE;CACjB,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CAChC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;CACzC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;CAC9D,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC;AACD;CACA;CACA;CACA;CACA,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE;;CClCzB;AACA,gBAAe;CACf,CAAC,aAAa,EAAE,KAAK;CACrB,CAAC,SAAS,EAAE,CAAC;CACb,CAAC,MAAM,EAAE,IAAI;CACb,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,MAAM;CACtE,CAAC,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,EAAE;CAC3B,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE;CACpB,GAAG,UAAU,EAAE,OAAO,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;CACpC,GAAG;CACH,EAAE;CACF,CAAC;;CCRM,MAAM,MAAM,GAAG;CACtB;CACA,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC;CAClE,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC;CAClE,CAAC,CAAC;AACF;CACO,SAAS,QAAQ,EAAE,IAAI,EAAE;CAChC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;CAC1B,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;CACrB,CAAC;AACD;CACA;CACe,SAASA,OAAK,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE;CAC1D,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;CACnB,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AACnB;CACA,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE;CACjB,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC,+BAA+B,EAAE,CAAC,EAAE,GAAG,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;CACvH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;CAChB;CACA,EAAE,OAAO,GAAG,CAAC;CACb,EAAE;AACF;CACA,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAClC;CACA,CAAC,KAAK,CAAC,GAAG,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;AAC9C;CACA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;CACb,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE;CACtD,GAAG,GAAG,CAAC,CAAC,GAAG;CACX,IAAI,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,CAAC,mBAAmB,EAAE;CACtE,IAAI,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,oBAAoB,EAAE;CACtE,IAAI,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE;CACvE,IAAI,CAAC;CACL,GAAG;CACH,OAAO,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE;AAC3D;CACA,GAAG,GAAG,CAAC,CAAC,GAAG;CACX,IAAI,EAAE,iBAAiB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE;CACpE,IAAI,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE;CACrE,IAAI,EAAE,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE;CACtE,IAAI,CAAC;CACL,GAAG;CACH,EAAE;AACF;CACA,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;AAC5C;CACA,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE;CACZ,EAAE,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;CAC1C,EAAE;CACF,MAAM;CACN,EAAE,MAAM,IAAI,SAAS,CAAC,oEAAoE,CAAC,CAAC;CAC5F,EAAE;CACF;;CCxDA,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;AACnE;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;CACpD,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK;CACtE,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;CAC5C,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;CACtB,EAAE,IAAI,YAAY,GAAG,GAAG,EAAE,IAAI,CAAC;AAC/B;CACA;CACA;CACA,EAAE,IAAI,IAAI,CAAC;CACX,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE;CAChB,GAAG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACnD,GAAG;CACH,OAAO;CACP,GAAG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,CAAC;CACpD,GAAG;AACH;CACA;CACA,EAAE,IAAI,CAAC,IAAI,EAAE;CACb;CACA,GAAG,IAAI,SAAS,GAAG,SAAS,CAAC,IAAI,IAAI,EAAE,CAAC;CACxC,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,EAAE,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,iBAAiB,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;CAC/F,GAAG;AACH;CACA,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B;CACA,EAAE,IAAI,YAAY,KAAK,cAAc,EAAE;CACvC,GAAG,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACxB,GAAG;AACH;CACA,EAAE,IAAI,OAAO,GAAG,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,CAAC;AACtD;CACA,EAAE,IAAI,SAAS,IAAI,OAAO,EAAE;CAC5B,GAAG,MAAM,CAAC,CAAC,CAAC,GAAGC,QAAa,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5D,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;CACd,EAAE,CAAC,CAAC;AACJ;CACA,CAAC,OAAO,KAAK,CAAC;CACd,CAAC;AACD;AACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,SAAS,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;CACjD,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;CACxC,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;AAC/B;CACA,CAAC,IAAI,GAAG,CAAC,KAAK,EAAE;CAChB,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC;CACnB,EAAE;AACF;CACA,CAAC,GAAG,CAAC,MAAM,GAAGC,aAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C;CACA,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;CACjB;CACA,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AAC7B;CACA,EAAE,IAAI,IAAI,KAAK,OAAO,EAAE;CACxB;CACA,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;CACpC;CACA,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;CACvE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;CAC/B,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/E;CACA,GAAG,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,EAAE;CACrC,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC7C;CACA,IAAI,IAAI,SAAS,EAAE;CACnB,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE;CACvG;CACA;CACA;CACA,MAAM,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACtF;CACA,MAAM,IAAI,KAAK,CAAC;AAChB;CACA,MAAM,IAAI,SAAS,CAAC,YAAY,EAAE;CAClC,OAAO,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;CAC/D,OAAO;AACP;CACA,MAAM,IAAI,IAAI,EAAE;CAChB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;CACvD,OAAO;AACP;CACA,MAAM,IAAI,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;CACjE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,sEAAsE,CAAC;CAC1G,qBAAqB,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,CAAC,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACrF,OAAO;CACP,MAAM,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;CACjE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,oDAAoD,CAAC;CACxF,qBAAqB,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,CAAC,4BAA4B,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACrF,OAAO;AACP;CACA,MAAM,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;CAChD,MAAM;CACN,KAAK;CACL,IAAI;AACJ;CACA;CACA,GAAG,IAAI,UAAU,GAAG,EAAE,CAAC;CACvB,GAAG,IAAI,UAAU,GAAG,EAAE,IAAI,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG,WAAW,CAAC;CACjE,GAAG,IAAI,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE;CAC1C;CACA,IAAI,IAAI,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC;AACnE;CACA,IAAI,IAAI,KAAK,EAAE;CACf,KAAK,UAAU,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;CAClD,KAAK;CACL,IAAI;AACJ;CACA,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,mBAAmB,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,UAAU,IAAI,mBAAmB,CAAC,CAAC,CAAC;CAC5F,GAAG;CACH,OAAO;CACP,GAAG,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,EAAE;CACrC;CACA,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;CACvC,IAAI,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;CAC9C,KAAK,IAAI,KAAK,GAAG,CAAC,CAAC;AACnB;CACA,KAAK,IAAI,MAAM,CAAC,SAAS,IAAIC,IAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;CAC/D,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;CACpC,MAAM;AACN;CACA,KAAK,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AAClC;CACA,KAAK,IAAI,KAAK,CAAC;AACf;CACA,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;CAC9B,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;CACxD,MAAM;AACN;CACA,KAAK,IAAI,IAAI,EAAE;CACf,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;CAC1D,MAAM;AACN;CACA,KAAK,OAAO;CACZ,MAAM,OAAO,EAAE,KAAK,CAAC,EAAE;CACvB,MAAM,MAAM,EAAE,KAAK;CACnB,MAAM,CAAC;CACP,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE;CACF,MAAM;CACN;CACA,EAAE,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,EAAE;CACpC,GAAG,KAAK,IAAI,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;CACvC,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACzC;CACA,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;CAClC,KAAK,SAAS;CACd,KAAK;AACL;CACA,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;CAC9C,KAAK,SAAS;CACd,KAAK;AACL;CACA,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtC;CACA,IAAI,IAAI,KAAK,EAAE;CACf,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;AACvB;CACA,KAAK,IAAI,IAAI,EAAE;CACf,MAAM,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;CAC/B,MAAM;AACN;CACA,KAAK,OAAO,KAAK,CAAC;CAClB,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE;AACF;AACA;CACA;CACA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,8BAA8B,CAAC,CAAC,CAAC;CAC7E;;CCjMA;CACA;CACA;CACA;CACA;CACe,SAAS,QAAQ,EAAE,KAAK,EAAE;CACzC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;CAC3B,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;CAC7B,EAAE;AACF;CACA,CAAC,IAAI,CAAC,KAAK,EAAE;CACb,EAAE,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;CAC/C,EAAE;AACF;CACA,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;CACtB,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;CACvB,EAAE;AACF;CACA;CACA,CAAC,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC;AAC1C;CACA,CAAC,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,EAAE;CACrC;CACA,EAAE,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACtC,EAAE;AACF;CACA,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE;CAChC,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;CAClB,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC;CACd;;CC9BA,MAAMC,GAAC,GAAG,OAAO,CAAC;AAClB;CACA;CACA;CACA;CACe,MAAM,UAAU,CAAC;CAChC,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE;CACvB,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;CACvB,EAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;CACjE,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACjC;CACA,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;CACjB,GAAG,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;CACpC,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;CAChC,GAAG;AACH;CACA;AACA;CACA,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAClD;CACA,EAAE,KAAK,IAAI,IAAI,IAAI,MAAM,EAAE;CAC3B,GAAG,IAAI,EAAE,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;CAClC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;CAC7B,IAAI;CACJ,GAAG;CACH,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACvB;CACA;AACA;CACA,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC;CACxD,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC/B;CACA;AACA;CACA,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;AACvC;CACA,EAAE,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;CACjC,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;CACnC,GAAG,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;CAC9B,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;CACxB,GAAG;AACH;CACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE;CAC/B,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;CACxB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;CAC/B,IAAI,EAAE,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE;CAChC,IAAI,CAAC;CACL,GAAG;AACH;CACA;AACA;CACA,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE;CAC1B;CACA,GAAG,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,KAAK,MAAM,GAAG,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;CAC/F,GAAG;CACH,OAAO;CACP;CACA,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;CACrB;CACA,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;CAChC,IAAI;CACJ,QAAQ;CACR,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC;CAC5B,IAAI;CACJ,GAAG;AACH;CACA;CACA,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;CACnC,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,KAAK;CACvC,IAAI,OAAO,IAAI,CAAC;CAChB,IAAI,CAAC;CACL,GAAG;AACH;CACA;CACA,EAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACnC;CACA;CACA,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;CACtC,GAAG,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE;CACjC,GAAG,QAAQ,EAAE,KAAK;CAClB,GAAG,UAAU,EAAE,IAAI;CACnB,GAAG,YAAY,EAAE,IAAI;CACrB,GAAG,CAAC,CAAC;AACL;CACA,EAAE,KAAK,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;CACzC,EAAE;AACF;CACA,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,GAAGA,GAAC,CAAC,GAAG,EAAE,EAAE;CACtC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;CACrC,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;CAC7C,GAAG,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;CACrD,GAAG;AACH;CACA,EAAE,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7C;CACA,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CAChC,GAAG,IAAI,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC3B;CACA,GAAG,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;CAC5C,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;CACzB;CACA,KAAK,OAAO,IAAI,CAAC;CACjB,KAAK;AACL;CACA,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;CAChC,IAAI,OAAO,CAAC,GAAG,KAAK,SAAS,IAAI,CAAC,IAAI,GAAG,GAAG,OAAO;CACnD,YAAY,GAAG,KAAK,SAAS,IAAI,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,CAAC;CACrD,IAAI;AACJ;CACA,GAAG,OAAO,IAAI,CAAC;CACf,GAAG,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,WAAW,CAAC,GAAG;CACpB,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC;CACxE,EAAE;AACF;CACA,CAAC,IAAI,KAAK,CAAC,GAAG;CACd,EAAE,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;CAC5C,EAAE;AACF;CACA,CAAC,IAAI,OAAO,CAAC,GAAG;CAChB,EAAE,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;CAC9B,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;CACzC,IAAI,OAAO,IAAI,CAAC;CAChB,IAAI;CACJ,GAAG;AACH;CACA,EAAE,OAAO,KAAK,CAAC;CACf,EAAE;AACF;CACA,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE;CACpB,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;CAClC,GAAG,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;CACxC,GAAG,OAAO,MAAM,CAAC;CACjB,GAAG;AACH;CACA,EAAE,IAAI,GAAG,CAAC;CACV,EAAE,IAAI,MAAM,KAAK,SAAS,EAAE;CAC5B;CACA,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;CACxC,GAAG;CACH,OAAO;CACP,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;CAC9B,GAAG;AACH;CACA,EAAE,IAAI,GAAG,EAAE;CACX,GAAG,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;CAClC,GAAG,OAAO,GAAG,CAAC;CACd,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;CAChB,EAAE,IAAI,CAAC,KAAK,EAAE;CACd,GAAG,OAAO,KAAK,CAAC;CAChB,GAAG;AACH;CACA,EAAE,OAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;CACrE,EAAE;AACF;CACA,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE;CACpB,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;CAC9B,GAAG,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;CACjC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CACjD,GAAG;AACH;CACA,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC;CACA,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;CAC1B;CACA,GAAG,OAAO,MAAM,CAAC;CACjB,GAAG;AACH;CACA;CACA,EAAE,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACpD;CACA;CACA,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;AAC7B;CACA,EAAE,IAAI,eAAe,EAAE,oBAAoB,CAAC;AAC5C;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC1C,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;CACvC,IAAI,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;CAChC,IAAI,oBAAoB,GAAG,CAAC,CAAC;CAC7B,IAAI;CACJ,QAAQ;CACR,IAAI,MAAM;CACV,IAAI;CACJ,GAAG;AACH;CACA,EAAE,IAAI,CAAC,eAAe,EAAE;CACxB;CACA,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;CAC9G,GAAG;AACH;CACA;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,EAAE,CAAC,EAAE,EAAE;CACjE,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;CACrC,GAAG;AACH;CACA;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,oBAAoB,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACpE,GAAG,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC1C,GAAG;AACH;CACA,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;AACF;CACA,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE;CACtB,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;CAC9B,GAAG,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;CACjC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CACjD,GAAG;AACH;CACA,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC;CACA,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;CAChC,EAAE;AACF;CACA,CAAC,QAAQ,CAAC,GAAG;CACb,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CACrC,EAAE;AACF;CACA,CAAC,YAAY,CAAC,GAAG;CACjB,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf;CACA,EAAE,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;CAC9B,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;CAC9B,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC;CAC3C,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;CAC7B,GAAG;AACH;CACA,EAAE,OAAO,GAAG,CAAC;CACb,EAAE;AACF;CACA,CAAC,OAAO,QAAQ,GAAG,EAAE,CAAC;AACtB;CACA;CACA,CAAC,WAAW,GAAG,CAAC,GAAG;CACnB,EAAE,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CAC1D,EAAE;AACF;CACA,CAAC,OAAO,QAAQ,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE;CAC7B,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;CAC9B,GAAG,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;CACxB,GAAG,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;CACjB,GAAG;AACH;CACA,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B;CACA,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,KAAK,EAAE;CACxD,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAClE,GAAG;CACH,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;AAC5B;CACA;CACA,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE;CAC/C,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE;CACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CAChC,IAAI;CACJ,GAAG;AACH;CACA,EAAE,OAAO,KAAK,CAAC;CACf,EAAE;AACF;CACA;CACA;CACA;CACA;CACA,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,YAAY,EAAE;CACrC,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,YAAY,UAAU,EAAE;CAC7C,GAAG,OAAO,KAAK,CAAC;CAChB,GAAG;AACH;CACA,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B;CACA,EAAE,IAAI,OAAO,KAAK,QAAQ,EAAE;CAC5B;CACA,GAAG,IAAI,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;AACtD;CACA,GAAG,IAAI,CAAC,GAAG,EAAE;CACb,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACrE,IAAI;AACJ;CACA,GAAG,OAAO,GAAG,CAAC;CACd,GAAG;AACH;CACA,EAAE,IAAI,YAAY,CAAC,MAAM,EAAE;CAC3B,GAAG,OAAO,UAAU,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;CAC1C,GAAG;AACH;CACA,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;CAC7D,EAAE;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,OAAO,YAAY,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE;CACzC,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;CAC5B,EAAE,IAAI,KAAK,EAAE,KAAK,CAAC;AACnB;CACA,EAAE,IAAI,SAAS,KAAK,QAAQ,EAAE;CAC9B,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;CAC1B;CACA,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;CACpC,IAAI;CACJ,QAAQ;CACR;CACA,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;CAC7B,IAAI;CACJ,GAAG;CACH,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;CAC/B,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC;CACxB,GAAG;CACH,OAAO;CACP;CACA,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;CACrB,GAAG,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC;CACvB,GAAG;AACH;CACA,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC;CACA,EAAE,IAAI,CAAC,KAAK,EAAE;CACd,GAAG,KAAK,GAAG,YAAY,CAAC;CACxB,GAAG;AACH;CACA,EAAE,IAAI,CAAC,KAAK,EAAE;CACd,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,uEAAuE,CAAC,CAAC,CAAC;CAC5I,GAAG;AACH;CACA,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B;CACA,EAAE,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,IAAI,CAAC,EAAE;CACtE;CACA,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;AAClD;CACA,GAAG,IAAI,IAAI,EAAE;CACb,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CAC1D,IAAI;CACJ,GAAG;AACH;CACA,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC;CACA,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;AAC5C;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;CACZ,EAAE,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE;CAC/B,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC/B;CACA,GAAG,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,eAAe,IAAI,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,eAAe,EAAE;CAC7F,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;CAC1C,IAAI;AACJ;CACA,GAAG,CAAC,EAAE,CAAC;CACP,GAAG;AACH;CACA,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CACvI,EAAE;AACF;CACA,CAAC,OAAO,cAAc,GAAG;CACzB,EAAE,IAAI,EAAE,WAAW;CACnB,EAAE,IAAI,EAAE,OAAO;CACf,EAAE,CAAC;CACH,CAAC;AACD;CACA,SAAS,OAAO,EAAE,KAAK,EAAE;CACzB,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACnB;CACA,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG;CAClC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACd,EAAE;AACF;CACA,CAAC,OAAO,GAAG,CAAC;CACZ,CAAC;AACD;CACA,SAAS,aAAa,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE;CAC/C,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;CAC5C,EAAE,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;CAC7B,EAAE,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC;AAC1B;CACA;CACA,EAAE,MAAM,CAAC,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACzD;CACA,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK;CACxE;CACA,GAAG,IAAI,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C;CACA,GAAG,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,CAAC;CACzD,GAAG,IAAI,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC;AAC/C;CACA;CACA,GAAG,IAAI,UAAU,IAAI,cAAc,EAAE;CACrC,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;CACvB,IAAI,MAAM,GAAG,GAAG,CAAC;CACjB,IAAI;CACJ,QAAQ,IAAI,UAAU,IAAI,SAAS,EAAE;CACrC,IAAI,MAAM,GAAG,KAAK,CAAC;CACnB,IAAI;AACJ;CACA,GAAG,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;CACxC,GAAG,CAAC,CAAC;AACL;CACA,EAAE,MAAM,CAAC,eAAe,GAAG,CAAC,MAAM,EAAE,SAAS,KAAK;CAClD,GAAG,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CAC/B,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACvD;CACA,IAAI,IAAI,SAAS,IAAI,OAAO,EAAE;CAC9B,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;CACzC,KAAK;AACL;CACA,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AACtD;CACA,IAAI,OAAO,CAAC,CAAC;CACb,IAAI,CAAC,CAAC;CACN,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC;CACf;;ACrbA,eAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,SAAS;CACd,CAAC,IAAI,EAAE,SAAS;CAChB,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;CAChB,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;CAChB,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;CAChB,EAAE;CACF,CAAC,KAAK,EAAE,KAAK;CACb,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,GAAG,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;CAC1B,GAAG;CACH,EAAE;CACF,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;CACjB,CAAC,CAAC;;CCZF;CACA;CACA;CACA;CACe,MAAM,aAAa,SAAS,UAAU,CAAC;CACtD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE;CACvB,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;CACvB,GAAG,OAAO,CAAC,MAAM,GAAG;CACpB,IAAI,CAAC,EAAE;CACP,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAClB,KAAK,IAAI,EAAE,KAAK;CAChB,KAAK;CACL,IAAI,CAAC,EAAE;CACP,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAClB,KAAK,IAAI,EAAE,OAAO;CAClB,KAAK;CACL,IAAI,CAAC,EAAE;CACP,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAClB,KAAK,IAAI,EAAE,MAAM;CACjB,KAAK;CACL,IAAI,CAAC;CACL,GAAG;AACH;CACA,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;CACrB,GAAG,OAAO,CAAC,IAAI,GAAGC,OAAO,CAAC;CAC1B,GAAG;AACH;CACA,EAAE,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE;CAC5C,GAAG,OAAO,CAAC,MAAM,KAAK,GAAG,IAAI;CAC7B,IAAI,IAAI,GAAG,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACrD;CACA,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;CACxC;CACA,KAAK,GAAG,GAAGL,OAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;CACnD,KAAK;AACL;CACA,IAAI,OAAO,GAAG,CAAC;CACf,IAAI,CAAC;AACL;CACA,GAAG,OAAO,CAAC,QAAQ,KAAK,GAAG,IAAI;CAC/B,IAAI,GAAG,GAAGA,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;CAClD,IAAI,OAAO,gBAAgB,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;CACpD,IAAI,CAAC;CACL,GAAG;AACH;CACA,EAAE,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;AACjC;CACA,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;CACjB,EAAE;CACF;;CC5DA;CACA;CACA;CACA;CACA;CACA;CACe,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE;CAC9C,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;CACA,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;CAC1C;CACA,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;CAC9B,EAAE;AACF;CACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC/B,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC1B;;CCfe,SAAS,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;CAC1C,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;CACA,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;CACjE,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CACnC,CAAC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;CACtB;;CCPe,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;CACtD,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;CACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC/B,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;CAC9C,CAAC,OAAO,KAAK,CAAC;CACd,CAAC;AACD;CACA,MAAM,CAAC,OAAO,GAAG,OAAO;;CCJxB;CACe,SAAS,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;CACjD,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;CACA,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;CAChE;CACA,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;CAC5B,EAAE,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;CACxB,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5B,GAAG;CACH,EAAE;CACF,MAAM;CACN,EAAE,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;CACnC,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;CACnC,GAAG;AACH;CACA,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;CAClE,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CACpC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;CACxB,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;CAC/B,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC;CACd,CAAC;AACD;CACA,GAAG,CAAC,OAAO,GAAG,OAAO;;AC5BrB,eAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,SAAS;CACd,CAAC,IAAI,EAAE,SAAS;CAChB,CAAC,KAAK,EAAE,KAAK;CACb,CAAC,IAAI,EAAEK,OAAO;CACd,CAAC,QAAQ,EAAE,MAAM,IAAIL,OAAK,CAACK,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;CACxD,CAAC,MAAM,EAAE,MAAM,IAAIL,OAAK,CAAC,KAAK,EAAEK,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;CACtD,CAAC,CAAC;;CCPF;CACA,MAAMD,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAME,IAAE,GAAG,EAAE,GAAG,GAAG,CAAC;CACpB,MAAMC,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB;CACA,IAAIC,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC;AACvB;AACA,WAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,KAAK;CACV,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE;AACF;CACA;CACA;CACA,QAACA,OAAK;AACN;CACA,CAAC,IAAI,EAAEC,OAAO;CACd;CACA;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,GAAGD,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD;CACA;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,GAAGJ,GAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAACG,GAAC,GAAG,KAAK,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;AAClF;CACA,EAAE,OAAO;CACT,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;CACpB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACtB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACtB,GAAG,CAAC;CACJ,EAAE;CACF;CACA;CACA;CACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd;CACA,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;CACb,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;CAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAC7B;CACA;CACA,EAAE,IAAI,GAAG,GAAG;CACZ,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKD,IAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIC,GAAC;CACzE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAC;CAC9D,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKD,IAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIC,GAAC;CACzE,GAAG,CAAC;AACJ;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,GAAGC,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACjD,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;CACxG,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CCzEK,SAAS,SAAS,EAAE,KAAK,EAAE;CAClC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;CACpC,CAAC;AACD;CACO,SAAS,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;CACrC,CAAC,IAAI,GAAG,KAAK,KAAK,EAAE;CACpB,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;AACF;CACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACtC;CACA,CAAC,IAAI,SAAS,GAAG,EAAE,GAAG,EAAE,CAAC;AACzB;CACA,CAAC,IAAI,GAAG,KAAK,YAAY,EAAE;CAC3B,EAAE,IAAI,SAAS,GAAG,CAAC,EAAE;CACrB,GAAG,EAAE,IAAI,GAAG,CAAC;CACb,GAAG;CACH,EAAE;CACF,MAAM,IAAI,GAAG,KAAK,YAAY,EAAE;CAChC,EAAE,IAAI,SAAS,GAAG,CAAC,EAAE;CACrB,GAAG,EAAE,IAAI,GAAG,CAAC;CACb,GAAG;CACH,EAAE;CACF,MAAM,IAAI,GAAG,KAAK,QAAQ,EAAE;CAC5B,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS,IAAI,SAAS,GAAG,GAAG,EAAE;CAC3C,GAAG,IAAI,SAAS,GAAG,CAAC,EAAE;CACtB,IAAI,EAAE,IAAI,GAAG,CAAC;CACd,IAAI;CACJ,QAAQ;CACR,IAAI,EAAE,IAAI,GAAG,CAAC;CACd,IAAI;CACJ,GAAG;CACH,EAAE;CACF,MAAM,IAAI,GAAG,KAAK,SAAS,EAAE;CAC7B,EAAE,IAAI,SAAS,GAAG,GAAG,EAAE;CACvB,GAAG,EAAE,IAAI,GAAG,CAAC;CACb,GAAG;CACH,OAAO,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE;CAC7B,GAAG,EAAE,IAAI,GAAG,CAAC;CACb,GAAG;CACH,EAAE;AACF;CACA,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACjB;;ACvCA,WAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,KAAK;CACV,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,QAAQ;CACjB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAEE,GAAG;CACV,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;CACtB,EAAE,IAAI,GAAG,CAAC;CACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;AACjB;CACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;CAC1C,GAAG,GAAG,GAAG,GAAG,CAAC;CACb,GAAG;CACH,OAAO;CACP,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;CAC1C,GAAG;AACH;CACA,EAAE,OAAO;CACT,GAAG,CAAC;CACJ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAC7B,GAAGC,SAAc,CAAC,GAAG,CAAC;CACtB,GAAG,CAAC;CACJ,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd;CACA,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;CACrC;CACA,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;CAClB,GAAG,MAAM,GAAG,CAAC,CAAC;CACd,GAAG;CACH;CACA,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;CAClB,GAAG,GAAG,GAAG,CAAC,CAAC;CACX,GAAG;CACH,EAAE,OAAO;CACT,GAAG,SAAS;CACZ,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;CACzC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;CACzC,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,oBAAoB,CAAC;CACvF,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CC7DF;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,OAAO,GAAG,EAAE,IAAI,CAAC,CAAC;CACxB,MAAMC,GAAC,GAAG,IAAI,CAAC,EAAE,CAAC;CAClB,MAAM,GAAG,GAAG,GAAG,GAAGA,GAAC,CAAC;CACpB,MAAMC,KAAG,GAAGD,GAAC,GAAG,GAAG,CAAC;AACpB;CACA,SAAS,IAAI,EAAE,CAAC,EAAE;CAClB;AACA;CACA,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;CAClB,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC7B;CACA,CAAC,OAAO,EAAE,CAAC;CACX,CAAC;AACD;CACe,mBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;CACvE,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;CACA;CACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACpC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACzC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CACrC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC;CACA;CACA;CACA;AACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;CACb,EAAE,EAAE,GAAG,CAAC,CAAC;CACT,EAAE;CACF,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;CACb,EAAE,EAAE,GAAG,CAAC,CAAC;CACT,EAAE;AACF;CACA,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC1B;CACA;CACA;CACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AACrB;CACA,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACpD;CACA;CACA;CACA,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;CAC3B,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AAC3B;CACA;CACA,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;CAC/C,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/C;CACA;CACA;AACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;CAClE,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAClE;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;CACb,EAAE,EAAE,IAAI,CAAC,GAAGA,GAAC,CAAC;CACd,EAAE;CACF,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;CACb,EAAE,EAAE,IAAI,CAAC,GAAGA,GAAC,CAAC;CACd,EAAE;AACF;CACA,CAAC,EAAE,IAAI,GAAG,CAAC;CACX,CAAC,EAAE,IAAI,GAAG,CAAC;AACX;CACA;CACA,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAClB,CAAC,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC;AAC1B;CACA;CACA,CAAC,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;CACpB,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC5B,CAAC,IAAI,EAAE,CAAC;AACR;CACA,CAAC,IAAI,MAAM,GAAG,MAAM,KAAK,CAAC,EAAE;CAC5B,EAAE,EAAE,GAAG,CAAC,CAAC;CACT,EAAE;CACF,MAAM,IAAI,IAAI,IAAI,GAAG,EAAE;CACvB,EAAE,EAAE,GAAG,KAAK,CAAC;CACb,EAAE;CACF,MAAM,IAAI,KAAK,GAAG,GAAG,EAAE;CACvB,EAAE,EAAE,GAAG,KAAK,GAAG,GAAG,CAAC;CACnB,EAAE;CACF,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE;CACxB,EAAE,EAAE,GAAG,KAAK,GAAG,GAAG,CAAC;CACnB,EAAE;CACF,MAAM;CACN,EAAE,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;CAChD,EAAE;AACF;CACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGC,KAAG,GAAG,CAAC,CAAC,CAAC;AAClE;CACA;CACA,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;CAC3B,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;CACnC,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B;CACA;CACA;CACA;CACA,CAAC,IAAI,KAAK,CAAC;CACX,CAAC,IAAI,MAAM,GAAG,MAAM,KAAK,CAAC,EAAE;CAC5B,EAAE,KAAK,GAAG,IAAI,CAAC;CACf,EAAE;CACF,MAAM,IAAI,IAAI,IAAI,GAAG,EAAE;CACvB,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;CACnB,EAAE;CACF,MAAM,IAAI,IAAI,GAAG,GAAG,EAAE;CACtB,EAAE,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC;CAC3B,EAAE;CACF,MAAM;CACN,EAAE,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC;CAC3B,EAAE;AACF;CACA;CACA;AACA;CACA;CACA;CACA,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;CAC7B,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AACpD;CACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AAC5B;CACA;CACA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACX,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE,KAAKA,KAAG,CAAC,CAAC,CAAC;CAClD,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,UAAUA,KAAG,CAAC,CAAC,CAAC;CAClD,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,KAAKA,KAAG,CAAC,CAAC,CAAC;CAClD,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,EAAE,IAAIA,KAAG,CAAC,CAAC,CAAC;AAClD;CACA;CACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AAChC;CACA;CACA;CACA;CACA;CACA,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;CAC1D,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;CACrD,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAG,CAAC,GAAG,EAAE,CAAC;AAC3C;CACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;CAChC,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;CAC7B,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;CAC7B,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;CAChD,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;CACtB;CACA;;CC9KA;CACA;CACA,MAAMC,YAAU,GAAG;CACnB,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CAChE,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,GAAG,kBAAkB,EAAE;CAChE,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,GAAG,kBAAkB,EAAE;CAChE,CAAC,CAAC;CACF;CACA,MAAMC,YAAU,GAAG;CACnB,CAAC,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;CAClE,CAAC,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CAClE,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;CAClE,CAAC,CAAC;CACF,MAAM,UAAU,GAAG;CACnB,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CACjE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;CACjE,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CACjE,CAAC,CAAC;CACF;CACA,MAAM,UAAU,GAAG;CACnB,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,EAAE;CACjE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CACjE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CACjE,CAAC,CAAC;AACF;AACA,aAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,OAAO;CACZ,CAAC,IAAI,EAAE,OAAO;CACd,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACnB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE;AACF;CACA;CACA,CAAC,KAAK,EAAE,KAAK;CACb,CAAC,IAAI,EAAEV,OAAO;CACd,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA,EAAE,IAAI,GAAG,GAAG,gBAAgB,CAACS,YAAU,EAAE,GAAG,CAAC,CAAC;AAC9C;CACA;CACA,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C;CACA,EAAE,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC5C;CACA,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;CAChB;CACA,EAAE,IAAI,IAAI,GAAG,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACjD;CACA;CACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;AACtC;CACA,EAAE,OAAO,gBAAgB,CAACC,YAAU,EAAE,GAAG,CAAC,CAAC;CAC3C,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,OAAO,EAAE;CACX,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;CACxG,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CCzEF;CACA;AACA;AAGA;CACe,iBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;CACxC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;CACA;CACA;CACA;CACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACtC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CACvC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAClB,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;CAC/C;;CCfA,MAAMZ,GAAC,GAAG,OAAO,CAAC;AAClB;CACA;CACA;CACA;CACA;CACe,SAAS,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,OAAO,GAAGA,GAAC,CAAC,GAAG,EAAE,EAAE;CACnE,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;CACA,CAAC,IAAI,CAAC,KAAK,EAAE;CACb,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;CACtB,EAAE;AACF;CACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC/B,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC3B;CACA,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;CAC5B,EAAE,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC7B,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;CACzC;;CCxBe,SAAS,KAAK,EAAE,KAAK,EAAE;CACtC,CAAC,OAAO;CACR,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK;CACpB,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE;CAC9B,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK;CACpB,EAAE,CAAC;CACH;;CCJA;CACA;CACA;CACe,SAAS,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE;CACjE,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B;CACA;CACA,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CAClC,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC;CACA,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,KAAK;CACjD,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CACtB,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;CAC9B,GAAG,OAAO,GAAG,CAAC;CACd,GAAG;AACH;CACA,EAAE,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;CAC9B,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;CACR;;CCjBe,SAAS,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;CACjD;CACA,CAAC,OAAO,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;CACvC;;CCFA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;CAClB,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AACpB;CACe,kBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;CAC7D,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;CACA;CACA;CACA;AACA;CACA;CACA;CACA;AACA;CACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACpC,CAAC,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;CAC9C,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CACrC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC;CACA;CACA;CACA;CACA;CACA;AACA;CACA;CACA;CACA;AACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;CACb,EAAE,EAAE,GAAG,CAAC,CAAC;CACT,EAAE;CACF,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;CACb,EAAE,EAAE,GAAG,CAAC,CAAC;CACT,EAAE;AACF;CACA;AACA;CACA;CACA;CACA,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB;CACA,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB;CACA;AACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;CAC5C;CACA;CACA;AACA;CACA;CACA;AACA;CACA;CACA;CACA;CACA;AACA;CACA;AACA;CACA;CACA;AACA;CACA;CACA,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;CAChB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;CACf,EAAE,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;CAC5C,EAAE;AACF;CACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC;AACtD;CACA;CACA,CAAC,IAAI,CAAC,CAAC;CACP,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;CACvB,EAAE,EAAE,GAAG,CAAC,CAAC;CACT,EAAE;AACF;CACA,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;CAC7B,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;CACxD,EAAE;CACF,MAAM;CACN,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;CACvD,EAAE;CACF;AACA;CACA;CACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;CAC1B,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;CACrC,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC;CACA;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;CAC/B,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;CAC5B,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;CACxB;CACA,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;CACtB;CACA;;CC9GA,MAAMa,IAAE,GAAG,GAAG,CAAC;AACf;AACA,mBAAe,IAAI,UAAU,CAAC;CAC9B;CACA;CACA;CACA;CACA,CAAC,EAAE,EAAE,aAAa;CAClB,CAAC,KAAK,EAAE,eAAe;CACvB,CAAC,IAAI,EAAE,kBAAkB;CACzB,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;CACxB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACvB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;CACzB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAEZ,OAAO;CACd,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGY,IAAE,EAAE,CAAC,CAAC,CAAC,CAAC;CAC5C,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;CACjB;CACA,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGA,IAAE,EAAE,CAAC,CAAC,CAAC,CAAC;CAC9C,EAAE;CACF,CAAC,CAAC;;CCnCF,MAAMC,GAAC,GAAG,IAAI,CAAC;CACf,MAAM,CAAC,GAAG,IAAI,CAAC;CACf,MAAMC,GAAC,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;CAC3B,MAAMC,MAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC;CAC9B,MAAMC,IAAE,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;CAC5B,MAAMC,IAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC3B,MAAMC,IAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC3B,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAChC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC;CACrC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;CAChB,MAAM,EAAE,GAAG,sBAAsB,CAAC;AAClC;CACA,MAAM,WAAW,GAAG;CACpB,CAAC,GAAG,UAAU,EAAE,QAAQ,GAAG,SAAS,EAAE;CACtC,CAAC,EAAE,CAAC,SAAS,GAAG,QAAQ,GAAG,SAAS,EAAE;CACtC,CAAC,EAAE,CAAC,SAAS,GAAG,QAAQ,GAAG,SAAS,EAAE;CACtC,CAAC,CAAC;CACF;CACA,MAAM,WAAW,GAAG;CACpB,CAAC,GAAG,kBAAkB,GAAG,CAAC,kBAAkB,GAAG,iBAAiB,IAAI;CACpE,CAAC,GAAG,mBAAmB,GAAG,kBAAkB,EAAE,CAAC,mBAAmB,EAAE;CACpE,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,GAAG;CACpE,CAAC,CAAC;CACF,MAAM,WAAW,GAAG;CACpB,CAAC,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,SAAS;CACpC,CAAC,GAAG,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,EAAE;CACpC,CAAC,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC,QAAQ,EAAE;CACpC,CAAC,CAAC;CACF;CACA,MAAM,WAAW,GAAG;CACpB,CAAC,EAAE,CAAC,oBAAoB,kBAAkB,IAAI,mBAAmB,EAAE;CACnE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,CAAC,mBAAmB,EAAE;CACnE,CAAC,EAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,GAAG;CACnE,CAAC,CAAC;AACF;AACA,cAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,QAAQ;CACb,CAAC,IAAI,EAAE,QAAQ;CACf,CAAC,MAAM,EAAE;CACT,EAAE,EAAE,EAAE;CACN,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACnB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG;CACH,EAAE,EAAE,EAAE;CACN,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE,EAAE,EAAE;CACN,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,WAAW;CAClB,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA;CACA;CACA;AACA;CACA,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC;AAC3B;CACA;CACA,EAAE,IAAI,EAAE,GAAG,CAACL,GAAC,GAAG,EAAE,KAAK,CAACA,GAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;CACrC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AACrC;CACA;CACA,EAAE,IAAI,GAAG,GAAG,gBAAgB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1D;CACA;CACA,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;CACtC,GAAG,IAAI,GAAG,GAAGG,IAAE,IAAIC,IAAE,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKH,GAAC,CAAC,CAAC,CAAC;CAC9C,GAAG,IAAI,KAAK,GAAG,CAAC,IAAII,IAAE,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKJ,GAAC,CAAC,CAAC,CAAC;AAC/C;CACA,GAAG,OAAO,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,CAAC;CAC9B,GAAG,CAAC,CAAC;AACL;CACA;CACA,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;CAC3D;AACA;CACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;CAChD,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;CACtB,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;CACjB,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC;CAC5B,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/C;CACA;CACA,EAAE,IAAI,KAAK,GAAG,gBAAgB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC5D;CACA;CACA,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CACrC,GAAG,IAAI,GAAG,IAAIE,IAAE,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;CAClC,GAAG,IAAI,KAAK,GAAG,CAACE,IAAE,IAAI,GAAG,IAAI,IAAI,CAAC,IAAID,IAAE,CAAC;CACzC,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKF,MAAI,CAAC,CAAC;AAC3C;CACA,GAAG,QAAQ,CAAC,EAAE;CACd,GAAG,CAAC,CAAC;AACL;CACA;CACA,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,gBAAgB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AAC1D;CACA;CACA,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAACF,GAAC,GAAG,CAAC,IAAI,EAAE,CAAC,IAAIA,GAAC,CAAC;CACrC,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;CACrC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CACxB,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV;CACA,EAAE,OAAO,EAAE;CACX,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;CACxG,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;ACjHF,cAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,QAAQ;CACb,CAAC,IAAI,EAAE,QAAQ;CACf,CAAC,MAAM,EAAE;CACT,EAAE,EAAE,EAAE;CACN,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACnB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG;CACH,EAAE,EAAE,EAAE;CACN,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACnB,GAAG,IAAI,EAAE,QAAQ;CACjB,GAAG;CACH,EAAE,EAAE,EAAE;CACN,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,MAAM;CACb,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;CACnB;CACA,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC;CAC5B,EAAE,IAAI,GAAG,CAAC;CACV,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC;AACnB;CACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;CAC5C,GAAG,GAAG,GAAG,GAAG,CAAC;CACb,GAAG;CACH,OAAO;CACP,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;CAC5C,GAAG;AACH;CACA,EAAE,OAAO;CACT,GAAG,EAAE;CACL,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;CAC/B,GAAGP,SAAc,CAAC,GAAG,CAAC;CACtB,GAAG,CAAC;CACJ,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;CACjB;CACA;CACA,EAAE,OAAO;CACT,GAAG,MAAM,CAAC,CAAC,CAAC;CACZ,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;CAClD,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;CAClD,GAAG,CAAC;CACJ,EAAE;CACF,CAAC,CAAC;;CCjDF;CACA;AACA;CACA;CACA;CACA;AACA;CACe,iBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;CACxC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;CACA;CACA;CACA;CACA,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC1C,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC3C;CACA;CACA;CACA,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;CACpB,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AACpB;CACA;CACA,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;CACjD;CACA,EAAE,GAAG,GAAG,CAAC,CAAC;CACV,EAAE,GAAG,GAAG,CAAC,CAAC;CACV,EAAE;CACF,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;CAC7B;CACA,EAAE,GAAG,GAAG,GAAG,CAAC;CACZ,EAAE;CACF,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;CAC7B,EAAE,GAAG,GAAG,GAAG,CAAC;CACZ,EAAE;AACF;CACA,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;CACpB,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1E;CACA,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;CAC/C;;CCtCA,MAAMU,IAAE,GAAG,IAAI,GAAG,IAAI,CAAC;CACvB,MAAMC,IAAE,GAAG,IAAI,GAAG,GAAG,CAAC;CACtB,MAAMC,IAAE,GAAG,IAAI,GAAG,GAAG,CAAC;CACtB,MAAMC,IAAE,GAAG,IAAI,GAAG,KAAK,CAAC;CACxB,MAAM,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC;CACrB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC;CACzB,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;AACtB;CACA;CACA;CACA,MAAM,UAAU,GAAG;CACnB,CAAC,GAAG,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CAClE,CAAC,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,EAAE;CAClE,CAAC,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,EAAE;CAClE,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAM,UAAU,GAAG;CACnB,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,CAAC,OAAO;CAC9C,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;CAC9C,CAAC,EAAE,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE;CAC9C,CAAC,CAAC;AACF;CACA;CACA,MAAM,UAAU,GAAG;CACnB,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,EAAE;CACjE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CACjE,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CACjE,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAM,UAAU,GAAG;CACnB,CAAC,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;CAClE,CAAC,GAAG,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;CAClE,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;CAClE,CAAC,CAAC;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA,aAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,OAAO;CACZ,CAAC,IAAI,EAAE,OAAO;CACd;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACnB,GAAG,IAAI,EAAE,GAAG;CACZ,GAAG;CACH,EAAE,EAAE,EAAE;CACN,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG;CACH,EAAE,EAAE,EAAE;CACN,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,WAAW;CAClB,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA,EAAE,IAAI,GAAG,GAAG,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC9C;CACA,EAAE,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;CACzB,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;CAChB,EAAE,IAAI,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAC9B;CACA,EAAE,OAAO,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;CAC3C,EAAE;CACF,CAAC,CAAC,CAAC;AACH;CACA,SAAS,UAAU,EAAE,GAAG,EAAE;CAC1B;CACA;CACA,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;CACrC,EAAE,IAAI,GAAG,GAAGH,IAAE,IAAIC,IAAE,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKE,IAAE,CAAC,CAAC,CAAC;CAC9C,EAAE,IAAI,KAAK,GAAG,CAAC,IAAID,IAAE,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKC,IAAE,CAAC,CAAC,CAAC;AAC/C;CACA,EAAE,OAAO,CAAC,GAAG,GAAG,KAAK,MAAM,EAAE,CAAC;CAC9B,EAAE,CAAC,CAAC;AACJ;CACA;CACA,CAAC,OAAO,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;CAC5C,CAAC;AACD;CACA,SAAS,UAAU,EAAE,KAAK,EAAE;CAC5B,CAAC,IAAI,KAAK,GAAG,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACjD;CACA;CACA,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;CACrC,EAAE,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAIH,IAAE,EAAE,CAAC,CAAC,CAAC;CAC5C,EAAE,IAAI,KAAK,IAAIC,IAAE,IAAIC,IAAE,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;CACzC,EAAE,OAAO,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC;CACxC,EAAE,CAAC,CAAC;AACJ;CACA,CAAC,OAAO,GAAG,CAAC;CACZ;;CCjIA;CACA;CACA;AACA;CACe,kBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;CACxC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;CACA;CACA;CACA;CACA;AACA;CACA,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACxC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzC;CACA;CACA;CACA;AACA;CACA,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;CACnF;;CCjBA,MAAMf,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC;CACzB,MAAM,WAAW,GAAG,IAAI,CAAC;CACzB,MAAM,cAAc,GAAG,CAAC,GAAG,WAAW,CAAC;CACvC,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACxB;CACA,MAAM,KAAK,GAAG;CACd,CAAC,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC,QAAQ,EAAE;CACpC,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE;CACpC,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE;CACpC,CAAC,CAAC;AACF;CACA,MAAM,QAAQ,GAAG;CACjB,CAAC,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;CAC/D,CAAC,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,oBAAoB,CAAC;CACjE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;CAClE,CAAC,CAAC;AACF;CACA,MAAM,EAAE,GAAG;CACX,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;CACtB,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC;CACxB,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC;CACzB,CAAC,CAAC;AACF;CACA,MAAM,WAAW,GAAG;CACpB,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC;CACxB,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;CACtB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;CACtB,CAAC,CAAC;AACF;CACA,MAAM,UAAU,GAAG;CACnB;CACA,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;CAC1C,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;CAC7B,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;CACrC,CAAC,CAAC;AACF;CACA,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;CAC9B,MAAMiB,SAAO,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AAC9B;CACO,SAASzB,OAAK,EAAE,MAAM,EAAE,EAAE,EAAE;CACnC,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;CAC9B,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,WAAW,CAAC,CAAC;CACvD,EAAE,OAAO,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;CAC5C,EAAE,CAAC,CAAC;CACJ,CAAC,OAAO,IAAI,CAAC;CACb,CAAC;AACD;CACO,SAAS,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;CACtC,CAAC,MAAM,QAAQ,GAAG,GAAG,GAAG,EAAE,IAAI,KAAK,IAAI,cAAc,CAAC,CAAC;CACvD,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI;CACzB,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC3B,EAAE,OAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;CAC3E,EAAE,CAAC,CAAC;CACJ,CAAC;AACD;CACO,SAAS,aAAa,EAAE,CAAC,EAAE;CAClC,CAAC,IAAI,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;CACvB,CAAC,IAAI,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;CAC5B,EAAE,EAAE,IAAI,GAAG,CAAC;CACZ,EAAE;AACF;CACA,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;CAC5C,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAChD,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAChD,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B;CACA,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;CAC1B,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;CAChD,CAAC;AACD;CACO,SAAS,gBAAgB,EAAE,CAAC,EAAE;CACrC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;CAClC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;CACjC,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;CACf,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAChD,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD;CACA,CAAC,OAAO,SAAS;CACjB,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG;CAC9C,GAAG,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;CAC/B,EAAE,CAAC;CACH,CAAC;AACD;CACO,SAAS,WAAW;CAC3B,CAAC,QAAQ;CACT,CAAC,iBAAiB;CAClB,CAAC,mBAAmB;CACpB,CAAC,QAAQ;CACT,CAAC,WAAW;CACZ,EAAE;AACF;CACA,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;CAC/B,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;CACzB,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;CACzB,CAAC,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;CAChC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;CACjB,EAAE,CAAC,CAAC;AACJ;CACA;CACA,CAAC,GAAG,CAAC,EAAE,GAAG,iBAAiB,CAAC;CAC5B;CACA,CAAC,GAAG,CAAC,EAAE,GAAG,mBAAmB,CAAC;CAC9B;CACA,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB;CACA;CACA,CAAC,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC5C;CACA;CACA,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;CACtC,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;CACvB,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;CACrB,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACtB;CACA,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;CAChC,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACnB;CACA;CACA,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;CAC5E,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC;AAC7B;CACA,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;CACrB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACjC,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACnC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACnB;CACA;CACA;CACA,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW;CACvB,EAAE,CAAC;CACH,EAAE,IAAI,CAAC,GAAG;CACV,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACjE,GAAG,CAAC;CACJ,GAAG,CAAC;CACJ,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI;CAC1B,EAAE,OAAO,WAAW,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;CACnC,EAAE,CAAC,CAAC;CACJ,CAAC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI;CACjC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;CACf,EAAE,CAAC,CAAC;AACJ;CACA;CACA,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CAClC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACzB,EAAE,CAAC,CAAC;CACJ,CAAC,MAAM,KAAK,GAAGA,OAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;CACpC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE;CACA;AACA;CACA,CAAC,OAAO,GAAG,CAAC;CACZ,CAAC;AACD;CACA;CACA,MAAM0B,mBAAiB,GAAG,WAAW;CACrC,CAAClB,OAAK;CACN,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE;CACvB,CAAC,SAAS;CACV,CAAC,KAAK;CACN,CAAC,CAAC;AACF;CACO,SAAS,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE;AACvC;CACA;CACA;CACA,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE;CAC3D,EAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;CACtE,EAAE;AACF;CACA,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE;CACrF,EAAE,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;CAC3E,EAAE;AACF;CACA;CACA,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE;CAC3D,EAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;CACtE,EAAE;AACF;CACA;CACA,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,KAAK,GAAG,EAAE;CACzC,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;CACzB,EAAE;AACF;CACA;CACA,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC;CAChB,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;CAC5B,EAAE,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGiB,SAAO,CAAC;CACtC,EAAE;CACF,MAAM;CACN,EAAE,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGA,SAAO,CAAC;CAC7C,EAAE;AACF;CACA,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAC7B,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B;CACA;CACA,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC;CACjB,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;CAC5B,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;CACrC,EAAE;CACF,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;CACjC,EAAE,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;CAC/D,EAAE;AACF;CACA;CACA,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC;CACjB,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;CAC5B,EAAE,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;CAC1B,EAAE;CACF,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;CACjC,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,CAAC;CACzC,EAAE;CACF,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;CACjC,EAAE,KAAK,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CACzD,EAAE;CACF,CAAC,MAAM,CAAC,GAAG,IAAI;CACf,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;CACvD,EAAE,EAAE,GAAG,CAAC;CACR,EAAE,CAAC;AACH;CACA;CACA,CAAC,MAAM,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9C;CACA;CACA,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;CACA;CACA,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;CAC7C,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC;CACxB,CAAC,MAAM,CAAC;CACR,EAAE,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;CACnB,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;CACjD,EAAE,CAAC;CACH,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACpB;CACA;CACA,CAAC,MAAM,KAAK,GAAG,OAAO;CACtB,EAAE,gBAAgB,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI;CAC5C,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;CACvB,GAAG,CAAC;CACJ,EAAE,GAAG,CAAC,EAAE;CACR,EAAE,CAAC;CACH,CAAC,OAAO,gBAAgB;CACxB,EAAE,QAAQ;CACV,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CACtB,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;CAC7B,GAAG,CAAC;CACJ,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI;CACZ,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;CACjB,EAAE,CAAC,CAAC;CACJ,CAAC;AACD;AACA;CACO,SAAS,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE;CACtC;CACA,CAAC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;CAChC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;CACjB,EAAE,CAAC,CAAC;CACJ,CAAC,MAAM,IAAI,GAAGzB,OAAK;CACnB,EAAE,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CAChD,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CAC1B,GAAG,CAAC;CACJ,EAAE,GAAG,CAAC,EAAE;CACR,EAAE,CAAC;AACH;CACA;CACA,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;CACpD,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;CACjD,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACrD;CACA;CACA,CAAC,MAAM,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9C;CACA,CAAC,MAAM,CAAC;CACR,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG;CAC7B,EAAE,IAAI;CACN,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAClC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK;CAC7C,GAAG;CACH,EAAE,CAAC;CACH,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC3E;CACA;CACA,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D;CACA,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD;CACA;CACA,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAChC;CACA;CACA,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;AAC3D;CACA;CACA,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AACzB;CACA;CACA,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;AAC1B;CACA;CACA,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;AACrC;CACA;CACA,CAAC,MAAM,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5B;CACA;CACA,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D;CACA;AACA;CACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACnD,CAAC;AACD;AACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA,aAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,WAAW;CAChB,CAAC,KAAK,EAAE,aAAa;CACrB,CAAC,IAAI,EAAE,WAAW;CAClB,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,GAAG;CACZ,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACvB,GAAG,IAAI,EAAE,cAAc;CACvB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,OAAO;AACd;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE0B,mBAAiB,CAAC,CAAC;CAChD,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;CACrC,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;CAChB,EAAE,OAAO,SAAS;CAClB,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;CAC1C,GAAGA,mBAAiB;CACpB,GAAG,CAAC;CACJ,EAAE;CACF,CAAC,CAAC;;CCnWF,MAAMlB,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC;CACzB,MAAMJ,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAMG,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB;CACA,SAAS,OAAO,EAAE,CAAC,EAAE;CACrB;AACA;CACA,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAGH,GAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAACG,GAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;CACxD,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,IAAI,IAAI,CAAC;CAC5B,CAAC;AACD;CACA,SAAS,SAAS,EAAE,KAAK,EAAE;CAC3B;AACA;CACA,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGA,GAAC,CAAC;CACnE,CAAC;AACD;CACA,SAAS,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE;CAC/B;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;CACxB,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;CACd,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX;CACA;CACA,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;CACd,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;CACzB,EAAE;AACF;CACA;CACA,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACtB;CACA;CACA;CACA,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;CACZ,EAAE,CAAC,GAAG,mBAAmB,GAAG,CAAC,IAAI,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,CAAC;CAChF,EAAE;CACF,MAAM;CACN,EAAE,CAAC,GAAG,qBAAqB,GAAG,CAAC,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,CAAC;CACpF,EAAE;AACF;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC;CACzB,CAAC,MAAM,YAAY,GAAG,EAAE,CAAC;AACzB;CACA,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC;CACjB,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;AAErB;CACA;CACA,CAAC,OAAO,OAAO,IAAI,YAAY,EAAE;CACjC,EAAE,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3C;CACA;CACA;CACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;CACrC,EAAE,IAAI,KAAK,GAAG,IAAI,EAAE;CACpB,GAAG,IAAI,KAAK,IAAI,SAAS,EAAE;CAC3B,IAAI,OAAO,GAAG,CAAC;CACf,IAAI;CAEJ,GAAG,IAAI,GAAG,KAAK,CAAC;CAChB,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C;CACA,EAAE,OAAO,IAAI,CAAC,CAAC;CACf,EAAE;AACF;CACA;CACA;CACA,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;CAC3C,CAAC;AACD;CACA,SAAS,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;CAC1B;AACA;CACA,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3B,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;CAChB,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;CACzB,EAAE;CACF,CAAC,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;CAC/C,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACzC,CAAC;AACD;CACA;CACO,MAAM,iBAAiB,GAAG,WAAW;CAC5C,CAACC,OAAK,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC;CACvC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG;CACtB,CAAC,SAAS;CACV,CAAC,KAAK;CACN,CAAC,CAAC;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA,WAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,KAAK;CACV,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,cAAc;CACvB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,MAAM;CACf,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,OAAO;AACd;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,OAAO,KAAK,CAAC,GAAsB,CAAC,CAAC;CACvC,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;CACzC,EAAE;CACF,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,EAAE,EAAE,OAAO;CACd,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;CACvF,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CCvJF,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;CAC9B,MAAM,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACvC;CACA;CACA;CACA;CACA;CACA;CACA,SAAS,YAAY,EAAE,MAAM,EAAE;CAC/B;CACA;CACA;CACA;CACA;CACA,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;CACpB,EAAE,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;CAC5C,EAAE;AACF;CACA;CACA;CACA;CACA,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;CACzG,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;CAClC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAC9B,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B;CACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CAC1B,CAAC;AACD;AACA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,kBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;CACxC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;CACA,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;CACpD,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACrD;CACA;CACA;CACA,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;CACpE;;AChCA,qBAAe;CACf,CAAC,QAAQ;CACT,CAAC,SAAS;CACV,CAAC,UAAU;CACX,CAAC,QAAQ;CACT,CAAC,SAAS;CACV,CAAC,QAAQ;CACT,CAAC,SAAS;CACV,CAAC;;CCXD;CACA;CACA;CACA;CACA;CACA,SAAS,WAAW,EAAE,GAAG,EAAE;CAC3B;AACA;CACA,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAClE;CACA,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;CACrD,CAAC;AACD;CACA,MAAM,UAAU,GAAG;CACnB,CAAC,KAAK,EAAE;CACR,EAAE,MAAM,EAAE,OAAO;CACjB,EAAE,GAAG,EAAE,CAAC;CACR,EAAE,YAAY,EAAE,KAAK;CACrB,EAAE,eAAe,EAAE,EAAE;CACrB,EAAE;CACF,CAAC,WAAW,EAAE;CACd,EAAE,MAAM,EAAE,OAAO;CACjB,EAAE,GAAG,EAAE,CAAC;CACR,EAAE,YAAY,EAAE,KAAK;CACrB,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;CACzD,EAAE;CACF,CAAC,CAAC;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACe,SAAS,OAAO;CAC/B,CAAC,KAAK;CACN,CAAC;CACD,EAAE,MAAM,GAAG,QAAQ,CAAC,aAAa;CACjC,EAAE,KAAK,GAAG,SAAS;CACnB,EAAE,YAAY,GAAG,EAAE;CACnB,EAAE,GAAG,GAAG,CAAC;CACT,EAAE,eAAe,GAAG,EAAE;CACtB,EAAE,GAAG,EAAE;CACP,EAAE;CACF,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;CACA,CAAC,IAAImB,QAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;CAClC,EAAE,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;CACvB,EAAE;CACF,MAAM,IAAI,CAAC,KAAK,EAAE;CAClB,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;CACtB,EAAE;AACF;CACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B;CACA;CACA;CACA;CACA;AACA;CACA,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE;CAC5C,EAAE,OAAO,KAAK,CAAC;CACf,EAAE;AACF;CACA,CAAC,IAAI,UAAU,CAAC;CAChB,CAAC,IAAI,MAAM,KAAK,KAAK,EAAE;CACvB,EAAE,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;CAC5C,EAAE;CACF,MAAM;CACN,EAAE,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;AACnD;CACA,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;CACjE,IAAI,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,eAAe,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,EAAE;CACxE,IAAI;AACJ;CACA;CACA,GAAG,IAAI,EAAE,GAAG,UAAU,CAAC;CACvB,GAAG,IAAI,YAAY,KAAK,EAAE,EAAE;CAC5B,IAAI,KAAK,IAAI,CAAC,IAAI,aAAa,EAAE;CACjC,KAAK,IAAI,QAAQ,GAAG,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;CACpE,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;CAC5B,MAAM,MAAM;CACZ,MAAM;CACN,KAAK;CACL,IAAI;AACJ;CACA,GAAG,IAAI,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;CACtE,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,GAAG,EAAE;AACjC;CACA;CACA,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;CACnD,KAAK,IAAI,WAAW,GAAG,UAAU,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;CACxE,KAAK,IAAI,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;CACrE,KAAK,IAAIC,MAAW,CAAC,OAAO,CAAC,EAAE;CAC/B,MAAM,OAAO,GAAG,CAAC,CAAC;CAClB,MAAM;CACN,KAAK,IAAI,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE;CACzC,MAAM,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;CAC1E,MAAM;CACN,UAAU,IAAI,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE;CAC9C,MAAM,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;CACtE,MAAM;CACN,KAAK;AACL;CACA;CACA,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;CACpD,IAAI,IAAI,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;CACnC,IAAI,IAAI,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC;AAC/B;CACA,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;CAC1C;CACA,IAAI,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CACzC,KAAK,IAAIA,MAAW,CAAC,CAAC,CAAC,EAAE;CACzB,MAAM,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CAChC,MAAM;CACN,KAAK,CAAC,CAAC;CACP,IAAI,IAAI,MAAM,GAAG,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,CAAC;CACvD,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;CACxB,IAAI,IAAI,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;CAC7B,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;CAClB,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AACzC;CACA,IAAI,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE;CAC3B,KAAK,IAAI,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;CACtC,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;CAC3D,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC3C;CACA,KAAK,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE;CAC3B,MAAM,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;CACtC,MAAM;CACN,UAAU;CACV,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;CACvC,MAAM;AACN;CACA,KAAK,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;CACjD,KAAK;AACL;CACA,IAAI,UAAU,GAAG,EAAE,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;CACxC,IAAI;CACJ,QAAQ;CACR,IAAI,UAAU,GAAG,OAAO,CAAC;CACzB,IAAI;CACJ,GAAG;CACH,OAAO;CACP,GAAG,UAAU,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CACjC,GAAG;AACH;CACA,EAAE,IAAI,MAAM,KAAK,MAAM;CACvB;CACA,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;CACjD,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACpE;CACA,GAAG,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CACvD,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B;CACA,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;CAC3B,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAC1B,KAAK;AACL;CACA,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;CAC3B,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;CAC1B,KAAK;AACL;CACA,IAAI,OAAO,CAAC,CAAC;CACb,IAAI,CAAC,CAAC;CACN,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;CAC5B,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;CAC3C,EAAE;AACF;CACA,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;CAClC,CAAC,OAAO,KAAK,CAAC;CACd,CAAC;AACD;CACA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1B;CACA;CACA;CACA;CACA,MAAM,MAAM,GAAG;CACf,CAAC,KAAK,EAAE,EAAE,KAAK,EAAEZ,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;CAC3C,CAAC,KAAK,EAAE,EAAE,KAAK,EAAEA,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;CAC3C,CAAC,CAAC;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,UAAU,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE;CAClD,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC;CAClB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAClB;CACA,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;CACA,CAAC,IAAI,CAAC,KAAK,EAAE;CACb,EAAE,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;CACvB,EAAE;AACF;CACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC/B,CAAC,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC5C;CACA,CAAC,IAAI,KAAK,CAAC,WAAW,EAAE;CACxB,EAAE,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;CAC3B,EAAE;AACF;CACA,CAAC,MAAM,YAAY,GAAG,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;CAC7C,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChC;CACA;CACA,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;CACb,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CACxC,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CAC1B,EAAE;CACF,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;CACb,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CACxC,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CAC1B,EAAE;AACF;CACA,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;CACjD,EAAE,OAAO,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;CACjC,EAAE;AACF;CACA,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE;CACxB,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;CACtC,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;CAClD,EAAE,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;CAC5D,GAAG,IAAI,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;CACtC,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;CACjD,IAAI,OAAOa,KAAU,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;CACvC,IAAI;CACJ,GAAG,OAAO,KAAK,CAAC;CAChB,GAAG,CAAC,CAAC;CACL,EAAE,OAAO,SAAS,CAAC;CACnB,EAAE;CACF,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;CACb,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;CAClC,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC;CACxB,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;CACnC,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B;CACA,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CACpC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;CACd,EAAE,OAAO,OAAO,CAAC;CACjB,EAAE;AACF;CACA,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE;CACzB,EAAE,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;CACjC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;CAC7B,EAAE,IAAI,WAAW,IAAI,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;CAC5D,GAAG,GAAG,GAAG,MAAM,CAAC;CAChB,GAAG;CACH,OAAO;CACP,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;CAC3B,GAAG,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CAClC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE;CAChB,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG;CACvB,KAAK,MAAM;CACX,KAAK;CACL,SAAS;CACT,KAAK,WAAW,GAAG,KAAK,CAAC;CACzB,KAAK,GAAG,GAAG,MAAM,CAAC;CAClB,KAAK;CACL,IAAI;CACJ,QAAQ;CACR,IAAI,GAAG,GAAG,MAAM,CAAC;CACjB,IAAI;CACJ,GAAG;CACH,EAAE;CACF,CAAC,OAAO,OAAO,CAAC;CAChB;;CCjTA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,SAAS,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE;CAC1D,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;CACzB,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B;CACA,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAChC,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/C;CACA,CAAC,IAAI,OAAO,EAAE;CACd,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,OAAO,KAAK,IAAI,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC;CAC7D,EAAE;AACF;CACA,CAAC,OAAO,GAAG,CAAC;CACZ,CAAC;AACD;CACA,EAAE,CAAC,OAAO,GAAG,OAAO;;CCjBpB;CACA;CACA;CACA;CACA;CACA;CACe,SAAS,SAAS,EAAE,KAAK,EAAE;CAC1C,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS;CAC/B,CAAC,MAAM,GAAG,SAAS;CACnB,UAACC,SAAO,GAAG,IAAI;CACf,CAAC,GAAG,aAAa;CACjB,CAAC,GAAG,EAAE,EAAE;CACR,CAAC,IAAI,GAAG,CAAC;AACT;CACA,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;CACA,CAAC,IAAI,QAAQ,GAAG,MAAM,CAAC;CACvB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;CACvC,WAAW,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC;CAC3C,WAAW,UAAU,CAAC,cAAc,CAAC;AACrC;CACA;CACA;CACA;AACA;CACA,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACnC;CACA,CAACA,SAAO,KAAK,MAAM,CAAC,OAAO,CAAC;AAC5B;CACA,CAAC,IAAIA,SAAO,IAAI,CAACC,OAAY,CAAC,KAAK,CAAC,EAAE;CACtC;CACA,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAED,SAAO,KAAK,IAAI,GAAG,SAAS,GAAGA,SAAO,CAAC,CAAC,MAAM,CAAC;CAChF,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;CAC/B,EAAE,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;AACtC;CACA,EAAE,IAAI,MAAM,CAAC,SAAS,EAAE;CACxB,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;CAC9D,GAAG;CACH,OAAO;CACP,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,wDAAwD,CAAC,CAAC,CAAC;CACrG,GAAG;CACH,EAAE;CACF,MAAM;CACN;CACA,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACpC;CACA,EAAE,IAAI,MAAM,CAAC,eAAe,EAAE;CAC9B,GAAG,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;CACtD,GAAG;CACH,OAAO;CACP,GAAG,IAAI,SAAS,KAAK,IAAI,EAAE;CAC3B,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;CAC7B,KAAK,OAAOE,eAAoB,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;CACjD,KAAK,CAAC,CAAC;CACP,IAAI;CACJ,GAAG;AACH;CACA,EAAE,IAAI,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AACzB;CACA,EAAE,IAAI,IAAI,KAAK,OAAO,EAAE;CACxB;CACA,GAAG,IAAI,KAAK,GAAG,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;CAC9D,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CACvB,GAAG;AACH;CACA,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;CAC1B,EAAE,IAAI,SAAS,KAAK,IAAI,EAAE;CAC1B,GAAG,KAAK,GAAGA,eAAoB,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;CACpD,GAAG;AACH;CACA,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,GAAG,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;CACpG,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;CACvE,EAAE;AACF;CACA,CAAC,OAAO,GAAG,CAAC;CACZ;;CCnFA;CACA;CACA;CACA;CACA,MAAMC,SAAO,GAAG;CAChB,CAAC,EAAE,kBAAkB,EAAE,mBAAmB,GAAG,kBAAkB,GAAG;CAClE,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE;CAClE,CAAC,EAAE,iBAAiB,GAAG,oBAAoB,EAAE,iBAAiB,IAAI;CAClE,CAAC,CAAC;AACF;CACA;CACA,MAAMC,WAAS,GAAG;CAClB,CAAC,GAAG,iBAAiB,GAAG,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,GAAG;CACjE,CAAC,EAAE,CAAC,iBAAiB,IAAI,iBAAiB,GAAG,kBAAkB,EAAE;CACjE,CAAC,GAAG,iBAAiB,GAAG,CAAC,iBAAiB,GAAG,iBAAiB,GAAG;CACjE,CAAC,CAAC;AACF;AACA,qBAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,gBAAgB;CACrB,CAAC,KAAK,EAAE,kBAAkB;CAC1B,CAAC,IAAI,EAAE,iBAAiB;CACxB,CAAC,KAAK,EAAE,KAAK;CACb,UAACD,SAAO;CACR,YAACC,WAAS;CACV,CAAC,CAAC;;CCxBF;AACA;CACA,MAAM,CAAC,GAAG,gBAAgB,CAAC;CAC3B,MAAM,CAAC,GAAG,iBAAiB,CAAC;AAC5B;AACA,eAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,SAAS;CACd,CAAC,IAAI,EAAE,UAAU;CACjB,CAAC,IAAI,EAAE,aAAa;CACpB;CACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CAChC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE;CACtB,IAAI,OAAO,GAAG,GAAG,GAAG,CAAC;CACrB,IAAI;AACJ;CACA,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;CAChD,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CAChC,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE;CACjB,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;CAC7C,IAAI;AACJ;CACA,GAAG,OAAO,GAAG,GAAG,GAAG,CAAC;CACpB,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC,CAAC;;CC5BF,MAAMD,SAAO,GAAG;CAChB,CAAC,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,kBAAkB,CAAC;CAC9D,CAAC,CAAC,kBAAkB,EAAE,kBAAkB,GAAG,iBAAiB,CAAC;CAC7D,CAAC,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;CAC7D,CAAC,CAAC;AACF;CACA,MAAMC,WAAS,GAAG;CAClB,CAAC,EAAE,iBAAiB,IAAI,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,CAAC;CAClE,CAAC,CAAC,CAAC,kBAAkB,IAAI,kBAAkB,GAAG,oBAAoB,CAAC;CACnE,CAAC,EAAE,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;CACjE,CAAC,CAAC;AACF;AACA,gBAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,WAAW;CAChB,CAAC,KAAK,EAAE,qBAAqB;CAC7B,CAAC,IAAI,EAAE,WAAW;CAClB,CAAC,KAAK,EAAE,KAAK;CACb,UAACD,SAAO;CACR,YAACC,WAAS;CACV,CAAC,CAAC;;CCnBF;CACA;CACA;AACA;CACA;CACA;CACA;CACA,MAAMD,SAAO,GAAG;CAChB,CAAC,EAAE,mBAAmB,EAAE,iBAAiB,IAAI,kBAAkB,GAAG;CAClE,CAAC,EAAE,mBAAmB,EAAE,iBAAiB,IAAI,mBAAmB,EAAE;CAClE,CAAC,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,kBAAkB,GAAG;CAClE,CAAC,CAAC;AACF;CACA;CACA;CACO,MAAMC,WAAS,GAAG;CACzB,CAAC,GAAG,kBAAkB,GAAG,CAAC,iBAAiB,IAAI,CAAC,kBAAkB,GAAG;CACrE,CAAC,EAAE,CAAC,kBAAkB,IAAI,kBAAkB,IAAI,mBAAmB,EAAE;CACrE,CAAC,GAAG,mBAAmB,EAAE,CAAC,mBAAmB,GAAG,kBAAkB,GAAG;CACrE,CAAC,CAAC;AACF;AACA,kBAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,aAAa;CAClB,CAAC,IAAI,EAAE,aAAa;CACpB,CAAC,KAAK,EAAE,KAAK;CACb,UAACD,SAAO;CACR,YAACC,WAAS;CACV,CAAC,CAAC;;CC7BF;CACA;CACA;CACA;AACA;CACA;CACA;CACA;AACA,gBAAe;CACf,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACvC,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAClB,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACxC,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC3B,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACpC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnB,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC5C,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAClB,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CACzC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAChC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC9C,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAClC,CAAC,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACpD,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACtC,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC3C,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAClB,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9B,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACtC,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAClD,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAC/B,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACzC,CAAC,gBAAgB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAClD,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAChC,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC7B,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAChD,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjD,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAChD,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAChD,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACxC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACrC,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACjC,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,CAAC,YAAY,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACvC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC7C,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACzC,CAAC,aAAa,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC/C,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACrB,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACxC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAC1B,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC9C,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1C,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAC3B,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC;CACxC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1C,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACtC,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACrC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC7C,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACnC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3B,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACvC,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAChD,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC/B,CAAC,sBAAsB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1D,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAChD,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACvC,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACzC,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,CAAC,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACpD,CAAC,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACpD,CAAC,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACpD,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACjC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAClB,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC7C,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACrB,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC5B,CAAC,kBAAkB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACtD,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAChC,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjD,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,CAAC,gBAAgB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnD,CAAC,iBAAiB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACrD,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,iBAAiB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACpD,CAAC,iBAAiB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACpD,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAChD,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACvC,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACvC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACtC,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACzC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1B,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACnC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC9C,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAC5B,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC;CAC9B,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC5C,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnD,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnD,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnD,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACxC,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACvC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CACzC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAChD,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACpC,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACjB,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC/C,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC5C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC/C,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC5C,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACtC,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC1C,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC5C,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClC,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACjC,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACzC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClC,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAClC,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC5C,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnB,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAChD,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACpB,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAChD,CAAC;;CCzJD,IAAI,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;CACpE,IAAI,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC3D;AACA,YAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,MAAM;CACX,CAAC,IAAI,EAAE,MAAM;CACb,CAAC,IAAI,EAAE,UAAU;CACjB,CAAC,QAAQ,EAAE,GAAG,IAAI;CAClB;CACA;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;CACxB,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;CAC/B,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACxB;CACA,GAAG,IAAI,GAAG,GAAG,SAAS,EAAE;CACxB,IAAI,OAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;CACvD,IAAI;AACJ;CACA,GAAG,OAAO,KAAK,GAAG,GAAG,CAAC;CACtB,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC,MAAM,EAAE,GAAG,IAAI;CAChB;CACA;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;CACxB,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;CAC/B,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACxB;CACA,GAAG,IAAI,GAAG,IAAI,OAAO,EAAE;CACvB,IAAI,OAAO,GAAG,GAAG,KAAK,CAAC;CACvB,IAAI;AACJ;CACA,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC,CAAC;CAClD,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,MAAM,EAAE,YAAY;CACvB,GAAG;CACH,EAAE,YAAY,EAAE;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG,MAAM,EAAE,IAAI;CACf,GAAG,MAAM,EAAE,kBAAkB;CAC7B,GAAG,OAAO,EAAE,IAAI;CAChB,GAAG;CACH,EAAE,OAAO,EAAE,sBAAsB;CACjC,EAAE,MAAM,EAAE;CACV,GAAG,MAAM,EAAE,YAAY;CACvB,GAAG,MAAM,EAAE,IAAI;CACf,GAAG,SAAS,EAAE,IAAI;CAClB,GAAG;CACH,EAAE,aAAa,EAAE;CACjB,GAAG,IAAI,EAAE,MAAM;CACf,GAAG,MAAM,EAAE,IAAI;CACf,GAAG,MAAM,EAAE,kBAAkB;CAC7B,GAAG;CACH,EAAE,KAAK,EAAE;CACT,GAAG,IAAI,EAAE,QAAQ;CACjB,GAAG,OAAO,EAAE,IAAI;CAChB,GAAG,IAAI,EAAE,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,GAAG,CAAC;CACpD,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE;CACf,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;CACzB;CACA,KAAK,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;CAC7C,KAAK;AACL;CACA,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;CAClB,IAAI,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,SAAS,IAAI;CAC9C,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;CAC9C,KAAK,CAAC,CAAC;AACP;CACA,IAAI,OAAO;CACX,KAAK,OAAO,EAAE,MAAM;CACpB,KAAK,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;CAC7B,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5B,KAAK,CAAC;CACN,IAAI;CACJ,GAAG,SAAS,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE;CAC9B,IAAI,QAAQ,GAAG,IAAI;CACnB,IAAI,GAAG,EAAE,KAAK;CACd,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;CACnB,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACxB,KAAK;AACL;CACA,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClD;CACA,IAAI,IAAI,WAAW,GAAG,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AAClE;CACA,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;CAC9B,KAAK,IAAI,WAAW,EAAE;CACtB,MAAM,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;CACnC,MAAM;AACN;CACA,KAAK,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;CAC5C,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAChB;CACA,IAAI,OAAO,GAAG,GAAG,GAAG,CAAC;CACrB,IAAI;CACJ,GAAG;CACH,EAAE,SAAS,EAAE;CACb,GAAG,IAAI,EAAE,QAAQ;CACjB,GAAG,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC;CACrC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE;CACf,IAAI,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;CAC5B,IAAI,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AACxD;CACA,IAAI,IAAI,GAAG,KAAK,aAAa,EAAE;CAC/B,KAAK,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;CACjC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;CACnB,KAAK;CACL,SAAS;CACT,KAAK,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;CAChC,KAAK;AACL;CACA,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;CACpB,KAAK,OAAO,GAAG,CAAC;CAChB,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;AC1HF,UAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,IAAI;CACT,CAAC,KAAK,EAAE,YAAY;CACpB,CAAC,IAAI,EAAE,IAAI;CACX,CAAC,IAAI,EAAE,QAAQ;CACf;CACA,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ;CACxB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;CACpB,CAAC,CAAC;;CCFF;CACA,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B;CACA,IAAI,YAAY,CAAC;AACjB;CACA,IAAI,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,CAAC,QAAQ,EAAE;CAChD;CACA,CAAC,KAAK,IAAI,KAAK,IAAI,CAACxB,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE;CACvC,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;CACpC,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;CACxC,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7B;CACA,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;CAClC,GAAG,QAAQ,CAAC,aAAa,GAAG,KAAK,CAAC;CAClC,GAAG,MAAM;CACT,GAAG;CACH,EAAE;CACF,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,SAAS,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,EAAE;CAC3F,CAAC,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACrC;CACA,CAAC,IAAI,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;CAC1F,EAAE,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;CACxB,EAAE,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;CACpB,EAAE;CACF,MAAM;CACN;CACA,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC;AAC5B;CACA;CACA,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACjE;CACA,EAAE,IAAI,OAAO,EAAE;CACf;CACA,GAAG,IAAI,EAAE,YAAY,KAAK,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,EAAE;CACvE;CACA,IAAI,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;CACjC,IAAI,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;CAC9D,IAAI,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACxD;CACA,IAAI,GAAG,GAAG,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAC5C;CACA,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;CACpC;CACA,KAAK,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;CAC3B,KAAK,GAAG,CAAC,KAAK,GAAG,aAAa,CAAC;CAC/B,KAAK,OAAO,GAAG,CAAC;CAChB,KAAK;CACL,IAAI;CACJ,GAAG;AACH;CACA;CACA;CACA,EAAE,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;CAC3C,EAAE,GAAG,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC;CACtD,EAAE,GAAG,CAAC,KAAK,GAAG,aAAa,CAAC;CAC5B,EAAE;AACF;CACA,CAAC,OAAO,GAAG,CAAC;CACZ;;CChFe,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;CAChD,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;CACA,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;CACrC,WAAW,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;CACxC,WAAW,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;CACjE;;CCTA;CACA;CACA;AAIA;CACO,SAAS,YAAY,EAAE,KAAK,EAAE;CACrC;CACA,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;CACnC,CAAC;AACD;CACO,SAAS,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE;CAC5C;CACA,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;CACnC,CAAC;AACD;CACO,SAASyB,UAAQ,EAAE,KAAK,EAAE;CACjC,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE;CACrD,EAAE,GAAG,CAAC,GAAG;CACT,GAAG,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;CAC7B,GAAG;CACH,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE;CACd,GAAG,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;CAC7B,GAAG;CACH,EAAE,CAAC,CAAC;CACJ;;;;;;;;;CC1BA;CACA;CACA;AACA;AAGA;CACe,SAAS,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE;CACxD,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;CACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;CAC5C,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C;CACA,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;CACd,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACtB,EAAE;AACF;CACA,CAAC,OAAO,CAAC,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;CAChC;;CCnBA;CACA;CACA;AACA;AAGA;CACA;CACA,MAAM,MAAM,GAAG,IAAI,CAAC;CACpB,MAAM,OAAO,GAAG,IAAI,CAAC;CACrB,MAAM,MAAM,GAAG,IAAI,CAAC;CACpB,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB;CACA;CACA,MAAM,OAAO,GAAG,KAAK,CAAC;CACtB,MAAM,OAAO,GAAG,KAAK,CAAC;CACtB,MAAM,MAAM,GAAG,GAAG,CAAC;CACnB,MAAM,SAAS,GAAG,MAAM,CAAC;AACzB;CACA;CACA;CACA,MAAM,QAAQ,GAAG,IAAI,CAAC;CACtB,MAAM,WAAW,GAAG,KAAK,CAAC;CAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC;AAEtB;CACA,SAAS,MAAM,EAAE,CAAC,EAAE;CACpB,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE;CACnB,EAAE,OAAO,CAAC,CAAC;CACX,EAAE;CACF,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,KAAK,OAAO,CAAC;CACrC,CAAC;AACD;CACA,SAAS,SAAS,EAAE,GAAG,EAAE;CACzB,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;CAC7B,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACzB,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;CAClC,CAAC;AACD;CACA;CACe,SAAS,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE;CAC9D,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;CACnC,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;AACnC;CACA,CAAC,IAAI,CAAC,CAAC;CACP,CAAC,IAAI,CAAC,CAAC;CACP,CAAC,IAAI,IAAI,CAAC;AACV;CACA;CACA,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACb;CACA,CAAC,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;CACrC;AACA;CACA;CACA;CACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;CAC/B,CAAC,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AAC7F;CACA,CAAC,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;CACrC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;CAC/B,CAAC,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AAC5F;CACA;CACA,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;CAC3B,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACzB;CACA;CACA,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACtB;CACA;CACA;CACA;CACA,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,SAAS,EAAE;CACvC,EAAE,CAAC,GAAG,CAAC,CAAC;CACR,EAAE;CACF,MAAM;CACN,EAAE,IAAI,GAAG,EAAE;CACX;CACA,GAAG,CAAC,GAAG,GAAG,IAAI,MAAM,GAAG,IAAI,IAAI,OAAO,CAAC;CACvC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;CACpB,GAAG;CACH,OAAO;CACP;CACA,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI,MAAM,CAAC;CACrC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;CACpB,GAAG;CACH,EAAE;CACF,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE;CAC3B,EAAE,IAAI,GAAG,CAAC,CAAC;CACX,EAAE;CACF,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;CACjB;CACA;CACA,EAAE,IAAI,GAAG,CAAC,GAAG,WAAW,CAAC;CACzB,EAAE;CACF,MAAM;CACN,EAAE,IAAI,GAAG,CAAC,GAAG,WAAW,CAAC;CACzB,EAAE;AACF;CACA,CAAC,OAAO,IAAI,GAAG,GAAG,CAAC;CACnB;;CCrGA;CACA;CACA;CACA;AACA;AAGA;CACe,SAAS,iBAAiB,EAAE,MAAM,EAAE,MAAM,EAAE;CAC3D,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;CACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;CAC5C,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C;CACA,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;CACd,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACtB,EAAE;AACF;CACA,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;CACvB,CAAC,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,KAAK,CAAC;CAC5C;;CCrBA;CACA;CACA;CACA;AACA;AAGA;CACA;CACA;CACA;CACA;CACA,MAAM,GAAG,GAAG,KAAK,CAAC;AAClB;CACe,SAAS,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE;CACvD,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;CACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;CAC5C,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C;CACA,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;CACd,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACtB,EAAE;AACF;CACA,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;CACxC;;CC1BA;CACA;CACA;AACA;AAIA;CACe,SAAS,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE;CACvD,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;CACA,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;CAClC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAClC;CACA,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;CAC1B;;CCZA;CACA,MAAM/B,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;CACpB,MAAMG,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB;CACA,IAAIC,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC;AACvB;AACA,eAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,SAAS;CACd,CAAC,IAAI,EAAE,SAAS;CAChB,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE;AACF;CACA;CACA;CACA,QAACA,OAAK;AACN;CACA,CAAC,IAAI,EAAE,OAAO;CACd;CACA;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,GAAGA,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD;CACA;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,GAAGJ,GAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAACG,GAAC,GAAG,KAAK,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;AAClF;CACA,EAAE,OAAO;CACT,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;CACpB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACtB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACtB,GAAG,CAAC;CACJ,EAAE;CACF;CACA;CACA;CACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd;CACA,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;CACb,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;CAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAC7B;CACA;CACA,EAAE,IAAI,GAAG,GAAG;CACZ,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIA,GAAC;CACzE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAC;CAC9D,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIA,GAAC;CACzE,GAAG,CAAC;AACJ;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,GAAGC,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACjD,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,SAAS,EAAE;CACb,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;CACxG,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CCzEF;CACA;CACA;CACA;AACA;AAKA;CACA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC;CACe,SAAS,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE;CAC1D,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;CACA,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;CACzC,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;AACzC;CACA,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1E;CACA,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpE;CACA,CAAC,OAAO,CAAC,QAAQ,GAAG,GAAG,IAAI,GAAG,GAAG,QAAQ,EAAE;CAC3C;;;;;;;;;;;;CCnBe,SAAS,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE;CAClE,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;CAClB,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;CACrB,EAAE;AACF;CACA,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9B;CACA,CAAC,IAAI,CAAC,SAAS,EAAE;CACjB,EAAE,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC4B,eAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACnG,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC,uEAAuE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;CAC9G,EAAE;AACF;CACA,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;CACnC,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;AACnC;CACA,CAAC,KAAK,IAAI,CAAC,IAAIA,eAAkB,EAAE;CACnC,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;CAChE,GAAG,OAAOA,eAAkB,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;CAC9D,GAAG;CACH,EAAE;AACF;CACA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;CACjE;;CCxBA;CACO,SAAS,EAAE,EAAE,KAAK,EAAE;CAC3B;CACA,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CACxC,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CAChC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;CACvC,CAAC;AACD;CACO,SAAS,EAAE,EAAE,KAAK,EAAE;CAC3B;CACA,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CACxC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACtB,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;CAC3B,CAAC;AACD;CACO,SAASD,UAAQ,EAAE,KAAK,EAAE;CACjC;CACA;CACA,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE;CAC9C,EAAE,GAAG,CAAC,GAAG;CACT,GAAG,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;CACnB,GAAG;CACH,EAAE,CAAC,CAAC;AACJ;CACA,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE;CAC9C,EAAE,GAAG,CAAC,GAAG;CACT,GAAG,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;CACnB,GAAG;CACH,EAAE,CAAC,CAAC;CACJ;;;;;;;;;CC5Be,SAAS,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;CAChD,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;CAClB,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;CAClB,EAAE;AACF;CACA,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C;CACA,CAAC,KAAK,IAAI,CAAC,IAAI,aAAa,EAAE;CAC9B,EAAE,IAAI,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;CAC3D,GAAG,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;CACzC,GAAG;CACH,EAAE;AACF;CACA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;CACzD;;CCfO,SAAS,OAAO,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE;CAC9C,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;CAC5C,CAAC,IAAI,SAAS,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;CAC9B,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;CACrD,CAAC;AACD;CACO,SAAS,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE;CAC7C,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;CAC5C,CAAC,IAAI,SAAS,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;CAC9B,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;CACrD;;;;;;;;CCbA;CACA;CACA;AAYA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;CAC7C,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC;CACA,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;CAC3B,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;CACnB,EAAE;AACF;CACA,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;CAC1B,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;CACb,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE;CAC7C,CAAC,IAAI,UAAU,CAAC;AAChB;CACA,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE;CAClB;CACA,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACnC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC;CACzC,EAAE;AACF;CACA,CAAC,IAAI;CACL,EAAE,SAAS,EAAE,YAAY;CACzB,EAAE,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI;CAC5B,EAAE,GAAG,YAAY;CACjB,EAAE,GAAG,OAAO,CAAC;AACb;CACA,CAAC,IAAI,CAAC,UAAU,EAAE;CAClB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;CAC1C,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;CAC3C,EAAE;AACF;CACA,CAAC,IAAI,UAAU,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACjC,CAAC,IAAI,WAAW,GAAG,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;CAClG,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;AACd;CACA,CAAC,IAAI,QAAQ,KAAK,SAAS,EAAE;CAC7B,EAAE,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;CAChD,EAAE;AACF;CACA,CAAC,IAAI,WAAW,KAAK,CAAC,EAAE;CACxB,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CACzC,EAAE;CACF,MAAM;CACN,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;CACnC,EAAE,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;CACpD,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;CACpB,GAAG,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;CACpC,GAAG,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE;CACpB;CACA,EAAE,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK;CAC7C,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;CAChB,IAAI,OAAO,CAAC,CAAC;CACb,IAAI;AACJ;CACA,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;CAC9D,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;CAC5B,GAAG,EAAE,CAAC,CAAC,CAAC;AACR;CACA,EAAE,OAAO,QAAQ,GAAG,SAAS,EAAE;CAC/B;CACA;CACA,GAAG,QAAQ,GAAG,CAAC,CAAC;AAChB;CACA,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,MAAM,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE;CACrE,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;CAC1B,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB;CACA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;CACjC,IAAI,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;CAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;CACvF,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAChD,IAAI,CAAC,EAAE,CAAC;CACR,IAAI;CACJ,GAAG;CACH,EAAE;AACF;CACA,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7B;CACA,CAAC,OAAO,GAAG,CAAC;CACZ,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;CACrD,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;CACtB;CACA,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACtC;CACA,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;CAC5E,EAAE;AACF;CACA,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,GAAG,OAAO,CAAC;AAChE;CACA,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;CACA;CACA,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;CACxB,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AACxB;CACA,CAAC,IAAI,SAAS,GAAG,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;AACrD;CACA,CAAC,IAAI,KAAK,EAAE;CACZ,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAChC,EAAE;CACF,MAAM;CACN,EAAE,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC;CAC3E,EAAE;AACF;CACA,CAAC,WAAW,GAAG,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;AACjE;CACA,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;CAC5B,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5B;CACA;CACA,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CAC1B,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1B;CACA;CACA;CACA,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;CACxD,EAAE,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,SAAS,CAAC;AACnD;CACA,EAAE,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;CACzB,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;CACtD;CACA;CACA;CACA,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;CAC/B,GAAG,EAAE,GAAG,EAAE,CAAC;CACX,GAAG;CACH,OAAO,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;CACpC,GAAG,EAAE,GAAG,EAAE,CAAC;CACX,GAAG;CACH,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAGE,MAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;CAC1C,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;CACvB,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;CACvB,EAAE;AACF;CACA,CAAC,IAAI,aAAa,EAAE;CACpB;CACA,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;CAC3D,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;CAC3D,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI;CAC3B,EAAE,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACvC,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK;CAC/C,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;CAC9B,GAAG,OAAO,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;CACrC,GAAG,CAAC,CAAC;AACL;CACA,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;CACzD,EAAE,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC;CACA,EAAE,IAAI,aAAa,EAAE;CACrB;CACA,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;CAC/C,GAAG;AACH;CACA,EAAE,IAAI,WAAW,KAAK,KAAK,EAAE;CAC7B,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;CAC9B,GAAG;AACH;CACA,EAAE,OAAO,GAAG,CAAC;CACb,EAAE,EAAE;CACJ,EAAE,SAAS;CACX,EAAE,CAAC,CAAC;CACJ,CAAC;AACD;CACO,SAAS,OAAO,EAAE,GAAG,EAAE;CAC9B,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC;CACpD,CAAC;AACD;CACA,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACpC;CACO,SAAS,QAAQ,EAAE,KAAK,EAAE;CACjC,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;CACtD,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;CACpE,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;CACjE;;;;;;;;;;;AC1NA,WAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,KAAK;CACV,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,YAAY;CACrB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,IAAI;AACX;CACA;CACA,CAAC,QAAQ,EAAE,GAAG,IAAI;CAClB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;CAC7B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;CAC7B,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;CACtB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;CAC5C,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACpB;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;CACf,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACjE;CACA,GAAG,QAAQ,GAAG;CACd,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;CACrD,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;CACvC,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CAChC,IAAI;AACJ;CACA,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;CACd,GAAG;AACH;CACA;CACA;CACA;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;CACb,GAAG,CAAC,IAAI,GAAG,CAAC;CACZ,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACnB,GAAG;AACH;CACA,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE;CAChB,GAAG,CAAC,IAAI,GAAG,CAAC;CACZ,GAAG;AACH;CACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;CAC/B,EAAE;AACF;CACA;CACA,CAAC,MAAM,EAAE,GAAG,IAAI;CAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;CACtB,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACd;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;CACb,GAAG,CAAC,IAAI,GAAG,CAAC;CACZ,GAAG;AACH;CACA,EAAE,CAAC,IAAI,GAAG,CAAC;CACX,EAAE,CAAC,IAAI,GAAG,CAAC;AACX;CACA,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE;CACjB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CAC7B,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAClC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAC1D,GAAG;AACH;CACA,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5B,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,cAAc,CAAC;CACjE,GAAG;CACH,EAAE,MAAM,EAAE;CACV,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,cAAc,CAAC;CACjE,GAAG,MAAM,EAAE,IAAI;CACf,GAAG,SAAS,EAAE,IAAI;CAClB,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CCvFF;CACA;CACA;CACA;AACA;AACA,WAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,KAAK;CACV,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,YAAY;CACrB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,GAAG;CACV;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;CACtB,EAAE,CAAC,IAAI,GAAG,CAAC;CACX,EAAE,CAAC,IAAI,GAAG,CAAC;AACX;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC;CACA,EAAE,OAAO;CACT,GAAG,CAAC;CACJ,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CAClC,GAAG,GAAG,GAAG,CAAC;CACV,GAAG,CAAC;CACJ,EAAE;CACF;CACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;CACA,EAAE,CAAC,IAAI,GAAG,CAAC;CACX,EAAE,CAAC,IAAI,GAAG,CAAC;AACX;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1B;CACA,EAAE,OAAO;CACT,GAAG,CAAC;CACJ,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG;CAClE,GAAG,CAAC,GAAG,GAAG;CACV,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,EAAE,EAAE,OAAO;CACd,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;CACvF,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CC7DF;CACA;CACA;CACA;AACA;AACA,WAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,KAAK;CACV,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,GAAG;CACV,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;CACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;CAC3C,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;CACA;CACA,EAAE,CAAC,IAAI,GAAG,CAAC;CACX,EAAE,CAAC,IAAI,GAAG,CAAC;AACX;CACA;CACA,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;CAClB,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE;CAChB,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;CACtB,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;CAC7B,GAAG;AACH;CACA,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;CAClB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACpC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;CAC/B,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;CACvF,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CCvDF;CACA;CACA;CACA;CACA;CACA;CACA,MAAMJ,SAAO,GAAG;CAChB,CAAC,EAAE,kBAAkB,IAAI,kBAAkB,IAAI,kBAAkB,GAAG;CACpE,CAAC,EAAE,mBAAmB,GAAG,kBAAkB,IAAI,mBAAmB,EAAE;CACpE,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,GAAG,kBAAkB,GAAG;CACpE,CAAC,CAAC;AACF;CACA,MAAMC,WAAS,GAAG;CAClB,CAAC,GAAG,kBAAkB,KAAK,CAAC,kBAAkB,IAAI,CAAC,mBAAmB,EAAE;CACxE,CAAC,EAAE,CAAC,kBAAkB,MAAM,kBAAkB,KAAK,mBAAmB,EAAE;CACxE,CAAC,GAAG,oBAAoB,GAAG,CAAC,mBAAmB,IAAI,kBAAkB,GAAG;CACxE,CAAC,CAAC;AACF;AACA,iBAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,eAAe;CACpB,CAAC,KAAK,EAAE,kBAAkB;CAC1B,CAAC,IAAI,EAAE,iCAAiC;CACxC,CAAC,KAAK,EAAE,KAAK;CACb,UAACD,SAAO;CACR,YAACC,WAAS;CACV,CAAC,CAAC;;ACxBF,cAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,QAAQ;CACb,CAAC,KAAK,EAAE,SAAS;CACjB,CAAC,IAAI,EAAE,0BAA0B;CACjC,CAAC,IAAI,EAAE,SAAS;CAChB,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACnF,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACrF,CAAC,CAAC;;CCPF;CACA;CACA;CACA;CACA,MAAMD,SAAO,GAAG;CAChB,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;CACpE,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;CACpE,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;CACpE,CAAC,CAAC;AACF;CACA,MAAMC,WAAS,GAAG;CAClB,CAAC,GAAG,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,EAAE;CACrE,CAAC,EAAE,CAAC,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;CACrE,CAAC,GAAG,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;CACrE,CAAC,CAAC;AACF;AACA,sBAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,iBAAiB;CACtB,CAAC,KAAK,EAAE,uBAAuB;CAC/B,CAAC,IAAI,EAAE,iBAAiB;CACxB,CAAC,KAAK,EAAE,KAAK;CACb,CAAC,IAAI,EAAE,OAAO;CACd,UAACD,SAAO;CACR,YAACC,WAAS;CACV,CAAC,CAAC;;CCxBF,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;CACnB,MAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;AACrB;AACA,gBAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,UAAU;CACf,CAAC,KAAK,EAAE,cAAc;CACtB,CAAC,IAAI,EAAE,UAAU;CACjB,CAAC,IAAI,EAAE,cAAc;CACrB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;CACnD,EAAE;CACF,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;CACzD,EAAE;CACF,CAAC,CAAC;;ACdF,aAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,OAAO;CACZ,CAAC,IAAI,EAAE,OAAO;CACd,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACnB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,QAAQ;CACjB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE;CACF,CAAC,KAAK,EAAE,KAAK;AACb;CACA,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE;CAClB;CACA,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;CACxB,EAAE,IAAI,CAAC,CAAC;CACR,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC;AACnB;CACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;CAC1C,GAAG,CAAC,GAAG,GAAG,CAAC;CACX,GAAG;CACH,OAAO;CACP,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;CACxC,GAAG;AACH;CACA,EAAE,OAAO;CACT,GAAG,CAAC;CACJ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAC7B,GAAGvB,SAAc,CAAC,CAAC,CAAC;CACpB,GAAG,CAAC;CACJ,EAAE;CACF;CACA,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;CAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;CACxB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AACX;CACA;CACA,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;CAChB,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG;CACH,OAAO;CACP,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;CACvC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;CACvC,GAAG;AACH;CACA,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CACrB,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,OAAO,EAAE;CACX,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,8BAA8B,EAAE,oBAAoB,CAAC;CAC5F,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CC7DF,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC;AACvB;CACA,MAAMP,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAMG,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;CACrB,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3E;AACA,WAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,KAAK;CACV,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH;CACA,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACxB,GAAG;CACH,EAAE;AACF;CACA,CAAC,KAAK,EAAE,KAAK;CACb,CAAC,IAAI,EAAE,OAAO;AACd;CACA;CACA;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACnE,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACjB;CACA,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AACnD;CACA;CACA,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;CACpD,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACpB,GAAG;AACH;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAIH,GAAC,GAAGG,GAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;CACnD,EAAE,OAAO;CACT,GAAG,CAAC;CACJ,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,aAAa,CAAC;CAChC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,aAAa,CAAC;CAChC,IAAI,CAAC;CACL,EAAE;AACF;CACA;CACA;CACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;CACA;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;CAC5B,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACpB,GAAG;AACH;CACA,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;CAClB,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClB;CACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,aAAa,CAAC;CAC1C,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,aAAa,CAAC;AAC1C;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,GAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;AACvD;CACA,EAAE,OAAO;CACT,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;CAC5B,GAAG,CAAC;CACJ,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;CAC3C,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,EAAE,EAAE,OAAO;CACd,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;CACxG,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;AChFF,aAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,OAAO;CACZ,CAAC,IAAI,EAAE,OAAO;CACd,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,QAAQ;CACjB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,GAAG;CACV,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;CACtB,EAAE,IAAI,GAAG,CAAC;CACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;AACjB;CACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;CAC1C,GAAG,GAAG,GAAG,GAAG,CAAC;CACb,GAAG;CACH,OAAO;CACP,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;CAC1C,GAAG;AACH;CACA,EAAE,OAAO;CACT,GAAG,CAAC;CACJ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAC7B,GAAGI,SAAc,CAAC,GAAG,CAAC;CACtB,GAAG,CAAC;CACJ,EAAE;CACF,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd;CACA,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;CACrC;CACA,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;CAClB,GAAG,MAAM,GAAG,CAAC,CAAC;CACd,GAAG;CACH;CACA,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;CAClB,GAAG,GAAG,GAAG,CAAC,CAAC;CACX,GAAG;CACH,EAAE,OAAO;CACT,GAAG,SAAS;CACZ,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;CACzC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;CACzC,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,EAAE,EAAE,SAAS;CAChB,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,oBAAoB,CAAC;CACvF,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CCnEF;CACA;AACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;AAMA;CACA,MAAMP,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAM,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB;CACA,MAAM,IAAI,GAAG8B,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;CACA,SAAS,uBAAuB,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE;CAC3D,CAAC,MAAM,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;CACnE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;CAC7B,CAAC;AACD;CACO,SAAS,sBAAsB,EAAE,CAAC,EAAE;CAC3C,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC;CAC5C,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG9B,GAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;CACtC,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;CACnD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;CACpE,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;CACpD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;CACnD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;CACpE,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;CACpD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;CACnD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;CACpE,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AACpD;CACA,CAAC,OAAO;CACR,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG;CAChB,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;CACpB,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC;CAC3B,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC;CAC1C,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG;CAChB,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;CACpB,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC;CAC3B,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC;CAC1C,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG;CAChB,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;CACpB,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC;CAC3B,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC;CAC1C,EAAE,CAAC;CACH,CAAC;AACD;CACA,SAAS,kBAAkB,EAAE,KAAK,EAAE,CAAC,EAAE;CACvC,CAAC,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;CACtC,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;CAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;CAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;CAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;CAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;CAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClE;CACA,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;CACzC,CAAC;AACD;AACA,aAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,OAAO;CACZ,CAAC,IAAI,EAAE,OAAO;CACd,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,YAAY;CACrB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,UAAU,EAAE,IAAI;AACjB;CACA;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACzE,EAAE,IAAI,CAAC,CAAC;AACR;CACA,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE;CACtB,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG,CAAC,GAAG,GAAG,CAAC;CACX,GAAG;CACH,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE;CAC3B,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG;CACH,OAAO;CACP,GAAG,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;CACzC,GAAG,IAAI,GAAG,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;CAC1C,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;CACrB,GAAG;AACH;CACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACnB,EAAE;AACF;CACA;CACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACzE,EAAE,IAAI,CAAC,CAAC;AACR;CACA,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE;CACtB,GAAG,CAAC,GAAG,GAAG,CAAC;CACX,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG;CACH,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE;CAC3B,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG;CACH,OAAO;CACP,GAAG,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;CACzC,GAAG,IAAI,GAAG,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;CAC1C,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;CACrB,GAAG;AACH;CACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACnB,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,EAAE,EAAE,SAAS;CAChB,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;CACvF,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CCjKF;CACA;AACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;AASA;AACa8B,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC7B;CACA,SAAS,kBAAkB,EAAE,KAAK,EAAE,SAAS,EAAE;CAC/C,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;CAChE,CAAC;AACD;CACA,SAAS,kBAAkB,EAAE,KAAK,EAAE;CACpC,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;CACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;CACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;CACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;CACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;CACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD;CACA,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;CACzC,CAAC;AACD;AACA,aAAe,IAAI,UAAU,CAAC;CAC9B,CAAC,EAAE,EAAE,OAAO;CACZ,CAAC,IAAI,EAAE,OAAO;CACd,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACrB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,YAAY;CACrB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB,GAAG,IAAI,EAAE,WAAW;CACpB,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,EAAE,KAAK;CACZ,CAAC,UAAU,EAAE,MAAM;AACnB;CACA;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACzE,EAAE,IAAI,CAAC,CAAC;AACR;CACA,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE;CACtB,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG,CAAC,GAAG,GAAG,CAAC;CACX,GAAG;CACH,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE;CAC3B,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG;CACH,OAAO;CACP,GAAG,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;CACzC,GAAG,IAAI,GAAG,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;CACvC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;CACrB,GAAG;CACH,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACnB,EAAE;AACF;CACA;CACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACzE,EAAE,IAAI,CAAC,CAAC;AACR;CACA,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE;CACtB,GAAG,CAAC,GAAG,GAAG,CAAC;CACX,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG;CACH,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE;CAC3B,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG,CAAC,GAAG,CAAC,CAAC;CACT,GAAG;CACH,OAAO;CACP,GAAG,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;CACzC,GAAG,IAAI,GAAG,GAAG,kBAAkB,CAAC,KAAQ,CAAC,CAAC;CAC1C,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;CACrB,GAAG;AACH;CACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CACnB,EAAE;AACF;CACA,CAAC,OAAO,EAAE;CACV,EAAE,KAAK,EAAE;CACT,GAAG,EAAE,EAAE,SAAS;CAChB,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;CACvF,GAAG;CACH,EAAE;CACF,CAAC,CAAC;;CC9HF,MAAM,EAAE,GAAG,GAAG,CAAC;CACf,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;CAC3B,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC;CAC9B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC1B,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;CAC7B,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;CAC5B,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC3B,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3B;AACA,iBAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,WAAW;CAChB,CAAC,KAAK,EAAE,YAAY;CACpB,CAAC,IAAI,EAAE,aAAa;CACpB,CAAC,IAAI,EAAE,aAAa;CACpB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CAChC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;CACvF,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE;CAC3B,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CAChC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;CACzC,GAAG,IAAI,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CACpC,GAAG,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC;CACA,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,EAAE;CAChC,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC,CAAC;;CCjCF;AACA;CACA,MAAM,CAAC,GAAG,UAAU,CAAC;CACrB,MAAM,CAAC,GAAG,UAAU,CAAC;CACrB,MAAM,CAAC,GAAG,UAAU,CAAC;AACrB;CACA,MAAM,KAAK,GAAG,MAAM,CAAC;AACrB;AACA,kBAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,YAAY;CACjB,CAAC,KAAK,EAAE,aAAa;CACrB,CAAC,IAAI,EAAE,cAAc;CACrB,CAAC,QAAQ,EAAE,OAAO;AAClB;CACA,CAAC,IAAI,EAAE,aAAa;CACpB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CAChC;CACA;CACA;CACA;CACA,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;CACnB,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;CAClC,IAAI;CACJ,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC;CACvD,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB;CACA;CACA;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CAChC;CACA,GAAG,GAAG,IAAI,KAAK,CAAC;CAChB;CACA;CACA;CACA,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE;CACtB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;CAC9B,IAAI;CACJ,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;CACzC,GAAG,CAAC,CAAC;CACL,EAAE;CACF,CAAC,CAAC;;CC5CK,MAAM,IAAI,GAAG,EAAE,CAAC;AACvB;CACA,KAAK,CAAC,GAAG,CAAC,4BAA4B,EAAE,GAAG,IAAI;CAC/C,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;CACzB,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;CACpD,EAAE;CACF,CAAC,CAAC,CAAC;AACH;CACA,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,GAAG,IAAI;CAC7C,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;CACb,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;CACpD,EAAE;CACF,CAAC,CAAC,CAAC;AACH;CACO,SAAS,SAAS,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAE;CACvD;CACA,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;CACzB,CAAC;AACD;CACO,SAAS,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE;CAChD;CACA;CACA;CACA;CACA,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;AACvB;CACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;CAC1D,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAC1D;CACA;CACA,CAAC,IAAI,KAAK,GAAG;CACb,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO;CAC/B,EAAE,CAAC,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,OAAO;CAC/B,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC;CAC/B,EAAE,CAAC;CACH;AACA;CACA,CAAC,IAAI,aAAa,GAAG,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;CAC9D,CAAC,IAAI,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;CAClE;CACA,CAAC,OAAO,OAAO,CAAC;CAChB,CAAC;AACD;CACA,SAAS,CAAC;CACV,CAAC,EAAE,EAAE,WAAW;CAChB,CAAC,QAAQ,EAAE;CACX,EAAE,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC,SAAS,EAAE;CACxC,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;CACxC,EAAE,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;CACxC,EAAE;CACF,CAAC,UAAU,EAAE;CACb,EAAE,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,IAAI,mBAAmB,MAAM;CACxE,EAAE,EAAE,kBAAkB,GAAG,kBAAkB,GAAG,CAAC,uBAAuB,EAAE;CACxE,EAAE,EAAE,CAAC,oBAAoB,CAAC,qBAAqB,kBAAkB,OAAO;CACxE,EAAE;CACF,CAAC,CAAC,CAAC;AACH;CACA,SAAS,CAAC;CACV,CAAC,EAAE,EAAE,UAAU;CACf;CACA;CACA,CAAC,QAAQ,EAAE;CACX,EAAE,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC,SAAS,EAAE;CACxC,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;CACxC,EAAE,GAAG,SAAS,EAAE,CAAC,SAAS,GAAG,SAAS,EAAE;CACxC,EAAE;CACF;CACA,CAAC,UAAU,EAAE;CACb,EAAE,GAAG,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,GAAG;CACrE,EAAE,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,oBAAoB,EAAE;CACrE,EAAE,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,gBAAgB,MAAM;CACrE,EAAE;CACF,CAAC,CAAC,CAAC;AACH;CACA,SAAS,CAAC;CACV,CAAC,EAAE,EAAE,OAAO;CACZ;CACA,CAAC,QAAQ,EAAE;CACX,EAAE,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC,SAAS,EAAE;CACxC,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;CACxC,EAAE,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;CACxC,EAAE;CACF,CAAC,UAAU,EAAE;CACb,EAAE,GAAG,kBAAkB,IAAI,CAAC,mBAAmB,EAAE,mBAAmB,EAAE;CACtE,EAAE,GAAG,kBAAkB,KAAK,kBAAkB,GAAG,mBAAmB,EAAE;CACtE,EAAE,EAAE,CAAC,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,GAAG;CACtE,EAAE;CACF,CAAC,CAAC,CAAC;AACH;CACA,SAAS,CAAC;CACV,CAAC,EAAE,EAAE,OAAO;CACZ,CAAC,QAAQ,EAAE;CACX,EAAE,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC,QAAQ,EAAE;CACrC,EAAE,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE;CACrC,EAAE,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE;CACrC,EAAE;CACF;CACA,CAAC,UAAU,EAAE;CACb,EAAE,GAAG,iBAAiB,IAAI,CAAC,kBAAkB,GAAG,mBAAmB,GAAG;CACtE,EAAE,GAAG,kBAAkB,IAAI,kBAAkB,EAAE,CAAC,oBAAoB,EAAE;CACtE,EAAE,EAAE,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,IAAI;CACtE,EAAE;CACF,CAAC,CAAC,CAAC;AACH;CACA,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;CACtB;CACA;CACA;CACA,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC;CACA;CACA,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;AAClC;CACA;CACA;CACA;CACA,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CACjC,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC;CACA;CACA,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC;CACA;CACA,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CACjC,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CACjC,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CACjC,CAAC,CAAC;;CC9HF;CACA;CACA;CACA;CACA,MAAM,CAAC,IAAI,GAAG,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,GAAG,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,CAAC;AACpF;CACA;CACA,MAAM,OAAO,GAAG;CAChB,CAAC,GAAG,kBAAkB,IAAI,mBAAmB,GAAG,kBAAkB,GAAG;CACrE,CAAC,GAAG,mBAAmB,GAAG,kBAAkB,IAAI,mBAAmB,EAAE;CACrE,CAAC,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,kBAAkB,GAAG;CACrE,CAAC,CAAC;CACF,MAAM,SAAS,GAAG;CAClB,CAAC,GAAG,kBAAkB,IAAI,CAAC,gBAAgB,KAAK,CAAC,mBAAmB,GAAG;CACvE,CAAC,EAAE,CAAC,kBAAkB,KAAK,kBAAkB,IAAI,oBAAoB,EAAE;CACvE,CAAC,GAAG,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,IAAI;CACvE,CAAC,CAAC;AACF;AACA,cAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,QAAQ;CACb,CAAC,KAAK,EAAE,UAAU;CAClB,CAAC,IAAI,EAAE,QAAQ;AACf;CACA;CACA;CACA;CACA,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACpB,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACpB,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACpB,GAAG,IAAI,EAAE,MAAM;CACf,GAAG;CACH,EAAE;AACF;CACA,CAAC,QAAQ,EAAE,OAAO;AAClB;CACA,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI;AACnB;CACA,CAAC,OAAO;CACR,CAAC,SAAS;CACV,CAAC,CAAC,CAAC;AACH;CACA;;CCjDA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;AACnB;CACA;CACA;CACA,MAAM,gBAAgB,GAAG,CAAC,UAAU,CAAC;AACrC;CACA;CACA,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AACtD;AACA,cAAe,IAAI,aAAa,CAAC;CACjC,CAAC,EAAE,EAAE,QAAQ;CACb,CAAC,KAAK,EAAE,UAAU;CAClB,CAAC,IAAI,EAAE,QAAQ;CACf;CACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA,CAAC,MAAM,EAAE;CACT,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC;CACzC,GAAG,IAAI,EAAE,KAAK;CACd,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC;CACzC,GAAG,IAAI,EAAE,OAAO;CAChB,GAAG;CACH,EAAE,CAAC,EAAE;CACL,GAAG,KAAK,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC;CACzC,GAAG,IAAI,EAAE,MAAM;CACf,GAAG;CACH,EAAE;CACF,CAAC,QAAQ,EAAE,OAAO;AAClB;CACA,CAAC,IAAI,EAAE,MAAM;CACb;CACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;CACd,EAAE,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,EAAE,IAAI,KAAK,CAAC;AAClC;CACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CAChC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;CACnB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CACjD,IAAI;CACJ,QAAQ,IAAI,GAAG,GAAG,WAAW,EAAE;CAC/B,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC;CACvC,IAAI;CACJ,QAAQ;CACR,IAAI,OAAO,KAAK,CAAC;CACjB,IAAI;CACJ,GAAG,CAAC,CAAC;CACL,EAAE;AACF;CACA;CACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;CAChB,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;CAChC,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE;CACjB,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;CACzC,IAAI;CACJ,QAAQ,IAAI,GAAG,GAAG,CAAC,EAAE;CACrB,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;CACtD,IAAI;CACJ,QAAQ;CACR,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;CAC5C,IAAI;CACJ,GAAG,CAAC,CAAC;CACL,EAAE;CACF;CACA;CACA,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CClDF;CACA;CACA;CACe,MAAM,KAAK,CAAC;CAC3B;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,EAAE;CACvB,EAAE,IAAI,KAAK,CAAC;AACZ;CACA,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;CACzB,GAAG,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B,GAAG;AACH;CACA,EAAE,IAAI,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;AAC3B;CACA,EAAE,IAAI,KAAK,EAAE;CACb,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC;CACxC,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;CACzB,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;CACvB,GAAG;CACH,OAAO;CACP;CACA,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;CACjC,GAAG;AACH;CACA,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;CACvC,GAAG,KAAK,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC;CAC/B,GAAG,QAAQ,EAAE,KAAK;CAClB,GAAG,UAAU,EAAE,IAAI;CACnB,GAAG,YAAY,EAAE,IAAI;CACrB,GAAG,CAAC,CAAC;AACL;CACA,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpD;CACA;CACA,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AAC9E;CACA;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC/C,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;CACjC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;CACzB,IAAI;CACJ,GAAG;AACH;CACA;CACA,EAAE,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;CACpC,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,EAAE;CACnC,IAAI,GAAG,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;CAC3B,IAAI,GAAG,EAAE,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC;CACrC,IAAI,CAAC,CAAC;CACN,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,OAAO,CAAC,GAAG;CAChB,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;CACvB,EAAE;AACF;CACA,CAAC,KAAK,CAAC,GAAG;CACV,EAAE,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;CACxD,EAAE;AACF;CACA,CAAC,MAAM,CAAC,GAAG;CACX,EAAE,OAAO;CACT,GAAG,OAAO,EAAE,IAAI,CAAC,OAAO;CACxB,GAAG,MAAM,EAAE,IAAI,CAAC,MAAM;CACtB,GAAG,KAAK,EAAE,IAAI,CAAC,KAAK;CACpB,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,EAAE;CACnB,EAAE,IAAI,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACnC;CACA;CACA,EAAE,GAAG,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACnC;CACA,EAAE,OAAO,GAAG,CAAC;CACb,EAAE;AACF;CACA;CACA;CACA;CACA;CACA,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,EAAE;CAC7B,EAAE,IAAI,KAAK,YAAY,KAAK,EAAE;CAC9B,GAAG,OAAO,KAAK,CAAC;CAChB,GAAG;AACH;CACA,EAAE,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;CACnC,EAAE;AACF;CACA,CAAC,OAAO,cAAc,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE;CAC9C,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACrC;CACA,EAAE,IAAI,IAAI,GAAG,UAAU,GAAG,IAAI,EAAE;CAChC,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAC3B;CACA,GAAG,IAAI,OAAO,KAAK,OAAO,EAAE;CAC5B,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACzB,IAAI;CACJ,QAAQ,IAAI,OAAO,KAAK,iBAAiB,EAAE;CAC3C,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;CAChB,IAAI,GAAG,GAAG,UAAU,GAAG,IAAI,EAAE;CAC7B,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;CAC1B,KAAK,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CAC3B,KAAK,CAAC;CACN;CACA,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAC1B,IAAI;CACJ,QAAQ,IAAI,OAAO,KAAK,cAAc,EAAE;CACxC,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACrC,IAAI;AACJ;CACA,GAAG,OAAO,GAAG,CAAC;CACd,GAAG,CAAC;AACJ;CACA,EAAE,IAAI,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE;CACxB,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;CACtB,GAAG;AACH;CACA,EAAE,IAAI,QAAQ,EAAE;CAChB,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE;CAC9C,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;CAC/B,IAAI,CAAC;CACL,GAAG;CACH,EAAE;AACF;CACA,CAAC,OAAO,eAAe,CAAC,CAAC,CAAC,EAAE;CAC5B,EAAE,KAAK,IAAI,IAAI,IAAI,CAAC,EAAE;CACtB,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;CAChD,GAAG;CACH,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE;CACzB,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE;CACxB,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;CAC3B,GAAG;CACH,OAAO;CACP;CACA,GAAG,KAAK,IAAI,IAAI,IAAI,OAAO,EAAE;CAC7B,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;CAC9C,IAAI;CACJ,GAAG;CACH,EAAE;CACF,CAAC;AACD;CACA,KAAK,CAAC,eAAe,CAAC;CACtB,CAAC,GAAG;CACJ,CAAC,MAAM;CACP,CAAC,GAAG;CACJ,CAAC,MAAM;CACP,CAAC,EAAE;CACH,CAAC,MAAM;CACP,CAAC,OAAO;CACR,CAAC,OAAO;CACR,CAAC,QAAQ;CACT,CAAC,QAAQ,EAAE,SAAS;CACpB,CAAC,CAAC,CAAC;AACH;CACA,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;CACrB,CAAC,IAAI;CACL,CAAC,KAAK;CACN,CAAC,MAAM;CACP,CAAC,KAAK,EAAE,UAAU;CAClB,CAAC,MAAM,EAAE,UAAU,CAAC,QAAQ;CAC5B,CAAC,KAAK;AACN;CACA;CACA,CAAC,QAAQ;CACT,CAAC,CAAC;;CCnMF,KAAK,IAAI,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;CACrC,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;CAClC;;CCPA;CACA;CACA;CACA;AAIA;CACA;CACA,KAAK,IAAI,EAAE,IAAI,UAAU,CAAC,QAAQ,EAAE;CACpC,CAAC,iBAAiB,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;CAChD,CAAC;AACD;CACA;CACA,KAAK,CAAC,GAAG,CAAC,qBAAqB,EAAE,KAAK,IAAI;CAC1C,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;CACpC,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI;CACjC,EAAE,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CAClC,EAAE,CAAC,CAAC;CACJ,CAAC,CAAC,CAAC;AACH;CACA,SAAS,iBAAiB,EAAE,EAAE,EAAE,KAAK,EAAE;CACvC,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACpC;CACA,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE;CAChD;CACA;CACA;CACA,EAAE,GAAG,CAAC,GAAG;CACT,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC7B;CACA,GAAG,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;CACrC;CACA,IAAI,OAAO,GAAG,CAAC;CACf,IAAI;AACJ;CACA;CACA,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE;CACzB,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE,QAAQ,KAAK;CAC5B,KAAK,IAAI;CACT,MAAM,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;CACjD,MAAM,OAAO,IAAI,CAAC;CAClB,MAAM;CACN,KAAK,OAAO,CAAC,EAAE,EAAE;AACjB;CACA,KAAK,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;CACvC,KAAK;CACL,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,KAAK;CACtC,KAAK,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,EAAE;CACzE,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC/D;CACA,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE;CACtB,OAAO,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC;CACzB,OAAO;CACP,MAAM;AACN;CACA,KAAK,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;CACjD,KAAK;CACL,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,KAAK;CAC7C,KAAK,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE;CAC1F,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC/D;CACA,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE;CACtB,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC1B;CACA;CACA,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AAC5B;CACA,OAAO,OAAO,IAAI,CAAC;CACnB,OAAO;CACP,MAAM;AACN;CACA,KAAK,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;CACxD,KAAK;CACL,IAAI,CAAC,CAAC;CACN,GAAG;CACH;CACA;CACA;CACA,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE;CACf,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;CAC3B,GAAG;CACH,EAAE,YAAY,EAAE,IAAI;CACpB,EAAE,UAAU,EAAE,IAAI;CAClB,EAAE,CAAC,CAAC;CACJ;;CCrFA;AASA;CACA,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;CAC5B,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;CACvB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;CAItC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;CAGzB,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;CAGzB,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;CAG3B,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;CAGxB,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;CAG5B,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC;;;;;;;;"}