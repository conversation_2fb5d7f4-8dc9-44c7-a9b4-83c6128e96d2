{"version": 3, "file": "color.global.min.js", "sources": ["../src/multiply-matrices.js", "../src/util.js", "../src/hooks.js", "../src/defaults.js", "../src/adapt.js", "../src/parse.js", "../src/getColor.js", "../src/space.js", "../src/spaces/xyz-d65.js", "../src/rgbspace.js", "../src/getAll.js", "../src/get.js", "../src/setAll.js", "../src/set.js", "../src/spaces/xyz-d50.js", "../src/spaces/lab.js", "../src/angles.js", "../src/spaces/lch.js", "../src/deltaE/deltaE2000.js", "../src/spaces/oklab.js", "../src/deltaE/deltaEOK.js", "../src/inGamut.js", "../src/clone.js", "../src/distance.js", "../src/deltaE/deltaECMC.js", "../src/spaces/xyz-abs-d65.js", "../src/spaces/jzazbz.js", "../src/spaces/jzczhz.js", "../src/spaces/ictcp.js", "../src/spaces/cam16.js", "../src/spaces/hct.js", "../src/deltaE/deltaEHCT.js", "../src/deltaE/index.js", "../src/deltaE/deltaE76.js", "../src/deltaE/deltaEJz.js", "../src/deltaE/deltaEITP.js", "../src/toGamut.js", "../src/to.js", "../src/serialize.js", "../src/spaces/rec2020-linear.js", "../src/spaces/rec2020.js", "../src/spaces/p3-linear.js", "../src/spaces/srgb-linear.js", "../src/keywords.js", "../src/spaces/srgb.js", "../src/spaces/p3.js", "../src/display.js", "../src/luminance.js", "../src/contrast/APCA.js", "../src/spaces/lab-d65.js", "../src/contrast/deltaPhi.js", "../src/contrast/Lstar.js", "../src/contrast/Michelson.js", "../src/contrast/WCAG21.js", "../src/contrast/Weber.js", "../src/chromaticity.js", "../src/deltaE.js", "../src/variations.js", "../src/interpolation.js", "../src/spaces/hsl.js", "../src/spaces/hsv.js", "../src/spaces/hwb.js", "../src/spaces/a98rgb-linear.js", "../src/spaces/a98rgb.js", "../src/spaces/prophoto-linear.js", "../src/spaces/prophoto.js", "../src/spaces/oklch.js", "../src/spaces/luv.js", "../src/spaces/lchuv.js", "../src/spaces/hsluv.js", "../src/spaces/hpluv.js", "../src/spaces/rec2100-pq.js", "../src/spaces/rec2100-hlg.js", "../src/CATs.js", "../src/spaces/acescg.js", "../src/spaces/acescc.js", "../src/color.js", "../src/equals.js", "../src/spaces/index.js", "../src/space-accessors.js", "../src/index.js", "../src/contrast.js"], "sourcesContent": ["// A is m x n. B is n x p. product is m x p.\nexport default function multiplyMatrices (A, B) {\n\tlet m = A.length;\n\n\tif (!Array.isArray(A[0])) {\n\t\t// A is vector, convert to [[a, b, c, ...]]\n\t\tA = [A];\n\t}\n\n\tif (!Array.isArray(B[0])) {\n\t\t// B is vector, convert to [[a], [b], [c], ...]]\n\t\tB = B.map(x => [x]);\n\t}\n\n\tlet p = B[0].length;\n\tlet B_cols = B[0].map((_, i) => B.map(x => x[i])); // transpose B\n\tlet product = A.map(row => B_cols.map(col => {\n\t\tlet ret = 0;\n\n\t\tif (!Array.isArray(row)) {\n\t\t\tfor (let c of col) {\n\t\t\t\tret += row * c;\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tfor (let i = 0; i < row.length; i++) {\n\t\t\tret += row[i] * (col[i] || 0);\n\t\t}\n\n\t\treturn ret;\n\t}));\n\n\tif (m === 1) {\n\t\tproduct = product[0]; // Avoid [[a, b, c, ...]]\n\t}\n\n\tif (p === 1) {\n\t\treturn product.map(x => x[0]); // Avoid [[a], [b], [c], ...]]\n\t}\n\n\treturn product;\n}\n", "/**\n * Various utility functions\n */\n\nexport {default as multiplyMatrices} from \"./multiply-matrices.js\";\n\n/**\n * Check if a value is a string (including a String object)\n * @param {*} str - Value to check\n * @returns {boolean}\n */\nexport function isString (str) {\n\treturn type(str) === \"string\";\n}\n\n/**\n * Determine the internal JavaScript [[Class]] of an object.\n * @param {*} o - Value to check\n * @returns {string}\n */\nexport function type (o) {\n\tlet str = Object.prototype.toString.call(o);\n\n\treturn (str.match(/^\\[object\\s+(.*?)\\]$/)[1] || \"\").toLowerCase();\n}\n\nexport function serializeNumber (n, {precision, unit }) {\n\tif (isNone(n)) {\n\t\treturn \"none\";\n\t}\n\n\treturn toPrecision(n, precision) + (unit ?? \"\");\n}\n\n/**\n * Check if a value corresponds to a none argument\n * @param {*} n - Value to check\n * @returns {boolean}\n */\nexport function isNone (n) {\n\treturn Number.isNaN(n) || (n instanceof Number && n?.none);\n}\n\n/**\n * Replace none values with 0\n */\nexport function skipNone (n) {\n\treturn isNone(n) ? 0 : n;\n}\n\n/**\n * Round a number to a certain number of significant digits\n * @param {number} n - The number to round\n * @param {number} precision - Number of significant digits\n */\nexport function toPrecision (n, precision) {\n\tif (n === 0) {\n\t\treturn 0;\n\t}\n\tlet integer = ~~n;\n\tlet digits = 0;\n\tif (integer && precision) {\n\t\tdigits = ~~Math.log10(Math.abs(integer)) + 1;\n\t}\n\tconst multiplier = 10.0 ** (precision - digits);\n\treturn Math.floor(n * multiplier + 0.5) / multiplier;\n}\n\nconst angleFactor = {\n\tdeg: 1,\n\tgrad: 0.9,\n\trad: 180 / Math.PI,\n\tturn: 360,\n};\n\n/**\n* Parse a CSS function, regardless of its name and arguments\n* @param String str String to parse\n* @return {{name, args, rawArgs}}\n*/\nexport function parseFunction (str) {\n\tif (!str) {\n\t\treturn;\n\t}\n\n\tstr = str.trim();\n\n\tconst isFunctionRegex = /^([a-z]+)\\((.+?)\\)$/i;\n\tconst isNumberRegex = /^-?[\\d.]+$/;\n\tconst unitValueRegex = /%|deg|g?rad|turn$/;\n\tconst singleArgument = /\\/?\\s*(none|[-\\w.]+(?:%|deg|g?rad|turn)?)/g;\n\tlet parts = str.match(isFunctionRegex);\n\n\tif (parts) {\n\t\t// It is a function, parse args\n\t\tlet args = [];\n\t\tparts[2].replace(singleArgument, ($0, rawArg) => {\n\t\t\tlet match = rawArg.match(unitValueRegex);\n\t\t\tlet arg = rawArg;\n\n\t\t\tif (match) {\n\t\t\t\tlet unit = match[0];\n\t\t\t\t// Drop unit from value\n\t\t\t\tlet unitlessArg = arg.slice(0, -unit.length);\n\n\t\t\t\tif (unit === \"%\") {\n\t\t\t\t\t// Convert percentages to 0-1 numbers\n\t\t\t\t\targ = new Number(unitlessArg / 100);\n\t\t\t\t\targ.type = \"<percentage>\";\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// Multiply angle by appropriate factor for its unit\n\t\t\t\t\targ = new Number(unitlessArg * angleFactor[unit]);\n\t\t\t\t\targ.type = \"<angle>\";\n\t\t\t\t\targ.unit = unit;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (isNumberRegex.test(arg)) {\n\t\t\t\t// Convert numerical args to numbers\n\t\t\t\targ = new Number(arg);\n\t\t\t\targ.type = \"<number>\";\n\t\t\t}\n\t\t\telse if (arg === \"none\") {\n\t\t\t\targ = new Number(NaN);\n\t\t\t\targ.none = true;\n\t\t\t}\n\n\t\t\tif ($0.startsWith(\"/\")) {\n\t\t\t\t// It's alpha\n\t\t\t\targ = arg instanceof Number ? arg : new Number(arg);\n\t\t\t\targ.alpha = true;\n\t\t\t}\n\n\t\t\tif (typeof arg === \"object\" && arg instanceof Number) {\n\t\t\t\targ.raw = rawArg;\n\t\t\t}\n\n\t\t\targs.push(arg);\n\t\t});\n\n\t\treturn {\n\t\t\tname: parts[1].toLowerCase(),\n\t\t\trawName: parts[1],\n\t\t\trawArgs: parts[2],\n\t\t\t// An argument could be (as of css-color-4):\n\t\t\t// a number, percentage, degrees (hue), ident (in color())\n\t\t\targs,\n\t\t};\n\t}\n}\n\nexport function last (arr) {\n\treturn arr[arr.length - 1];\n}\n\nexport function interpolate (start, end, p) {\n\tif (isNaN(start)) {\n\t\treturn end;\n\t}\n\n\tif (isNaN(end)) {\n\t\treturn start;\n\t}\n\n\treturn start + (end - start) * p;\n}\n\nexport function interpolateInv (start, end, value) {\n\treturn (value - start) / (end - start);\n}\n\nexport function mapRange (from, to, value) {\n\treturn interpolate(to[0], to[1], interpolateInv(from[0], from[1], value));\n}\n\nexport function parseCoordGrammar (coordGrammars) {\n\treturn coordGrammars.map(coordGrammar => {\n\t\treturn coordGrammar.split(\"|\").map(type => {\n\t\t\ttype = type.trim();\n\t\t\tlet range = type.match(/^(<[a-z]+>)\\[(-?[.\\d]+),\\s*(-?[.\\d]+)\\]?$/);\n\n\t\t\tif (range) {\n\t\t\t\tlet ret = new String(range[1]);\n\t\t\t\tret.range = [+range[2], +range[3]];\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn type;\n\t\t});\n\t});\n}\n\n/**\n * Clamp value between the minimum and maximum\n * @param {number} min minimum value to return\n * @param {number} val the value to return if it is between min and max\n * @param {number} max maximum value to return\n * @returns number\n */\nexport function clamp (min, val, max) {\n\treturn Math.max(Math.min(max, val), min);\n}\n\n/**\n * Copy sign of one value to another.\n * @param {number} - to number to copy sign to\n * @param {number} - from number to copy sign from\n * @returns number\n */\nexport function copySign (to, from) {\n\treturn Math.sign(to) === Math.sign(from) ? to : -to;\n}\n\n/**\n * Perform pow on a signed number and copy sign to result\n * @param {number} - base the base number\n * @param {number} - exp the exponent\n * @returns number\n */\nexport function spow (base, exp) {\n\treturn copySign(Math.abs(base) ** exp, base);\n}\n\n/**\n * Perform a divide, but return zero if the numerator is zero\n * @param {number} n - the numerator\n * @param {number} d - the denominator\n * @returns number\n */\nexport function zdiv (n, d) {\n\treturn (d === 0) ? 0 : n / d;\n}\n\n/**\n * Perform a bisect on a sorted list and locate the insertion point for\n * a value in arr to maintain sorted order.\n * @param {number[]} arr - array of sorted numbers\n * @param {number} value - value to find insertion point for\n * @param {number} lo - used to specify a the low end of a subset of the list\n * @param {number} hi - used to specify a the high end of a subset of the list\n * @returns number\n */\nexport function bisectLeft (arr, value, lo = 0, hi = arr.length) {\n\twhile (lo < hi) {\n\t\tconst mid = (lo + hi) >> 1;\n\t\tif (arr[mid] < value) {\n\t\t\tlo = mid + 1;\n\t\t}\n\t\telse {\n\t\t\thi = mid;\n\t\t}\n\t}\n\treturn lo;\n}\n", "/**\n * A class for adding deep extensibility to any piece of JS code\n */\nexport class Hooks {\n\tadd (name, callback, first) {\n\t\tif (typeof arguments[0] != \"string\") {\n\t\t\t// Multiple hooks\n\t\t\tfor (var name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\tthis[name] = this[name] || [];\n\n\t\t\tif (callback) {\n\t\t\t\tthis[name][first ? \"unshift\" : \"push\"](callback);\n\t\t\t}\n\t\t}, this);\n\t}\n\n\trun (name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * The instance of {@link Hooks} used throughout Color.js\n */\nconst hooks = new Hooks();\n\nexport default hooks;\n", "// Global defaults one may want to configure\nexport default {\n\tgamut_mapping: \"css\",\n\tprecision: 5,\n\tdeltaE: \"76\", // Default deltaE method\n\tverbose: globalThis?.process?.env?.NODE_ENV?.toLowerCase() !== \"test\",\n\twarn: function warn (msg) {\n\t\tif (this.verbose) {\n\t\t\tglobalThis?.console?.warn?.(msg);\n\t\t}\n\t},\n};\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\n\nexport const WHITES = {\n\t// for compatibility, the four-digit chromaticity-derived ones everyone else uses\n\tD50: [0.3457 / 0.3585, 1.00000, (1.0 - 0.3457 - 0.3585) / 0.3585],\n\tD65: [0.3127 / 0.3290, 1.00000, (1.0 - 0.3127 - 0.3290) / 0.3290],\n};\n\nexport function getWhite (name) {\n\tif (Array.isArray(name)) {\n\t\treturn name;\n\t}\n\n\treturn WHITES[name];\n}\n\n// Adapt XYZ from white point W1 to W2\nexport default function adapt (W1, W2, XYZ, options = {}) {\n\tW1 = getWhite(W1);\n\tW2 = getWhite(W2);\n\n\tif (!W1 || !W2) {\n\t\tthrow new TypeError(`Missing white point to convert ${!W1 ? \"from\" : \"\"}${!W1 && !W2 ? \"/\" : \"\"}${!W2 ? \"to\" : \"\"}`);\n\t}\n\n\tif (W1 === W2) {\n\t\t// Same whitepoints, no conversion needed\n\t\treturn XYZ;\n\t}\n\n\tlet env = {W1, W2, XYZ, options};\n\n\thooks.run(\"chromatic-adaptation-start\", env);\n\n\tif (!env.M) {\n\t\tif (env.W1 === WHITES.D65 && env.W2 === WHITES.D50) {\n\t\t\tenv.M = [\n\t\t\t\t[ 1.0479297925449969, 0.022946870601609652, -0.05019226628920524 ],\n\t\t\t\t[ 0.02962780877005599, 0.9904344267538799, -0.017073799063418826 ],\n\t\t\t\t[ -0.009243040646204504, 0.015055191490298152, 0.7518742814281371 ],\n\t\t\t];\n\t\t}\n\t\telse if (env.W1 === WHITES.D50 && env.W2 === WHITES.D65) {\n\n\t\t\tenv.M = [\n\t\t\t\t[ 0.955473421488075, -0.02309845494876471, 0.06325924320057072 ],\n\t\t\t\t[ -0.0283697093338637, 1.0099953980813041, 0.021041441191917323 ],\n\t\t\t\t[ 0.012314014864481998, -0.020507649298898964, 1.330365926242124 ],\n\t\t\t];\n\t\t}\n\t}\n\n\thooks.run(\"chromatic-adaptation-end\", env);\n\n\tif (env.M) {\n\t\treturn multiplyMatrices(env.M, env.XYZ);\n\t}\n\telse {\n\t\tthrow new TypeError(\"Only Bradford CAT with white points D50 and D65 supported for now.\");\n\t}\n}\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\n\nconst noneTypes = new Set([\"<number>\", \"<percentage>\", \"<angle>\"]);\n\n/**\n * Validates the coordinates of a color against a format's coord grammar and\n * maps the coordinates to the range or refRange of the coordinates.\n * @param {ColorSpace} space - Colorspace the coords are in\n * @param {object} format - the format object to validate against\n * @param {string} name - the name of the color function. e.g. \"oklab\" or \"color\"\n * @returns {object[]} - an array of type metadata for each coordinate\n */\nfunction coerceCoords (space, format, name, coords) {\n\tlet types = Object.entries(space.coords).map(([id, coordMeta], i) => {\n\t\tlet coordGrammar = format.coordGrammar[i];\n\t\tlet arg = coords[i];\n\t\tlet providedType = arg?.type;\n\n\t\t// Find grammar alternative that matches the provided type\n\t\t// Non-strict equals is intentional because we are comparing w/ string objects\n\t\tlet type;\n\t\tif (arg.none) {\n\t\t\ttype = coordGrammar.find(c => noneTypes.has(c));\n\t\t}\n\t\telse {\n\t\t\ttype = coordGrammar.find(c => c == providedType);\n\t\t}\n\n\t\t// Check that each coord conforms to its grammar\n\t\tif (!type) {\n\t\t\t// Type does not exist in the grammar, throw\n\t\t\tlet coordName = coordMeta.name || id;\n\t\t\tthrow new TypeError(`${providedType ?? arg.raw} not allowed for ${coordName} in ${name}()`);\n\t\t}\n\n\t\tlet fromRange = type.range;\n\n\t\tif (providedType === \"<percentage>\") {\n\t\t\tfromRange ||= [0, 1];\n\t\t}\n\n\t\tlet toRange = coordMeta.range || coordMeta.refRange;\n\n\t\tif (fromRange && toRange) {\n\t\t\tcoords[i] = util.mapRange(fromRange, toRange, coords[i]);\n\t\t}\n\n\t\treturn type;\n\t});\n\n\treturn types;\n}\n\n\n/**\n * Convert a CSS Color string to a color object\n * @param {string} str\n * @param {object} [options]\n * @param {object} [options.meta] - Object for additional information about the parsing\n * @returns {Color}\n */\nexport default function parse (str, {meta} = {}) {\n\tlet env = {\"str\": String(str)?.trim()};\n\thooks.run(\"parse-start\", env);\n\n\tif (env.color) {\n\t\treturn env.color;\n\t}\n\n\tenv.parsed = util.parseFunction(env.str);\n\n\tif (env.parsed) {\n\t\t// Is a functional syntax\n\t\tlet name = env.parsed.name;\n\n\t\tif (name === \"color\") {\n\t\t\t// color() function\n\t\t\tlet id = env.parsed.args.shift();\n\t\t\t// Check against both <dashed-ident> and <ident> versions\n\t\t\tlet alternateId = id.startsWith(\"--\") ? id.substring(2) : `--${id}`;\n\t\t\tlet ids = [id, alternateId];\n\t\t\tlet alpha = env.parsed.rawArgs.indexOf(\"/\") > 0 ? env.parsed.args.pop() : 1;\n\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\tlet colorSpec = space.getFormat(\"color\");\n\n\t\t\t\tif (colorSpec) {\n\t\t\t\t\tif (ids.includes(colorSpec.id) || colorSpec.ids?.filter((specId) => ids.includes(specId)).length) {\n\t\t\t\t\t\t// From https://drafts.csswg.org/css-color-4/#color-function\n\t\t\t\t\t\t// If more <number>s or <percentage>s are provided than parameters that the colorspace takes, the excess <number>s at the end are ignored.\n\t\t\t\t\t\t// If less <number>s or <percentage>s are provided than parameters that the colorspace takes, the missing parameters default to 0. (This is particularly convenient for multichannel printers where the additional inks are spot colors or varnishes that most colors on the page won’t use.)\n\t\t\t\t\t\tconst coords = Object.keys(space.coords).map((_, i) => env.parsed.args[i] || 0);\n\n\t\t\t\t\t\tlet types;\n\n\t\t\t\t\t\tif (colorSpec.coordGrammar) {\n\t\t\t\t\t\t\ttypes = coerceCoords(space, colorSpec, \"color\", coords);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (meta) {\n\t\t\t\t\t\t\tObject.assign(meta, {formatId: \"color\", types});\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (colorSpec.id.startsWith(\"--\") && !id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a non-standard space and not currently supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use prefixed color(${colorSpec.id}) instead of color(${id}).`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (id.startsWith(\"--\") && !colorSpec.id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a standard space and supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use color(${colorSpec.id}) instead of prefixed color(${id}).`);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {spaceId: space.id, coords, alpha};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Not found\n\t\t\tlet didYouMean = \"\";\n\t\t\tlet registryId = id in ColorSpace.registry ? id : alternateId;\n\t\t\tif (registryId in ColorSpace.registry) {\n\t\t\t\t// Used color space id instead of color() id, these are often different\n\t\t\t\tlet cssId = ColorSpace.registry[registryId].formats?.color?.id;\n\n\t\t\t\tif (cssId) {\n\t\t\t\t\tdidYouMean = `Did you mean color(${cssId})?`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthrow new TypeError(`Cannot parse color(${id}). ` + (didYouMean || \"Missing a plugin?\"));\n\t\t}\n\t\telse {\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\t// color space specific function\n\t\t\t\tlet format = space.getFormat(name);\n\t\t\t\tif (format && format.type === \"function\") {\n\t\t\t\t\tlet alpha = 1;\n\n\t\t\t\t\tif (format.lastAlpha || util.last(env.parsed.args).alpha) {\n\t\t\t\t\t\talpha = env.parsed.args.pop();\n\t\t\t\t\t}\n\n\t\t\t\t\tlet coords = env.parsed.args;\n\n\t\t\t\t\tlet types;\n\n\t\t\t\t\tif (format.coordGrammar) {\n\t\t\t\t\t\ttypes = coerceCoords(space, format, name, coords);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tObject.assign(meta, {formatId: format.name, types});\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tspaceId: space.id,\n\t\t\t\t\t\tcoords, alpha,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\telse {\n\t\t// Custom, colorspace-specific format\n\t\tfor (let space of ColorSpace.all) {\n\t\t\tfor (let formatId in space.formats) {\n\t\t\t\tlet format = space.formats[formatId];\n\n\t\t\t\tif (format.type !== \"custom\") {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (format.test && !format.test(env.str)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tlet color = format.parse(env.str);\n\n\t\t\t\tif (color) {\n\t\t\t\t\tcolor.alpha ??= 1;\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tmeta.formatId = formatId;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t// If we're here, we couldn't parse\n\tthrow new TypeError(`Could not parse ${str} as a color. Missing a plugin?`);\n}\n", "import ColorSpace from \"./space.js\";\nimport {isString} from \"./util.js\";\nimport parse from \"./parse.js\";\n\n/**\n * Resolves a color reference (object or string) to a plain color object\n * @param {Color | {space, coords, alpha} | string | Array<Color | {space, coords, alpha} | string> } color\n * @returns {{space, coords, alpha} | Array<{space, coords, alpha}}>\n */\nexport default function getColor (color) {\n\tif (Array.isArray(color)) {\n\t\treturn color.map(getColor);\n\t}\n\n\tif (!color) {\n\t\tthrow new TypeError(\"Empty color reference\");\n\t}\n\n\tif (isString(color)) {\n\t\tcolor = parse(color);\n\t}\n\n\t// Object fixup\n\tlet space = color.space || color.spaceId;\n\n\tif (!(space instanceof ColorSpace)) {\n\t\t// Convert string id to color space object\n\t\tcolor.space = ColorSpace.get(space);\n\t}\n\n\tif (color.alpha === undefined) {\n\t\tcolor.alpha = 1;\n\t}\n\n\treturn color;\n}\n", "import {type, parseCoordGrammar, serialize<PERSON><PERSON>ber, mapRange} from \"./util.js\";\nimport {getWhite} from \"./adapt.js\";\nimport hooks from \"./hooks.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Class to represent a color space\n */\nexport default class ColorSpace {\n\tconstructor (options) {\n\t\tthis.id = options.id;\n\t\tthis.name = options.name;\n\t\tthis.base = options.base ? ColorSpace.get(options.base) : null;\n\t\tthis.aliases = options.aliases;\n\n\t\tif (this.base) {\n\t\t\tthis.fromBase = options.fromBase;\n\t\t\tthis.toBase = options.toBase;\n\t\t}\n\n\t\t// Coordinate metadata\n\n\t\tlet coords = options.coords ?? this.base.coords;\n\n\t\tfor (let name in coords) {\n\t\t\tif (!(\"name\" in coords[name])) {\n\t\t\t\tcoords[name].name = name;\n\t\t\t}\n\t\t}\n\t\tthis.coords = coords;\n\n\t\t// White point\n\n\t\tlet white = options.white ?? this.base.white ?? \"D65\";\n\t\tthis.white = getWhite(white);\n\n\t\t// Sort out formats\n\n\t\tthis.formats = options.formats ?? {};\n\n\t\tfor (let name in this.formats) {\n\t\t\tlet format = this.formats[name];\n\t\t\tformat.type ||= \"function\";\n\t\t\tformat.name ||= name;\n\t\t}\n\n\t\tif (!this.formats.color?.id) {\n\t\t\tthis.formats.color = {\n\t\t\t\t...this.formats.color ?? {},\n\t\t\t\tid: options.cssId || this.id,\n\t\t\t};\n\t\t}\n\n\t\t// Gamut space\n\n\t\tif (options.gamutSpace) {\n\t\t\t// Gamut space explicitly specified\n\t\t\tthis.gamutSpace = options.gamutSpace === \"self\" ? this : ColorSpace.get(options.gamutSpace);\n\t\t}\n\t\telse {\n\t\t\t// No gamut space specified, calculate a sensible default\n\t\t\tif (this.isPolar) {\n\t\t\t\t// Do not check gamut through polar coordinates\n\t\t\t\tthis.gamutSpace = this.base;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.gamutSpace =  this;\n\t\t\t}\n\t\t}\n\n\t\t// Optimize inGamut for unbounded spaces\n\t\tif (this.gamutSpace.isUnbounded) {\n\t\t\tthis.inGamut = (coords, options) => {\n\t\t\t\treturn true;\n\t\t\t};\n\t\t}\n\n\t\t// Other stuff\n\t\tthis.referred = options.referred;\n\n\t\t// Compute ancestors and store them, since they will never change\n\t\tObject.defineProperty(this, \"path\", {\n\t\t\tvalue: getPath(this).reverse(),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t});\n\n\t\thooks.run(\"colorspace-init-end\", this);\n\t}\n\n\tinGamut (coords, {epsilon = ε} = {}) {\n\t\tif (!this.equals(this.gamutSpace)) {\n\t\t\tcoords = this.to(this.gamutSpace, coords);\n\t\t\treturn this.gamutSpace.inGamut(coords, {epsilon});\n\t\t}\n\n\t\tlet coordMeta = Object.values(this.coords);\n\n\t\treturn coords.every((c, i) => {\n\t\t\tlet meta = coordMeta[i];\n\n\t\t\tif (meta.type !== \"angle\" && meta.range) {\n\t\t\t\tif (Number.isNaN(c)) {\n\t\t\t\t\t// NaN is always in gamut\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tlet [min, max] = meta.range;\n\t\t\t\treturn (min === undefined || c >= min - epsilon)\n\t\t\t\t    && (max === undefined || c <= max + epsilon);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t});\n\t}\n\n\tget isUnbounded () {\n\t\treturn Object.values(this.coords).every(coord => !(\"range\" in coord));\n\t}\n\n\tget cssId () {\n\t\treturn this.formats?.color?.id || this.id;\n\t}\n\n\tget isPolar () {\n\t\tfor (let id in this.coords) {\n\t\t\tif (this.coords[id].type === \"angle\") {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tgetFormat (format) {\n\t\tif (typeof format === \"object\") {\n\t\t\tformat = processFormat(format, this);\n\t\t\treturn format;\n\t\t}\n\n\t\tlet ret;\n\t\tif (format === \"default\") {\n\t\t\t// Get first format\n\t\t\tret = Object.values(this.formats)[0];\n\t\t}\n\t\telse {\n\t\t\tret = this.formats[format];\n\t\t}\n\n\t\tif (ret) {\n\t\t\tret = processFormat(ret, this);\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Check if this color space is the same as another color space reference.\n\t * Allows proxying color space objects and comparing color spaces with ids.\n\t * @param {string | ColorSpace} space ColorSpace object or id to compare to\n\t * @returns {boolean}\n\t */\n\tequals (space) {\n\t\tif (!space) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this === space || this.id === space || this.id === space.id;\n\t}\n\n\tto (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (this.equals(space)) {\n\t\t\t// Same space, no change needed\n\t\t\treturn coords;\n\t\t}\n\n\t\t// Convert NaN to 0, which seems to be valid in every coordinate of every color space\n\t\tcoords = coords.map(c => Number.isNaN(c) ? 0 : c);\n\n\t\t// Find connection space = lowest common ancestor in the base tree\n\t\tlet myPath = this.path;\n\t\tlet otherPath = space.path;\n\n\t\tlet connectionSpace, connectionSpaceIndex;\n\n\t\tfor (let i = 0; i < myPath.length; i++) {\n\t\t\tif (myPath[i].equals(otherPath[i])) {\n\t\t\t\tconnectionSpace = myPath[i];\n\t\t\t\tconnectionSpaceIndex = i;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (!connectionSpace) {\n\t\t\t// This should never happen\n\t\t\tthrow new Error(`Cannot convert between color spaces ${this} and ${space}: no connection space was found`);\n\t\t}\n\n\t\t// Go up from current space to connection space\n\t\tfor (let i = myPath.length - 1; i > connectionSpaceIndex; i--) {\n\t\t\tcoords = myPath[i].toBase(coords);\n\t\t}\n\n\t\t// Go down from connection space to target space\n\t\tfor (let i = connectionSpaceIndex + 1; i < otherPath.length; i++) {\n\t\t\tcoords = otherPath[i].fromBase(coords);\n\t\t}\n\n\t\treturn coords;\n\t}\n\n\tfrom (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\treturn space.to(this, coords);\n\t}\n\n\ttoString () {\n\t\treturn `${this.name} (${this.id})`;\n\t}\n\n\tgetMinCoords () {\n\t\tlet ret = [];\n\n\t\tfor (let id in this.coords) {\n\t\t\tlet meta = this.coords[id];\n\t\t\tlet range = meta.range || meta.refRange;\n\t\t\tret.push(range?.min ?? 0);\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tstatic registry = {};\n\n\t// Returns array of unique color spaces\n\tstatic get all () {\n\t\treturn [...new Set(Object.values(ColorSpace.registry))];\n\t}\n\n\tstatic register (id, space) {\n\t\tif (arguments.length === 1) {\n\t\t\tspace = arguments[0];\n\t\t\tid = space.id;\n\t\t}\n\n\t\tspace = this.get(space);\n\n\t\tif (this.registry[id] && this.registry[id] !== space) {\n\t\t\tthrow new Error(`Duplicate color space registration: '${id}'`);\n\t\t}\n\t\tthis.registry[id] = space;\n\n\t\t// Register aliases when called without an explicit ID.\n\t\tif (arguments.length === 1 && space.aliases) {\n\t\t\tfor (let alias of space.aliases) {\n\t\t\t\tthis.register(alias, space);\n\t\t\t}\n\t\t}\n\n\t\treturn space;\n\t}\n\n\t/**\n\t * Lookup ColorSpace object by name\n\t * @param {ColorSpace | string} name\n\t */\n\tstatic get (space, ...alternatives) {\n\t\tif (!space || space instanceof ColorSpace) {\n\t\t\treturn space;\n\t\t}\n\n\t\tlet argType = type(space);\n\n\t\tif (argType === \"string\") {\n\t\t\t// It's a color space id\n\t\t\tlet ret = ColorSpace.registry[space.toLowerCase()];\n\n\t\t\tif (!ret) {\n\t\t\t\tthrow new TypeError(`No color space found with id = \"${space}\"`);\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tif (alternatives.length) {\n\t\t\treturn ColorSpace.get(...alternatives);\n\t\t}\n\n\t\tthrow new TypeError(`${space} is not a valid color space`);\n\t}\n\n\t/**\n\t * Get metadata about a coordinate of a color space\n\t *\n\t * @static\n\t * @param {Array | string} ref\n\t * @param {ColorSpace | string} [workingSpace]\n\t * @return {Object}\n\t */\n\tstatic resolveCoord (ref, workingSpace) {\n\t\tlet coordType = type(ref);\n\t\tlet space, coord;\n\n\t\tif (coordType === \"string\") {\n\t\t\tif (ref.includes(\".\")) {\n\t\t\t\t// Absolute coordinate\n\t\t\t\t[space, coord] = ref.split(\".\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Relative coordinate\n\t\t\t\t[space, coord] = [, ref];\n\t\t\t}\n\t\t}\n\t\telse if (Array.isArray(ref)) {\n\t\t\t[space, coord] = ref;\n\t\t}\n\t\telse {\n\t\t\t// Object\n\t\t\tspace = ref.space;\n\t\t\tcoord = ref.coordId;\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (!space) {\n\t\t\tspace = workingSpace;\n\t\t}\n\n\t\tif (!space) {\n\t\t\tthrow new TypeError(`Cannot resolve coordinate reference ${ref}: No color space specified and relative references are not allowed here`);\n\t\t}\n\n\t\tcoordType = type(coord);\n\n\t\tif (coordType === \"number\" || coordType === \"string\" && coord >= 0) {\n\t\t\t// Resolve numerical coord\n\t\t\tlet meta = Object.entries(space.coords)[coord];\n\n\t\t\tif (meta) {\n\t\t\t\treturn {space, id: meta[0], index: coord, ...meta[1]};\n\t\t\t}\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tlet normalizedCoord = coord.toLowerCase();\n\n\t\tlet i = 0;\n\t\tfor (let id in space.coords) {\n\t\t\tlet meta = space.coords[id];\n\n\t\t\tif (id.toLowerCase() === normalizedCoord || meta.name?.toLowerCase() === normalizedCoord) {\n\t\t\t\treturn {space, id, index: i, ...meta};\n\t\t\t}\n\n\t\t\ti++;\n\t\t}\n\n\t\tthrow new TypeError(`No \"${coord}\" coordinate found in ${space.name}. Its coordinates are: ${Object.keys(space.coords).join(\", \")}`);\n\t}\n\n\tstatic DEFAULT_FORMAT = {\n\t\ttype: \"functions\",\n\t\tname: \"color\",\n\t};\n}\n\nfunction getPath (space) {\n\tlet ret = [space];\n\n\tfor (let s = space; s = s.base;) {\n\t\tret.push(s);\n\t}\n\n\treturn ret;\n}\n\nfunction processFormat (format, {coords} = {}) {\n\tif (format.coords && !format.coordGrammar) {\n\t\tformat.type ||= \"function\";\n\t\tformat.name ||= \"color\";\n\n\t\t// Format has not been processed\n\t\tformat.coordGrammar = parseCoordGrammar(format.coords);\n\n\t\tlet coordFormats = Object.entries(coords).map(([id, coordMeta], i) => {\n\t\t\t// Preferred format for each coord is the first one\n\t\t\tlet outputType = format.coordGrammar[i][0];\n\n\t\t\tlet fromRange = coordMeta.range || coordMeta.refRange;\n\t\t\tlet toRange = outputType.range, suffix = \"\";\n\n\t\t\t// Non-strict equals intentional since outputType could be a string object\n\t\t\tif (outputType == \"<percentage>\") {\n\t\t\t\ttoRange = [0, 100];\n\t\t\t\tsuffix = \"%\";\n\t\t\t}\n\t\t\telse if (outputType == \"<angle>\") {\n\t\t\t\tsuffix = \"deg\";\n\t\t\t}\n\n\t\t\treturn  {fromRange, toRange, suffix};\n\t\t});\n\n\t\tformat.serializeCoords = (coords, precision) => {\n\t\t\treturn coords.map((c, i) => {\n\t\t\t\tlet {fromRange, toRange, suffix} = coordFormats[i];\n\n\t\t\t\tif (fromRange && toRange) {\n\t\t\t\t\tc = mapRange(fromRange, toRange, c);\n\t\t\t\t}\n\n\t\t\t\tc = serializeNumber(c, {precision, unit: suffix});\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t};\n\t}\n\n\treturn format;\n}\n", "import ColorSpace from \"../space.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d65\",\n\tname: \"XYZ D65\",\n\tcoords: {\n\t\tx: {name: \"X\"},\n\t\ty: {name: \"Y\"},\n\t\tz: {name: \"Z\"},\n\t},\n\twhite: \"D65\",\n\tformats: {\n\t\tcolor: {\n\t\t\tids: [\"xyz-d65\", \"xyz\"],\n\t\t},\n\t},\n\taliases: [\"xyz\"],\n});\n", "import ColorSpace from \"./space.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport adapt from \"./adapt.js\";\nimport XYZ_D65 from \"./spaces/xyz-d65.js\";\n\n/**\n * Convenience class for RGB color spaces\n * @extends {ColorSpace}\n */\nexport default class RGBColorSpace extends ColorSpace {\n\t/**\n\t * Creates a new RGB ColorSpace.\n\t * If coords are not specified, they will use the default RGB coords.\n\t * Instead of `fromBase()` and `toBase()` functions,\n\t * you can specify to/from XYZ matrices and have `toBase()` and `fromBase()` automatically generated.\n\t * @param {*} options - Same options as {@link ColorSpace} plus:\n\t * @param {number[][]} options.toXYZ_M - Matrix to convert to XYZ\n\t * @param {number[][]} options.fromXYZ_M - Matrix to convert from XYZ\n\t */\n\tconstructor (options) {\n\t\tif (!options.coords) {\n\t\t\toptions.coords = {\n\t\t\t\tr: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"Red\",\n\t\t\t\t},\n\t\t\t\tg: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t\tb: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif (!options.base) {\n\t\t\toptions.base = XYZ_D65;\n\t\t}\n\n\t\tif (options.toXYZ_M && options.fromXYZ_M) {\n\t\t\toptions.toBase ??= rgb => {\n\t\t\t\tlet xyz = multiplyMatrices(options.toXYZ_M, rgb);\n\n\t\t\t\tif (this.white !== this.base.white) {\n\t\t\t\t\t// Perform chromatic adaptation\n\t\t\t\t\txyz = adapt(this.white, this.base.white, xyz);\n\t\t\t\t}\n\n\t\t\t\treturn xyz;\n\t\t\t};\n\n\t\t\toptions.fromBase ??= xyz => {\n\t\t\t\txyz = adapt(this.base.white, this.white, xyz);\n\t\t\t\treturn multiplyMatrices(options.fromXYZ_M, xyz);\n\t\t\t};\n\t\t}\n\n\t\toptions.referred ??= \"display\";\n\n\t\tsuper(options);\n\t}\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\n/**\n * Get the coordinates of a color in any color space\n * @param {Color} color\n * @param {string | ColorSpace} [space = color.space] The color space to convert to. Defaults to the color's current space\n * @returns {number[]} The color coordinates in the given color space\n */\nexport default function getAll (color, space) {\n\tcolor = getColor(color);\n\n\tif (!space || color.space.equals(space)) {\n\t\t// No conversion needed\n\t\treturn color.coords.slice();\n\t}\n\n\tspace = ColorSpace.get(space);\n\treturn space.from(color);\n}\n", "import ColorSpace from \"./space.js\";\nimport getAll from \"./getAll.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function get (color, prop) {\n\tcolor = getColor(color);\n\n\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\tlet coords = getAll(color, space);\n\treturn coords[index];\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function setAll (color, space, coords) {\n\tcolor = getColor(color);\n\n\tspace = ColorSpace.get(space);\n\tcolor.coords = space.to(color.space, coords);\n\treturn color;\n}\n\nsetAll.returns = \"color\";\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\nimport get from \"./get.js\";\nimport getAll from \"./getAll.js\";\nimport setAll from \"./setAll.js\";\nimport {type} from \"./util.js\";\n\n// Set properties and return current instance\nexport default function set (color, prop, value) {\n\tcolor = getColor(color);\n\n\tif (arguments.length === 2 && type(arguments[1]) === \"object\") {\n\t\t// Argument is an object literal\n\t\tlet object = arguments[1];\n\t\tfor (let p in object) {\n\t\t\tset(color, p, object[p]);\n\t\t}\n\t}\n\telse {\n\t\tif (typeof value === \"function\") {\n\t\t\tvalue = value(get(color, prop));\n\t\t}\n\n\t\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\t\tlet coords = getAll(color, space);\n\t\tcoords[index] = value;\n\t\tsetAll(color, space, coords);\n\t}\n\n\treturn color;\n}\n\nset.returns = \"color\";\n", "import ColorSpace from \"../space.js\";\nimport adapt from \"../adapt.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d50\",\n\tname: \"XYZ D50\",\n\twhite: \"D50\",\n\tbase: XYZ_D65,\n\tfromBase: coords => adapt(XYZ_D65.white, \"D50\", coords),\n\ttoBase: coords => adapt(\"D50\", XYZ_D65.white, coords),\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d50 from \"./xyz-d50.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D50;\n\nexport default new ColorSpace({\n\tid: \"lab\",\n\tname: \"Lab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D50, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d50,\n\t// Convert D50-adapted XYX to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D50-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "export function constrain (angle) {\n\treturn ((angle % 360) + 360) % 360;\n}\n\nexport function adjust (arc, angles) {\n\tif (arc === \"raw\") {\n\t\treturn angles;\n\t}\n\n\tlet [a1, a2] = angles.map(constrain);\n\n\tlet angleDiff = a2 - a1;\n\n\tif (arc === \"increasing\") {\n\t\tif (angleDiff < 0) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\telse if (arc === \"decreasing\") {\n\t\tif (angleDiff > 0) {\n\t\t\ta1 += 360;\n\t\t}\n\t}\n\telse if (arc === \"longer\") {\n\t\tif (-180 < angleDiff && angleDiff < 180) {\n\t\t\tif (angleDiff > 0) {\n\t\t\t\ta1 += 360;\n\t\t\t}\n\t\t\telse {\n\t\t\t\ta2 += 360;\n\t\t\t}\n\t\t}\n\t}\n\telse if (arc === \"shorter\") {\n\t\tif (angleDiff > 180) {\n\t\t\ta1 += 360;\n\t\t}\n\t\telse if (angleDiff < -180) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\n\treturn [a1, a2];\n}\n", "import ColorSpace from \"../space.js\";\nimport Lab from \"./lab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lch\",\n\tname: \"<PERSON><PERSON>\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 150],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Lab,\n\tfromBase (Lab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = Lab;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // a\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // b\n\t\t];\n\t},\n\n\tformats: {\n\t\t\"lch\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import defaults from \"../defaults.js\";\nimport lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// deltaE2000 is a statistically significant improvement\n// and is recommended by the CIE and Idealliance\n// especially for color differences less than 10 deltaE76\n// but is wicked complicated\n// and many implementations have small errors!\n// DeltaE2000 is also discontinuous; in case this\n// matters to you, use deltaECMC instead.\n\nconst Gfactor = 25 ** 7;\nconst π = Math.PI;\nconst r2d = 180 / π;\nconst d2r = π / 180;\n\nfunction pow7 (x) {\n\t// Faster than x ** 7 or Math.pow(x, 7)\n\n\tconst x2 = x * x;\n\tconst x7 = x2 * x2 * x2 * x;\n\n\treturn x7;\n}\n\nexport default function (color, sample, {kL = 1, kC = 1, kH = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and the function parameter as the sample,\n\t// calculate deltaE 2000.\n\n\t// This implementation assumes the parametric\n\t// weighting factors kL, kC and kH\n\t// for the influence of viewing conditions\n\t// are all 1, as sadly seems typical.\n\t// kL should be increased for lightness texture or noise\n\t// and kC increased for chroma noise\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet C1 = lch.from(lab, [L1, a1, b1])[1];\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\tlet Cbar = (C1 + C2) / 2; // mean Chroma\n\n\t// calculate a-axis asymmetry factor from mean Chroma\n\t// this turns JND ellipses for near-neutral colors back into circles\n\tlet C7 = pow7(Cbar);\n\n\tlet G = 0.5 * (1 - Math.sqrt(C7 / (C7 + Gfactor)));\n\n\t// scale a axes by asymmetry factor\n\t// this by the way is why there is no Lab2000 colorspace\n\tlet adash1 = (1 + G) * a1;\n\tlet adash2 = (1 + G) * a2;\n\n\t// calculate new Chroma from scaled a and original b axes\n\tlet Cdash1 = Math.sqrt(adash1 ** 2 + b1 ** 2);\n\tlet Cdash2 = Math.sqrt(adash2 ** 2 + b2 ** 2);\n\n\t// calculate new hues, with zero hue for true neutrals\n\t// and in degrees, not radians\n\n\tlet h1 = (adash1 === 0 && b1 === 0) ? 0 : Math.atan2(b1, adash1);\n\tlet h2 = (adash2 === 0 && b2 === 0) ? 0 : Math.atan2(b2, adash2);\n\n\tif (h1 < 0) {\n\t\th1 += 2 * π;\n\t}\n\tif (h2 < 0) {\n\t\th2 += 2 * π;\n\t}\n\n\th1 *= r2d;\n\th2 *= r2d;\n\n\t// Lightness and Chroma differences; sign matters\n\tlet ΔL = L2 - L1;\n\tlet ΔC = Cdash2 - Cdash1;\n\n\t// Hue difference, getting the sign correct\n\tlet hdiff = h2 - h1;\n\tlet hsum = h1 + h2;\n\tlet habs = Math.abs(hdiff);\n\tlet Δh;\n\n\tif (Cdash1 * Cdash2 === 0) {\n\t\tΔh = 0;\n\t}\n\telse if (habs <= 180) {\n\t\tΔh = hdiff;\n\t}\n\telse if (hdiff > 180) {\n\t\tΔh = hdiff - 360;\n\t}\n\telse if (hdiff < -180) {\n\t\tΔh = hdiff + 360;\n\t}\n\telse {\n\t\tdefaults.warn(\"the unthinkable has happened\");\n\t}\n\n\t// weighted Hue difference, more for larger Chroma\n\tlet ΔH = 2 * Math.sqrt(Cdash2 * Cdash1) * Math.sin(Δh * d2r / 2);\n\n\t// calculate mean Lightness and Chroma\n\tlet Ldash = (L1 + L2) / 2;\n\tlet Cdash = (Cdash1 + Cdash2) / 2;\n\tlet Cdash7 = pow7(Cdash);\n\n\t// Compensate for non-linearity in the blue region of Lab.\n\t// Four possibilities for hue weighting factor,\n\t// depending on the angles, to get the correct sign\n\tlet hdash;\n\tif (Cdash1 * Cdash2 === 0) {\n\t\thdash = hsum;   // which should be zero\n\t}\n\telse if (habs <= 180) {\n\t\thdash = hsum / 2;\n\t}\n\telse if (hsum < 360) {\n\t\thdash = (hsum + 360) / 2;\n\t}\n\telse {\n\t\thdash = (hsum - 360) / 2;\n\t}\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor\n\t// a background with L=50 is assumed\n\tlet lsq = (Ldash - 50) ** 2;\n\tlet SL = 1 + ((0.015 * lsq) / Math.sqrt(20 + lsq));\n\n\t// SC Chroma factor, similar to those in CMC and deltaE 94 formulae\n\tlet SC = 1 + 0.045 * Cdash;\n\n\t// Cross term T for blue non-linearity\n\tlet T = 1;\n\tT -= (0.17 * Math.cos((     hdash - 30)  * d2r));\n\tT += (0.24 * Math.cos(  2 * hdash        * d2r));\n\tT += (0.32 * Math.cos(((3 * hdash) + 6)  * d2r));\n\tT -= (0.20 * Math.cos(((4 * hdash) - 63) * d2r));\n\n\t// SH Hue factor depends on Chroma,\n\t// as well as adjusted hue angle like deltaE94.\n\tlet SH = 1 + 0.015 * Cdash * T;\n\n\t// RT Hue rotation term compensates for rotation of JND ellipses\n\t// and Munsell constant hue lines\n\t// in the medium-high Chroma blue region\n\t// (Hue 225 to 315)\n\tlet Δθ = 30 * Math.exp(-1 * (((hdash - 275) / 25) ** 2));\n\tlet RC = 2 * Math.sqrt(Cdash7 / (Cdash7 + Gfactor));\n\tlet RT = -1 * Math.sin(2 * Δθ * d2r) * RC;\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (kL * SL)) ** 2;\n\tdE += (ΔC / (kC * SC)) ** 2;\n\tdE += (ΔH / (kH * SH)) ** 2;\n\tdE += RT * (ΔC / (kC * SC)) * (ΔH / (kH * SH));\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\n// Recalculated for consistent reference white\n// see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484\nconst XY<PERSON>toLMS_M = [\n\t[ 0.8190224379967030, 0.3619062600528904, -0.1288737815209879 ],\n\t[ 0.0329836539323885, 0.9292868615863434,  0.0361446663506424 ],\n\t[ 0.0481771893596242, 0.2642395317527308,  0.6335478284694309 ],\n];\n// inverse of XYZtoLMS_M\nconst LMStoXYZ_M = [\n\t[  1.2268798758459243, -0.5578149944602171,  0.2813910456659647 ],\n\t[ -0.0405757452148008,  1.1122868032803170, -0.0717110580655164 ],\n\t[ -0.0763729366746601, -0.4214933324022432,  1.5869240198367816 ],\n];\nconst LMStoLab_M = [\n\t[ 0.2104542683093140,  0.7936177747023054, -0.0040720430116193 ],\n\t[ 1.9779985324311684, -2.4285922420485799,  0.4505937096174110 ],\n\t[ 0.0259040424655478,  0.7827717124575296, -0.8086757549230774 ],\n];\n// LMStoIab_M inverted\nconst LabtoLMS_M = [\n\t[ 1.0000000000000000,  0.3963377773761749,  0.2158037573099136 ],\n\t[ 1.0000000000000000, -0.1055613458156586, -0.0638541728258133 ],\n\t[ 1.0000000000000000, -0.0894841775298119, -1.2914855480194092 ],\n];\n\nexport default new ColorSpace({\n\tid: \"oklab\",\n\tname: \"Oklab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t},\n\n\t// Note that XYZ is relative to D65\n\twhite: \"D65\",\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\t// non-linearity\n\t\tlet LMSg = LMS.map(val => Math.cbrt(val));\n\n\t\treturn multiplyMatrices(LMStoLab_M, LMSg);\n\n\t},\n\ttoBase (OKLab) {\n\t\t// move to LMS cone domain\n\t\tlet LMSg = multiplyMatrices(LabtoLMS_M, OKLab);\n\n\t\t// restore linearity\n\t\tlet LMS = LMSg.map(val => val ** 3);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n\n\tformats: {\n\t\t\"oklab\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in CIE Lab\n\nimport oklab from \"../spaces/oklab.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaEOK, term by term as root sum of squares\n\tlet [L1, a1, b1] = oklab.from(color);\n\tlet [L2, a2, b2] = oklab.from(sample);\n\tlet ΔL = L1 - L2;\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\treturn Math.sqrt(ΔL ** 2 + Δa ** 2 + Δb ** 2);\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Check if a color is in gamut of either its own or another color space\n * @return {Boolean} Is the color in gamut?\n */\nexport default function inGamut (color, space, {epsilon = ε} = {}) {\n\tcolor = getColor(color);\n\n\tif (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tlet coords = color.coords;\n\n\tif (space !== color.space) {\n\t\tcoords = space.from(color);\n\t}\n\n\treturn space.inGamut(coords, {epsilon});\n}\n", "export default function clone (color) {\n\treturn {\n\t\tspace: color.space,\n\t\tcoords: color.coords.slice(),\n\t\talpha: color.alpha,\n\t};\n}\n", "import ColorSpace from \"./space.js\";\n\n/**\n * Euclidean distance of colors in an arbitrary color space\n */\nexport default function distance (color1, color2, space = \"lab\") {\n\tspace = ColorSpace.get(space);\n\n\t// Assume getColor() is called on color in space.from()\n\tlet coords1 = space.from(color1);\n\tlet coords2 = space.from(color2);\n\n\treturn Math.sqrt(coords1.reduce((acc, c1, i) => {\n\t\tlet c2 = coords2[i];\n\t\tif (isNaN(c1) || isNaN(c2)) {\n\t\t\treturn acc;\n\t\t}\n\n\t\treturn acc + (c2 - c1) ** 2;\n\t}, 0));\n}\n", "import lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// CMC by the Color Measurement Committee of the\n// Bradford Society of Dyeists and Colorsts, 1994.\n// Uses LCH rather than Lab,\n// with different weights for L, C and H differences\n// A nice increase in accuracy for modest increase in complexity\nconst π = Math.PI;\nconst d2r = π / 180;\n\nexport default function (color, sample, {l = 2, c = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE CMC.\n\n\t// This implementation assumes the parametric\n\t// weighting factors l:c are 2:1\n\t// which is typical for non-textile uses.\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet [, C1, H1] = lch.from(lab, [L1, a1, b1]);\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// let [L1, a1, b1] = color.getAll(lab);\n\t// let C1 = color.get(\"lch.c\");\n\t// let H1 = color.get(\"lch.h\");\n\t// let [L2, a2, b2] = sample.getAll(lab);\n\t// let C2 = sample.get(\"lch.c\");\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\t// we don't need H2 as ΔH is calculated from Δa, Δb and ΔC\n\n\t// Lightness and Chroma differences\n\t// These are (color - sample), unlike deltaE2000\n\tlet ΔL = L1 - L2;\n\tlet ΔC = C1 - C2;\n\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\n\t// weighted Hue difference, less for larger Chroma difference\n\n\tlet H2 = (Δa ** 2) + (Δb ** 2) - (ΔC ** 2);\n\t// due to roundoff error it is possible that, for zero a and b,\n\t// ΔC > Δa + Δb is 0, resulting in attempting\n\t// to take the square root of a negative number\n\n\t// trying instead the equation from Industrial Color Physics\n\t// By Georg A. Klein\n\n\t// let ΔH = ((a1 * b2) - (a2 * b1)) / Math.sqrt(0.5 * ((C2 * C1) + (a2 * a1) + (b2 * b1)));\n\t// console.log({ΔH});\n\t// This gives the same result to 12 decimal places\n\t// except it sometimes NaNs when trying to root a negative number\n\n\t// let ΔH = Math.sqrt(H2); we never actually use the root, it gets squared again!!\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor, depends entirely on L1 not L2\n\tlet SL = 0.511;\t// linear portion of the Y to L transfer function\n\tif (L1 >= 16) {\t// cubic portion\n\t\tSL = (0.040975 * L1) / (1 + 0.01765 * L1);\n\t}\n\n\t// SC Chroma factor\n\tlet SC = ((0.0638 * C1) / (1 + 0.0131 * C1)) + 0.638;\n\n\t// Cross term T for blue non-linearity\n\tlet T;\n\tif (Number.isNaN(H1)) {\n\t\tH1 = 0;\n\t}\n\n\tif (H1 >= 164 && H1 <= 345) {\n\t\tT = 0.56 + Math.abs(0.2 * Math.cos((H1 + 168) * d2r));\n\t}\n\telse {\n\t\tT = 0.36 + Math.abs(0.4 * Math.cos((H1 + 35) * d2r));\n\t}\n\t// console.log({T});\n\n\t// SH Hue factor also depends on C1,\n\tlet C4 = Math.pow(C1, 4);\n\tlet F = Math.sqrt(C4 / (C4 + 1900));\n\tlet SH = SC * ((F * T) + 1 - F);\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (l * SL)) ** 2;\n\tdE += (ΔC / (c * SC)) ** 2;\n\tdE += (H2 / (SH ** 2));\n\t// dE += (ΔH / SH)  ** 2;\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nconst Yw = 203;\t// absolute luminance of media white\n\nexport default new ColorSpace({\n// Absolute CIE XYZ, with a D65 whitepoint,\n// as used in most HDR colorspaces as a starting point.\n// SDR spaces are converted per BT.2048\n// so that diffuse, media white is 203 cd/m²\n\tid: \"xyz-abs-d65\",\n\tcssId: \"--xyz-abs-d65\",\n\tname: \"Absolute XYZ D65\",\n\tcoords: {\n\t\tx: {\n\t\t\trefRange: [0, 9504.7],\n\t\t\tname: \"Xa\",\n\t\t},\n\t\ty: {\n\t\t\trefRange: [0, 10000],\n\t\t\tname: \"Ya\",\n\t\t},\n\t\tz: {\n\t\t\trefRange: [0, 10888.3],\n\t\t\tname: \"Za\",\n\t\t},\n\t},\n\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// Make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\treturn XYZ.map (v => Math.max(v * Yw, 0));\n\t},\n\ttoBase (AbsXYZ) {\n\t\t// Convert to media-white relative XYZ\n\t\treturn AbsXYZ.map(v => Math.max(v / Yw, 0));\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst b = 1.15;\nconst g = 0.66;\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\nconst p = 1.7 * 2523 / (2 ** 5);\nconst pinv = (2 ** 5) / (1.7 * 2523);\nconst d = -0.56;\nconst d0 = 1.6295499532821566E-11;\n\nconst XYZtoCone_M = [\n\t[  0.41478972, 0.579999,  0.0146480 ],\n\t[ -0.2015100,  1.120649,  0.0531008 ],\n\t[ -0.0166008,  0.264800,  0.6684799 ],\n];\n// XYZtoCone_M inverted\nconst Coneto<PERSON>Y<PERSON>_M = [\n\t[  1.9242264357876067,  -1.0047923125953657,  0.037651404030618   ],\n\t[  0.35031676209499907,  0.7264811939316552, -0.06538442294808501 ],\n\t[ -0.09098281098284752, -0.3127282905230739,  1.5227665613052603  ],\n];\nconst ConetoIab_M = [\n\t[  0.5,       0.5,       0        ],\n\t[  3.524000, -4.066708,  0.542708 ],\n\t[  0.199076,  1.096799, -1.295875 ],\n];\n// ConetoIab_M inverted\nconst IabtoCone_M = [\n\t[ 1,                   0.1386050432715393,   0.05804731615611886 ],\n\t[ 0.9999999999999999, -0.1386050432715393,  -0.05804731615611886 ],\n\t[ 0.9999999999999998, -0.09601924202631895, -0.8118918960560388  ],\n];\n\nexport default new ColorSpace({\n\tid: \"jzazbz\",\n\tname: \"Jzazbz\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Jz\",\n\t\t},\n\t\taz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t\tbz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// First make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\t// BT.2048 says media white Y=203 at PQ 58\n\n\t\tlet [ Xa, Ya, Za ] = XYZ;\n\n\t\t// modify X and Y\n\t\tlet Xm = (b * Xa) - ((b - 1) * Za);\n\t\tlet Ym = (g * Ya) - ((g - 1) * Xa);\n\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoCone_M, [ Xm, Ym, Za ]);\n\n\t\t// PQ-encode LMS\n\t\tlet PQLMS = LMS.map (function (val) {\n\t\t\tlet num = c1 + (c2 * ((val / 10000) ** n));\n\t\t\tlet denom = 1 + (c3 * ((val / 10000) ** n));\n\n\t\t\treturn (num / denom)  ** p;\n\t\t});\n\n\t\t// almost there, calculate Iz az bz\n\t\tlet [ Iz, az, bz] = multiplyMatrices(ConetoIab_M, PQLMS);\n\t\t// console.log({Iz, az, bz});\n\n\t\tlet Jz = ((1 + d) * Iz) / (1 + (d * Iz)) - d0;\n\t\treturn [Jz, az, bz];\n\t},\n\ttoBase (Jzazbz) {\n\t\tlet [Jz, az, bz] = Jzazbz;\n\t\tlet Iz = (Jz + d0) / (1 + d - d * (Jz + d0));\n\n\t\t// bring into LMS cone domain\n\t\tlet PQLMS = multiplyMatrices(IabtoCone_M, [ Iz, az, bz ]);\n\n\t\t// convert from PQ-coded to linear-light\n\t\tlet LMS = PQLMS.map(function (val) {\n\t\t\tlet num = (c1 - (val ** pinv));\n\t\t\tlet denom = (c3 * (val ** pinv)) - c2;\n\t\t\tlet x = 10000 * ((num / denom) ** ninv);\n\n\t\t\treturn (x); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\n\t\t// modified abs XYZ\n\t\tlet [ Xm, Ym, Za ] = multiplyMatrices(ConetoXYZ_M, LMS);\n\n\t\t// restore standard D50 relative XYZ, relative to media white\n\t\tlet Xa = (Xm + ((b - 1) * Za)) / b;\n\t\tlet Ya = (Ym + ((g - 1) * Xa)) / g;\n\t\treturn [ Xa, Ya, Za ];\n\t},\n\n\tformats: {\n\t\t// https://drafts.csswg.org/css-color-hdr/#Jzazbz\n\t\t\"color\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport <PERSON><PERSON><PERSON>b<PERSON> from \"./jzazbz.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"jzczhz\",\n\tname: \"J<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\tcz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\thz: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Jzazbz,\n\tfromBase (jzazbz) {\n\t\t// Convert to polar form\n\t\tlet [Jz, az, bz] = jzazbz;\n\t\tlet hue;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(az) < ε && Math.abs(bz) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(bz, az) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tJz, // Jz is still Jz\n\t\t\tMath.sqrt(az ** 2 + bz ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (jzczhz) {\n\t\t// Convert from polar form\n\t\t// debugger;\n\t\treturn [\n\t\t\tjzczhz[0], // Jz is still Jz\n\t\t\tjzczhz[1] * Math.cos(jzczhz[2] * Math.PI / 180), // az\n\t\t\tjzczhz[1] * Math.sin(jzczhz[2] * Math.PI / 180),  // bz\n\t\t];\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst c1 = 3424 / 4096;\nconst c2 = 2413 / 128;\nconst c3 = 2392 / 128;\nconst m1 = 2610 / 16384;\nconst m2 = 2523 / 32;\nconst im1 = 16384 / 2610;\nconst im2 = 32 / 2523;\n\n// The matrix below includes the 4% crosstalk components\n// and is from the Dolby \"What is ICtCp\" paper\"\nconst XYZtoLMS_M = [\n\t[  0.3592832590121217,  0.6976051147779502, -0.0358915932320290 ],\n\t[ -0.1920808463704993,  1.1004767970374321,  0.0753748658519118 ],\n\t[  0.0070797844607479,  0.0748396662186362,  0.8433265453898765 ],\n];\n// linear-light Rec.2020 to LMS, again with crosstalk\n// rational terms from <PERSON>,\n// Encoding High Dynamic Range andWide Color Gamut Imagery, p.97\n// and ITU-R BT.2124-0 p.2\n/*\nconst Rec2020toLMS_M = [\n\t[ 1688 / 4096,  2146 / 4096,   262 / 4096 ],\n\t[  683 / 4096,  2951 / 4096,   462 / 4096 ],\n\t[   99 / 4096,   309 / 4096,  3688 / 4096 ]\n];\n*/\n// this includes the Ebner LMS coefficients,\n// the rotation, and the scaling to [-0.5,0.5] range\n// rational terms from Fröhlich p.97\n// and ITU-R BT.2124-0 pp.2-3\nconst LMStoIPT_M = [\n\t[  2048 / 4096,   2048 / 4096,       0      ],\n\t[  6610 / 4096, -13613 / 4096,  7003 / 4096 ],\n\t[ 17933 / 4096, -17390 / 4096,  -543 / 4096 ],\n];\n\n// inverted matrices, calculated from the above\nconst IPTtoLMS_M = [\n\t[ 0.9999999999999998,  0.0086090370379328,  0.1110296250030260 ],\n\t[ 0.9999999999999998, -0.0086090370379328, -0.1110296250030259 ],\n\t[ 0.9999999999999998,  0.5600313357106791, -0.3206271749873188 ],\n];\n/*\nconst LMStoRec2020_M = [\n\t[ 3.4375568932814012112,   -2.5072112125095058195,   0.069654319228104608382],\n\t[-0.79142868665644156125,   1.9838372198740089874,  -0.19240853321756742626 ],\n\t[-0.025646662911506476363, -0.099240248643945566751, 1.1248869115554520431  ]\n];\n*/\nconst LMStoXYZ_M = [\n\t[  2.0701522183894223, -1.3263473389671563,  0.2066510476294053 ],\n\t[  0.3647385209748072,  0.6805660249472273, -0.0453045459220347 ],\n\t[ -0.0497472075358123, -0.0492609666966131,  1.1880659249923042 ],\n];\n\n// Only the PQ form of ICtCp is implemented here. There is also an HLG form.\n// from Dolby, \"WHAT IS ICTCP?\"\n// https://professional.dolby.com/siteassets/pdfs/ictcp_dolbywhitepaper_v071.pdf\n// and\n// Dolby, \"Perceptual Color Volume\n// Measuring the Distinguishable Colors of HDR and WCG Displays\"\n// https://professional.dolby.com/siteassets/pdfs/dolby-vision-measuring-perceptual-color-volume-v7.1.pdf\nexport default new ColorSpace({\n\tid: \"ictcp\",\n\tname: \"ICTCP\",\n\t// From BT.2100-2 page 7:\n\t// During production, signal values are expected to exceed the\n\t// range E′ = [0.0 : 1.0]. This provides processing headroom and avoids\n\t// signal degradation during cascaded processing. Such values of E′,\n\t// below 0.0 or exceeding 1.0, should not be clipped during production\n\t// and exchange.\n\t// Values below 0.0 should not be clipped in reference displays (even\n\t// though they represent “negative” light) to allow the black level of\n\t// the signal (LB) to be properly set using test signals known as “PLUGE”\n\tcoords: {\n\t\ti: {\n\t\t\trefRange: [0, 1],\t// Constant luminance,\n\t\t\tname: \"I\",\n\t\t},\n\t\tct: {\n\t\t\trefRange: [-0.5, 0.5],\t// Full BT.2020 gamut in range [-0.5, 0.5]\n\t\t\tname: \"CT\",\n\t\t},\n\t\tcp: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t\tname: \"CP\",\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\treturn LMStoICtCp(LMS);\n\t},\n\ttoBase (ICtCp) {\n\t\tlet LMS = ICtCptoLMS(ICtCp);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n});\n\nfunction LMStoICtCp (LMS) {\n\t// apply the PQ EOTF\n\t// we can't ever be dividing by zero because of the \"1 +\" in the denominator\n\tlet PQLMS = LMS.map (function (val) {\n\t\tlet num = c1 + (c2 * ((val / 10000) ** m1));\n\t\tlet denom = 1 + (c3 * ((val / 10000) ** m1));\n\n\t\treturn (num / denom)  ** m2;\n\t});\n\n\t// LMS to IPT, with rotation for Y'C'bC'r compatibility\n\treturn multiplyMatrices(LMStoIPT_M, PQLMS);\n}\n\nfunction ICtCptoLMS (ICtCp) {\n\tlet PQLMS = multiplyMatrices(IPTtoLMS_M, ICtCp);\n\n\t// From BT.2124-0 Annex 2 Conversion 3\n\tlet LMS = PQLMS.map (function (val) {\n\t\tlet num  = Math.max((val ** im2) - c1, 0);\n\t\tlet denom = (c2 - (c3 * (val ** im2)));\n\t\treturn 10000 * ((num / denom) ** im1);\n\t});\n\n\treturn LMS;\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices, interpolate, copySign, spow, zdiv, bisectLeft} from \"../util.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst adaptedCoef = 0.42;\nconst adaptedCoefInv = 1 / adaptedCoef;\nconst tau = 2 * Math.PI;\n\nconst cat16 = [\n\t[  0.401288,  0.650173, -0.051461 ],\n\t[ -0.250268,  1.204414,  0.045854 ],\n\t[ -0.002079,  0.048952,  0.953127 ],\n];\n\nconst cat16Inv = [\n\t[1.8620678550872327, -1.0112546305316843, 0.14918677544445175],\n\t[0.38752654323613717, 0.6214474419314753, -0.008973985167612518],\n\t[-0.015841498849333856, -0.03412293802851557, 1.0499644368778496],\n];\n\nconst m1 = [\n\t[460.0, 451.0, 288.0],\n\t[460.0, -891.0, -261.0],\n\t[460.0, -220.0, -6300.0],\n];\n\nconst surroundMap = {\n\tdark: [0.8, 0.525, 0.8],\n\tdim: [0.9, 0.59, 0.9],\n\taverage: [1, 0.69, 1],\n};\n\nconst hueQuadMap = {\n\t// Red, Yellow, Green, Blue, Red\n\th: [20.14, 90.00, 164.25, 237.53, 380.14],\n\te: [0.8, 0.7, 1.0, 1.2, 0.8],\n\tH: [0.0, 100.0, 200.0, 300.0, 400.0],\n};\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\n\nexport function adapt (coords, fl) {\n\tconst temp = coords.map(c => {\n\t\tconst x = spow(fl * Math.abs(c) * 0.01, adaptedCoef);\n\t\treturn 400 * copySign(x, c) / (x + 27.13);\n\t});\n\treturn temp;\n}\n\nexport function unadapt (adapted, fl) {\n\tconst constant = 100 / fl * (27.13 ** adaptedCoefInv);\n\treturn adapted.map(c => {\n\t\tconst cabs = Math.abs(c);\n\t\treturn copySign(constant * spow(cabs / (400 - cabs), adaptedCoefInv), c);\n\t});\n}\n\nexport function hueQuadrature (h) {\n\tlet hp = constrain(h);\n\tif (hp <= hueQuadMap.h[0]) {\n\t\thp += 360;\n\t}\n\n\tconst i = bisectLeft(hueQuadMap.h, hp) - 1;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\tconst Hi = hueQuadMap.H[i];\n\n\tconst t = (hp - hi) / ei;\n\treturn Hi + (100 * t) / (t + (hii - hp) / eii);\n}\n\nexport function invHueQuadrature (H) {\n\tlet Hp = ((H % 400 + 400) % 400);\n\tconst i = Math.floor(0.01 * Hp);\n\tHp = Hp % 100;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\n\treturn constrain(\n\t\t(Hp * (eii * hi - ei * hii) - 100 * hi * eii) /\n\t\t(Hp * (eii - ei) - 100 * eii),\n\t);\n}\n\nexport function environment (\n\trefWhite,\n\tadaptingLuminance,\n\tbackgroundLuminance,\n\tsurround,\n\tdiscounting,\n) {\n\n\tconst env = {};\n\n\tenv.discounting = discounting;\n\tenv.refWhite = refWhite;\n\tenv.surround = surround;\n\tconst xyzW = refWhite.map(c => {\n\t\treturn c * 100;\n\t});\n\n\t// The average luminance of the environment in `cd/m^2cd/m` (a.k.a. nits)\n\tenv.la = adaptingLuminance;\n\t// The relative luminance of the nearby background\n\tenv.yb = backgroundLuminance;\n\t// Absolute luminance of the reference white.\n\tconst yw = xyzW[1];\n\n\t// Cone response for reference white\n\tconst rgbW = multiplyMatrices(cat16, xyzW);\n\n\t// Surround: dark, dim, and average\n\tsurround = surroundMap[env.surround];\n\tconst f = surround[0];\n\tenv.c = surround[1];\n\tenv.nc = surround[2];\n\n\tconst k = 1 / (5 * env.la + 1);\n\tconst k4 = k ** 4;\n\n\t// Factor of luminance level adaptation\n\tenv.fl = (k4 * env.la + 0.1 * (1 - k4) * (1 - k4) * Math.cbrt(5 * env.la));\n\tenv.flRoot = env.fl ** 0.25;\n\n\tenv.n = env.yb / yw;\n\tenv.z = 1.48 + Math.sqrt(env.n);\n\tenv.nbb = 0.725 * (env.n ** -0.2);\n\tenv.ncb = env.nbb;\n\n\t// Degree of adaptation calculating if not discounting\n\t// illuminant (assumed eye is fully adapted)\n\tconst d = (discounting) ?\n\t\t1 :\n\t\tMath.max(\n\t\t\tMath.min(f * (1 - 1 / 3.6 * Math.exp((-env.la - 42) / 92)), 1),\n\t\t\t0,\n\t\t);\n\tenv.dRgb = rgbW.map(c => {\n\t\treturn interpolate(1, yw / c, d);\n\t});\n\tenv.dRgbInv = env.dRgb.map(c => {\n\t\treturn 1 / c;\n\t});\n\n\t// Achromatic response\n\tconst rgbCW = rgbW.map((c, i) => {\n\t\treturn c * env.dRgb[i];\n\t});\n\tconst rgbAW = adapt(rgbCW, env.fl);\n\tenv.aW = env.nbb * (2 * rgbAW[0] + rgbAW[1] + 0.05 * rgbAW[2]);\n\n\t// console.log(env);\n\n\treturn env;\n}\n\n// Pre-calculate everything we can with the viewing conditions\nconst viewingConditions = environment(\n\twhite,\n\t64 / Math.PI * 0.2, 20,\n\t\"average\",\n\tfalse,\n);\n\nexport function fromCam16 (cam16, env) {\n\n\t// These check ensure one, and only one attribute for a\n\t// given category is provided.\n\tif (!((cam16.J !== undefined) ^ (cam16.Q !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'J' or 'Q'\");\n\t}\n\n\tif (!((cam16.C !== undefined) ^ (cam16.M !== undefined) ^ (cam16.s !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'C', 'M' or 's'\");\n\t}\n\n\t// Hue is absolutely required\n\tif (!((cam16.h !== undefined) ^ (cam16.H !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'h' or 'H'\");\n\t}\n\n\t// Black\n\tif (cam16.J === 0.0 || cam16.Q === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Break hue into Cartesian components\n\tlet hRad = 0.0;\n\tif (cam16.h !== undefined) {\n\t\thRad = constrain(cam16.h) * deg2rad;\n\t}\n\telse {\n\t\thRad = invHueQuadrature(cam16.H) * deg2rad;\n\t}\n\n\tconst cosh = Math.cos(hRad);\n\tconst sinh = Math.sin(hRad);\n\n\t// Calculate `Jroot` from one of the lightness derived coordinates.\n\tlet Jroot = 0.0;\n\tif (cam16.J !== undefined) {\n\t\tJroot = spow(cam16.J, 1 / 2) * 0.1;\n\t}\n\telse if (cam16.Q !== undefined) {\n\t\tJroot = 0.25 * env.c * cam16.Q / ((env.aW + 4) * env.flRoot);\n\t}\n\n\t// Calculate the `t` value from one of the chroma derived coordinates\n\tlet alpha = 0.0;\n\tif (cam16.C !== undefined) {\n\t\talpha = cam16.C / Jroot;\n\t}\n\telse if (cam16.M !== undefined) {\n\t\talpha = (cam16.M / env.flRoot) / Jroot;\n\t}\n\telse if (cam16.s !== undefined) {\n\t\talpha = 0.0004 * (cam16.s ** 2) * (env.aW + 4) / env.c;\n\t}\n\tconst t = spow(\n\t\talpha * Math.pow(1.64 - Math.pow(0.29, env.n), -0.73),\n\t\t10 / 9,\n\t);\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\t// Achromatic response\n\tconst A = env.aW * spow(Jroot, 2 / env.c / env.z);\n\n\t// Calculate red-green and yellow-blue components\n\tconst p1 = 5e4 / 13 * env.nc * env.ncb * et;\n\tconst p2 = A / env.nbb;\n\tconst r = (\n\t\t23 * (p2 + 0.305) *\n\t\tzdiv(t, 23 * p1 + t * (11 * cosh + 108 * sinh))\n\t);\n\tconst a = r * cosh;\n\tconst b = r * sinh;\n\n\t// Calculate back from cone response to XYZ\n\tconst rgb_c = unadapt(\n\t\tmultiplyMatrices(m1, [p2, a, b]).map(c => {\n\t\t\treturn c * 1 / 1403;\n\t\t}),\n\t\tenv.fl,\n\t);\n\treturn multiplyMatrices(\n\t\tcat16Inv,\n\t\trgb_c.map((c, i) => {\n\t\t\treturn c * env.dRgbInv[i];\n\t\t}),\n\t).map(c => {\n\t\treturn c / 100;\n\t});\n}\n\n\nexport function toCam16 (xyzd65, env) {\n\t// Cone response\n\tconst xyz100 = xyzd65.map(c => {\n\t\treturn c * 100;\n\t});\n\tconst rgbA = adapt(\n\t\tmultiplyMatrices(cat16, xyz100).map((c, i) => {\n\t\t\treturn c * env.dRgb[i];\n\t\t}),\n\t\tenv.fl,\n\t);\n\n\t// Calculate hue from red-green and yellow-blue components\n\tconst a = rgbA[0] + (-12 * rgbA[1] + rgbA[2]) / 11;\n\tconst b = (rgbA[0] + rgbA[1] - 2 * rgbA[2]) / 9;\n\tconst hRad = ((Math.atan2(b, a) % tau) + tau) % tau;\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\tconst t = (\n\t\t5e4 / 13 * env.nc * env.ncb *\n\t\tzdiv(\n\t\t\tet * Math.sqrt(a ** 2 + b ** 2),\n\t\t\trgbA[0] + rgbA[1] + 1.05 * rgbA[2] + 0.305,\n\t\t)\n\t);\n\tconst alpha = spow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, env.n), 0.73);\n\n\t// Achromatic response\n\tconst A = env.nbb * (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]);\n\n\tconst Jroot = spow(A / env.aW, 0.5 * env.c * env.z);\n\n\t// Lightness\n\tconst J = 100 * spow(Jroot, 2);\n\n\t// Brightness\n\tconst Q = (4 / env.c * Jroot * (env.aW + 4) * env.flRoot);\n\n\t// Chroma\n\tconst C = alpha * Jroot;\n\n\t// Colorfulness\n\tconst M = C * env.flRoot;\n\n\t// Hue\n\tconst h = constrain(hRad * rad2deg);\n\n\t// Hue quadrature\n\tconst H = hueQuadrature(h);\n\n\t// Saturation\n\tconst s = 50 * spow(env.c * alpha / (env.aW + 4), 1 / 2);\n\n\t// console.log({J: J, C: C, h: h, s: s, Q: Q, M: M, H: H});\n\n\treturn {J: J, C: C, h: h, s: s, Q: Q, M: M, H: H};\n}\n\n\n// Provided as a way to directly evaluate the CAM16 model\n// https://observablehq.com/@jrus/cam16: reference implementation\n// https://arxiv.org/pdf/1802.06067.pdf: Nico Schlömer\n// https://onlinelibrary.wiley.com/doi/pdf/10.1002/col.22324: hue quadrature\n// https://www.researchgate.net/publication/318152296_Comprehensive_color_solutions_CAM16_CAT16_and_CAM16-UCS\n// Results compared against: https://github.com/colour-science/colour\nexport default new ColorSpace({\n\tid: \"cam16-jmh\",\n\tcssId: \"--cam16-jmh\",\n\tname: \"CAM16-JMh\",\n\tcoords: {\n\t\tj: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"J\",\n\t\t},\n\t\tm: {\n\t\t\trefRange: [0, 105.0],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\tconst cam16 = toCam16(xyz, viewingConditions);\n\t\treturn [cam16.J, cam16.M, cam16.h];\n\t},\n\ttoBase (cam16) {\n\t\treturn fromCam16(\n\t\t\t{J: cam16[0], M: cam16[1], h: cam16[2]},\n\t\t\tviewingConditions,\n\t\t);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {fromCam16, toCam16, environment} from \"./cam16.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nfunction toLstar (y) {\n\t// Convert XYZ Y to L*\n\n\tconst fy = (y > ε) ? Math.cbrt(y) : (κ * y + 16) / 116;\n\treturn (116.0 * fy) - 16.0;\n}\n\nfunction fromLstar (lstar) {\n\t// Convert L* back to XYZ Y\n\n\treturn (lstar > 8) ?  Math.pow((lstar + 16) / 116, 3) : lstar / κ;\n}\n\nfunction fromHct (coords, env) {\n\t// Use Newton's method to try and converge as quick as possible or\n\t// converge as close as we can. While the requested precision is achieved\n\t// most of the time, it may not always be achievable. Especially past the\n\t// visible spectrum, the algorithm will likely struggle to get the same\n\t// precision. If, for whatever reason, we cannot achieve the accuracy we\n\t// seek in the allotted iterations, just return the closest we were able to\n\t// get.\n\n\tlet [h, c, t] = coords;\n\tlet xyz = [];\n\tlet j = 0;\n\n\t// Shortcut out for black\n\tif (t === 0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Calculate the Y we need to target\n\tlet y = fromLstar(t);\n\n\t// A better initial guess yields better results. Polynomials come from\n\t// curve fitting the T vs J response.\n\tif (t > 0) {\n\t\tj = 0.00379058511492914 * t ** 2 + 0.608983189401032 * t + 0.9155088574762233;\n\t}\n\telse {\n\t\tj = 9.514440756550361e-06 * t ** 2 + 0.08693057439788597 * t - 21.928975842194614;\n\t}\n\n\t// Threshold of how close is close enough, and max number of attempts.\n\t// More precision and more attempts means more time spent iterating. Higher\n\t// required precision gives more accuracy but also increases the chance of\n\t// not hitting the goal. 2e-12 allows us to convert round trip with\n\t// reasonable accuracy of six decimal places or more.\n\tconst threshold = 2e-12;\n\tconst max_attempts = 15;\n\n\tlet attempt = 0;\n\tlet last = Infinity;\n\tlet best = j;\n\n\t// Try to find a J such that the returned y matches the returned y of the L*\n\twhile (attempt <= max_attempts) {\n\t\txyz = fromCam16({J: j, C: c, h: h}, env);\n\n\t\t// If we are within range, return XYZ\n\t\t// If we are closer than last time, save the values\n\t\tconst delta = Math.abs(xyz[1] - y);\n\t\tif (delta < last) {\n\t\t\tif (delta <= threshold) {\n\t\t\t\treturn xyz;\n\t\t\t}\n\t\t\tbest = j;\n\t\t\tlast = delta;\n\t\t}\n\n\t\t// f(j_root) = (j ** (1 / 2)) * 0.1\n\t\t// f(j) = ((f(j_root) * 100) ** 2) / j - 1 = 0\n\t\t// f(j_root) = Y = y / 100\n\t\t// f(j) = (y ** 2) / j - 1\n\t\t// f'(j) = (2 * y) / j\n\t\tj = j - (xyz[1] - y) * j / (2 * xyz[1]);\n\n\t\tattempt += 1;\n\t}\n\n\t// We could not acquire the precision we desired,\n\t// return our closest attempt.\n\treturn fromCam16({J: j, C: c, h: h}, env);\n}\n\nfunction toHct (xyz, env) {\n\t// Calculate HCT by taking the L* of CIE LCh D65 and CAM16 chroma and hue.\n\n\tconst t = toLstar(xyz[1]);\n\tif (t === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\tconst cam16 = toCam16(xyz, viewingConditions);\n\treturn [constrain(cam16.h), cam16.C, t];\n}\n\n// Pre-calculate everything we can with the viewing conditions\nexport const viewingConditions = environment(\n\twhite, 200 / Math.PI * fromLstar(50.0),\n\tfromLstar(50.0) * 100,\n\t\"average\",\n\tfalse,\n);\n\n// https://material.io/blog/science-of-color-design\n// This is not a port of the material-color-utilities,\n// but instead implements the full color space as described,\n// combining CAM16 JCh and Lab D65. This does not clamp conversion\n// to HCT to specific chroma bands and provides support for wider\n// gamuts than Google currently supports and does so at a greater\n// precision (> 8 bits back to sRGB).\n// This implementation comes from https://github.com/facelessuser/coloraide\n// which is licensed under MIT.\nexport default new ColorSpace({\n\tid: \"hct\",\n\tname: \"HCT\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 145],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\tt: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Tone\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\treturn toHct(xyz, viewingConditions);\n\t},\n\ttoBase (hct) {\n\t\treturn fromHct(hct, viewingConditions);\n\t},\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hct\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import hct from \"../spaces/hct.js\";\nimport {viewingConditions} from \"../spaces/hct.js\";\nimport getColor from \"../getColor.js\";\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\nconst ucsCoeff = [1.00, 0.007, 0.0228];\n\n/**\n* Convert HCT chroma and hue (CAM16 JMh colorfulness and hue) using UCS logic for a and b.\n* @param {number[]} coords - HCT coordinates.\n* @return {number[]}\n*/\nfunction convertUcsAb (coords) {\n\t// We want the distance between the actual color.\n\t// If chroma is negative, it will throw off our calculations.\n\t// Normally, converting back to the base and forward will correct it.\n\t// If we have a negative chroma after this, then we have a color that\n\t// cannot resolve to positive chroma.\n\tif (coords[1] < 0) {\n\t\tcoords = hct.fromBase(hct.toBase(coords));\n\t}\n\n\t// Only in extreme cases (usually outside the visible spectrum)\n\t// can the input value for log become negative.\n\t// Avoid domain error by forcing a zero result via \"max\" if necessary.\n\tconst M = Math.log(Math.max(1 + ucsCoeff[2] * coords[1] * viewingConditions.flRoot, 1.0)) / ucsCoeff[2];\n\tconst hrad = coords[0] * deg2rad;\n\tconst a = M * Math.cos(hrad);\n\tconst b = M * Math.sin(hrad);\n\n\treturn [coords[2], a, b];\n}\n\n\n/**\n* Color distance using HCT.\n* @param {Color} color - Color to compare.\n* @param {Color} sample - Color to compare.\n* @return {number[]}\n*/\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\tlet [ t1, a1, b1 ] = convertUcsAb(hct.from(color));\n\tlet [ t2, a2, b2 ] = convertUcsAb(hct.from(sample));\n\n\t// Use simple euclidean distance with a and b using UCS conversion\n\t// and LCh lightness (HCT tone).\n\treturn Math.sqrt((t1 - t2) ** 2 + (a1 - a2) ** 2 + (b1 - b2) ** 2);\n}\n", "import deltaE76 from \"./deltaE76.js\";\nimport deltaECMC from \"./deltaECMC.js\";\nimport deltaE2000 from \"./deltaE2000.js\";\nimport deltaEJz from \"./deltaEJz.js\";\nimport deltaEITP from \"./deltaEITP.js\";\nimport deltaE<PERSON> from \"./deltaEOK.js\";\nimport deltaEHCT from \"./deltaEHCT.js\";\n\nexport {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n\nexport default {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n", "import distance from \"../distance.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function deltaE76 (color, sample) {\n\t// Assume getColor() is called in the distance function\n\treturn distance(color, sample, \"lab\");\n}\n", "import jzczhz from \"../spaces/jzczhz.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// Uses JzCzHz, which has improved perceptual uniformity\n// and thus a simple Euclidean root-sum of ΔL² ΔC² ΔH²\n// gives good results.\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in JzCzHz.\n\tlet [Jz1, Cz1, Hz1] = jzczhz.from(color);\n\tlet [Jz2, Cz2, Hz2] = jzczhz.from(sample);\n\n\t// Lightness and Chroma differences\n\t// sign does not matter as they are squared.\n\tlet ΔJ = Jz1 - Jz2;\n\tlet ΔC = Cz1 - Cz2;\n\n\t// length of chord for ΔH\n\tif ((Number.isNaN(Hz1)) && (Number.isNaN(Hz2))) {\n\t\t// both undefined hues\n\t\tHz1 = 0;\n\t\tHz2 = 0;\n\t}\n\telse if (Number.isNaN(Hz1)) {\n\t\t// one undefined, set to the defined hue\n\t\tHz1 = Hz2;\n\t}\n\telse if (Number.isNaN(Hz2)) {\n\t\tHz2 = Hz1;\n\t}\n\n\tlet Δh = Hz1 - Hz2;\n\tlet ΔH = 2 * Math.sqrt(Cz1 * Cz2) * Math.sin((Δh / 2) * (Math.PI / 180));\n\n\treturn Math.sqrt(ΔJ ** 2 + ΔC ** 2 + ΔH ** 2);\n}\n", "import ictcp from \"../spaces/ictcp.js\";\nimport getColor from \"../getColor.js\";\n\n// Delta E in ICtCp space,\n// which the ITU calls Delta E ITP, which is shorter\n// formulae from ITU Rec. ITU-R BT.2124-0\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in ICtCp\n\t// which is simply the Euclidean distance\n\n\tlet [ I1, T1, P1 ] = ictcp.from(color);\n\tlet [ I2, T2, P2 ] = ictcp.from(sample);\n\n\t// the 0.25 factor is to undo the encoding scaling in Ct\n\t// the 720 is so that 1 deltaE = 1 JND\n\t// per  ITU-R BT.2124-0 p.3\n\n\treturn 720 * Math.sqrt((I1 - I2) ** 2 + (0.25 * (T1 - T2) ** 2) + (P1 - P2) ** 2);\n}\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport deltaE2000 from \"./deltaE/deltaE2000.js\";\nimport deltaEOK from \"./deltaE/deltaEOK.js\";\nimport inGamut from \"./inGamut.js\";\nimport to from \"./to.js\";\nimport get from \"./get.js\";\nimport oklab from \"./spaces/oklab.js\";\nimport set from \"./set.js\";\nimport clone from \"./clone.js\";\nimport getColor from \"./getColor.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\nimport {WHITES} from \"./adapt.js\";\n\n/**\n * Calculate the epsilon to 2 degrees smaller than the specified JND.\n * @param {Number} jnd - The target \"just noticeable difference\".\n * @returns {Number}\n */\nfunction calcEpsilon (jnd) {\n\t// Calculate the epsilon to 2 degrees smaller than the specified JND.\n\n\tconst order = (!jnd) ? 0 : Math.floor(Math.log10(Math.abs(jnd)));\n\t// Limit to an arbitrary value to ensure value is never too small and causes infinite loops.\n\treturn Math.max(parseFloat(`1e${order - 2}`), 1e-6);\n}\n\nconst GMAPPRESET = {\n\t\"hct\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 2,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: {},\n\t},\n\t\"hct-tonal\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 0,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: { channel: \"hct.t\", min: 0, max: 100 },\n\t},\n};\n\n/**\n * Force coordinates to be in gamut of a certain color space.\n * Mutates the color it is passed.\n * @param {Object|string} options object or spaceId string\n * @param {string} options.method - How to force into gamut.\n *        If \"clip\", coordinates are just clipped to their reference range.\n *        If \"css\", coordinates are reduced according to the CSS 4 Gamut Mapping Algorithm.\n *        If in the form [colorSpaceId].[coordName], that coordinate is reduced\n *        until the color is in gamut. Please note that this may produce nonsensical\n *        results for certain coordinates (e.g. hue) or infinite loops if reducing the coordinate never brings the color in gamut.\n * @param {ColorSpace|string} options.space - The space whose gamut we want to map to\n * @param {string} options.deltaEMethod - The delta E method to use while performing gamut mapping.\n *        If no method is specified, delta E 2000 is used.\n * @param {Number} options.jnd - The \"just noticeable difference\" to target.\n * @param {Object} options.blackWhiteClamp - Used to configure SDR black and clamping.\n *        \"channel\" indicates the \"space.channel\" to use for determining when to clamp.\n *        \"min\" indicates the lower limit for black clamping and \"max\" indicates the upper\n *        limit for white clamping.\n */\n\nexport default function toGamut (\n\tcolor,\n\t{\n\t\tmethod = defaults.gamut_mapping,\n\t\tspace = undefined,\n\t\tdeltaEMethod = \"\",\n\t\tjnd = 2,\n\t\tblackWhiteClamp = {},\n\t} = {},\n) {\n\tcolor = getColor(color);\n\n\tif (util.isString(arguments[1])) {\n\t\tspace = arguments[1];\n\t}\n\telse if (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\n\t// 3 spaces:\n\t// color.space: current color space\n\t// space: space whose gamut we are mapping to\n\t// mapSpace: space with the coord we're reducing\n\n\tif (inGamut(color, space, { epsilon: 0 })) {\n\t\treturn color;\n\t}\n\n\tlet spaceColor;\n\tif (method === \"css\") {\n\t\tspaceColor = toGamutCSS(color, { space });\n\t}\n\telse {\n\t\tif (method !== \"clip\" && !inGamut(color, space)) {\n\n\t\t\tif (Object.prototype.hasOwnProperty.call(GMAPPRESET, method)) {\n\t\t\t\t({method, jnd, deltaEMethod, blackWhiteClamp} = GMAPPRESET[method]);\n\t\t\t}\n\n\t\t\t// Get the correct delta E method\n\t\t\tlet de = deltaE2000;\n\t\t\tif (deltaEMethod !== \"\") {\n\t\t\t\tfor (let m in deltaEMethods) {\n\t\t\t\t\tif (\"deltae\" + deltaEMethod.toLowerCase() === m.toLowerCase()) {\n\t\t\t\t\t\tde = deltaEMethods[m];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet clipped = toGamut(to(color, space), { method: \"clip\", space });\n\t\t\tif (de(color, clipped) > jnd) {\n\n\t\t\t\t// Clamp to SDR white and black if required\n\t\t\t\tif (Object.keys(blackWhiteClamp).length === 3) {\n\t\t\t\t\tlet channelMeta = ColorSpace.resolveCoord(blackWhiteClamp.channel);\n\t\t\t\t\tlet channel = get(to(color, channelMeta.space), channelMeta.id);\n\t\t\t\t\tif (util.isNone(channel)) {\n\t\t\t\t\t\tchannel = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (channel >= blackWhiteClamp.max) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: WHITES[\"D65\"] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t\telse if (channel <= blackWhiteClamp.min) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: [0, 0, 0] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Reduce a coordinate of a certain color space until the color is in gamut\n\t\t\t\tlet coordMeta = ColorSpace.resolveCoord(method);\n\t\t\t\tlet mapSpace = coordMeta.space;\n\t\t\t\tlet coordId = coordMeta.id;\n\n\t\t\t\tlet mappedColor = to(color, mapSpace);\n\t\t\t\t// If we were already in the mapped color space, we need to resolve undefined channels\n\t\t\t\tmappedColor.coords.forEach((c, i) => {\n\t\t\t\t\tif (util.isNone(c)) {\n\t\t\t\t\t\tmappedColor.coords[i] = 0;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tlet bounds = coordMeta.range || coordMeta.refRange;\n\t\t\t\tlet min = bounds[0];\n\t\t\t\tlet ε = calcEpsilon(jnd);\n\t\t\t\tlet low = min;\n\t\t\t\tlet high = get(mappedColor, coordId);\n\n\t\t\t\twhile (high - low > ε) {\n\t\t\t\t\tlet clipped = clone(mappedColor);\n\t\t\t\t\tclipped = toGamut(clipped, { space, method: \"clip\" });\n\t\t\t\t\tlet deltaE = de(mappedColor, clipped);\n\n\t\t\t\t\tif (deltaE - jnd < ε) {\n\t\t\t\t\t\tlow = get(mappedColor, coordId);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thigh = get(mappedColor, coordId);\n\t\t\t\t\t}\n\n\t\t\t\t\tset(mappedColor, coordId, (low + high) / 2);\n\t\t\t\t}\n\n\t\t\t\tspaceColor = to(mappedColor, space);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tspaceColor = clipped;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tspaceColor = to(color, space);\n\t\t}\n\n\t\tif (method === \"clip\" // Dumb coord clipping\n\t\t\t// finish off smarter gamut mapping with clip to get rid of ε, see #17\n\t\t\t|| !inGamut(spaceColor, space, { epsilon: 0 })\n\t\t) {\n\t\t\tlet bounds = Object.values(space.coords).map(c => c.range || []);\n\n\t\t\tspaceColor.coords = spaceColor.coords.map((c, i) => {\n\t\t\t\tlet [min, max] = bounds[i];\n\n\t\t\t\tif (min !== undefined) {\n\t\t\t\t\tc = Math.max(min, c);\n\t\t\t\t}\n\n\t\t\t\tif (max !== undefined) {\n\t\t\t\t\tc = Math.min(c, max);\n\t\t\t\t}\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t}\n\t}\n\n\tif (space !== color.space) {\n\t\tspaceColor = to(spaceColor, color.space);\n\t}\n\n\tcolor.coords = spaceColor.coords;\n\treturn color;\n}\n\ntoGamut.returns = \"color\";\n\n// The reference colors to be used if lightness is out of the range 0-1 in the\n// `Oklch` space. These are created in the `Oklab` space, as it is used by the\n// DeltaEOK calculation, so it is guaranteed to be imported.\nconst COLORS = {\n\tWHITE: { space: oklab, coords: [1, 0, 0] },\n\tBLACK: { space: oklab, coords: [0, 0, 0] },\n};\n\n/**\n * Given a color `origin`, returns a new color that is in gamut using\n * the CSS Gamut Mapping Algorithm. If `space` is specified, it will be in gamut\n * in `space`, and returned in `space`. Otherwise, it will be in gamut and\n * returned in the color space of `origin`.\n * @param {Object} origin\n * @param {Object} options\n * @param {ColorSpace|string} options.space\n * @returns {Color}\n */\nexport function toGamutCSS (origin, {space} = {}) {\n\tconst JND = 0.02;\n\tconst ε = 0.0001;\n\n\torigin = getColor(origin);\n\n\tif (!space) {\n\t\tspace = origin.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tconst oklchSpace = ColorSpace.get(\"oklch\");\n\n\tif (space.isUnbounded) {\n\t\treturn to(origin, space);\n\t}\n\n\tconst origin_OKLCH = to(origin, oklchSpace);\n\tlet L = origin_OKLCH.coords[0];\n\n\t// return media white or black, if lightness is out of range\n\tif (L >= 1) {\n\t\tconst white = to(COLORS.WHITE, space);\n\t\twhite.alpha = origin.alpha;\n\t\treturn to(white, space);\n\t}\n\tif (L <= 0) {\n\t\tconst black = to(COLORS.BLACK, space);\n\t\tblack.alpha = origin.alpha;\n\t\treturn to(black, space);\n\t}\n\n\tif (inGamut(origin_OKLCH, space, {epsilon: 0})) {\n\t\treturn to(origin_OKLCH, space);\n\t}\n\n\tfunction clip (_color) {\n\t\tconst destColor = to(_color, space);\n\t\tconst spaceCoords = Object.values(space.coords);\n\t\tdestColor.coords = destColor.coords.map((coord, index) => {\n\t\t\tif (\"range\" in spaceCoords[index]) {\n\t\t\t\tconst [min, max] =  spaceCoords[index].range;\n\t\t\t\treturn util.clamp(min, coord, max);\n\t\t\t}\n\t\t\treturn coord;\n\t\t});\n\t\treturn destColor;\n\t}\n\tlet min = 0;\n\tlet max = origin_OKLCH.coords[1];\n\tlet min_inGamut = true;\n\tlet current = clone(origin_OKLCH);\n\tlet clipped = clip(current);\n\n\tlet E = deltaEOK(clipped, current);\n\tif (E < JND) {\n\t\treturn clipped;\n\t}\n\n\twhile ((max - min) > ε) {\n\t\tconst chroma = (min + max) / 2;\n\t\tcurrent.coords[1] = chroma;\n\t\tif (min_inGamut && inGamut(current, space, {epsilon: 0})) {\n\t\t\tmin = chroma;\n\t\t}\n\t\telse {\n\t\t\tclipped = clip(current);\n\t\t\tE = deltaEOK(clipped, current);\n\t\t\tif (E < JND) {\n\t\t\t\tif ((JND - E < ε)) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tmin_inGamut = false;\n\t\t\t\t\tmin = chroma;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tmax = chroma;\n\t\t\t}\n\t\t}\n\t}\n\treturn clipped;\n}\n", "import getColor from \"./getColor.js\";\nimport ColorSpace from \"./space.js\";\nimport toGamut from \"./toGamut.js\";\n\n/**\n * Convert to color space and return a new color\n * @param {Object|string} space - Color space object or id\n * @param {Object} options\n * @param {boolean} options.inGamut - Whether to force resulting color in gamut\n * @returns {Color}\n */\nexport default function to (color, space, {inGamut} = {}) {\n\tcolor = getColor(color);\n\tspace = ColorSpace.get(space);\n\n\tlet coords = space.from(color);\n\tlet ret = {space, coords, alpha: color.alpha};\n\n\tif (inGamut) {\n\t\tret = toGamut(ret, inGamut === true ? undefined : inGamut);\n\t}\n\n\treturn ret;\n}\n\nto.returns = \"color\";\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport getColor from \"./getColor.js\";\nimport checkInGamut from \"./inGamut.js\";\nimport toGamut from \"./toGamut.js\";\nimport clone from \"./clone.js\";\n\n/**\n * Generic toString() method, outputs a color(spaceId ...coords) function, a functional syntax, or custom formats defined by the color space\n * @param {Object} options\n * @param {number} options.precision - Significant digits\n * @param {boolean} options.inGamut - Adjust coordinates to fit in gamut first? [default: false]\n */\nexport default function serialize (color, {\n\tprecision = defaults.precision,\n\tformat = \"default\",\n\tinGamut = true,\n\t...customOptions\n} = {}) {\n\tlet ret;\n\n\tcolor = getColor(color);\n\n\tlet formatId = format;\n\tformat = color.space.getFormat(format)\n\t       ?? color.space.getFormat(\"default\")\n\t       ?? ColorSpace.DEFAULT_FORMAT;\n\n\t// The assignment to coords and inGamut needs to stay in the order they are now\n\t// The order of the assignment was changed as a workaround for a bug in Next.js\n\t// See this issue for details: https://github.com/color-js/color.js/issues/260\n\n\tlet coords = color.coords.slice(); // clone so we can manipulate it\n\n\tinGamut ||= format.toGamut;\n\n\tif (inGamut && !checkInGamut(color)) {\n\t\t// FIXME what happens if the color contains NaNs?\n\t\tcoords = toGamut(clone(color), inGamut === true ? undefined : inGamut).coords;\n\t}\n\n\tif (format.type === \"custom\") {\n\t\tcustomOptions.precision = precision;\n\n\t\tif (format.serialize) {\n\t\t\tret = format.serialize(coords, color.alpha, customOptions);\n\t\t}\n\t\telse {\n\t\t\tthrow new TypeError(`format ${formatId} can only be used to parse colors, not for serialization`);\n\t\t}\n\t}\n\telse {\n\t\t// Functional syntax\n\t\tlet name = format.name || \"color\";\n\n\t\tif (format.serializeCoords) {\n\t\t\tcoords = format.serializeCoords(coords, precision);\n\t\t}\n\t\telse {\n\t\t\tif (precision !== null) {\n\t\t\t\tcoords = coords.map(c => {\n\t\t\t\t\treturn util.serializeNumber(c, {precision});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tlet args = [...coords];\n\n\t\tif (name === \"color\") {\n\t\t\t// If output is a color() function, add colorspace id as first argument\n\t\t\tlet cssId = format.id || format.ids?.[0] || color.space.id;\n\t\t\targs.unshift(cssId);\n\t\t}\n\n\t\tlet alpha = color.alpha;\n\t\tif (precision !== null) {\n\t\t\talpha = util.serializeNumber(alpha, {precision});\n\t\t}\n\n\t\tlet strAlpha = color.alpha >= 1 || format.noAlpha ? \"\" : `${format.commas ? \",\" : \" /\"} ${alpha}`;\n\t\tret = `${name}(${args.join(format.commas ? \", \" : \" \")}${strAlpha})`;\n\t}\n\n\treturn ret;\n}\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light rec2020 values to CIE XYZ\n// using  D65 (no chromatic adaptation)\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// 0 is actually calculated as  4.994106574466076e-17\nconst toXYZ_M = [\n\t[ 0.6369580483012914, 0.14461690358620832,  0.1688809751641721  ],\n\t[ 0.2627002120112671, 0.6779980715188708,   0.05930171646986196 ],\n\t[ 0.000000000000000,  0.028072693049087428, 1.060985057710791   ],\n];\n\n// from ITU-R BT.2124-0 Annex 2 p.3\nconst fromXYZ_M = [\n\t[  1.716651187971268,  -0.355670783776392, -0.253366281373660  ],\n\t[ -0.666684351832489,   1.616481236634939,  0.0157685458139111 ],\n\t[  0.017639857445311,  -0.042770613257809,  0.942103121235474  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"rec2020-linear\",\n\tcssId: \"--rec2020-linear\",\n\tname: \"Linear REC.2020\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n// import sRGB from \"./srgb.js\";\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\n\nexport default new RGBColorSpace({\n\tid: \"rec2020\",\n\tname: \"REC.2020\",\n\tbase: REC2020Linear,\n\t// Non-linear transfer function from Rec. ITU-R BT.2020-2 table 4\n\ttoBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val < β * 4.5) {\n\t\t\t\treturn val / 4.5;\n\t\t\t}\n\n\t\t\treturn Math.pow((val + α - 1) / α, 1 / 0.45);\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val >= β) {\n\t\t\t\treturn α * Math.pow(val, 0.45) - (α - 1);\n\t\t\t}\n\n\t\t\treturn 4.5 * val;\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\nconst toXYZ_M = [\n\t[0.4865709486482162, 0.26566769316909306, 0.1982172852343625],\n\t[0.2289745640697488, 0.6917385218365064,  0.079286914093745],\n\t[0.0000000000000000, 0.04511338185890264, 1.043944368900976],\n];\n\nconst fromXYZ_M = [\n\t[ 2.493496911941425,   -0.9313836179191239, -0.40271078445071684],\n\t[-0.8294889695615747,   1.7626640603183463,  0.023624685841943577],\n\t[ 0.03584583024378447, -0.07617238926804182, 0.9568845240076872],\n];\n\nexport default new RGBColorSpace({\n\tid: \"p3-linear\",\n\tcssId: \"--display-p3-linear\",\n\tname: \"Linear P3\",\n\twhite: \"D65\",\n\tto<PERSON><PERSON><PERSON>_<PERSON>,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// This is the linear-light version of sRGB\n// as used for example in SVG filters\n// or in Canvas\n\n// This matrix was calculated directly from the RGB and white chromaticities\n// when rounded to 8 decimal places, it agrees completely with the official matrix\n// see https://github.com/w3c/csswg-drafts/issues/5922\nconst toXYZ_M = [\n\t[ 0.41239079926595934, 0.357584339383878,   0.1804807884018343  ],\n\t[ 0.21263900587151027, 0.715168678767756,   0.07219231536073371 ],\n\t[ 0.01933081871559182, 0.11919477979462598, 0.9505321522496607  ],\n];\n\n// This matrix is the inverse of the above;\n// again it agrees with the official definition when rounded to 8 decimal places\nexport const fromXYZ_M = [\n\t[  3.2409699419045226,  -1.537383177570094,   -0.4986107602930034  ],\n\t[ -0.9692436362808796,   1.8759675015077202,   0.04155505740717559 ],\n\t[  0.05563007969699366, -0.20397695888897652,  1.0569715142428786  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"srgb-linear\",\n\tname: \"Linear sRGB\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "/* List of CSS color keywords\n * Note that this does not include currentColor, transparent,\n * or system colors\n */\n\n// To produce: Visit https://www.w3.org/TR/css-color-4/#named-colors\n// and run in the console:\n// copy($$(\"tr\", $(\".named-color-table tbody\")).map(tr => `\"${tr.cells[2].textContent.trim()}\": [${tr.cells[4].textContent.trim().split(/\\s+/).map(c => c === \"0\"? \"0\" : c === \"255\"? \"1\" : c + \" / 255\").join(\", \")}]`).join(\",\\n\"))\nexport default {\n\t\"aliceblue\": [240 / 255, 248 / 255, 1],\n\t\"antiquewhite\": [250 / 255, 235 / 255, 215 / 255],\n\t\"aqua\": [0, 1, 1],\n\t\"aquamarine\": [127 / 255, 1, 212 / 255],\n\t\"azure\": [240 / 255, 1, 1],\n\t\"beige\": [245 / 255, 245 / 255, 220 / 255],\n\t\"bisque\": [1, 228 / 255, 196 / 255],\n\t\"black\": [0, 0, 0],\n\t\"blanchedalmond\": [1, 235 / 255, 205 / 255],\n\t\"blue\": [0, 0, 1],\n\t\"blueviolet\": [138 / 255, 43 / 255, 226 / 255],\n\t\"brown\": [165 / 255, 42 / 255, 42 / 255],\n\t\"burlywood\": [222 / 255, 184 / 255, 135 / 255],\n\t\"cadetblue\": [95 / 255, 158 / 255, 160 / 255],\n\t\"chartreuse\": [127 / 255, 1, 0],\n\t\"chocolate\": [210 / 255, 105 / 255, 30 / 255],\n\t\"coral\": [1, 127 / 255, 80 / 255],\n\t\"cornflowerblue\": [100 / 255, 149 / 255, 237 / 255],\n\t\"cornsilk\": [1, 248 / 255, 220 / 255],\n\t\"crimson\": [220 / 255, 20 / 255, 60 / 255],\n\t\"cyan\": [0, 1, 1],\n\t\"darkblue\": [0, 0, 139 / 255],\n\t\"darkcyan\": [0, 139 / 255, 139 / 255],\n\t\"darkgoldenrod\": [184 / 255, 134 / 255, 11 / 255],\n\t\"darkgray\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkgreen\": [0, 100 / 255, 0],\n\t\"darkgrey\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkkhaki\": [189 / 255, 183 / 255, 107 / 255],\n\t\"darkmagenta\": [139 / 255, 0, 139 / 255],\n\t\"darkolivegreen\": [85 / 255, 107 / 255, 47 / 255],\n\t\"darkorange\": [1, 140 / 255, 0],\n\t\"darkorchid\": [153 / 255, 50 / 255, 204 / 255],\n\t\"darkred\": [139 / 255, 0, 0],\n\t\"darksalmon\": [233 / 255, 150 / 255, 122 / 255],\n\t\"darkseagreen\": [143 / 255, 188 / 255, 143 / 255],\n\t\"darkslateblue\": [72 / 255, 61 / 255, 139 / 255],\n\t\"darkslategray\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkslategrey\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkturquoise\": [0, 206 / 255, 209 / 255],\n\t\"darkviolet\": [148 / 255, 0, 211 / 255],\n\t\"deeppink\": [1, 20 / 255, 147 / 255],\n\t\"deepskyblue\": [0, 191 / 255, 1],\n\t\"dimgray\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dimgrey\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dodgerblue\": [30 / 255, 144 / 255, 1],\n\t\"firebrick\": [178 / 255, 34 / 255, 34 / 255],\n\t\"floralwhite\": [1, 250 / 255, 240 / 255],\n\t\"forestgreen\": [34 / 255, 139 / 255, 34 / 255],\n\t\"fuchsia\": [1, 0, 1],\n\t\"gainsboro\": [220 / 255, 220 / 255, 220 / 255],\n\t\"ghostwhite\": [248 / 255, 248 / 255, 1],\n\t\"gold\": [1, 215 / 255, 0],\n\t\"goldenrod\": [218 / 255, 165 / 255, 32 / 255],\n\t\"gray\": [128 / 255, 128 / 255, 128 / 255],\n\t\"green\": [0, 128 / 255, 0],\n\t\"greenyellow\": [173 / 255, 1, 47 / 255],\n\t\"grey\": [128 / 255, 128 / 255, 128 / 255],\n\t\"honeydew\": [240 / 255, 1, 240 / 255],\n\t\"hotpink\": [1, 105 / 255, 180 / 255],\n\t\"indianred\": [205 / 255, 92 / 255, 92 / 255],\n\t\"indigo\": [75 / 255, 0, 130 / 255],\n\t\"ivory\": [1, 1, 240 / 255],\n\t\"khaki\": [240 / 255, 230 / 255, 140 / 255],\n\t\"lavender\": [230 / 255, 230 / 255, 250 / 255],\n\t\"lavenderblush\": [1, 240 / 255, 245 / 255],\n\t\"lawngreen\": [124 / 255, 252 / 255, 0],\n\t\"lemonchiffon\": [1, 250 / 255, 205 / 255],\n\t\"lightblue\": [173 / 255, 216 / 255, 230 / 255],\n\t\"lightcoral\": [240 / 255, 128 / 255, 128 / 255],\n\t\"lightcyan\": [224 / 255, 1, 1],\n\t\"lightgoldenrodyellow\": [250 / 255, 250 / 255, 210 / 255],\n\t\"lightgray\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightgreen\": [144 / 255, 238 / 255, 144 / 255],\n\t\"lightgrey\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightpink\": [1, 182 / 255, 193 / 255],\n\t\"lightsalmon\": [1, 160 / 255, 122 / 255],\n\t\"lightseagreen\": [32 / 255, 178 / 255, 170 / 255],\n\t\"lightskyblue\": [135 / 255, 206 / 255, 250 / 255],\n\t\"lightslategray\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightslategrey\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightsteelblue\": [176 / 255, 196 / 255, 222 / 255],\n\t\"lightyellow\": [1, 1, 224 / 255],\n\t\"lime\": [0, 1, 0],\n\t\"limegreen\": [50 / 255, 205 / 255, 50 / 255],\n\t\"linen\": [250 / 255, 240 / 255, 230 / 255],\n\t\"magenta\": [1, 0, 1],\n\t\"maroon\": [128 / 255, 0, 0],\n\t\"mediumaquamarine\": [102 / 255, 205 / 255, 170 / 255],\n\t\"mediumblue\": [0, 0, 205 / 255],\n\t\"mediumorchid\": [186 / 255, 85 / 255, 211 / 255],\n\t\"mediumpurple\": [147 / 255, 112 / 255, 219 / 255],\n\t\"mediumseagreen\": [60 / 255, 179 / 255, 113 / 255],\n\t\"mediumslateblue\": [123 / 255, 104 / 255, 238 / 255],\n\t\"mediumspringgreen\": [0, 250 / 255, 154 / 255],\n\t\"mediumturquoise\": [72 / 255, 209 / 255, 204 / 255],\n\t\"mediumvioletred\": [199 / 255, 21 / 255, 133 / 255],\n\t\"midnightblue\": [25 / 255, 25 / 255, 112 / 255],\n\t\"mintcream\": [245 / 255, 1, 250 / 255],\n\t\"mistyrose\": [1, 228 / 255, 225 / 255],\n\t\"moccasin\": [1, 228 / 255, 181 / 255],\n\t\"navajowhite\": [1, 222 / 255, 173 / 255],\n\t\"navy\": [0, 0, 128 / 255],\n\t\"oldlace\": [253 / 255, 245 / 255, 230 / 255],\n\t\"olive\": [128 / 255, 128 / 255, 0],\n\t\"olivedrab\": [107 / 255, 142 / 255, 35 / 255],\n\t\"orange\": [1, 165 / 255, 0],\n\t\"orangered\": [1, 69 / 255, 0],\n\t\"orchid\": [218 / 255, 112 / 255, 214 / 255],\n\t\"palegoldenrod\": [238 / 255, 232 / 255, 170 / 255],\n\t\"palegreen\": [152 / 255, 251 / 255, 152 / 255],\n\t\"paleturquoise\": [175 / 255, 238 / 255, 238 / 255],\n\t\"palevioletred\": [219 / 255, 112 / 255, 147 / 255],\n\t\"papayawhip\": [1, 239 / 255, 213 / 255],\n\t\"peachpuff\": [1, 218 / 255, 185 / 255],\n\t\"peru\": [205 / 255, 133 / 255, 63 / 255],\n\t\"pink\": [1, 192 / 255, 203 / 255],\n\t\"plum\": [221 / 255, 160 / 255, 221 / 255],\n\t\"powderblue\": [176 / 255, 224 / 255, 230 / 255],\n\t\"purple\": [128 / 255, 0, 128 / 255],\n\t\"rebeccapurple\": [102 / 255, 51 / 255, 153 / 255],\n\t\"red\": [1, 0, 0],\n\t\"rosybrown\": [188 / 255, 143 / 255, 143 / 255],\n\t\"royalblue\": [65 / 255, 105 / 255, 225 / 255],\n\t\"saddlebrown\": [139 / 255, 69 / 255, 19 / 255],\n\t\"salmon\": [250 / 255, 128 / 255, 114 / 255],\n\t\"sandybrown\": [244 / 255, 164 / 255, 96 / 255],\n\t\"seagreen\": [46 / 255, 139 / 255, 87 / 255],\n\t\"seashell\": [1, 245 / 255, 238 / 255],\n\t\"sienna\": [160 / 255, 82 / 255, 45 / 255],\n\t\"silver\": [192 / 255, 192 / 255, 192 / 255],\n\t\"skyblue\": [135 / 255, 206 / 255, 235 / 255],\n\t\"slateblue\": [106 / 255, 90 / 255, 205 / 255],\n\t\"slategray\": [112 / 255, 128 / 255, 144 / 255],\n\t\"slategrey\": [112 / 255, 128 / 255, 144 / 255],\n\t\"snow\": [1, 250 / 255, 250 / 255],\n\t\"springgreen\": [0, 1, 127 / 255],\n\t\"steelblue\": [70 / 255, 130 / 255, 180 / 255],\n\t\"tan\": [210 / 255, 180 / 255, 140 / 255],\n\t\"teal\": [0, 128 / 255, 128 / 255],\n\t\"thistle\": [216 / 255, 191 / 255, 216 / 255],\n\t\"tomato\": [1, 99 / 255, 71 / 255],\n\t\"turquoise\": [64 / 255, 224 / 255, 208 / 255],\n\t\"violet\": [238 / 255, 130 / 255, 238 / 255],\n\t\"wheat\": [245 / 255, 222 / 255, 179 / 255],\n\t\"white\": [1, 1, 1],\n\t\"whitesmoke\": [245 / 255, 245 / 255, 245 / 255],\n\t\"yellow\": [1, 1, 0],\n\t\"yellowgreen\": [154 / 255, 205 / 255, 50 / 255],\n};\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport sRGBLinear from \"./srgb-linear.js\";\nimport KEYWORDS from \"../keywords.js\";\n\nlet coordGrammar = Array(3).fill(\"<percentage> | <number>[0, 255]\");\nlet coordGrammarNumber = Array(3).fill(\"<number>[0, 255]\");\n\nexport default new RGBColorSpace({\n\tid: \"srgb\",\n\tname: \"sRGB\",\n\tbase: sRGBLinear,\n\tfromBase: rgb => {\n\t\t// convert an array of linear-light sRGB values in the range 0.0-1.0\n\t\t// to gamma corrected form\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs > 0.0031308) {\n\t\t\t\treturn sign * (1.055 * (abs ** (1 / 2.4)) - 0.055);\n\t\t\t}\n\n\t\t\treturn 12.92 * val;\n\t\t});\n\t},\n\ttoBase: rgb => {\n\t\t// convert an array of sRGB values in the range 0.0 - 1.0\n\t\t// to linear light (un-companded) form.\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs <= 0.04045) {\n\t\t\t\treturn val / 12.92;\n\t\t\t}\n\n\t\t\treturn sign * (((abs + 0.055) / 1.055) ** 2.4);\n\t\t});\n\t},\n\tformats: {\n\t\t\"rgb\": {\n\t\t\tcoords: coordGrammar,\n\t\t},\n\t\t\"rgb_number\": {\n\t\t\tname: \"rgb\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t\tnoAlpha: true,\n\t\t},\n\t\t\"color\": { /* use defaults */ },\n\t\t\"rgba\": {\n\t\t\tcoords: coordGrammar,\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t\t\"rgba_number\": {\n\t\t\tname: \"rgba\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t},\n\t\t\"hex\": {\n\t\t\ttype: \"custom\",\n\t\t\ttoGamut: true,\n\t\t\ttest: str => /^#([a-f0-9]{3,4}){1,2}$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tif (str.length <= 5) {\n\t\t\t\t\t// #rgb or #rgba, duplicate digits\n\t\t\t\t\tstr = str.replace(/[a-f0-9]/gi, \"$&$&\");\n\t\t\t\t}\n\n\t\t\t\tlet rgba = [];\n\t\t\t\tstr.replace(/[a-f0-9]{2}/gi, component => {\n\t\t\t\t\trgba.push(parseInt(component, 16) / 255);\n\t\t\t\t});\n\n\t\t\t\treturn {\n\t\t\t\t\tspaceId: \"srgb\",\n\t\t\t\t\tcoords: rgba.slice(0, 3),\n\t\t\t\t\talpha: rgba.slice(3)[0],\n\t\t\t\t};\n\t\t\t},\n\t\t\tserialize: (coords, alpha, {\n\t\t\t\tcollapse = true, // collapse to 3-4 digit hex when possible?\n\t\t\t} = {}) => {\n\t\t\t\tif (alpha < 1) {\n\t\t\t\t\tcoords.push(alpha);\n\t\t\t\t}\n\n\t\t\t\tcoords = coords.map(c => Math.round(c * 255));\n\n\t\t\t\tlet collapsible = collapse && coords.every(c => c % 17 === 0);\n\n\t\t\t\tlet hex = coords.map(c => {\n\t\t\t\t\tif (collapsible) {\n\t\t\t\t\t\treturn (c / 17).toString(16);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn c.toString(16).padStart(2, \"0\");\n\t\t\t\t}).join(\"\");\n\n\t\t\t\treturn \"#\" + hex;\n\t\t\t},\n\t\t},\n\t\t\"keyword\": {\n\t\t\ttype: \"custom\",\n\t\t\ttest: str => /^[a-z]+$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tstr = str.toLowerCase();\n\t\t\t\tlet ret = {spaceId: \"srgb\", coords: null, alpha: 1};\n\n\t\t\t\tif (str === \"transparent\") {\n\t\t\t\t\tret.coords = KEYWORDS.black;\n\t\t\t\t\tret.alpha = 0;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tret.coords = KEYWORDS[str];\n\t\t\t\t}\n\n\t\t\t\tif (ret.coords) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport P3Linear from \"./p3-linear.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new RGBColorSpace({\n\tid: \"p3\",\n\tcssId: \"display-p3\",\n\tname: \"P3\",\n\tbase: P3Linear,\n\t// Gamma encoding/decoding is the same as sRGB\n\tfromBase: sRGB.fromBase,\n\ttoBase: sRGB.toBase,\n});\n", "import { isNone, skipNone } from \"./util.js\";\nimport defaults from \"./defaults.js\";\nimport to from \"./to.js\";\nimport serialize from \"./serialize.js\";\nimport clone from \"./clone.js\";\nimport REC2020 from \"./spaces/rec2020.js\";\nimport P3 from \"./spaces/p3.js\";\nimport Lab from \"./spaces/lab.js\";\nimport sRGB from \"./spaces/srgb.js\";\n\n// Default space for CSS output. Code in Color.js makes this wider if there's a DOM available\ndefaults.display_space = sRGB;\n\nlet supportsNone;\n\nif (typeof CSS !== \"undefined\" && CSS.supports) {\n\t// Find widest supported color space for CSS\n\tfor (let space of [Lab, REC2020, P3]) {\n\t\tlet coords = space.getMinCoords();\n\t\tlet color = {space, coords, alpha: 1};\n\t\tlet str = serialize(color);\n\n\t\tif (CSS.supports(\"color\", str)) {\n\t\t\tdefaults.display_space = space;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\n/**\n * Returns a serialization of the color that can actually be displayed in the browser.\n * If the default serialization can be displayed, it is returned.\n * Otherwise, the color is converted to Lab, REC2020, or P3, whichever is the widest supported.\n * In Node.js, this is basically equivalent to `serialize()` but returns a `String` object instead.\n *\n * @export\n * @param {{space, coords} | Color | string} color\n * @param {*} [options={}] Options to be passed to serialize()\n * @param {ColorSpace | string} [options.space = defaults.display_space] Color space to use for serialization if default is not supported\n * @returns {String} String object containing the serialized color with a color property containing the converted color (or the original, if no conversion was necessary)\n */\nexport default function display (color, {space = defaults.display_space, ...options} = {}) {\n\tlet ret = serialize(color, options);\n\n\tif (typeof CSS === \"undefined\" || CSS.supports(\"color\", ret) || !defaults.display_space) {\n\t\tret = new String(ret);\n\t\tret.color = color;\n\t}\n\telse {\n\t\t// If we're here, what we were about to output is not supported\n\t\tlet fallbackColor = color;\n\n\t\t// First, check if the culprit is none values\n\t\tlet hasNone = color.coords.some(isNone) || isNone(color.alpha);\n\n\t\tif (hasNone) {\n\t\t\t// Does the browser support none values?\n\t\t\tif (!(supportsNone ??= CSS.supports(\"color\", \"hsl(none 50% 50%)\"))) {\n\t\t\t\t// Nope, try again without none\n\t\t\t\tfallbackColor = clone(color);\n\t\t\t\tfallbackColor.coords = fallbackColor.coords.map(skipNone);\n\t\t\t\tfallbackColor.alpha = skipNone(fallbackColor.alpha);\n\n\t\t\t\tret = serialize(fallbackColor, options);\n\n\t\t\t\tif (CSS.supports(\"color\", ret)) {\n\t\t\t\t\t// We're done, now it's supported\n\t\t\t\t\tret = new String(ret);\n\t\t\t\t\tret.color = fallbackColor;\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// If we're here, the color function is not supported\n\t\t// Fall back to fallback space\n\t\tfallbackColor = to(fallbackColor, space);\n\t\tret = new String(serialize(fallbackColor, options));\n\t\tret.color = fallbackColor;\n\t}\n\n\treturn ret;\n}\n", "/**\n * Relative luminance\n */\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport xyz_d65 from \"./spaces/xyz-d65.js\";\n\nexport function getLuminance (color) {\n\t// Assume getColor() is called on color in get()\n\treturn get(color, [xyz_d65, \"y\"]);\n}\n\nexport function setLuminance (color, value) {\n\t// Assume getColor() is called on color in set()\n\tset(color, [xyz_d65, \"y\"], value);\n}\n\nexport function register (Color) {\n\tObject.defineProperty(Color.prototype, \"luminance\", {\n\t\tget () {\n\t\t\treturn getLuminance(this);\n\t\t},\n\t\tset (value) {\n\t\t\tsetLuminance(this, value);\n\t\t},\n\t});\n}\n", "// APCA 0.0.98G\n// https://github.com/Myndex/apca-w3\n// see also https://github.com/w3c/silver/issues/643\n\nimport getColor from \"../getColor.js\";\nimport to from \"../to.js\";\n\n// exponents\nconst normBG = 0.56;\nconst normTXT = 0.57;\nconst revTXT = 0.62;\nconst revBG = 0.65;\n\n// clamps\nconst blkThrs = 0.022;\nconst blkClmp = 1.414;\nconst loClip = 0.1;\nconst deltaYmin = 0.0005;\n\n// scalers\n// see https://github.com/w3c/silver/issues/645\nconst scaleBoW = 1.14;\nconst loBoWoffset = 0.027;\nconst scaleWoB = 1.14;\nconst loWoBoffset = 0.027;\n\nfunction fclamp (Y) {\n\tif (Y >= blkThrs) {\n\t\treturn Y;\n\t}\n\treturn Y + (blkThrs - Y) ** blkClmp;\n}\n\nfunction linearize (val) {\n\tlet sign = val < 0 ? -1 : 1;\n\tlet abs = Math.abs(val);\n\treturn sign * Math.pow(abs, 2.4);\n}\n\n// Not symmetric, requires a foreground (text) color, and a background color\nexport default function contrastAPCA (background, foreground) {\n\tforeground = getColor(foreground);\n\tbackground = getColor(background);\n\n\tlet S;\n\tlet C;\n\tlet Sapc;\n\n\t// Myndex as-published, assumes sRGB inputs\n\tlet R, G, B;\n\n\tforeground = to(foreground, \"srgb\");\n\t// Should these be clamped to in-gamut values?\n\n\t// Calculates \"screen luminance\" with non-standard simple gamma EOTF\n\t// weights should be from CSS Color 4, not the ones here which are via Myndex and copied from Lindbloom\n\t[R, G, B] = foreground.coords;\n\tlet lumTxt = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\tbackground = to(background, \"srgb\");\n\t[R, G, B] = background.coords;\n\tlet lumBg = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\t// toe clamping of very dark values to account for flare\n\tlet Ytxt = fclamp(lumTxt);\n\tlet Ybg = fclamp(lumBg);\n\n\t// are we \"Black on White\" (dark on light), or light on dark?\n\tlet BoW = Ybg > Ytxt;\n\n\t// why is this a delta, when Y is not perceptually uniform?\n\t// Answer: it is a noise gate, see\n\t// https://github.com/LeaVerou/color.js/issues/208\n\tif (Math.abs(Ybg - Ytxt) < deltaYmin) {\n\t\tC = 0;\n\t}\n\telse {\n\t\tif (BoW) {\n\t\t\t// dark text on light background\n\t\t\tS = Ybg ** normBG - Ytxt ** normTXT;\n\t\t\tC = S * scaleBoW;\n\t\t}\n\t\telse {\n\t\t\t// light text on dark background\n\t\t\tS = Ybg ** revBG - Ytxt ** revTXT;\n\t\t\tC = S * scaleWoB;\n\t\t}\n\t}\n\tif (Math.abs(C) < loClip) {\n\t\tSapc = 0;\n\t}\n\telse if (C > 0) {\n\t\t// not clear whether Woffset is loBoWoffset or loWoBoffset\n\t\t// but they have the same value\n\t\tSapc = C - loBoWoffset;\n\t}\n\telse {\n\t\tSapc = C + loBoWoffset;\n\t}\n\n\treturn Sapc * 100;\n}\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D65;\n\nexport default new ColorSpace({\n\tid: \"lab-d65\",\n\tname: \"Lab D65\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D65, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d65,\n\t// Convert D65-adapted XYZ to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D65-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab-d65\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// Delta Phi Star perceptual lightness contrast\n// See https://github.com/Myndex/deltaphistar\n// The (difference between two Lstars each raised to phi) raised to (1/phi)\n// Symmetric, does not matter which is foreground and which is background\n\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab_d65 from \"../spaces/lab-d65.js\";\n\nconst phi = Math.pow(5, 0.5) * 0.5 + 0.5; // Math.phi can be used if Math.js\n\nexport default function contrastDeltaPhi (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Lstr1 = get(color1, [lab_d65, \"l\"]);\n\tlet Lstr2 = get(color2, [lab_d65, \"l\"]);\n\n\tlet deltaPhiStar = Math.abs(Math.pow(Lstr1, phi) - Math.pow(Lstr2, phi));\n\n\tlet contrast = Math.pow(deltaPhiStar, (1 / phi)) * Math.SQRT2 - 40;\n\n\treturn (contrast < 7.5) ? 0.0 : contrast ;\n}\n", "// CIE Lightness difference, as used by Google Material Design\n// Google HCT Tone is the same as CIE Lightness\n// https://material.io/blog/science-of-color-design\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab from \"../spaces/lab.js\";\n\nexport default function contrastLstar (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet L1 = get(color1, [lab, \"l\"]);\n\tlet L2 = get(color2, [lab, \"l\"]);\n\n\treturn Math.abs(L1 - L2);\n}\n", "// Michelson  luminance contrast\n// the relation between the spread and the sum of the two luminances\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrast<PERSON><PERSON><PERSON> (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\tlet denom = (Y1 + Y2);\n\treturn denom === 0 ? 0 : (Y1 - Y2) / denom;\n}\n", "// WCAG 2.0 contrast https://www.w3.org/TR/WCAG20-TECHS/G18.html\n// Simple contrast, with fixed 5% viewing flare contribution\n// Symmetric, does not matter which is foreground and which is background\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrastWCAG21 (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn (Y1 + .05) / (Y2 + .05);\n}\n", "// Weber luminance contrast\n// The difference between the two luminances divided by the lower luminance\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\n// the darkest sRGB color above black is #000001 and this produces\n// a plain Weber contrast of ~45647.\n// So, setting the divide-by-zero result at 50000 is a reasonable\n// max clamp for the plain Weber\nconst max = 50000;\n\nexport default function contrastWeber (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn Y2 === 0 ? max : (Y1 - Y2) / Y2;\n}\n", "import xyz_d65 from \"./spaces/xyz-d65.js\";\nimport getAll from \"./getAll.js\";\n\n// Chromaticity coordinates\nexport function uv (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet denom = X + 15 * Y + 3 * Z;\n\treturn [4 * X / denom, 9 * Y / denom];\n}\n\nexport function xy (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet  sum = X + Y + Z;\n\treturn [X / sum, Y / sum];\n}\n\nexport function register (Color) {\n\t// no setters, as lightness information is lost\n\t// when converting color to chromaticity\n\tObject.defineProperty(Color.prototype, \"uv\", {\n\t\tget () {\n\t\t\treturn uv(this);\n\t\t},\n\t});\n\n\tObject.defineProperty(Color.prototype, \"xy\", {\n\t\tget () {\n\t\t\treturn xy(this);\n\t\t},\n\t});\n}\n", "import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nexport default function deltaE (c1, c2, o = {}) {\n\tif (isString(o)) {\n\t\to = {method: o};\n\t}\n\n\tlet {method = defaults.deltaE, ...rest} = o;\n\n\tfor (let m in deltaEMethods) {\n\t\tif (\"deltae\" + method.toLowerCase() === m.toLowerCase()) {\n\t\t\treturn deltaEMethods[m](c1, c2, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown deltaE method: ${method}`);\n}\n", "import ColorSpace from \"./space.js\";\nimport set from \"./set.js\";\n\nexport function lighten (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 + amount));\n}\n\nexport function darken (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 - amount));\n}\n", "/**\n * Functions related to color interpolation\n */\nimport ColorSpace from \"./space.js\";\nimport {type, interpolate} from \"./util.js\";\nimport getColor from \"./getColor.js\";\nimport clone from \"./clone.js\";\nimport to from \"./to.js\";\nimport toGamut from \"./toGamut.js\";\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport defaults from \"./defaults.js\";\nimport * as angles from \"./angles.js\";\nimport deltaE from \"./deltaE.js\";\n\n/**\n * Return an intermediate color between two colors\n * Signatures: mix(c1, c2, p, options)\n *             mix(c1, c2, options)\n *             mix(color)\n * @param {Color | string} c1 The first color\n * @param {Color | string} [c2] The second color\n * @param {number} [p=.5] A 0-1 percentage where 0 is c1 and 1 is c2\n * @param {Object} [o={}]\n * @return {Color}\n */\nexport function mix (c1, c2, p = .5, o = {}) {\n\t[c1, c2] = [getColor(c1), getColor(c2)];\n\n\tif (type(p) === \"object\") {\n\t\t[p, o] = [.5, p];\n\t}\n\n\tlet r = range(c1, c2, o);\n\treturn r(p);\n}\n\n/**\n *\n * @param {Color | string | Function} c1 The first color or a range\n * @param {Color | string} [c2] The second color if c1 is not a range\n * @param {Object} [options={}]\n * @return {Color[]}\n */\nexport function steps (c1, c2, options = {}) {\n\tlet colorRange;\n\n\tif (isRange(c1)) {\n\t\t// Tweaking existing range\n\t\t[colorRange, options] = [c1, c2];\n\t\t[c1, c2] = colorRange.rangeArgs.colors;\n\t}\n\n\tlet {\n\t\tmaxDeltaE, deltaEMethod,\n\t\tsteps = 2, maxSteps = 1000,\n\t\t...rangeOptions\n\t} = options;\n\n\tif (!colorRange) {\n\t\t[c1, c2] = [getColor(c1), getColor(c2)];\n\t\tcolorRange = range(c1, c2, rangeOptions);\n\t}\n\n\tlet totalDelta = deltaE(c1, c2);\n\tlet actualSteps = maxDeltaE > 0 ? Math.max(steps, Math.ceil(totalDelta / maxDeltaE) + 1) : steps;\n\tlet ret = [];\n\n\tif (maxSteps !== undefined) {\n\t\tactualSteps = Math.min(actualSteps, maxSteps);\n\t}\n\n\tif (actualSteps === 1) {\n\t\tret = [{p: .5, color: colorRange(.5)}];\n\t}\n\telse {\n\t\tlet step = 1 / (actualSteps - 1);\n\t\tret = Array.from({length: actualSteps}, (_, i) => {\n\t\t\tlet p = i * step;\n\t\t\treturn {p, color: colorRange(p)};\n\t\t});\n\t}\n\n\tif (maxDeltaE > 0) {\n\t\t// Iterate over all stops and find max deltaE\n\t\tlet maxDelta = ret.reduce((acc, cur, i) => {\n\t\t\tif (i === 0) {\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tlet ΔΕ = deltaE(cur.color, ret[i - 1].color, deltaEMethod);\n\t\t\treturn Math.max(acc, ΔΕ);\n\t\t}, 0);\n\n\t\twhile (maxDelta > maxDeltaE) {\n\t\t\t// Insert intermediate stops and measure maxDelta again\n\t\t\t// We need to do this for all pairs, otherwise the midpoint shifts\n\t\t\tmaxDelta = 0;\n\n\t\t\tfor (let i = 1; (i < ret.length) && (ret.length < maxSteps); i++) {\n\t\t\t\tlet prev = ret[i - 1];\n\t\t\t\tlet cur = ret[i];\n\n\t\t\t\tlet p = (cur.p + prev.p) / 2;\n\t\t\t\tlet color = colorRange(p);\n\t\t\t\tmaxDelta = Math.max(maxDelta, deltaE(color, prev.color), deltaE(color, cur.color));\n\t\t\t\tret.splice(i, 0, {p, color: colorRange(p)});\n\t\t\t\ti++;\n\t\t\t}\n\t\t}\n\t}\n\n\tret = ret.map(a => a.color);\n\n\treturn ret;\n}\n\n/**\n * Interpolate to color2 and return a function that takes a 0-1 percentage\n * @param {Color | string | Function} color1 The first color or an existing range\n * @param {Color | string} [color2] If color1 is a color, this is the second color\n * @param {Object} [options={}]\n * @returns {Function} A function that takes a 0-1 percentage and returns a color\n */\nexport function range (color1, color2, options = {}) {\n\tif (isRange(color1)) {\n\t\t// Tweaking existing range\n\t\tlet [r, options] = [color1, color2];\n\n\t\treturn range(...r.rangeArgs.colors, {...r.rangeArgs.options, ...options});\n\t}\n\n\tlet {space, outputSpace, progression, premultiplied} = options;\n\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\t// Make sure we're working on copies of these colors\n\tcolor1 = clone(color1);\n\tcolor2 = clone(color2);\n\n\tlet rangeArgs = {colors: [color1, color2], options};\n\n\tif (space) {\n\t\tspace = ColorSpace.get(space);\n\t}\n\telse {\n\t\tspace = ColorSpace.registry[defaults.interpolationSpace] || color1.space;\n\t}\n\n\toutputSpace = outputSpace ? ColorSpace.get(outputSpace) : space;\n\n\tcolor1 = to(color1, space);\n\tcolor2 = to(color2, space);\n\n\t// Gamut map to avoid areas of flat color\n\tcolor1 = toGamut(color1);\n\tcolor2 = toGamut(color2);\n\n\t// Handle hue interpolation\n\t// See https://github.com/w3c/csswg-drafts/issues/4735#issuecomment-635741840\n\tif (space.coords.h && space.coords.h.type === \"angle\") {\n\t\tlet arc = options.hue = options.hue || \"shorter\";\n\n\t\tlet hue = [space, \"h\"];\n\t\tlet [θ1, θ2] = [get(color1, hue), get(color2, hue)];\n\t\t// Undefined hues must be evaluated before hue fix-up to properly\n\t\t// calculate hue arcs between undefined and defined hues.\n\t\t// See https://github.com/w3c/csswg-drafts/issues/9436#issuecomment-1746957545\n\t\tif (isNaN(θ1) && !isNaN(θ2)) {\n\t\t\tθ1 = θ2;\n\t\t}\n\t\telse if (isNaN(θ2) && !isNaN(θ1)) {\n\t\t\tθ2 = θ1;\n\t\t}\n\t\t[θ1, θ2] = angles.adjust(arc, [θ1, θ2]);\n\t\tset(color1, hue, θ1);\n\t\tset(color2, hue, θ2);\n\t}\n\n\tif (premultiplied) {\n\t\t// not coping with polar spaces yet\n\t\tcolor1.coords = color1.coords.map(c => c * color1.alpha);\n\t\tcolor2.coords = color2.coords.map(c => c * color2.alpha);\n\t}\n\n\treturn Object.assign(p => {\n\t\tp = progression ? progression(p) : p;\n\t\tlet coords = color1.coords.map((start, i) => {\n\t\t\tlet end = color2.coords[i];\n\t\t\treturn interpolate(start, end, p);\n\t\t});\n\n\t\tlet alpha = interpolate(color1.alpha, color2.alpha, p);\n\t\tlet ret = {space, coords, alpha};\n\n\t\tif (premultiplied) {\n\t\t\t// undo premultiplication\n\t\t\tret.coords = ret.coords.map(c => c / alpha);\n\t\t}\n\n\t\tif (outputSpace !== space) {\n\t\t\tret = to(ret, outputSpace);\n\t\t}\n\n\t\treturn ret;\n\t}, {\n\t\trangeArgs,\n\t});\n}\n\nexport function isRange (val) {\n\treturn type(val) === \"function\" && !!val.rangeArgs;\n}\n\ndefaults.interpolationSpace = \"lab\";\n\nexport function register (Color) {\n\tColor.defineFunction(\"mix\", mix, {returns: \"color\"});\n\tColor.defineFunction(\"range\", range, {returns: \"function<color>\"});\n\tColor.defineFunction(\"steps\", steps, {returns: \"array<color>\"});\n}\n", "import ColorSpace from \"../space.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new ColorSpace({\n\tid: \"hsl\",\n\tname: \"H<PERSON>\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: sRGB,\n\n\t// Adapted from https://drafts.csswg.org/css-color-4/better-rgbToHsl.js\n\tfromBase: rgb => {\n\t\tlet max = Math.max(...rgb);\n\t\tlet min = Math.min(...rgb);\n\t\tlet [r, g, b] = rgb;\n\t\tlet [h, s, l] = [NaN, 0, (min + max) / 2];\n\t\tlet d = max - min;\n\n\t\tif (d !== 0) {\n\t\t\ts = (l === 0 || l === 1) ? 0 : (max - l) / Math.min(l, 1 - l);\n\n\t\t\tswitch (max) {\n\t\t\t\tcase r: h = (g - b) / d + (g < b ? 6 : 0); break;\n\t\t\t\tcase g: h = (b - r) / d + 2; break;\n\t\t\t\tcase b: h = (r - g) / d + 4;\n\t\t\t}\n\n\t\t\th = h * 60;\n\t\t}\n\n\t\t// Very out of gamut colors can produce negative saturation\n\t\t// If so, just rotate the hue by 180 and use a positive saturation\n\t\t// see https://github.com/w3c/csswg-drafts/issues/9222\n\t\tif (s < 0) {\n\t\t\th += 180;\n\t\t\ts = Math.abs(s);\n\t\t}\n\n\t\tif (h >= 360) {\n\t\t\th -= 360;\n\t\t}\n\n\t\treturn [h, s * 100, l * 100];\n\t},\n\n\t// Adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_RGB_alternative\n\ttoBase: hsl => {\n\t\tlet [h, s, l] = hsl;\n\t\th = h % 360;\n\n\t\tif (h < 0) {\n\t\t\th += 360;\n\t\t}\n\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tfunction f (n) {\n\t\t\tlet k = (n + h / 30) % 12;\n\t\t\tlet a = s * Math.min(l, 1 - l);\n\t\t\treturn l - a * Math.max(-1, Math.min(k - 3, 9 - k, 1));\n\t\t}\n\n\t\treturn [f(0), f(8), f(4)];\n\t},\n\n\tformats: {\n\t\t\"hsl\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t},\n\t\t\"hsla\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSL from \"./hsl.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hsv\",\n\tname: \"HSV\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tv: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Value\",\n\t\t},\n\t},\n\n\tbase: HSL,\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\tfromBase (hsl) {\n\t\tlet [h, s, l] = hsl;\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tlet v = l + s * Math.min(l, 1 - l);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\tv === 0 ? 0 : 200 * (1 - l / v), // s\n\t\t\t100 * v,\n\t\t];\n\t},\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\ttoBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\ts /= 100;\n\t\tv /= 100;\n\n\t\tlet l = v * (1 - s / 2);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\t(l === 0 || l === 1) ? 0 : ((v - l) / Math.min(l, 1 - l)) * 100,\n\t\t\tl * 100,\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSV from \"./hsv.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hwb\",\n\tname: \"HWB\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\tw: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Whiteness\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Blackness\",\n\t\t},\n\t},\n\n\tbase: HSV,\n\tfromBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\treturn [h, v * (100 - s) / 100, 100 - v];\n\t},\n\ttoBase (hwb) {\n\t\tlet [h, w, b] = hwb;\n\n\t\t// Now convert percentages to [0..1]\n\t\tw /= 100;\n\t\tb /= 100;\n\n\t\t// Achromatic check (white plus black >= 1)\n\t\tlet sum = w + b;\n\t\tif (sum >= 1) {\n\t\t\tlet gray = w / sum;\n\t\t\treturn [h, 0, gray * 100];\n\t\t}\n\n\t\tlet v = (1 - b);\n\t\tlet s = (v === 0) ? 0 : 1 - w / v;\n\t\treturn [h, s * 100, v * 100];\n\t},\n\n\tformats: {\n\t\t\"hwb\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light a98-rgb values to CIE XYZ\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// has greater numerical precision than section ******* of\n// https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf\n// but the values below were calculated from first principles\n// from the chromaticity coordinates of R G B W\nconst toXYZ_M = [\n\t[ 0.5766690429101305,   0.1855582379065463,   0.1882286462349947  ],\n\t[ 0.29734497525053605,  0.6273635662554661,   0.07529145849399788 ],\n\t[ 0.02703136138641234,  0.07068885253582723,  0.9913375368376388  ],\n];\n\nconst fromXYZ_M = [\n\t[  2.0415879038107465,    -0.5650069742788596,   -0.34473135077832956 ],\n\t[ -0.9692436362808795,     1.8759675015077202,    0.04155505740717557 ],\n\t[  0.013444280632031142,  -0.11836239223101838,   1.0151749943912054  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb-linear\",\n\tcssId: \"--a98-rgb-linear\",\n\tname: \"Linear Adobe® 98 RGB compatible\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport A98Linear from \"./a98rgb-linear.js\";\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb\",\n\tcssId: \"a98-rgb\",\n\tname: \"Adobe® 98 RGB compatible\",\n\tbase: A98Linear,\n\ttoBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 563 / 256) * Math.sign(val)),\n\tfromBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 256 / 563) * Math.sign(val)),\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport XYZ_D50 from \"./xyz-d50.js\";\n\n// convert an array of  prophoto-rgb values to CIE XYZ\n// using  D50 (so no chromatic adaptation needed afterwards)\n// matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy\n// see https://github.com/w3c/csswg-drafts/issues/7675\nconst toXYZ_M = [\n\t[ 0.79776664490064230,  0.13518129740053308,  0.03134773412839220 ],\n\t[ 0.28807482881940130,  0.71183523424187300,  0.00008993693872564 ],\n\t[ 0.00000000000000000,  0.00000000000000000,  0.82510460251046020 ],\n];\n\nconst fromXYZ_M = [\n\t[  1.34578688164715830, -0.25557208737979464, -0.05110186497554526 ],\n\t[ -0.54463070512490190,  1.50824774284514680,  0.02052744743642139 ],\n\t[  0.00000000000000000,  0.00000000000000000,  1.21196754563894520 ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"prophoto-linear\",\n\tcssId: \"--prophoto-rgb-linear\",\n\tname: \"Linear ProPhoto\",\n\twhite: \"D50\",\n\tbase: XYZ_D50,\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport ProPhotoLinear from \"./prophoto-linear.js\";\n\nconst Et = 1 / 512;\nconst Et2 = 16 / 512;\n\nexport default new RGBColorSpace({\n\tid: \"prophoto\",\n\tcssId: \"prophoto-rgb\",\n\tname: \"ProPhoto\",\n\tbase: ProPhotoLinear,\n\ttoBase (RGB) {\n\t\t// Transfer curve is gamma 1.8 with a small linear portion\n\t\treturn RGB.map(v => v < Et2 ? v / 16 : v ** 1.8);\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(v => v >= Et ? v ** (1 / 1.8) : 16 * v);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport OKLab from \"./oklab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"oklch\",\n\tname: \"Oklch\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 0.4],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\twhite: \"D65\",\n\n\tbase: OKLab,\n\tfromBase (oklab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = oklab;\n\t\tlet h;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\th = NaN;\n\t\t}\n\t\telse {\n\t\t\th = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // OKLab L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(h), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\t// Convert from polar form\n\ttoBase (oklch) {\n\t\tlet [L, C, h] = oklch;\n\t\tlet a, b;\n\n\t\t// check for NaN hue\n\t\tif (isNaN(h)) {\n\t\t\ta = 0;\n\t\t\tb = 0;\n\t\t}\n\t\telse {\n\t\t\ta = C * Math.cos(h * Math.PI / 180);\n\t\t\tb = C * Math.sin(h * Math.PI / 180);\n\t\t}\n\n\t\treturn [ L, a, b ];\n\t},\n\n\tformats: {\n\t\t\"oklch\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[0,1]\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {uv} from \"../chromaticity.js\";\nimport {isNone, skipNone} from \"../util.js\";\n\nlet white = WHITES.D65;\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\nconst [U_PRIME_WHITE, V_PRIME_WHITE] = uv({space: xyz_d65, coords: white});\n\nexport default new ColorSpace({\n\tid: \"luv\",\n\tname: \"Luv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\t// Reference ranges from https://facelessuser.github.io/coloraide/colors/luv/\n\t\tu: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t\tv: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t},\n\n\twhite: white,\n\tbase: xyz_d65,\n\n\t// Convert D65-adapted XYZ to Luv\n\t// https://en.wikipedia.org/wiki/CIELUV#The_forward_transformation\n\tfromBase (XYZ) {\n\t\tlet xyz = [skipNone(XYZ[0]), skipNone(XYZ[1]), skipNone(XYZ[2])];\n\t\tlet y = xyz[1];\n\n\t\tlet [up, vp] = uv({space: xyz_d65, coords: xyz});\n\n\t\t// Protect against XYZ of [0, 0, 0]\n\t\tif (!Number.isFinite(up) || !Number.isFinite(vp)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tlet L = y <= ε ? κ * y : 116 * Math.cbrt(y) - 16;\n\t\treturn [\n\t\t\tL,\n\t\t\t13 * L * (up - U_PRIME_WHITE),\n\t\t\t13 * L * (vp - V_PRIME_WHITE),\n\t\t ];\n\t},\n\n\t// Convert Luv to D65-adapted XYZ\n\t// https://en.wikipedia.org/wiki/CIELUV#The_reverse_transformation\n\ttoBase (Luv) {\n\t\tlet [L, u, v] = Luv;\n\n\t\t// Protect against division by zero and NaN Lightness\n\t\tif (L === 0 || isNone(L)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tu = skipNone(u);\n\t\tv = skipNone(v);\n\n\t\tlet up = (u / (13 * L)) + U_PRIME_WHITE;\n\t\tlet vp = (v / (13 * L)) + V_PRIME_WHITE;\n\n\t\tlet y = L <= 8 ? L / κ : Math.pow((L + 16) / 116, 3);\n\n\t\treturn [\n\t\t\ty * ((9 * up) / (4 * vp)),\n\t\t\ty,\n\t\t\ty * ((12 - 3 * up - 20 * vp) / (4 * vp)),\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--luv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport Luv from \"./luv.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lchuv\",\n\tname: \"<PERSON>Chuv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 220],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Luv,\n\tfromBase (Luv) {\n\t\t// Convert to polar form\n\t\tlet [L, u, v] = Luv;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(u) < ε && Math.abs(v) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(v, u) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(u ** 2 + v ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // u\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // v\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--lchuv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport sRGB from \"./srgb.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOriginAngle (slope, intercept, angle) {\n\tconst d = intercept / (Math.sin(angle) - slope * Math.cos(angle));\n\treturn d < 0 ? Infinity : d;\n}\n\nexport function calculateBoundingLines (l) {\n\tconst sub1 = Math.pow(l + 16, 3) / 1560896;\n\tconst sub2 = sub1 > ε ? sub1 : l / κ;\n\tconst s1r = sub2 * (284517 * m_r0 - 94839 * m_r2);\n\tconst s2r = sub2 * (838422 * m_r2 + 769860 * m_r1 + 731718 * m_r0);\n\tconst s3r = sub2 * (632260 * m_r2 - 126452 * m_r1);\n\tconst s1g = sub2 * (284517 * m_g0 - 94839 * m_g2);\n\tconst s2g = sub2 * (838422 * m_g2 + 769860 * m_g1 + 731718 * m_g0);\n\tconst s3g = sub2 * (632260 * m_g2 - 126452 * m_g1);\n\tconst s1b = sub2 * (284517 * m_b0 - 94839 * m_b2);\n\tconst s2b = sub2 * (838422 * m_b2 + 769860 * m_b1 + 731718 * m_b0);\n\tconst s3b = sub2 * (632260 * m_b2 - 126452 * m_b1);\n\n\treturn {\n\t\tr0s: s1r / s3r,\n\t\tr0i: s2r * l / s3r,\n\t\tr1s: s1r / (s3r + 126452),\n\t\tr1i: (s2r - 769860) * l / (s3r + 126452),\n\t\tg0s: s1g / s3g,\n\t\tg0i: s2g * l / s3g,\n\t\tg1s: s1g / (s3g + 126452),\n\t\tg1i: (s2g - 769860) * l / (s3g + 126452),\n\t\tb0s: s1b / s3b,\n\t\tb0i: s2b * l / s3b,\n\t\tb1s: s1b / (s3b + 126452),\n\t\tb1i: (s2b - 769860) * l / (s3b + 126452),\n\t};\n}\n\nfunction calcMaxChromaHsluv (lines, h) {\n\tconst hueRad = h / 360 * Math.PI * 2;\n\tconst r0 = distanceFromOriginAngle(lines.r0s, lines.r0i, hueRad);\n\tconst r1 = distanceFromOriginAngle(lines.r1s, lines.r1i, hueRad);\n\tconst g0 = distanceFromOriginAngle(lines.g0s, lines.g0i, hueRad);\n\tconst g1 = distanceFromOriginAngle(lines.g1s, lines.g1i, hueRad);\n\tconst b0 = distanceFromOriginAngle(lines.b0s, lines.b0i, hueRad);\n\tconst b1 = distanceFromOriginAngle(lines.b1s, lines.b1i, hueRad);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hsluv\",\n\tname: \"HSLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: sRGB,\n\n\t// Convert LCHuv to HSLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\ts = c / max * 100;\n\t\t}\n\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HSLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\nimport {calculateBoundingLines} from \"./hsluv.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOrigin (slope, intercept) {\n\treturn Math.abs(intercept) / Math.sqrt(Math.pow(slope, 2) + 1);\n}\n\nfunction calcMaxChromaHpluv (lines) {\n\tlet r0 = distanceFromOrigin(lines.r0s, lines.r0i);\n\tlet r1 = distanceFromOrigin(lines.r1s, lines.r1i);\n\tlet g0 = distanceFromOrigin(lines.g0s, lines.g0i);\n\tlet g1 = distanceFromOrigin(lines.g1s, lines.g1i);\n\tlet b0 = distanceFromOrigin(lines.b0s, lines.b0i);\n\tlet b1 = distanceFromOrigin(lines.b1s, lines.b1i);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hpluv\",\n\tname: \"HPLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: \"self\",\n\n\t// Convert LCHuv to HPLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines);\n\t\t\ts = c / max * 100;\n\t\t}\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HPLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hpluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\nconst Yw = 203;\t// absolute luminance of media white, cd/m²\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst m = 2523 / (2 ** 5);\nconst minv = (2 ** 5) / 2523;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\n\nexport default new RGBColorSpace({\n\tid: \"rec2100pq\",\n\tcssId: \"rec2100-pq\",\n\tname: \"REC.2100-PQ\",\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given PQ encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = ((Math.max(((val ** minv) - c1), 0) / (c2 - (c3 * (val ** minv)))) ** ninv);\n\t\t\treturn (x * 10000 / Yw); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// returnPQ encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = Math.max(val * Yw / 10000, 0); \t// absolute luminance of peak white is 10,000 cd/m².\n\t\t\tlet num = (c1 + (c2 * (x ** n)));\n\t\t\tlet denom = (1 + (c3 * (x ** n)));\n\n\t\t\treturn ((num / denom)  ** m);\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\n// FIXME see https://github.com/LeaVerou/color.js/issues/190\n\nconst a = 0.17883277;\nconst b = 0.28466892; // 1 - (4 * a)\nconst c = 0.55991073; // 0.5 - a * Math.log(4 *a)\n\nconst scale = 3.7743;\t// Place 18% grey at HLG 0.38, so media white at 0.75\n\nexport default new RGBColorSpace({\n\tid: \"rec2100hlg\",\n\tcssId: \"rec2100-hlg\",\n\tname: \"REC.2100-HLG\",\n\treferred: \"scene\",\n\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given HLG encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\t// first the HLG EOTF\n\t\t\t// ITU-R BT.2390-10 p.30 section\n\t\t\t// 6.3 The hybrid log-gamma electro-optical transfer function (EOTF)\n\t\t\t// Then scale by 3 so media white is 1.0\n\t\t\tif (val <= 0.5) {\n\t\t\t\treturn (val ** 2) / 3 * scale;\n\t\t\t}\n\t\t\treturn ((Math.exp((val - c) / a) + b) / 12) * scale;\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// where diffuse white is 1.0,\n\t\t// return HLG encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\t// first scale to put linear-light media white at 1/3\n\t\t\tval /= scale;\n\t\t\t// now the HLG OETF\n\t\t\t// ITU-R BT.2390-10 p.23\n\t\t\t// 6.1 The hybrid log-gamma opto-electronic transfer function (OETF)\n\t\t\tif (val <= 1 / 12) {\n\t\t\t\treturn Math.sqrt(3 * val);\n\t\t\t}\n\t\t\treturn a * Math.log(12 * val - b) + c;\n\t\t});\n\t},\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport {WHITES} from \"./adapt.js\";\n\nexport const CATs = {};\n\nhooks.add(\"chromatic-adaptation-start\", env => {\n\tif (env.options.method) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nhooks.add(\"chromatic-adaptation-end\", env => {\n\tif (!env.M) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nexport function defineCAT ({id, toCone_M, fromCone_M}) {\n\t// Use id, toCone_M, fromCone_M like variables\n\tCATs[id] = arguments[0];\n}\n\nexport function adapt (W1, W2, id = \"Bradford\") {\n\t// adapt from a source whitepoint or illuminant W1\n\t// to a destination whitepoint or illuminant W2,\n\t// using the given chromatic adaptation transform (CAT)\n\t// debugger;\n\tlet method = CATs[id];\n\n\tlet [ρs, γs, βs] = multiplyMatrices(method.toCone_M, W1);\n\tlet [ρd, γd, βd] = multiplyMatrices(method.toCone_M, W2);\n\n\t// all practical illuminants have non-zero XYZ so no division by zero can occur below\n\tlet scale = [\n\t\t[ρd / ρs,  0,        0      ],\n\t\t[0,        γd / γs,  0      ],\n\t\t[0,        0,        βd / βs],\n\t];\n\t// console.log({scale});\n\n\tlet scaled_cone_M = multiplyMatrices(scale, method.toCone_M);\n\tlet adapt_M\t= multiplyMatrices(method.fromCone_M, scaled_cone_M);\n\t// console.log({scaled_cone_M, adapt_M});\n\treturn adapt_M;\n}\n\ndefineCAT({\n\tid: \"von Kries\",\n\ttoCone_M: [\n\t\t[  0.4002400,  0.7076000, -0.0808100 ],\n\t\t[ -0.2263000,  1.1653200,  0.0457000 ],\n\t\t[  0.0000000,  0.0000000,  0.9182200 ],\n\t],\n\tfromCone_M: [\n\t\t[ 1.8599363874558397, -1.1293816185800916,   0.21989740959619328     ],\n\t\t[ 0.3611914362417676,  0.6388124632850422,  -0.000006370596838649899 ],\n\t\t[ 0,                   0,                    1.0890636230968613      ],\n\t],\n});\n\ndefineCAT({\n\tid: \"Bradford\",\n\t// Convert an array of XYZ values in the range 0.0 - 1.0\n\t// to cone fundamentals\n\ttoCone_M: [\n\t\t[  0.8951000,  0.2664000, -0.1614000 ],\n\t\t[ -0.7502000,  1.7135000,  0.0367000 ],\n\t\t[  0.0389000, -0.0685000,  1.0296000 ],\n\t],\n\t// and back\n\tfromCone_M: [\n\t\t[  0.9869929054667121, -0.14705425642099013, 0.15996265166373122  ],\n\t\t[  0.4323052697233945,  0.5183602715367774,  0.049291228212855594 ],\n\t\t[ -0.00852866457517732, 0.04004282165408486, 0.96848669578755     ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT02\",\n\t// with complete chromatic adaptation to W2, so D = 1.0\n\ttoCone_M: [\n\t\t[  0.7328000,  0.4296000, -0.1624000 ],\n\t\t[ -0.7036000,  1.6975000,  0.0061000 ],\n\t\t[  0.0030000,  0.0136000,  0.9834000 ],\n\t],\n\tfromCone_M: [\n\t\t[  1.0961238208355142,   -0.27886900021828726, 0.18274517938277307 ],\n\t\t[  0.4543690419753592,    0.4735331543074117,  0.07209780371722911 ],\n\t\t[ -0.009627608738429355, -0.00569803121611342, 1.0153256399545427  ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT16\",\n\ttoCone_M: [\n\t\t[  0.401288,  0.650173, -0.051461 ],\n\t\t[ -0.250268,  1.204414,  0.045854 ],\n\t\t[ -0.002079,  0.048952,  0.953127 ],\n\t],\n\t// the extra precision is needed to avoid roundtripping errors\n\tfromCone_M: [\n\t\t[  1.862067855087233,   -1.0112546305316845,  0.14918677544445172  ],\n\t\t[  0.3875265432361372,   0.6214474419314753, -0.008973985167612521 ],\n\t\t[ -0.01584149884933386, -0.03412293802851557, 1.0499644368778496   ],\n\t],\n});\n\nObject.assign(WHITES, {\n\t// whitepoint values from ASTM E308-01 with 10nm spacing, 1931 2 degree observer\n\t// all normalized to Y (luminance) = 1.00000\n\t// Illuminant A is a tungsten electric light, giving a very warm, orange light.\n\tA:   [1.09850, 1.00000, 0.35585],\n\n\t// Illuminant C was an early approximation to daylight: illuminant A with a blue filter.\n\tC:   [0.98074, 1.000000, 1.18232],\n\n\t// The daylight series of illuminants simulate natural daylight.\n\t// The color temperature (in degrees Kelvin/100) ranges from\n\t// cool, overcast daylight (D50) to bright, direct sunlight (D65).\n\tD55: [0.95682, 1.00000, 0.92149],\n\tD75: [0.94972, 1.00000, 1.22638],\n\n\t// Equal-energy illuminant, used in two-stage CAT16\n\tE:   [1.00000, 1.00000, 1.00000],\n\n\t// The F series of illuminants represent fluorescent lights\n\tF2:  [0.99186, 1.00000, 0.67393],\n\tF7:  [0.95041, 1.00000, 1.08747],\n\tF11: [1.00962, 1.00000, 0.64350],\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport {WHITES} from \"../adapt.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\n\n// The ACES whitepoint\n// see TB-2018-001 Derivation of the ACES White Point CIE Chromaticity Coordinates\n// also https://github.com/ampas/aces-dev/blob/master/documents/python/TB-2018-001/aces_wp.py\n// Similar to D60\nWHITES.ACES = [0.32168 / 0.33767, 1.00000, (1.00000 - 0.32168 - 0.33767) / 0.33767];\n\n// convert an array of linear-light ACEScc values to CIE XYZ\nconst toXYZ_M = [\n\t[  0.6624541811085053,   0.13400420645643313,  0.1561876870049078  ],\n\t[  0.27222871678091454,  0.6740817658111484,   0.05368951740793705 ],\n\t[ -0.005574649490394108, 0.004060733528982826, 1.0103391003129971  ],\n];\nconst fromXYZ_M = [\n\t[  1.6410233796943257,   -0.32480329418479,    -0.23642469523761225  ],\n\t[ -0.6636628587229829,    1.6153315916573379,   0.016756347685530137 ],\n\t[  0.011721894328375376, -0.008284441996237409, 0.9883948585390215   ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"acescg\",\n\tcssId: \"--acescg\",\n\tname: \"ACEScg\",\n\n\t// ACEScg – A scene-referred, linear-light encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescg/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\tcoords: {\n\t\tr: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\n\treferred: \"scene\",\n\n\twhite: WHITES.ACES,\n\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n\n// export default Color;\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\nimport ACEScg from \"./acescg.js\";\n\nconst ε = 2 ** -16;\n\n// the smallest value which, in the 32bit IEEE 754 float encoding,\n// decodes as a non-negative value\nconst ACES_min_nonzero = -0.35828683;\n\n// brightest encoded value, decodes to 65504\nconst ACES_cc_max = (Math.log2(65504) + 9.72) / 17.52; // 1.468\n\nexport default new RGBColorSpace({\n\tid: \"acescc\",\n\tcssId: \"--acescc\",\n\tname: \"ACEScc\",\n\t// see S-2014-003 ACEScc – A Logarithmic Encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescc/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\n\t// Appendix A: \"Very small ACES scene referred values below 7 1/4 stops\n\t// below 18% middle gray are encoded as negative ACEScc values.\n\t// These values should be preserved per the encoding in Section 4.4\n\t// so that all positive ACES values are maintained.\"\n\tcoords: {\n\t\tr: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\treferred: \"scene\",\n\n\tbase: ACEScg,\n\t// from section 4.4.2 Decoding Function\n\ttoBase (RGB) {\n\t\tconst low = (9.72 - 15) / 17.52; // -0.3014\n\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= low) {\n\t\t\t\treturn (2 ** ((val * 17.52) - 9.72) - ε) * 2; // very low values, below -0.3014\n\t\t\t}\n\t\t\telse if (val < ACES_cc_max) {\n\t\t\t\treturn 2 ** ((val * 17.52) - 9.72);\n\t\t\t}\n\t\t\telse { // val >= ACES_cc_max\n\t\t\t\treturn 65504;\n\t\t\t}\n\t\t});\n\t},\n\n\t// Non-linear encoding function from S-2014-003, section 4.4.1 Encoding Function\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= 0) {\n\t\t\t\treturn (Math.log2(ε) + 9.72) / 17.52; // -0.3584\n\t\t\t}\n\t\t\telse if (val < ε) {\n\t\t\t\treturn  (Math.log2(ε + val * 0.5) + 9.72) / 17.52;\n\t\t\t}\n\t\t\telse { // val >= ε\n\t\t\t\treturn  (Math.log2(val) + 9.72) / 17.52;\n\t\t\t}\n\t\t});\n\t},\n\t// encoded media white (rgb 1,1,1) => linear  [ 222.861, 222.861, 222.861 ]\n\t// encoded media black (rgb 0,0,0) => linear [ 0.0011857, 0.0011857, 0.0011857]\n});\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport defaults from \"./defaults.js\";\nimport ColorSpace from \"./space.js\";\nimport {WHITES} from \"./adapt.js\";\nimport {\n\tgetColor,\n\tparse,\n\tto,\n\tserialize,\n\tinGamut,\n\ttoGamut,\n\tdistance,\n\tequals,\n\tget,\n\tgetAll,\n\tset,\n\tsetAll,\n\tdisplay,\n} from \"./index-fn.js\";\n\n\nimport \"./spaces/xyz-d50.js\";\nimport \"./spaces/srgb.js\";\n\n/**\n * Class that represents a color\n */\nexport default class Color {\n\t/**\n\t * Creates an instance of Color.\n\t * Signatures:\n\t * - `new Color(stringToParse)`\n\t * - `new Color(otherColor)`\n\t * - `new Color({space, coords, alpha})`\n\t * - `new Color(space, coords, alpha)`\n\t * - `new Color(spaceId, coords, alpha)`\n\t */\n\tconstructor (...args) {\n\t\tlet color;\n\n\t\tif (args.length === 1) {\n\t\t\tcolor = getColor(args[0]);\n\t\t}\n\n\t\tlet space, coords, alpha;\n\n\t\tif (color) {\n\t\t\tspace = color.space || color.spaceId;\n\t\t\tcoords = color.coords;\n\t\t\talpha = color.alpha;\n\t\t}\n\t\telse {\n\t\t\t// default signature new Color(ColorSpace, array [, alpha])\n\t\t\t[space, coords, alpha] = args;\n\t\t}\n\n\t\tObject.defineProperty(this, \"space\", {\n\t\t\tvalue: ColorSpace.get(space),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true, // see note in https://262.ecma-international.org/8.0/#sec-proxy-object-internal-methods-and-internal-slots-get-p-receiver\n\t\t});\n\n\t\tthis.coords = coords ? coords.slice() : [0, 0, 0];\n\n\t\t// Clamp alpha to [0, 1]\n\t\tthis.alpha = alpha > 1 || alpha === undefined ? 1 : (alpha < 0 ? 0 : alpha);\n\n\t\t// Convert \"NaN\" to NaN\n\t\tfor (let i = 0; i < this.coords.length; i++) {\n\t\t\tif (this.coords[i] === \"NaN\") {\n\t\t\t\tthis.coords[i] = NaN;\n\t\t\t}\n\t\t}\n\n\t\t// Define getters and setters for each coordinate\n\t\tfor (let id in this.space.coords) {\n\t\t\tObject.defineProperty(this, id, {\n\t\t\t\tget: () => this.get(id),\n\t\t\t\tset: value => this.set(id, value),\n\t\t\t});\n\t\t}\n\t}\n\n\tget spaceId () {\n\t\treturn this.space.id;\n\t}\n\n\tclone () {\n\t\treturn new Color(this.space, this.coords, this.alpha);\n\t}\n\n\ttoJSON () {\n\t\treturn {\n\t\t\tspaceId: this.spaceId,\n\t\t\tcoords: this.coords,\n\t\t\talpha: this.alpha,\n\t\t};\n\t}\n\n\tdisplay (...args) {\n\t\tlet ret = display(this, ...args);\n\n\t\t// Convert color object to Color instance\n\t\tret.color = new Color(ret.color);\n\n\t\treturn ret;\n\t}\n\n\t/**\n\t * Get a color from the argument passed\n\t * Basically gets us the same result as new Color(color) but doesn't clone an existing color object\n\t */\n\tstatic get (color, ...args) {\n\t\tif (color instanceof Color) {\n\t\t\treturn color;\n\t\t}\n\n\t\treturn new Color(color, ...args);\n\t}\n\n\tstatic defineFunction (name, code, o = code) {\n\t\tlet {instance = true, returns} = o;\n\n\t\tlet func = function (...args) {\n\t\t\tlet ret = code(...args);\n\n\t\t\tif (returns === \"color\") {\n\t\t\t\tret = Color.get(ret);\n\t\t\t}\n\t\t\telse if (returns === \"function<color>\") {\n\t\t\t\tlet f = ret;\n\t\t\t\tret = function (...args) {\n\t\t\t\t\tlet ret = f(...args);\n\t\t\t\t\treturn Color.get(ret);\n\t\t\t\t};\n\t\t\t\t// Copy any function metadata\n\t\t\t\tObject.assign(ret, f);\n\t\t\t}\n\t\t\telse if (returns === \"array<color>\") {\n\t\t\t\tret = ret.map(c => Color.get(c));\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t};\n\n\t\tif (!(name in Color)) {\n\t\t\tColor[name] = func;\n\t\t}\n\n\t\tif (instance) {\n\t\t\tColor.prototype[name] = function (...args) {\n\t\t\t\treturn func(this, ...args);\n\t\t\t};\n\t\t}\n\t}\n\n\tstatic defineFunctions (o) {\n\t\tfor (let name in o) {\n\t\t\tColor.defineFunction(name, o[name], o[name]);\n\t\t}\n\t}\n\n\tstatic extend (exports) {\n\t\tif (exports.register) {\n\t\t\texports.register(Color);\n\t\t}\n\t\telse {\n\t\t\t// No register method, just add the module's functions\n\t\t\tfor (let name in exports) {\n\t\t\t\tColor.defineFunction(name, exports[name]);\n\t\t\t}\n\t\t}\n\t}\n}\n\nColor.defineFunctions({\n\tget,\n\tgetAll,\n\tset,\n\tsetAll,\n\tto,\n\tequals,\n\tinGamut,\n\ttoGamut,\n\tdistance,\n\ttoString: serialize,\n});\n\nObject.assign(Color, {\n\tutil,\n\thooks,\n\tWHITES,\n\tSpace: ColorSpace,\n\tspaces: ColorSpace.registry,\n\tparse,\n\n\t// Global defaults one may want to configure\n\tdefaults,\n});\n", "import getColor from \"./getColor.js\";\n\nexport default function equals (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\treturn color1.space === color2.space\n\t       && color1.alpha === color2.alpha\n\t       && color1.coords.every((c, i) => c === color2.coords[i]);\n}\n", "import ColorSpace from \"../space.js\";\nimport * as spaces from \"./index-fn.js\";\n\nexport * as spaces from \"./index-fn.js\";\n\nfor (let key of Object.keys(spaces)) {\n\tColorSpace.register(spaces[key]);\n}\n", "/**\n * This plugin defines getters and setters for color[spaceId]\n * e.g. color.lch on *any* color gives us the lch coords\n */\nimport ColorSpace from \"./space.js\";\nimport Color from \"./color.js\";\nimport hooks from \"./hooks.js\";\n\n// Add space accessors to existing color spaces\nfor (let id in ColorSpace.registry) {\n\taddSpaceAccessors(id, ColorSpace.registry[id]);\n}\n\n// Add space accessors to color spaces not yet created\nhooks.add(\"colorspace-init-end\", space => {\n\taddSpaceAccessors(space.id, space);\n\tspace.aliases?.forEach(alias => {\n\t\taddSpaceAccessors(alias, space);\n\t});\n});\n\nfunction addSpaceAccessors (id, space) {\n\tlet propId = id.replace(/-/g, \"_\");\n\n\tObject.defineProperty(Color.prototype, propId, {\n\t\t// Convert coords to coords in another colorspace and return them\n\t\t// Source colorspace: this.spaceId\n\t\t// Target colorspace: id\n\t\tget () {\n\t\t\tlet ret = this.getAll(id);\n\n\t\t\tif (typeof Proxy === \"undefined\") {\n\t\t\t\t// If proxies are not supported, just return a static array\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\t// Enable color.spaceId.coordName syntax\n\t\t\treturn new Proxy(ret, {\n\t\t\t\thas: (obj, property) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tColorSpace.resolveCoord([space, property]);\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tcatch (e) {}\n\n\t\t\t\t\treturn Reflect.has(obj, property);\n\t\t\t\t},\n\t\t\t\tget: (obj, property, receiver) => {\n\t\t\t\t\tif (property && typeof property !== \"symbol\" && !(property in obj)) {\n\t\t\t\t\t\tlet {index} = ColorSpace.resolveCoord([space, property]);\n\n\t\t\t\t\t\tif (index >= 0) {\n\t\t\t\t\t\t\treturn obj[index];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Reflect.get(obj, property, receiver);\n\t\t\t\t},\n\t\t\t\tset: (obj, property, value, receiver) => {\n\t\t\t\t\tif (property && typeof property !== \"symbol\" && !(property in obj) || property >= 0) {\n\t\t\t\t\t\tlet {index} = ColorSpace.resolveCoord([space, property]);\n\n\t\t\t\t\t\tif (index >= 0) {\n\t\t\t\t\t\t\tobj[index] = value;\n\n\t\t\t\t\t\t\t// Update color.coords\n\t\t\t\t\t\t\tthis.setAll(id, obj);\n\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Reflect.set(obj, property, value, receiver);\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t\t// Convert coords in another colorspace to internal coords and set them\n\t\t// Target colorspace: this.spaceId\n\t\t// Source colorspace: id\n\t\tset (coords) {\n\t\t\tthis.setAll(id, coords);\n\t\t},\n\t\tconfigurable: true,\n\t\tenumerable: true,\n\t});\n}\n", "// Import all modules of Color.js\nimport Color from \"./color.js\";\n\n// Import all color spaces\nimport \"./spaces/index.js\";\n\n// Import all DeltaE methods\nimport deltaE from \"./deltaE.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nColor.extend(deltaEMethods);\nColor.extend({deltaE});\nObject.assign(Color, {deltaEMethods});\n\n// Import optional modules\nimport * as variations from \"./variations.js\";\nColor.extend(variations);\n\nimport contrast from \"./contrast.js\";\nColor.extend({contrast});\n\nimport * as chromaticity from \"./chromaticity.js\";\nColor.extend(chromaticity);\n\nimport * as luminance from \"./luminance.js\";\nColor.extend(luminance);\n\nimport * as interpolation from \"./interpolation.js\";\nColor.extend(interpolation);\n\nimport * as contrastMethods from \"./contrast/index.js\";\nColor.extend(contrastMethods);\n\nimport \"./CATs.js\";\nimport \"./space-accessors.js\";\n\n// Re-export everything\nexport default Color;\n", "import getColor from \"./getColor.js\";\n// import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport * as contrastAlgorithms from \"./contrast/index.js\";\n\nexport default function contrast (background, foreground, o = {}) {\n\tif (isString(o)) {\n\t\to = {algorithm: o};\n\t}\n\n\tlet {algorithm, ...rest} = o;\n\n\tif (!algorithm) {\n\t\tlet algorithms = Object.keys(contrastAlgorithms).map(a => a.replace(/^contrast/, \"\")).join(\", \");\n\t\tthrow new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${algorithms}`);\n\t}\n\n\tbackground = getColor(background);\n\tforeground = getColor(foreground);\n\n\tfor (let a in contrastAlgorithms) {\n\t\tif (\"contrast\" + algorithm.toLowerCase() === a.toLowerCase()) {\n\t\t\treturn contrastAlgorithms[a](background, foreground, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown contrast algorithm: ${algorithm}`);\n}\n"], "names": ["multiplyMatrices", "A", "B", "m", "length", "Array", "isArray", "map", "x", "p", "B_cols", "_", "i", "product", "row", "col", "ret", "c", "isString", "str", "type", "o", "Object", "prototype", "toString", "call", "match", "toLowerCase", "serializeNumber", "n", "precision", "unit", "isNone", "toPrecision", "Number", "isNaN", "none", "<PERSON><PERSON><PERSON>", "integer", "digits", "Math", "log10", "abs", "multiplier", "floor", "angleFactor", "deg", "grad", "rad", "PI", "turn", "parseFunction", "trim", "isNumberRegex", "unitValueRegex", "singleArgument", "parts", "args", "replace", "$0", "rawArg", "arg", "unitlessArg", "slice", "test", "NaN", "startsWith", "alpha", "raw", "push", "name", "rawName", "rawArgs", "last", "arr", "interpolate", "start", "end", "interpolateInv", "value", "mapRange", "from", "to", "parseCoordGrammar", "coordGrammars", "coordGrammar", "split", "range", "String", "clamp", "min", "val", "max", "copySign", "sign", "spow", "base", "exp", "zdiv", "d", "bisectLeft", "lo", "hi", "mid", "hooks", "add", "callback", "first", "arguments", "for<PERSON>ach", "this", "run", "env", "context", "defaults", "gamut_mapping", "deltaE", "verbose", "globalThis", "process", "NODE_ENV", "warn", "msg", "console", "WHITES", "D50", "D65", "<PERSON><PERSON><PERSON><PERSON>", "adapt", "W1", "W2", "XYZ", "options", "TypeError", "M", "noneTypes", "Set", "coerceCoords", "space", "format", "coords", "types", "entries", "id", "coordMeta", "providedType", "find", "has", "coordName", "fromRange", "to<PERSON><PERSON><PERSON>", "refRange", "util.mapRange", "parse", "meta", "color", "parsed", "util.parseFunction", "shift", "alternateId", "substring", "ids", "indexOf", "pop", "ColorSpace", "all", "colorSpec", "getFormat", "includes", "filter", "specId", "keys", "assign", "formatId", "spaceId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registryId", "registry", "cssId", "formats", "lastAlpha", "util.last", "getColor", "get", "undefined", "constructor", "aliases", "fromBase", "toBase", "white", "gamutSpace", "isPolar", "isUnbounded", "inGamut", "referred", "defineProperty", "<PERSON><PERSON><PERSON>", "reverse", "writable", "enumerable", "configurable", "epsilon", "equals", "values", "every", "coord", "processFormat", "connectionSpace", "connectionSpaceIndex", "myPath", "path", "otherPath", "Error", "getMinCoords", "static", "register", "alias", "alternatives", "resolveCoord", "ref", "workingSpace", "coordType", "coordId", "index", "normalizedCoord", "join", "s", "coordFormats", "outputType", "suffix", "serializeCoords", "xyz_d65", "y", "z", "RGBColorSpace", "r", "g", "b", "XYZ_D65", "toXYZ_M", "fromXYZ_M", "rgb", "xyz", "super", "getAll", "prop", "setAll", "set", "object", "returns", "XYZ_D50", "ε3", "κ", "lab", "l", "a", "xyz_d50", "f", "cbrt", "Lab", "pow", "constrain", "angle", "lch", "h", "hue", "L", "atan2", "sqrt", "constrainAngle", "LCH", "Lightness", "Chroma", "<PERSON><PERSON>", "cos", "sin", "Gfactor", "π", "r2d", "d2r", "pow7", "x2", "deltaE2000", "sample", "kL", "kC", "kH", "L1", "a1", "b1", "C1", "L2", "a2", "b2", "C2", "C7", "G", "adash1", "adash2", "Cdash1", "Cdash2", "h1", "h2", "hdiff", "hsum", "habs", "hdash", "Ldash", "Cdash", "Cdash7", "lsq", "SL", "SC", "T", "SH", "RC", "dE", "XYZtoLMS_M", "LMStoXYZ_M", "LMStoLab_M", "LabtoLMS_M", "OKLab", "LMSg", "LMS", "oklab", "deltaEOK", "ε", "clone", "distance", "color1", "color2", "coords1", "coords2", "reduce", "acc", "c1", "c2", "XYZ_Abs_D65", "v", "AbsXYZ", "c3", "pinv", "d0", "XYZtoCone_M", "ConetoXYZ_M", "ConetoIab_M", "IabtoCone_M", "Jzazbz", "jz", "az", "bz", "Xa", "Ya", "<PERSON>a", "PQLMS", "Iz", "Jz", "Xm", "Ym", "jzczhz", "cz", "hz", "jzazbz", "m1", "m2", "im1", "im2", "LMStoIPT_M", "IPTtoLMS_M", "ictcp", "ct", "cp", "LMStoICtCp", "ICtCp", "ICtCptoLMS", "<PERSON><PERSON><PERSON><PERSON>", "adaptedCoefInv", "tau", "cat16", "cat16Inv", "surroundMap", "dark", "dim", "average", "hueQuadMap", "e", "H", "rad2deg", "deg2rad", "fl", "temp", "environment", "refWhite", "adaptingLuminance", "backgroundLuminance", "surround", "discounting", "xyzW", "la", "yb", "yw", "rgbW", "nc", "k4", "flRoot", "nbb", "ncb", "dRgb", "dRgbInv", "rgbCW", "rgbAW", "aW", "viewingConditions", "fromCam16", "cam16", "J", "Q", "C", "hRad", "Hp", "hii", "ei", "eii", "invHueQuadrature", "cosh", "sinh", "<PERSON><PERSON>", "t", "et", "p1", "p2", "rgb_c", "adapted", "constant", "cabs", "unadapt", "toCam16", "xyzd65", "xyz100", "rgbA", "hp", "hueQuadrature", "j", "fromLstar", "lstar", "toHct", "hct", "attempt", "Infinity", "delta", "fromHct", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "convertUcsAb", "log", "hrad", "deltaEMethods", "deltaE76", "deltaECMC", "H1", "H2", "C4", "F", "deltaEJz", "Jz1", "Cz1", "Hz1", "Jz2", "Cz2", "Hz2", "deltaEITP", "I1", "T1", "P1", "I2", "T2", "P2", "deltaEHCT", "t1", "t2", "GMAPPRESET", "method", "jnd", "deltaEMethod", "blackWhiteClamp", "channel", "toGamut", "util.isString", "spaceColor", "origin", "JND", "oklchSpace", "origin_OKLCH", "COLORS", "WHITE", "black", "BLACK", "clip", "_color", "destColor", "spaceCoords", "util.clamp", "min_inGamut", "current", "clipped", "E", "chroma", "toGamutCSS", "hasOwnProperty", "de", "channelMeta", "util.isNone", "mapSpace", "mappedColor", "order", "parseFloat", "calcEpsilon", "low", "high", "bounds", "serialize", "customOptions", "DEFAULT_FORMAT", "checkInGamut", "util.serializeNumber", "unshift", "strAlpha", "noAlpha", "commas", "REC2020Linear", "REC2020", "RGB", "P3Linear", "sRGBLinear", "KEYWORDS", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "whitesmoke", "yellow", "yellowgreen", "fill", "coordGrammarNumber", "sRGB", "rgb_number", "rgba", "rgba_number", "hex", "component", "parseInt", "collapse", "round", "collapsible", "padStart", "keyword", "P3", "supportsNone", "display_space", "CSS", "supports", "getLuminance", "setLuminance", "Color", "blkThrs", "blkClmp", "fclamp", "Y", "linearize", "lab_d65", "phi", "background", "foreground", "S", "Sapc", "R", "lumTxt", "lumBg", "Ytxt", "Ybg", "BoW", "Lstr1", "Lstr2", "deltaPhiStar", "contrast", "SQRT2", "Y1", "Y2", "denom", "uv", "X", "Z", "xy", "sum", "rest", "amount", "mix", "steps", "colorRange", "isRange", "rangeArgs", "colors", "maxDeltaE", "maxSteps", "rangeOptions", "totalDelta", "actualSteps", "ceil", "step", "max<PERSON><PERSON><PERSON>", "cur", "prev", "splice", "outputSpace", "progression", "premultiplied", "interpolationSpace", "arc", "angles", "angleDiff", "angles.adjust", "defineFunction", "HSL", "hsl", "k", "hsla", "HSV", "hsv", "hwb", "w", "A98Linear", "a98rgb", "ProPhotoLinear", "prophoto", "oklch", "U_PRIME_WHITE", "V_PRIME_WHITE", "<PERSON><PERSON>", "u", "up", "vp", "isFinite", "LCHuv", "m_r0", "m_r1", "m_r2", "m_g0", "m_g1", "m_g2", "m_b0", "m_b1", "m_b2", "distanceFromOriginAngle", "slope", "intercept", "calculateBoundingLines", "sub1", "sub2", "s1r", "s2r", "s3r", "s1g", "s2g", "s3g", "s1b", "s2b", "s3b", "r0s", "r0i", "r1s", "r1i", "g0s", "g0i", "g1s", "g1i", "b0s", "b0i", "b1s", "b1i", "calcMaxChromaHsluv", "lines", "hueRad", "r0", "r1", "g0", "g1", "b0", "hsluv", "distanceFromOrigin", "calcMaxChromaHpluv", "hpluv", "minv", "rec2100Pq", "scale", "rec2100Hlg", "CATs", "defineCAT", "toCone_M", "fromCone_M", "scaled_cone_M", "D55", "D75", "F2", "F7", "F11", "ACES", "ACEScg", "ACES_min_nonzero", "ACES_cc_max", "log2", "acescc", "toJSON", "display", "fallbackColor", "some", "code", "instance", "func", "defineFunctions", "extend", "exports", "util", "Space", "spaces", "key", "addSpaceAccessors", "propId", "Proxy", "obj", "property", "Reflect", "receiver", "variations", "algorithm", "algorithms", "contrastAlgorithms", "chromaticity", "luminance", "interpolation", "contrastMethods"], "mappings": "kCACe,SAASA,EAAkBC,EAAGC,GAC5C,IAAIC,EAAIF,EAAEG,OAELC,MAAMC,QAAQL,EAAE,MAEpBA,EAAI,CAACA,IAGDI,MAAMC,QAAQJ,EAAE,MAEpBA,EAAIA,EAAEK,KAAIC,GAAK,CAACA,MAGjB,IAAIC,EAAIP,EAAE,GAAGE,OACTM,EAASR,EAAE,GAAGK,KAAI,CAACI,EAAGC,IAAMV,EAAEK,KAAIC,GAAKA,EAAEI,OACzCC,EAAUZ,EAAEM,KAAIO,GAAOJ,EAAOH,KAAIQ,IACrC,IAAIC,EAAM,EAEV,IAAKX,MAAMC,QAAQQ,GAAM,CACxB,IAAK,IAAIG,KAAKF,EACbC,GAAOF,EAAMG,EAGd,OAAOD,CACP,CAED,IAAK,IAAIJ,EAAI,EAAGA,EAAIE,EAAIV,OAAQQ,IAC/BI,GAAOF,EAAIF,IAAMG,EAAIH,IAAM,GAG5B,OAAOI,CAAG,MAOX,OAJU,IAANb,IACHU,EAAUA,EAAQ,IAGT,IAANJ,EACII,EAAQN,KAAIC,GAAKA,EAAE,KAGpBK,CACR,CChCO,SAASK,EAAUC,GACzB,MAAqB,WAAdC,EAAKD,EACb,CAOO,SAASC,EAAMC,GAGrB,OAFUC,OAAOC,UAAUC,SAASC,KAAKJ,GAE7BK,MAAM,wBAAwB,IAAM,IAAIC,aACrD,CAEO,SAASC,EAAiBC,GAAGC,UAACA,EAASC,KAAEA,IAC/C,OAAIC,EAAOH,GACH,OAGDI,EAAYJ,EAAGC,IAAcC,GAAQ,GAC7C,CAOO,SAASC,EAAQH,GACvB,OAAOK,OAAOC,MAAMN,IAAOA,aAAaK,QAAUL,GAAGO,IACtD,CAKO,SAASC,EAAUR,GACzB,OAAOG,EAAOH,GAAK,EAAIA,CACxB,CAOO,SAASI,EAAaJ,EAAGC,GAC/B,GAAU,IAAND,EACH,OAAO,EAER,IAAIS,IAAYT,EACZU,EAAS,EACTD,GAAWR,IACdS,EAA2C,IAAhCC,KAAKC,MAAMD,KAAKE,IAAIJ,KAEhC,MAAMK,EAAa,KAASb,EAAYS,GACxC,OAAOC,KAAKI,MAAMf,EAAIc,EAAa,IAAOA,CAC3C,CAEA,MAAME,EAAc,CACnBC,IAAK,EACLC,KAAM,GACNC,IAAK,IAAMR,KAAKS,GAChBC,KAAM,KAQA,SAASC,EAAehC,GAC9B,IAAKA,EACJ,OAGDA,EAAMA,EAAIiC,OAEV,MACMC,EAAgB,aAChBC,EAAiB,oBACjBC,EAAiB,6CACvB,IAAIC,EAAQrC,EAAIO,MAJQ,wBAMxB,GAAI8B,EAAO,CAEV,IAAIC,EAAO,GA6CX,OA5CAD,EAAM,GAAGE,QAAQH,GAAgB,CAACI,EAAIC,KACrC,IAAIlC,EAAQkC,EAAOlC,MAAM4B,GACrBO,EAAMD,EAEV,GAAIlC,EAAO,CACV,IAAIK,EAAOL,EAAM,GAEboC,EAAcD,EAAIE,MAAM,GAAIhC,EAAK3B,QAExB,MAAT2B,GAEH8B,EAAM,IAAI3B,OAAO4B,EAAc,KAC/BD,EAAIzC,KAAO,iBAIXyC,EAAM,IAAI3B,OAAO4B,EAAcjB,EAAYd,IAC3C8B,EAAIzC,KAAO,UACXyC,EAAI9B,KAAOA,EAEZ,MACQsB,EAAcW,KAAKH,IAE3BA,EAAM,IAAI3B,OAAO2B,GACjBA,EAAIzC,KAAO,YAEK,SAARyC,IACRA,EAAM,IAAI3B,OAAO+B,KACjBJ,EAAIzB,MAAO,GAGRuB,EAAGO,WAAW,OAEjBL,EAAMA,aAAe3B,OAAS2B,EAAM,IAAI3B,OAAO2B,GAC/CA,EAAIM,OAAQ,GAGM,iBAARN,GAAoBA,aAAe3B,SAC7C2B,EAAIO,IAAMR,GAGXH,EAAKY,KAAKR,EAAI,IAGR,CACNS,KAAMd,EAAM,GAAG7B,cACf4C,QAASf,EAAM,GACfgB,QAAShB,EAAM,GAGfC,OAED,CACF,CAEO,SAASgB,EAAMC,GACrB,OAAOA,EAAIA,EAAItE,OAAS,EACzB,CAEO,SAASuE,EAAaC,EAAOC,EAAKpE,GACxC,OAAI0B,MAAMyC,GACFC,EAGJ1C,MAAM0C,GACFD,EAGDA,GAASC,EAAMD,GAASnE,CAChC,CAEO,SAASqE,EAAgBF,EAAOC,EAAKE,GAC3C,OAAQA,EAAQH,IAAUC,EAAMD,EACjC,CAEO,SAASI,EAAUC,EAAMC,EAAIH,GACnC,OAAOJ,EAAYO,EAAG,GAAIA,EAAG,GAAIJ,EAAeG,EAAK,GAAIA,EAAK,GAAIF,GACnE,CAEO,SAASI,EAAmBC,GAClC,OAAOA,EAAc7E,KAAI8E,GACjBA,EAAaC,MAAM,KAAK/E,KAAIa,IAElC,IAAImE,GADJnE,EAAOA,EAAKgC,QACK1B,MAAM,6CAEvB,GAAI6D,EAAO,CACV,IAAIvE,EAAM,IAAIwE,OAAOD,EAAM,IAE3B,OADAvE,EAAIuE,MAAQ,EAAEA,EAAM,IAAKA,EAAM,IACxBvE,CACP,CAED,OAAOI,CAAI,KAGd,CASO,SAASqE,EAAOC,EAAKC,EAAKC,GAChC,OAAOpD,KAAKoD,IAAIpD,KAAKkD,IAAIE,EAAKD,GAAMD,EACrC,CAQO,SAASG,EAAUX,EAAID,GAC7B,OAAOzC,KAAKsD,KAAKZ,KAAQ1C,KAAKsD,KAAKb,GAAQC,GAAMA,CAClD,CAQO,SAASa,EAAMC,EAAMC,GAC3B,OAAOJ,EAASrD,KAAKE,IAAIsD,IAASC,EAAKD,EACxC,CAQO,SAASE,EAAMrE,EAAGsE,GACxB,OAAc,IAANA,EAAW,EAAItE,EAAIsE,CAC5B,CAWO,SAASC,EAAY1B,EAAKK,EAAOsB,EAAK,EAAGC,EAAK5B,EAAItE,QACxD,KAAOiG,EAAKC,GAAI,CACf,MAAMC,EAAOF,EAAKC,GAAO,EACrB5B,EAAI6B,GAAOxB,EACdsB,EAAKE,EAAM,EAGXD,EAAKC,CAEN,CACD,OAAOF,CACR,mQC3NA,MAAMG,EAAQ,IA/BP,MACN,GAAAC,CAAKnC,EAAMoC,EAAUC,GACpB,GAA2B,iBAAhBC,UAAU,IASpBvG,MAAMC,QAAQgE,GAAQA,EAAO,CAACA,IAAOuC,SAAQ,SAAUvC,GACvDwC,KAAKxC,GAAQwC,KAAKxC,IAAS,GAEvBoC,GACHI,KAAKxC,GAAMqC,EAAQ,UAAY,QAAQD,EAExC,GAAEI,WAbF,IAAK,IAAIxC,KAAQsC,UAAU,GAC1BE,KAAKL,IAAInC,EAAMsC,UAAU,GAAGtC,GAAOsC,UAAU,GAa/C,CAED,GAAAG,CAAKzC,EAAM0C,GACVF,KAAKxC,GAAQwC,KAAKxC,IAAS,GAC3BwC,KAAKxC,GAAMuC,SAAQ,SAAUH,GAC5BA,EAASjF,KAAKuF,GAAOA,EAAIC,QAAUD,EAAIC,QAAUD,EAAKA,EACzD,GACE,GC3Ba,IAAAE,EAAA,CACdC,cAAe,MACfrF,UAAW,EACXsF,OAAQ,KACRC,QAA+D,SAAtDC,YAAYC,SAASP,KAAKQ,UAAU7F,cAC7C8F,KAAM,SAAeC,GAChBZ,KAAKO,SACRC,YAAYK,SAASF,OAAOC,EAE7B,GCPK,MAAME,EAAS,CAErBC,IAAK,CAAC,MAAS,MAAQ,EAAS,MAA0B,OAC1DC,IAAK,CAAC,MAAS,KAAQ,EAAS,MAA0B,OAGpD,SAASC,EAAUzD,GACzB,OAAIjE,MAAMC,QAAQgE,GACVA,EAGDsD,EAAOtD,EACf,CAGe,SAAS0D,EAAOC,EAAIC,EAAIC,EAAKC,EAAU,CAAA,GAIrD,GAHAH,EAAKF,EAASE,GACdC,EAAKH,EAASG,IAETD,IAAOC,EACX,MAAM,IAAIG,UAAU,kCAAmCJ,EAAc,GAAT,SAAeA,GAAOC,EAAW,GAAN,MAAYA,EAAY,GAAP,QAGzG,GAAID,IAAOC,EAEV,OAAOC,EAGR,IAAInB,EAAM,CAACiB,KAAIC,KAAIC,MAAKC,WAwBxB,GAtBA5B,EAAMO,IAAI,6BAA8BC,GAEnCA,EAAIsB,IACJtB,EAAIiB,KAAOL,EAAOE,KAAOd,EAAIkB,KAAON,EAAOC,IAC9Cb,EAAIsB,EAAI,CACP,CAAE,mBAAoB,qBAAuB,oBAC7C,CAAE,mBAAqB,mBAAqB,qBAC5C,EAAG,oBAAsB,oBAAsB,oBAGxCtB,EAAIiB,KAAOL,EAAOC,KAAOb,EAAIkB,KAAON,EAAOE,MAEnDd,EAAIsB,EAAI,CACP,CAAE,kBAAoB,mBAAqB,oBAC3C,EAAG,kBAAoB,mBAAoB,qBAC3C,CAAE,qBAAuB,oBAAsB,sBAKlD9B,EAAMO,IAAI,2BAA4BC,GAElCA,EAAIsB,EACP,OAAOtI,EAAiBgH,EAAIsB,EAAGtB,EAAImB,KAGnC,MAAM,IAAIE,UAAU,qEAEtB,CCxDA,MAAME,EAAY,IAAIC,IAAI,CAAC,WAAY,eAAgB,YAUvD,SAASC,EAAcC,EAAOC,EAAQrE,EAAMsE,GAC3C,IAAIC,EAAQvH,OAAOwH,QAAQJ,EAAME,QAAQrI,KAAI,EAAEwI,EAAIC,GAAYpI,KAC9D,IAMIQ,EANAiE,EAAesD,EAAOtD,aAAazE,GACnCiD,EAAM+E,EAAOhI,GACbqI,EAAepF,GAAKzC,KAaxB,GAPCA,EADGyC,EAAIzB,KACAiD,EAAa6D,MAAKjI,GAAKsH,EAAUY,IAAIlI,KAGrCoE,EAAa6D,MAAKjI,GAAKA,GAAKgI,KAI/B7H,EAAM,CAEV,IAAIgI,EAAYJ,EAAU1E,MAAQyE,EAClC,MAAM,IAAIV,UAAU,GAAGY,GAAgBpF,EAAIO,uBAAuBgF,QAAgB9E,MAClF,CAED,IAAI+E,EAAYjI,EAAKmE,MAEA,iBAAjB0D,IACHI,IAAc,CAAC,EAAG,IAGnB,IAAIC,EAAUN,EAAUzD,OAASyD,EAAUO,SAM3C,OAJIF,GAAaC,IAChBV,EAAOhI,GAAK4I,EAAcH,EAAWC,EAASV,EAAOhI,KAG/CQ,CAAI,IAGZ,OAAOyH,CACR,CAUe,SAASY,EAAOtI,GAAKuI,KAACA,GAAQ,CAAA,GAC5C,IAAI1C,EAAM,CAAC7F,IAAOqE,OAAOrE,IAAMiC,QAG/B,GAFAoD,EAAMO,IAAI,cAAeC,GAErBA,EAAI2C,MACP,OAAO3C,EAAI2C,MAKZ,GAFA3C,EAAI4C,OAASC,EAAmB7C,EAAI7F,KAEhC6F,EAAI4C,OAAQ,CAEf,IAAItF,EAAO0C,EAAI4C,OAAOtF,KAEtB,GAAa,UAATA,EAAkB,CAErB,IAAIyE,EAAK/B,EAAI4C,OAAOnG,KAAKqG,QAErBC,EAAchB,EAAG7E,WAAW,MAAQ6E,EAAGiB,UAAU,GAAK,KAAKjB,IAC3DkB,EAAM,CAAClB,EAAIgB,GACX5F,EAAQ6C,EAAI4C,OAAOpF,QAAQ0F,QAAQ,KAAO,EAAIlD,EAAI4C,OAAOnG,KAAK0G,MAAQ,EAE1E,IAAK,IAAIzB,KAAS0B,EAAWC,IAAK,CACjC,IAAIC,EAAY5B,EAAM6B,UAAU,SAEhC,GAAID,IACCL,EAAIO,SAASF,EAAUvB,KAAOuB,EAAUL,KAAKQ,QAAQC,GAAWT,EAAIO,SAASE,KAAStK,QAAQ,CAIjG,MAAMwI,EAAStH,OAAOqJ,KAAKjC,EAAME,QAAQrI,KAAI,CAACI,EAAGC,IAAMoG,EAAI4C,OAAOnG,KAAK7C,IAAM,IAE7E,IAAIiI,EAmBJ,OAjBIyB,EAAUjF,eACbwD,EAAQJ,EAAaC,EAAO4B,EAAW,QAAS1B,IAG7Cc,GACHpI,OAAOsJ,OAAOlB,EAAM,CAACmB,SAAU,QAAShC,UAGrCyB,EAAUvB,GAAG7E,WAAW,QAAU6E,EAAG7E,WAAW,OACnDgD,EAASO,KAAK,GAAGiB,EAAMpE,gGACagG,EAAUvB,wBAAwBA,OAEnEA,EAAG7E,WAAW,QAAUoG,EAAUvB,GAAG7E,WAAW,OACnDgD,EAASO,KAAK,GAAGiB,EAAMpE,qEACIgG,EAAUvB,iCAAiCA,OAGhE,CAAC+B,QAASpC,EAAMK,GAAIH,SAAQzE,QACnC,CAEF,CAGD,IAAI4G,EAAa,GACbC,EAAajC,KAAMqB,EAAWa,SAAWlC,EAAKgB,EAClD,GAAIiB,KAAcZ,EAAWa,SAAU,CAEtC,IAAIC,EAAQd,EAAWa,SAASD,GAAYG,SAASxB,OAAOZ,GAExDmC,IACHH,EAAa,sBAAsBG,MAEpC,CAED,MAAM,IAAI7C,UAAU,sBAAsBU,QAAWgC,GAAc,qBACnE,CAEA,IAAK,IAAIrC,KAAS0B,EAAWC,IAAK,CAEjC,IAAI1B,EAASD,EAAM6B,UAAUjG,GAC7B,GAAIqE,GAA0B,aAAhBA,EAAOvH,KAAqB,CACzC,IAAI+C,EAAQ,GAERwE,EAAOyC,WAAaC,EAAUrE,EAAI4C,OAAOnG,MAAMU,SAClDA,EAAQ6C,EAAI4C,OAAOnG,KAAK0G,OAGzB,IAEItB,EAFAD,EAAS5B,EAAI4C,OAAOnG,KAYxB,OARIkF,EAAOtD,eACVwD,EAAQJ,EAAaC,EAAOC,EAAQrE,EAAMsE,IAGvCc,GACHpI,OAAOsJ,OAAOlB,EAAM,CAACmB,SAAUlC,EAAOrE,KAAMuE,UAGtC,CACNiC,QAASpC,EAAMK,GACfH,SAAQzE,QAET,CACD,CAEF,MAGA,IAAK,IAAIuE,KAAS0B,EAAWC,IAC5B,IAAK,IAAIQ,KAAYnC,EAAMyC,QAAS,CACnC,IAAIxC,EAASD,EAAMyC,QAAQN,GAE3B,GAAoB,WAAhBlC,EAAOvH,KACV,SAGD,GAAIuH,EAAO3E,OAAS2E,EAAO3E,KAAKgD,EAAI7F,KACnC,SAGD,IAAIwI,EAAQhB,EAAOc,MAAMzC,EAAI7F,KAE7B,GAAIwI,EAOH,OANAA,EAAMxF,QAAU,EAEZuF,IACHA,EAAKmB,SAAWA,GAGVlB,CAER,CAMH,MAAM,IAAItB,UAAU,mBAAmBlH,kCACxC,CC5Le,SAASmK,EAAU3B,GACjC,GAAItJ,MAAMC,QAAQqJ,GACjB,OAAOA,EAAMpJ,IAAI+K,GAGlB,IAAK3B,EACJ,MAAM,IAAItB,UAAU,yBAGjBnH,EAASyI,KACZA,EAAQF,EAAME,IAIf,IAAIjB,EAAQiB,EAAMjB,OAASiB,EAAMmB,QAWjC,OATMpC,aAAiB0B,IAEtBT,EAAMjB,MAAQ0B,EAAWmB,IAAI7C,SAGV8C,IAAhB7B,EAAMxF,QACTwF,EAAMxF,MAAQ,GAGRwF,CACR,CCzBe,MAAMS,EACpB,WAAAqB,CAAarD,GACZtB,KAAKiC,GAAKX,EAAQW,GAClBjC,KAAKxC,KAAO8D,EAAQ9D,KACpBwC,KAAKd,KAAOoC,EAAQpC,KAAOoE,EAAWmB,IAAInD,EAAQpC,MAAQ,KAC1Dc,KAAK4E,QAAUtD,EAAQsD,QAEnB5E,KAAKd,OACRc,KAAK6E,SAAWvD,EAAQuD,SACxB7E,KAAK8E,OAASxD,EAAQwD,QAKvB,IAAIhD,EAASR,EAAQQ,QAAU9B,KAAKd,KAAK4C,OAEzC,IAAK,IAAItE,KAAQsE,EACV,SAAUA,EAAOtE,KACtBsE,EAAOtE,GAAMA,KAAOA,GAGtBwC,KAAK8B,OAASA,EAId,IAAIiD,EAAQzD,EAAQyD,OAAS/E,KAAKd,KAAK6F,OAAS,MAChD/E,KAAK+E,MAAQ9D,EAAS8D,GAItB/E,KAAKqE,QAAU/C,EAAQ+C,SAAW,CAAA,EAElC,IAAK,IAAI7G,KAAQwC,KAAKqE,QAAS,CAC9B,IAAIxC,EAAS7B,KAAKqE,QAAQ7G,GAC1BqE,EAAOvH,OAAS,WAChBuH,EAAOrE,OAASA,CAChB,CAEIwC,KAAKqE,QAAQxB,OAAOZ,KACxBjC,KAAKqE,QAAQxB,MAAQ,IACjB7C,KAAKqE,QAAQxB,OAAS,CAAE,EAC3BZ,GAAIX,EAAQ8C,OAASpE,KAAKiC,KAMxBX,EAAQ0D,WAEXhF,KAAKgF,WAAoC,SAAvB1D,EAAQ0D,WAAwBhF,KAAOsD,EAAWmB,IAAInD,EAAQ0D,YAI5EhF,KAAKiF,QAERjF,KAAKgF,WAAahF,KAAKd,KAGvBc,KAAKgF,WAAchF,KAKjBA,KAAKgF,WAAWE,cACnBlF,KAAKmF,QAAU,CAACrD,EAAQR,KAChB,GAKTtB,KAAKoF,SAAW9D,EAAQ8D,SAGxB5K,OAAO6K,eAAerF,KAAM,OAAQ,CACnC/B,MAAOqH,EAAQtF,MAAMuF,UACrBC,UAAU,EACVC,YAAY,EACZC,cAAc,IAGfhG,EAAMO,IAAI,sBAAuBD,KACjC,CAED,OAAAmF,CAASrD,GAAQ6D,QAACA,EAxFT,OAwFwB,CAAA,GAChC,IAAK3F,KAAK4F,OAAO5F,KAAKgF,YAErB,OADAlD,EAAS9B,KAAK5B,GAAG4B,KAAKgF,WAAYlD,GAC3B9B,KAAKgF,WAAWG,QAAQrD,EAAQ,CAAC6D,YAGzC,IAAIzD,EAAY1H,OAAOqL,OAAO7F,KAAK8B,QAEnC,OAAOA,EAAOgE,OAAM,CAAC3L,EAAGL,KACvB,IAAI8I,EAAOV,EAAUpI,GAErB,GAAkB,UAAd8I,EAAKtI,MAAoBsI,EAAKnE,MAAO,CACxC,GAAIrD,OAAOC,MAAMlB,GAEhB,OAAO,EAGR,IAAKyE,EAAKE,GAAO8D,EAAKnE,MACtB,YAAgBiG,IAAR9F,GAAqBzE,GAAKyE,EAAM+G,UACxBjB,IAAR5F,GAAqB3E,GAAK2E,EAAM6G,EACxC,CAED,OAAO,CAAI,GAEZ,CAED,eAAIT,GACH,OAAO1K,OAAOqL,OAAO7F,KAAK8B,QAAQgE,OAAMC,KAAW,UAAWA,IAC9D,CAED,SAAI3B,GACH,OAAOpE,KAAKqE,SAASxB,OAAOZ,IAAMjC,KAAKiC,EACvC,CAED,WAAIgD,GACH,IAAK,IAAIhD,KAAMjC,KAAK8B,OACnB,GAA6B,UAAzB9B,KAAK8B,OAAOG,GAAI3H,KACnB,OAAO,EAIT,OAAO,CACP,CAED,SAAAmJ,CAAW5B,GACV,GAAsB,iBAAXA,EAEV,OADAA,EAASmE,EAAcnE,EAAQ7B,MAIhC,IAAI9F,EASJ,OANCA,EAFc,YAAX2H,EAEGrH,OAAOqL,OAAO7F,KAAKqE,SAAS,GAG5BrE,KAAKqE,QAAQxC,GAGhB3H,GACHA,EAAM8L,EAAc9L,EAAK8F,MAClB9F,GAGD,IACP,CAQD,MAAA0L,CAAQhE,GACP,QAAKA,IAIE5B,OAAS4B,GAAS5B,KAAKiC,KAAOL,GAAS5B,KAAKiC,KAAOL,EAAMK,GAChE,CAED,EAAA7D,CAAIwD,EAAOE,GACV,GAAyB,IAArBhC,UAAUxG,OAAc,CAC3B,MAAMuJ,EAAQ2B,EAAS5C,IACtBA,EAAOE,GAAU,CAACe,EAAMjB,MAAOiB,EAAMf,OACtC,CAID,GAFAF,EAAQ0B,EAAWmB,IAAI7C,GAEnB5B,KAAK4F,OAAOhE,GAEf,OAAOE,EAIRA,EAASA,EAAOrI,KAAIU,GAAKiB,OAAOC,MAAMlB,GAAK,EAAIA,IAG/C,IAGI8L,EAAiBC,EAHjBC,EAASnG,KAAKoG,KACdC,EAAYzE,EAAMwE,KAItB,IAAK,IAAItM,EAAI,EAAGA,EAAIqM,EAAO7M,QACtB6M,EAAOrM,GAAG8L,OAAOS,EAAUvM,IADGA,IAEjCmM,EAAkBE,EAAOrM,GACzBoM,EAAuBpM,EAOzB,IAAKmM,EAEJ,MAAM,IAAIK,MAAM,uCAAuCtG,YAAY4B,oCAIpE,IAAK,IAAI9H,EAAIqM,EAAO7M,OAAS,EAAGQ,EAAIoM,EAAsBpM,IACzDgI,EAASqE,EAAOrM,GAAGgL,OAAOhD,GAI3B,IAAK,IAAIhI,EAAIoM,EAAuB,EAAGpM,EAAIuM,EAAU/M,OAAQQ,IAC5DgI,EAASuE,EAAUvM,GAAG+K,SAAS/C,GAGhC,OAAOA,CACP,CAED,IAAA3D,CAAMyD,EAAOE,GACZ,GAAyB,IAArBhC,UAAUxG,OAAc,CAC3B,MAAMuJ,EAAQ2B,EAAS5C,IACtBA,EAAOE,GAAU,CAACe,EAAMjB,MAAOiB,EAAMf,OACtC,CAID,OAFAF,EAAQ0B,EAAWmB,IAAI7C,IAEVxD,GAAG4B,KAAM8B,EACtB,CAED,QAAApH,GACC,MAAO,GAAGsF,KAAKxC,SAASwC,KAAKiC,KAC7B,CAED,YAAAsE,GACC,IAAIrM,EAAM,GAEV,IAAK,IAAI+H,KAAMjC,KAAK8B,OAAQ,CAC3B,IAAIc,EAAO5C,KAAK8B,OAAOG,GACnBxD,EAAQmE,EAAKnE,OAASmE,EAAKH,SAC/BvI,EAAIqD,KAAKkB,GAAOG,KAAO,EACvB,CAED,OAAO1E,CACP,CAEDsM,gBAAkB,CAAA,EAGlB,cAAWjD,GACV,MAAO,IAAI,IAAI7B,IAAIlH,OAAOqL,OAAOvC,EAAWa,WAC5C,CAED,eAAOsC,CAAUxE,EAAIL,GAQpB,GAPyB,IAArB9B,UAAUxG,SAEb2I,GADAL,EAAQ9B,UAAU,IACPmC,IAGZL,EAAQ5B,KAAKyE,IAAI7C,GAEb5B,KAAKmE,SAASlC,IAAOjC,KAAKmE,SAASlC,KAAQL,EAC9C,MAAM,IAAI0E,MAAM,wCAAwCrE,MAKzD,GAHAjC,KAAKmE,SAASlC,GAAML,EAGK,IAArB9B,UAAUxG,QAAgBsI,EAAMgD,QACnC,IAAK,IAAI8B,KAAS9E,EAAMgD,QACvB5E,KAAKyG,SAASC,EAAO9E,GAIvB,OAAOA,CACP,CAMD,UAAO6C,CAAK7C,KAAU+E,GACrB,IAAK/E,GAASA,aAAiB0B,EAC9B,OAAO1B,EAKR,GAAgB,WAFFtH,EAAKsH,GAEO,CAEzB,IAAI1H,EAAMoJ,EAAWa,SAASvC,EAAM/G,eAEpC,IAAKX,EACJ,MAAM,IAAIqH,UAAU,mCAAmCK,MAGxD,OAAO1H,CACP,CAED,GAAIyM,EAAarN,OAChB,OAAOgK,EAAWmB,OAAOkC,GAG1B,MAAM,IAAIpF,UAAU,GAAGK,+BACvB,CAUD,mBAAOgF,CAAcC,EAAKC,GACzB,IACIlF,EAAOmE,EADPgB,EAAYzM,EAAKuM,GA4BrB,GAzBkB,WAAdE,EACCF,EAAInD,SAAS,MAEf9B,EAAOmE,GAASc,EAAIrI,MAAM,MAI1BoD,EAAOmE,GAAS,CAAA,CAAGc,GAGbtN,MAAMC,QAAQqN,IACrBjF,EAAOmE,GAASc,GAIjBjF,EAAQiF,EAAIjF,MACZmE,EAAQc,EAAIG,SAGbpF,EAAQ0B,EAAWmB,IAAI7C,GAElBA,IACJA,EAAQkF,IAGJlF,EACJ,MAAM,IAAIL,UAAU,uCAAuCsF,4EAK5D,GAFAE,EAAYzM,EAAKyL,GAEC,WAAdgB,GAAwC,WAAdA,GAA0BhB,GAAS,EAAG,CAEnE,IAAInD,EAAOpI,OAAOwH,QAAQJ,EAAME,QAAQiE,GAExC,GAAInD,EACH,MAAO,CAAChB,QAAOK,GAAIW,EAAK,GAAIqE,MAAOlB,KAAUnD,EAAK,GAEnD,CAEDhB,EAAQ0B,EAAWmB,IAAI7C,GAEvB,IAAIsF,EAAkBnB,EAAMlL,cAExBf,EAAI,EACR,IAAK,IAAImI,KAAML,EAAME,OAAQ,CAC5B,IAAIc,EAAOhB,EAAME,OAAOG,GAExB,GAAIA,EAAGpH,gBAAkBqM,GAAmBtE,EAAKpF,MAAM3C,gBAAkBqM,EACxE,MAAO,CAACtF,QAAOK,KAAIgF,MAAOnN,KAAM8I,GAGjC9I,GACA,CAED,MAAM,IAAIyH,UAAU,OAAOwE,0BAA8BnE,EAAMpE,8BAA8BhD,OAAOqJ,KAAKjC,EAAME,QAAQqF,KAAK,QAC5H,CAEDX,sBAAwB,CACvBlM,KAAM,YACNkD,KAAM,SAIR,SAAS8H,EAAS1D,GACjB,IAAI1H,EAAM,CAAC0H,GAEX,IAAK,IAAIwF,EAAIxF,EAAOwF,EAAIA,EAAElI,MACzBhF,EAAIqD,KAAK6J,GAGV,OAAOlN,CACR,CAEA,SAAS8L,EAAenE,GAAQC,OAACA,GAAU,CAAA,GAC1C,GAAID,EAAOC,SAAWD,EAAOtD,aAAc,CAC1CsD,EAAOvH,OAAS,WAChBuH,EAAOrE,OAAS,QAGhBqE,EAAOtD,aAAeF,EAAkBwD,EAAOC,QAE/C,IAAIuF,EAAe7M,OAAOwH,QAAQF,GAAQrI,KAAI,EAAEwI,EAAIC,GAAYpI,KAE/D,IAAIwN,EAAazF,EAAOtD,aAAazE,GAAG,GAEpCyI,EAAYL,EAAUzD,OAASyD,EAAUO,SACzCD,EAAU8E,EAAW7I,MAAO8I,EAAS,GAWzC,MARkB,gBAAdD,GACH9E,EAAU,CAAC,EAAG,KACd+E,EAAS,KAEa,WAAdD,IACRC,EAAS,OAGF,CAAChF,YAAWC,UAAS+E,SAAO,IAGrC1F,EAAO2F,gBAAkB,CAAC1F,EAAQ9G,IAC1B8G,EAAOrI,KAAI,CAACU,EAAGL,KACrB,IAAIyI,UAACA,EAASC,QAAEA,EAAO+E,OAAEA,GAAUF,EAAavN,GAQhD,OANIyI,GAAaC,IAChBrI,EAAI+D,EAASqE,EAAWC,EAASrI,IAGlCA,EAAIW,EAAgBX,EAAG,CAACa,YAAWC,KAAMsM,GAEjC,GAGV,CAED,OAAO1F,CACR,CCrbe,IAAA4F,EAAA,IAAInE,EAAW,CAC7BrB,GAAI,UACJzE,KAAM,UACNsE,OAAQ,CACPpI,EAAG,CAAC8D,KAAM,KACVkK,EAAG,CAAClK,KAAM,KACVmK,EAAG,CAACnK,KAAM,MAEXuH,MAAO,MACPV,QAAS,CACRxB,MAAO,CACNM,IAAK,CAAC,UAAW,SAGnByB,QAAS,CAAC,SCPI,MAAMgD,UAAsBtE,EAU1C,WAAAqB,CAAarD,GACPA,EAAQQ,SACZR,EAAQQ,OAAS,CAChB+F,EAAG,CACFpJ,MAAO,CAAC,EAAG,GACXjB,KAAM,OAEPsK,EAAG,CACFrJ,MAAO,CAAC,EAAG,GACXjB,KAAM,SAEPuK,EAAG,CACFtJ,MAAO,CAAC,EAAG,GACXjB,KAAM,UAKJ8D,EAAQpC,OACZoC,EAAQpC,KAAO8I,GAGZ1G,EAAQ2G,SAAW3G,EAAQ4G,YAC9B5G,EAAQwD,SAAWqD,IAClB,IAAIC,EAAMlP,EAAiBoI,EAAQ2G,QAASE,GAO5C,OALInI,KAAK+E,QAAU/E,KAAKd,KAAK6F,QAE5BqD,EAAMlH,EAAMlB,KAAK+E,MAAO/E,KAAKd,KAAK6F,MAAOqD,IAGnCA,CAAG,EAGX9G,EAAQuD,WAAauD,IACpBA,EAAMlH,EAAMlB,KAAKd,KAAK6F,MAAO/E,KAAK+E,MAAOqD,GAClClP,EAAiBoI,EAAQ4G,UAAWE,KAI7C9G,EAAQ8D,WAAa,UAErBiD,MAAM/G,EACN,ECrDa,SAASgH,EAAQzF,EAAOjB,GAGtC,OAFAiB,EAAQ2B,EAAS3B,IAEZjB,GAASiB,EAAMjB,MAAMgE,OAAOhE,GAEzBiB,EAAMf,OAAO7E,SAGrB2E,EAAQ0B,EAAWmB,IAAI7C,IACVzD,KAAK0E,EACnB,CCfe,SAAS4B,EAAK5B,EAAO0F,GACnC1F,EAAQ2B,EAAS3B,GAEjB,IAAIjB,MAACA,EAAKqF,MAAEA,GAAS3D,EAAWsD,aAAa2B,EAAM1F,EAAMjB,OAEzD,OADa0G,EAAOzF,EAAOjB,GACbqF,EACf,CCPe,SAASuB,EAAQ3F,EAAOjB,EAAOE,GAK7C,OAJAe,EAAQ2B,EAAS3B,GAEjBjB,EAAQ0B,EAAWmB,IAAI7C,GACvBiB,EAAMf,OAASF,EAAMxD,GAAGyE,EAAMjB,MAAOE,GAC9Be,CACR,CCDe,SAAS4F,EAAK5F,EAAO0F,EAAMtK,GAGzC,GAFA4E,EAAQ2B,EAAS3B,GAEQ,IAArB/C,UAAUxG,QAAuC,WAAvBgB,EAAKwF,UAAU,IAAkB,CAE9D,IAAI4I,EAAS5I,UAAU,GACvB,IAAK,IAAInG,KAAK+O,EACbD,EAAI5F,EAAOlJ,EAAG+O,EAAO/O,GAEtB,KACI,CACiB,mBAAVsE,IACVA,EAAQA,EAAMwG,EAAI5B,EAAO0F,KAG1B,IAAI3G,MAACA,EAAKqF,MAAEA,GAAS3D,EAAWsD,aAAa2B,EAAM1F,EAAMjB,OACrDE,EAASwG,EAAOzF,EAAOjB,GAC3BE,EAAOmF,GAAShJ,EAChBuK,EAAO3F,EAAOjB,EAAOE,EACrB,CAED,OAAOe,CACR,CDnBA2F,EAAOG,QAAU,QCqBjBF,EAAIE,QAAU,QC5BC,IAAAC,EAAA,IAAItF,EAAW,CAC7BrB,GAAI,UACJzE,KAAM,UACNuH,MAAO,MACP7F,KAAM8I,EACNnD,SAAU/C,GAAUZ,EAAM8G,EAAQjD,MAAO,MAAOjD,GAChDgD,OAAQhD,GAAUZ,EAAM,MAAO8G,EAAQjD,MAAOjD,KCL/C,MACM+G,EAAK,GAAK,IACVC,EAAI,MAAQ,GAElB,IAAI/D,EAAQjE,EAAOC,IAEJ,IAAAgI,EAAA,IAAIzF,EAAW,CAC7BrB,GAAI,MACJzE,KAAM,MACNsE,OAAQ,CACPkH,EAAG,CACFvG,SAAU,CAAC,EAAG,KACdjF,KAAM,aAEPyL,EAAG,CACFxG,SAAU,EAAE,IAAK,MAElBsF,EAAG,CACFtF,SAAU,EAAE,IAAK,OAMpBsC,MAACA,EAEA7F,KAAMgK,EAGN,QAAArE,CAAUxD,GAET,IAGI8H,EAHM9H,EAAI5H,KAAI,CAACwE,EAAOnE,IAAMmE,EAAQ8G,EAAMjL,KAGlCL,KAAIwE,GAASA,EAlCjB,oBAkC6BvC,KAAK0N,KAAKnL,IAAU6K,EAAI7K,EAAQ,IAAM,MAE3E,MAAO,CACL,IAAMkL,EAAE,GAAM,GACf,KAAOA,EAAE,GAAKA,EAAE,IAChB,KAAOA,EAAE,GAAKA,EAAE,IAEjB,EAID,MAAArE,CAAQuE,GAEP,IAAIF,EAAI,GAaR,OAZAA,EAAE,IAAME,EAAI,GAAK,IAAM,IACvBF,EAAE,GAAKE,EAAI,GAAK,IAAMF,EAAE,GACxBA,EAAE,GAAKA,EAAE,GAAKE,EAAI,GAAK,IAGb,CACTF,EAAE,GAAON,EAAKnN,KAAK4N,IAAIH,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAML,EACrEO,EAAI,GAAK,EAAK3N,KAAK4N,KAAKD,EAAI,GAAK,IAAM,IAAK,GAAKA,EAAI,GAAKP,EAC1DK,EAAE,GAAON,EAAKnN,KAAK4N,IAAIH,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAML,GAI3DrP,KAAI,CAACwE,EAAOnE,IAAMmE,EAAQ8G,EAAMjL,IAC3C,EAEDuK,QAAS,CACR0E,IAAO,CACNjH,OAAQ,CAAC,0BAA2B,gCAAiC,qCCtEjE,SAASyH,EAAWC,GAC1B,OAASA,EAAQ,IAAO,KAAO,GAChC,CCEe,IAAAC,EAAA,IAAInG,EAAW,CAC7BrB,GAAI,MACJzE,KAAM,MACNsE,OAAQ,CACPkH,EAAG,CACFvG,SAAU,CAAC,EAAG,KACdjF,KAAM,aAEPrD,EAAG,CACFsI,SAAU,CAAC,EAAG,KACdjF,KAAM,UAEPkM,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,QAIR0B,KAAMmK,EACN,QAAAxE,CAAUwE,GAET,IACIM,GADCC,EAAGX,EAAGlB,GAAKsB,EAWhB,OANCM,EADGjO,KAAKE,IAAIqN,GAFH,KAEavN,KAAKE,IAAImM,GAFtB,IAGH5K,IAGmB,IAAnBzB,KAAKmO,MAAM9B,EAAGkB,GAAWvN,KAAKS,GAG9B,CACNyN,EACAlO,KAAKoO,KAAKb,GAAK,EAAIlB,GAAK,GACxBgC,EAAeJ,GAEhB,EACD,MAAA7E,CAAQkF,GAEP,IAAKC,EAAWC,EAAQC,GAAOH,EAS/B,OAPIE,EAAS,IACZA,EAAS,GAGN7O,MAAM8O,KACTA,EAAM,GAEA,CACNF,EACAC,EAASxO,KAAK0O,IAAID,EAAMzO,KAAKS,GAAK,KAClC+N,EAASxO,KAAK2O,IAAIF,EAAMzO,KAAKS,GAAK,KAEnC,EAEDkI,QAAS,CACRoF,IAAO,CACN3H,OAAQ,CAAC,0BAA2B,0BAA2B,0BClDlE,MAAMwI,EAAU,IAAM,EAChBC,EAAI7O,KAAKS,GACTqO,EAAM,IAAMD,EACZE,EAAMF,EAAI,IAEhB,SAASG,EAAMhR,GAGd,MAAMiR,EAAKjR,EAAIA,EAGf,OAFWiR,EAAKA,EAAKA,EAAKjR,CAG3B,CAEe,SAAQkR,EAAE/H,EAAOgI,GAAQC,GAACA,EAAK,EAACC,GAAEA,EAAK,EAACC,GAAEA,EAAK,GAAK,KACjEnI,EAAOgI,GAAUrG,EAAS,CAAC3B,EAAOgI,IAanC,IAAKI,EAAIC,EAAIC,GAAMpC,EAAI5K,KAAK0E,GACxBuI,EAAK3B,EAAItL,KAAK4K,EAAK,CAACkC,EAAIC,EAAIC,IAAK,IAChCE,EAAIC,EAAIC,GAAMxC,EAAI5K,KAAK0M,GACxBW,EAAK/B,EAAItL,KAAK4K,EAAK,CAACsC,EAAIC,EAAIC,IAAK,GAMjCH,EAAK,IACRA,EAAK,GAEFI,EAAK,IACRA,EAAK,GAGN,IAIIC,EAAKf,GAJGU,EAAKI,GAAM,GAMnBE,EAAI,IAAO,EAAIhQ,KAAKoO,KAAK2B,GAAMA,EAAKnB,KAIpCqB,GAAU,EAAID,GAAKR,EACnBU,GAAU,EAAIF,GAAKJ,EAGnBO,EAASnQ,KAAKoO,KAAK6B,GAAU,EAAIR,GAAM,GACvCW,EAASpQ,KAAKoO,KAAK8B,GAAU,EAAIL,GAAM,GAKvCQ,EAAiB,IAAXJ,GAAuB,IAAPR,EAAY,EAAIzP,KAAKmO,MAAMsB,EAAIQ,GACrDK,EAAiB,IAAXJ,GAAuB,IAAPL,EAAY,EAAI7P,KAAKmO,MAAM0B,EAAIK,GAErDG,EAAK,IACRA,GAAM,EAAIxB,GAEPyB,EAAK,IACRA,GAAM,EAAIzB,GAGXwB,GAAMvB,EACNwB,GAAMxB,EAGN,IAOI,EAPA,EAAKa,EAAKJ,EACV,EAAKa,EAASD,EAGdI,EAAQD,EAAKD,EACbG,EAAOH,EAAKC,EACZG,EAAOzQ,KAAKE,IAAIqQ,GAGhBJ,EAASC,GAAW,EACvB,EAAK,EAEGK,GAAQ,IAChB,EAAKF,EAEGA,EAAQ,IAChB,EAAKA,EAAQ,IAELA,GAAS,IACjB,EAAKA,EAAQ,IAGb7L,EAASO,KAAK,gCAIf,IAUIyL,EAVA,EAAK,EAAI1Q,KAAKoO,KAAKgC,EAASD,GAAUnQ,KAAK2O,IAAI,EAAKI,EAAM,GAG1D4B,GAASpB,EAAKI,GAAM,EACpBiB,GAAST,EAASC,GAAU,EAC5BS,EAAS7B,EAAK4B,GAOjBF,EADGP,EAASC,GAAW,EACfI,EAEAC,GAAQ,IACRD,EAAO,EAEPA,EAAO,KACNA,EAAO,KAAO,GAGdA,EAAO,KAAO,EAQxB,IAAIM,GAAOH,EAAQ,KAAO,EACtBI,EAAK,EAAM,KAAQD,EAAO9Q,KAAKoO,KAAK,GAAK0C,GAGzCE,EAAK,EAAI,KAAQJ,EAGjBK,EAAI,EACRA,GAAM,IAAOjR,KAAK0O,KAAUgC,EAAQ,IAAO3B,GAC3CkC,GAAM,IAAOjR,KAAK0O,IAAM,EAAIgC,EAAe3B,GAC3CkC,GAAM,IAAOjR,KAAK0O,KAAM,EAAIgC,EAAS,GAAM3B,GAC3CkC,GAAM,GAAOjR,KAAK0O,KAAM,EAAIgC,EAAS,IAAM3B,GAI3C,IAAImC,EAAK,EAAI,KAAQN,EAAQK,EAMzB,EAAK,GAAKjR,KAAKyD,KAAK,IAAOiN,EAAQ,KAAO,KAAO,GACjDS,EAAK,EAAInR,KAAKoO,KAAKyC,GAAUA,EAASjC,IAItCwC,GAAM,GAAMhC,EAAK2B,KAAQ,EAI7B,OAHAK,IAAO,GAAM/B,EAAK2B,KAAQ,EAC1BI,IAAO,GAAM9B,EAAK4B,KAAQ,EAC1BE,IANU,EAAIpR,KAAK2O,IAAI,EAAI,EAAKI,GAAOoC,GAM3B,GAAM9B,EAAK2B,KAAQ,GAAM1B,EAAK4B,IACnClR,KAAKoO,KAAKgD,EAElB,CC5KA,MAAMC,EAAa,CAClB,CAAE,iBAAoB,mBAAqB,mBAC3C,CAAE,kBAAoB,kBAAqB,mBAC3C,CAAE,kBAAoB,kBAAqB,oBAGtCC,EAAa,CAClB,CAAG,oBAAqB,kBAAqB,mBAC7C,EAAG,kBAAqB,mBAAqB,mBAC7C,EAAG,mBAAqB,kBAAqB,qBAExCC,EAAa,CAClB,CAAE,iBAAqB,mBAAqB,mBAC5C,CAAE,oBAAqB,iBAAqB,kBAC5C,CAAE,kBAAqB,mBAAqB,oBAGvCC,GAAa,CAClB,CAAE,EAAqB,kBAAqB,mBAC5C,CAAE,GAAqB,mBAAqB,mBAC5C,CAAE,GAAqB,mBAAqB,qBAG9B,IAAAC,GAAA,IAAI7J,EAAW,CAC7BrB,GAAI,QACJzE,KAAM,QACNsE,OAAQ,CACPkH,EAAG,CACFvG,SAAU,CAAC,EAAG,GACdjF,KAAM,aAEPyL,EAAG,CACFxG,SAAU,EAAE,GAAK,KAElBsF,EAAG,CACFtF,SAAU,EAAE,GAAK,MAKnBsC,MAAO,MACP7F,KAAM8I,EACN,QAAAnD,CAAUxD,GAET,IAGI+L,EAHMlU,EAAiB6T,EAAY1L,GAGxB5H,KAAIoF,GAAOnD,KAAK0N,KAAKvK,KAEpC,OAAO3F,EAAiB+T,EAAYG,EAEpC,EACD,MAAAtI,CAAQqI,GAEP,IAGIE,EAHOnU,EAAiBgU,GAAYC,GAGzB1T,KAAIoF,GAAOA,GAAO,IAEjC,OAAO3F,EAAiB8T,EAAYK,EACpC,EAEDhJ,QAAS,CACRiJ,MAAS,CACRxL,OAAQ,CAAC,0BAA2B,gCAAiC,qCChEzD,SAAAyL,GAAU1K,EAAOgI,IAC9BhI,EAAOgI,GAAUrG,EAAS,CAAC3B,EAAOgI,IAKnC,IAAKI,EAAIC,EAAIC,GAAMmC,GAAMnP,KAAK0E,IACzBwI,EAAIC,EAAIC,GAAM+B,GAAMnP,KAAK0M,GAC1B,EAAKI,EAAKI,EACV,EAAKH,EAAKI,EACV,EAAKH,EAAKI,EACd,OAAO7P,KAAKoO,KAAK,GAAM,EAAI,GAAM,EAAI,GAAM,EAC5C,CCfA,MAAM0D,GAAI,MAMK,SAASrI,GAAStC,EAAOjB,GAAO+D,QAACA,EAAU6H,IAAK,IAC9D3K,EAAQ2B,EAAS3B,GAEZjB,IACJA,EAAQiB,EAAMjB,OAGfA,EAAQ0B,EAAWmB,IAAI7C,GACvB,IAAIE,EAASe,EAAMf,OAMnB,OAJIF,IAAUiB,EAAMjB,QACnBE,EAASF,EAAMzD,KAAK0E,IAGdjB,EAAMuD,QAAQrD,EAAQ,CAAC6D,WAC/B,CCxBe,SAAS8H,GAAO5K,GAC9B,MAAO,CACNjB,MAAOiB,EAAMjB,MACbE,OAAQe,EAAMf,OAAO7E,QACrBI,MAAOwF,EAAMxF,MAEf,CCDe,SAASqQ,GAAUC,EAAQC,EAAQhM,EAAQ,OAIzD,IAAIiM,GAHJjM,EAAQ0B,EAAWmB,IAAI7C,IAGHzD,KAAKwP,GACrBG,EAAUlM,EAAMzD,KAAKyP,GAEzB,OAAOlS,KAAKoO,KAAK+D,EAAQE,QAAO,CAACC,EAAKC,EAAInU,KACzC,IAAIoU,EAAKJ,EAAQhU,GACjB,OAAIuB,MAAM4S,IAAO5S,MAAM6S,GACfF,EAGDA,GAAOE,EAAKD,IAAO,CAAC,GACzB,GACJ,CCRA,MACMxD,GADI/O,KAAKS,GACC,ICRD,IAAAgS,GAAA,IAAI7K,EAAW,CAK7BrB,GAAI,cACJmC,MAAO,gBACP5G,KAAM,mBACNsE,OAAQ,CACPpI,EAAG,CACF+I,SAAU,CAAC,EAAG,QACdjF,KAAM,MAEPkK,EAAG,CACFjF,SAAU,CAAC,EAAG,KACdjF,KAAM,MAEPmK,EAAG,CACFlF,SAAU,CAAC,EAAG,SACdjF,KAAM,OAIR0B,KAAM8I,EACNnD,SAAUxD,GAIFA,EAAI5H,KAAK2U,GAAK1S,KAAKoD,IA9BjB,IA8BqBsP,EAAQ,KAEvCtJ,OAAQuJ,GAEAA,EAAO5U,KAAI2U,GAAK1S,KAAKoD,IAAIsP,EAlCvB,IAkC+B,OCjC1C,MAAMrG,GAAI,KACJD,GAAI,IACJ/M,GAAI,KAAI,MAERkT,GAAK,SACLC,GAAK,KAAI,IACTI,GAAK,QAELC,GAAO,IAAY,IAAM,MACzBlP,IAAK,IACLmP,GAAK,sBAELC,GAAc,CACnB,CAAG,UAAY,QAAW,SAC1B,EAAG,OAAY,SAAW,UAC1B,EAAG,SAAY,MAAW,WAGrBC,GAAc,CACnB,CAAG,oBAAsB,mBAAqB,kBAC9C,CAAG,mBAAsB,mBAAqB,oBAC9C,EAAG,oBAAsB,kBAAqB,qBAEzCC,GAAc,CACnB,CAAG,GAAW,GAAW,GACzB,CAAG,OAAW,SAAW,SACzB,CAAG,QAAW,UAAW,WAGpBC,GAAc,CACnB,CAAE,EAAqB,kBAAsB,oBAC7C,CAAE,mBAAqB,mBAAsB,oBAC7C,CAAE,mBAAqB,oBAAsB,oBAG/B,IAAAC,GAAA,IAAIvL,EAAW,CAC7BrB,GAAI,SACJzE,KAAM,SACNsE,OAAQ,CACPgN,GAAI,CACHrM,SAAU,CAAC,EAAG,GACdjF,KAAM,MAEPuR,GAAI,CACHtM,SAAU,EAAE,GAAK,KAElBuM,GAAI,CACHvM,SAAU,EAAE,GAAK,MAInBvD,KAAMiP,GACN,QAAAtJ,CAAUxD,GAMT,IAAM4N,EAAIC,EAAIC,GAAO9N,EAUjB+N,EAHMlW,EAAiBuV,GAAa,CAJ9B1G,GAAIkH,GAAQlH,GAAI,GAAKoH,EACrBrH,GAAIoH,GAAQpH,GAAI,GAAKmH,EAGmBE,IAGlC1V,KAAK,SAAUoF,GAI9B,QAHUoP,GAAMC,IAAOrP,EAAM,MAAU9D,KAC3B,EAAKuT,IAAOzP,EAAM,MAAU9D,MA/DjC,kBAkEV,KAGQsU,EAAIN,EAAIC,GAAM9V,EAAiByV,GAAaS,GAIlD,MAAO,EADI,EAAI/P,IAAKgQ,GAAO,EAAKhQ,GAAIgQ,GAAOb,GAC/BO,EAAIC,EAChB,EACD,MAAAlK,CAAQ+J,GACP,IAAKS,EAAIP,EAAIC,GAAMH,EAOfxB,EAHQnU,EAAiB0V,GAAa,EAHhCU,EAAKd,KAAO,EAAInP,GAAIA,IAAKiQ,EAAKd,KAGQO,EAAIC,IAGpCvV,KAAI,SAAUoF,GAK7B,OAFQ,MAFGoP,GAAMpP,GAAO0P,KACXD,GAAMzP,GAAO0P,GAASL,MAzFzB,iBA6Fb,KAGQqB,EAAIC,EAAIL,GAAOjW,EAAiBwV,GAAarB,GAG/C4B,GAAMM,GAAOxH,GAAI,GAAKoH,GAAOpH,GAEjC,MAAO,CAAEkH,GADCO,GAAO1H,GAAI,GAAKmH,GAAOnH,GAChBqH,EACjB,EAED9K,QAAS,CAERxB,MAAS,CACRf,OAAQ,CAAC,0BAA2B,gCAAiC,qCC9GzD2N,GAAA,IAAInM,EAAW,CAC7BrB,GAAI,SACJzE,KAAM,SACNsE,OAAQ,CACPgN,GAAI,CACHrM,SAAU,CAAC,EAAG,GACdjF,KAAM,MAEPkS,GAAI,CACHjN,SAAU,CAAC,EAAG,GACdjF,KAAM,UAEPmS,GAAI,CACHlN,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,QAIR0B,KAAM2P,GACN,QAAAhK,CAAU+K,GAET,IACIjG,GADC2F,EAAIP,EAAIC,GAAMY,EAEnB,MAAM,EAAI,KASV,OANCjG,EADGjO,KAAKE,IAAImT,GAAM,GAAKrT,KAAKE,IAAIoT,GAAM,EAChC7R,IAGqB,IAArBzB,KAAKmO,MAAMmF,EAAID,GAAYrT,KAAKS,GAGhC,CACNmT,EACA5T,KAAKoO,KAAKiF,GAAM,EAAIC,GAAM,GAC1BjF,EAAeJ,GAEhB,EACD7E,OAAQ2K,GAGA,CACNA,EAAO,GACPA,EAAO,GAAK/T,KAAK0O,IAAIqF,EAAO,GAAK/T,KAAKS,GAAK,KAC3CsT,EAAO,GAAK/T,KAAK2O,IAAIoF,EAAO,GAAK/T,KAAKS,GAAK,QC7C9C,MAAM8R,GAAK,SACLC,GAAK,KAAO,IACZI,GAAK,QACLuB,GAAK,KAAO,MACZC,GAAK,KAAO,GACZC,GAAM,MAAQ,KACdC,GAAM,GAAK,KAIXjD,GAAa,CAClB,CAAG,kBAAqB,mBAAqB,kBAC7C,EAAG,kBAAqB,kBAAqB,mBAC7C,CAAG,kBAAqB,kBAAqB,oBAiBxCkD,GAAa,CAClB,CAAG,GAAe,GAAmB,GACrC,CAAG,KAAO,MAAO,MAAQ,KAAO,KAAO,MACvC,CAAE,MAAQ,MAAO,MAAQ,MAAQ,IAAM,OAIlCC,GAAa,CAClB,CAAE,kBAAqB,kBAAqB,kBAC5C,CAAE,mBAAqB,mBAAqB,mBAC5C,CAAE,kBAAqB,mBAAqB,oBASvClD,GAAa,CAClB,CAAG,oBAAqB,mBAAqB,mBAC7C,CAAG,kBAAqB,mBAAqB,mBAC7C,EAAG,mBAAqB,kBAAqB,qBAU/B,IAAAmD,GAAA,IAAI7M,EAAW,CAC7BrB,GAAI,QACJzE,KAAM,QAUNsE,OAAQ,CACPhI,EAAG,CACF2I,SAAU,CAAC,EAAG,GACdjF,KAAM,KAEP4S,GAAI,CACH3N,SAAU,EAAE,GAAK,IACjBjF,KAAM,MAEP6S,GAAI,CACH5N,SAAU,EAAE,GAAK,IACjBjF,KAAM,OAIR0B,KAAMiP,GACNtJ,SAAUxD,GAaX,SAAqBgM,GAGpB,IAAI+B,EAAQ/B,EAAI5T,KAAK,SAAUoF,GAI9B,QAHUoP,GAAMC,IAAOrP,EAAM,MAAUgR,KAC3B,EAAKvB,IAAOzP,EAAM,MAAUgR,MAEfC,EAC3B,IAGC,OAAO5W,EAAiB+W,GAAYb,EACrC,CArBSkB,CAFGpX,EAAiB6T,GAAY1L,IAIxC,MAAAyD,CAAQyL,GACP,IAAIlD,EAoBN,SAAqBkD,GACpB,IAAInB,EAAQlW,EAAiBgX,GAAYK,GAGrClD,EAAM+B,EAAM3V,KAAK,SAAUoF,GAG9B,OAAO,KAFInD,KAAKoD,IAAKD,GAAOmR,GAAO/B,GAAI,IAC1BC,GAAMI,GAAMzP,GAAOmR,MACCD,EACnC,IAEC,OAAO1C,CACR,CA/BYmD,CAAWD,GAErB,OAAOrX,EAAiB8T,GAAYK,EACpC,IClGF,MAAMtI,GAAQjE,EAAOE,IACfyP,GAAc,IACdC,GAAiB,EAAID,GACrBE,GAAM,EAAIjV,KAAKS,GAEfyU,GAAQ,CACb,CAAG,QAAW,SAAW,SACzB,EAAG,QAAW,SAAW,SACzB,EAAG,QAAW,QAAW,UAGpBC,GAAW,CAChB,CAAC,oBAAqB,mBAAoB,oBAC1C,CAAC,mBAAqB,mBAAqB,qBAC3C,EAAE,qBAAuB,mBAAqB,qBAGzChB,GAAK,CACV,CAAC,IAAO,IAAO,KACf,CAAC,KAAQ,KAAQ,KACjB,CAAC,KAAQ,KAAQ,OAGZiB,GAAc,CACnBC,KAAM,CAAC,GAAK,KAAO,IACnBC,IAAK,CAAC,GAAK,IAAM,IACjBC,QAAS,CAAC,EAAG,IAAM,IAGdC,GAAa,CAElBxH,EAAG,CAAC,MAAO,GAAO,OAAQ,OAAQ,QAClCyH,EAAG,CAAC,GAAK,GAAK,EAAK,IAAK,IACxBC,EAAG,CAAC,EAAK,IAAO,IAAO,IAAO,MAGzBC,GAAU,IAAM3V,KAAKS,GACrBmV,GAAU5V,KAAKS,GAAK,IAEnB,SAAS+E,GAAOY,EAAQyP,GAC9B,MAAMC,EAAO1P,EAAOrI,KAAIU,IACvB,MAAMT,EAAIuF,EAAKsS,EAAK7V,KAAKE,IAAIzB,GAAK,IAAMsW,IACxC,OAAO,IAAM1R,EAASrF,EAAGS,IAAMT,EAAI,MAAM,IAE1C,OAAO8X,CACR,CAsCO,SAASC,GACfC,EACAC,EACAC,EACAC,EACAC,GAGA,MAAM5R,EAAM,CAAA,EAEZA,EAAI4R,YAAcA,EAClB5R,EAAIwR,SAAWA,EACfxR,EAAI2R,SAAWA,EACf,MAAME,EAAOL,EAASjY,KAAIU,GACd,IAAJA,IAIR+F,EAAI8R,GAAKL,EAETzR,EAAI+R,GAAKL,EAET,MAAMM,EAAKH,EAAK,GAGVI,EAAOjZ,EAAiB0X,GAAOmB,GAI/B5I,GADN0I,EAAWf,GAAY5Q,EAAI2R,WACR,GACnB3R,EAAI/F,EAAI0X,EAAS,GACjB3R,EAAIkS,GAAKP,EAAS,GAElB,MACMQ,GADI,GAAK,EAAInS,EAAI8R,GAAK,KACZ,EAGhB9R,EAAIqR,GAAMc,EAAKnS,EAAI8R,GAAK,IAAO,EAAIK,IAAO,EAAIA,GAAM3W,KAAK0N,KAAK,EAAIlJ,EAAI8R,IACtE9R,EAAIoS,OAASpS,EAAIqR,IAAM,IAEvBrR,EAAInF,EAAImF,EAAI+R,GAAKC,EACjBhS,EAAIyH,EAAI,KAAOjM,KAAKoO,KAAK5J,EAAInF,GAC7BmF,EAAIqS,IAAM,KAASrS,EAAInF,IAAM,GAC7BmF,EAAIsS,IAAMtS,EAAIqS,IAId,MAAMlT,EAAI,EACT,EACA3D,KAAKoD,IACJpD,KAAKkD,IAAIuK,GAAK,EAAI,EAAI,IAAMzN,KAAKyD,MAAMe,EAAI8R,GAAK,IAAM,KAAM,GAC5D,GAEF9R,EAAIuS,KAAON,EAAK1Y,KAAIU,GACZ0D,EAAY,EAAGqU,EAAK/X,EAAGkF,KAE/Ba,EAAIwS,QAAUxS,EAAIuS,KAAKhZ,KAAIU,GACnB,EAAIA,IAIZ,MAAMwY,EAAQR,EAAK1Y,KAAI,CAACU,EAAGL,IACnBK,EAAI+F,EAAIuS,KAAK3Y,KAEf8Y,EAAQ1R,GAAMyR,EAAOzS,EAAIqR,IAK/B,OAJArR,EAAI2S,GAAK3S,EAAIqS,KAAO,EAAIK,EAAM,GAAKA,EAAM,GAAK,IAAOA,EAAM,IAIpD1S,CACR,CAGA,MAAM4S,GAAoBrB,GACzB1M,GACA,GAAKrJ,KAAKS,GAAK,GAAK,GACpB,WACA,GAGM,SAAS4W,GAAWC,EAAO9S,GAIjC,UAAmBwE,IAAZsO,EAAMC,OAAgCvO,IAAZsO,EAAME,GACtC,MAAM,IAAI5M,MAAM,oDAGjB,UAAmB5B,IAAZsO,EAAMG,OAAgCzO,IAAZsO,EAAMxR,OAAgCkD,IAAZsO,EAAM5L,GAChE,MAAM,IAAId,MAAM,yDAIjB,UAAmB5B,IAAZsO,EAAMtJ,OAAgChF,IAAZsO,EAAM5B,GACtC,MAAM,IAAI9K,MAAM,oDAIjB,GAAgB,IAAZ0M,EAAMC,GAAyB,IAAZD,EAAME,EAC5B,MAAO,CAAC,EAAK,EAAK,GAInB,IAAIE,EAAO,EAEVA,OADe1O,IAAZsO,EAAMtJ,EACFH,EAAUyJ,EAAMtJ,GAAK4H,GAtHvB,SAA2BF,GACjC,IAAIiC,GAAOjC,EAAI,IAAM,KAAO,IAC5B,MAAMtX,EAAI4B,KAAKI,MAAM,IAAOuX,GAC5BA,GAAU,IACV,MAAO7T,EAAI8T,GAAOpC,GAAWxH,EAAEzM,MAAMnD,EAAGA,EAAI,IACrCyZ,EAAIC,GAAOtC,GAAWC,EAAElU,MAAMnD,EAAGA,EAAI,GAE5C,OAAOyP,GACL8J,GAAMG,EAAMhU,EAAK+T,EAAKD,GAAO,IAAM9T,EAAKgU,IACxCH,GAAMG,EAAMD,GAAM,IAAMC,GAE3B,CA8GSC,CAAiBT,EAAM5B,GAAKE,GAGpC,MAAMoC,EAAOhY,KAAK0O,IAAIgJ,GAChBO,EAAOjY,KAAK2O,IAAI+I,GAGtB,IAAIQ,EAAQ,OACIlP,IAAZsO,EAAMC,EACTW,EAA+B,GAAvB3U,EAAK+T,EAAMC,EAAG,SAEFvO,IAAZsO,EAAME,IACdU,EAAQ,IAAO1T,EAAI/F,EAAI6Y,EAAME,IAAMhT,EAAI2S,GAAK,GAAK3S,EAAIoS,SAItD,IAAIjV,EAAQ,OACIqH,IAAZsO,EAAMG,EACT9V,EAAQ2V,EAAMG,EAAIS,OAEElP,IAAZsO,EAAMxR,EACdnE,EAAS2V,EAAMxR,EAAItB,EAAIoS,OAAUsB,OAEblP,IAAZsO,EAAM5L,IACd/J,EAAQ,KAAU2V,EAAM5L,GAAK,GAAMlH,EAAI2S,GAAK,GAAK3S,EAAI/F,GAEtD,MAAM0Z,EAAI5U,EACT5B,EAAQ3B,KAAK4N,IAAI,KAAO5N,KAAK4N,IAAI,IAAMpJ,EAAInF,IAAK,KAChD,GAAK,GAIA+Y,EAAK,KAAQpY,KAAK0O,IAAIgJ,EAAO,GAAK,KAGlCja,EAAI+G,EAAI2S,GAAK5T,EAAK2U,EAAO,EAAI1T,EAAI/F,EAAI+F,EAAIyH,GAGzCoM,EAAK,IAAM,GAAK7T,EAAIkS,GAAKlS,EAAIsS,IAAMsB,EACnCE,EAAK7a,EAAI+G,EAAIqS,IACb1K,EACL,IAAMmM,EAAK,MACX5U,EAAKyU,EAAG,GAAKE,EAAKF,GAAK,GAAKH,EAAO,IAAMC,IAMpCM,EAhMA,SAAkBC,EAAS3C,GACjC,MAAM4C,EAAW,IAAM5C,EAAM,OAASb,GACtC,OAAOwD,EAAQza,KAAIU,IAClB,MAAMia,EAAO1Y,KAAKE,IAAIzB,GACtB,OAAO4E,EAASoV,EAAWlV,EAAKmV,GAAQ,IAAMA,GAAO1D,IAAiBvW,EAAE,GAE1E,CA0Leka,CACbnb,EAAiB2W,GAAI,CAACmE,EALbnM,EAAI6L,EACJ7L,EAAI8L,IAIoBla,KAAIU,GACzB,EAAJA,EAAQ,OAEhB+F,EAAIqR,IAEL,OAAOrY,EACN2X,GACAoD,EAAMxa,KAAI,CAACU,EAAGL,IACNK,EAAI+F,EAAIwS,QAAQ5Y,MAEvBL,KAAIU,GACEA,EAAI,KAEb,CAGO,SAASma,GAASC,EAAQrU,GAEhC,MAAMsU,EAASD,EAAO9a,KAAIU,GACd,IAAJA,IAEFsa,EAAOvT,GACZhI,EAAiB0X,GAAO4D,GAAQ/a,KAAI,CAACU,EAAGL,IAChCK,EAAI+F,EAAIuS,KAAK3Y,KAErBoG,EAAIqR,IAICtI,EAAIwL,EAAK,KAAO,GAAKA,EAAK,GAAKA,EAAK,IAAM,GAC1C1M,GAAK0M,EAAK,GAAKA,EAAK,GAAK,EAAIA,EAAK,IAAM,EACxCrB,GAAS1X,KAAKmO,MAAM9B,EAAGkB,GAAK0H,GAAOA,IAAOA,GAG1CmD,EAAK,KAAQpY,KAAK0O,IAAIgJ,EAAO,GAAK,KASlC/V,EAAQ4B,EANb,IAAM,GAAKiB,EAAIkS,GAAKlS,EAAIsS,IACxBpT,EACC0U,EAAKpY,KAAKoO,KAAKb,GAAK,EAAIlB,GAAK,GAC7B0M,EAAK,GAAKA,EAAK,GAAK,KAAOA,EAAK,GAAK,MAGjB,IAAO/Y,KAAK4N,IAAI,KAAO5N,KAAK4N,IAAI,IAAMpJ,EAAInF,GAAI,KAK9D6Y,EAAQ3U,EAFJiB,EAAIqS,KAAO,EAAIkC,EAAK,GAAKA,EAAK,GAAK,IAAOA,EAAK,IAElCvU,EAAI2S,GAAI,GAAM3S,EAAI/F,EAAI+F,EAAIyH,GAG3CsL,EAAI,IAAMhU,EAAK2U,EAAO,GAGtBV,EAAK,EAAIhT,EAAI/F,EAAIyZ,GAAS1T,EAAI2S,GAAK,GAAK3S,EAAIoS,OAG5Ca,EAAI9V,EAAQuW,EAGZpS,EAAI2R,EAAIjT,EAAIoS,OAGZ5I,EAAIH,EAAU6J,EAAO/B,IAGrBD,EA3PA,SAAwB1H,GAC9B,IAAIgL,EAAKnL,EAAUG,GACfgL,GAAMxD,GAAWxH,EAAE,KACtBgL,GAAM,KAGP,MAAM5a,EAAIwF,EAAW4R,GAAWxH,EAAGgL,GAAM,GAClClV,EAAI8T,GAAOpC,GAAWxH,EAAEzM,MAAMnD,EAAGA,EAAI,IACrCyZ,EAAIC,GAAOtC,GAAWC,EAAElU,MAAMnD,EAAGA,EAAI,GAGtC+Z,GAAKa,EAAKlV,GAAM+T,EACtB,OAHWrC,GAAWE,EAAEtX,GAGX,IAAM+Z,GAAMA,GAAKP,EAAMoB,GAAMlB,EAC3C,CA8OWmB,CAAcjL,GAOxB,MAAO,CAACuJ,EAAGA,EAAGE,EAAGA,EAAGzJ,EAAGA,EAAGtC,EAJhB,GAAKnI,EAAKiB,EAAI/F,EAAIkD,GAAS6C,EAAI2S,GAAK,GAAI,IAIlBK,EAAGA,EAAG1R,EAAGA,EAAG4P,EAAGA,EAChD,CASe,IAAA4B,GAAA,IAAI1P,EAAW,CAC7BrB,GAAI,YACJmC,MAAO,cACP5G,KAAM,YACNsE,OAAQ,CACP8S,EAAG,CACFnS,SAAU,CAAC,EAAG,KACdjF,KAAM,KAEPnE,EAAG,CACFoJ,SAAU,CAAC,EAAG,KACdjF,KAAM,gBAEPkM,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,QAIR0B,KAAMuI,EAEN,QAAA5C,CAAUuD,GACT,MAAM4K,EAAQsB,GAAQlM,EAAK0K,IAC3B,MAAO,CAACE,EAAMC,EAAGD,EAAMxR,EAAGwR,EAAMtJ,EAChC,EACD5E,OAAQkO,GACAD,GACN,CAACE,EAAGD,EAAM,GAAIxR,EAAGwR,EAAM,GAAItJ,EAAGsJ,EAAM,IACpCF,MChWH,MAAM/N,GAAQjE,EAAOE,IACfwM,GAAI,IAAM,MACV1E,GAAI,MAAQ,GASlB,SAAS+L,GAAWC,GAGnB,OAAQA,EAAQ,EAAMpZ,KAAK4N,KAAKwL,EAAQ,IAAM,IAAK,GAAKA,EAAQhM,EACjE,CA0EA,SAASiM,GAAO3M,EAAKlI,GAGpB,MAAM2T,EApFE,MAJSnM,EAwFCU,EAAI,IArFNoF,GAAK9R,KAAK0N,KAAK1B,IAAMoB,GAAIpB,EAAI,IAAM,KAC7B,GAJvB,IAAkBA,EAyFjB,GAAU,IAANmM,EACH,MAAO,CAAC,EAAK,EAAK,GAEnB,MAAMb,EAAQsB,GAAQlM,EAAK0K,IAC3B,MAAO,CAACvJ,EAAUyJ,EAAMtJ,GAAIsJ,EAAMG,EAAGU,EACtC,CAGO,MAAMf,GAAoBrB,GAChC1M,GAAO,IAAMrJ,KAAKS,GAAK0Y,GAAU,IACf,IAAlBA,GAAU,IACV,WACA,GAYc,IAAAG,GAAA,IAAI1R,EAAW,CAC7BrB,GAAI,MACJzE,KAAM,MACNsE,OAAQ,CACP4H,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,OAEPrD,EAAG,CACFsI,SAAU,CAAC,EAAG,KACdjF,KAAM,gBAEPqW,EAAG,CACFpR,SAAU,CAAC,EAAG,KACdjF,KAAM,SAIR0B,KAAMuI,EAEN5C,SAAUuD,GACF2M,GAAM3M,GAEdtD,OAAQkQ,GA5HT,SAAkBlT,EAAQ5B,GASzB,IAAKwJ,EAAGvP,EAAG0Z,GAAK/R,EACZsG,EAAM,GACNwM,EAAI,EAGR,GAAU,IAANf,EACH,MAAO,CAAC,EAAK,EAAK,GAInB,IAAInM,EAAImN,GAAUhB,GAKjBe,EADGf,EAAI,EACH,mBAAsBA,GAAK,EAAI,iBAAoBA,EAAI,kBAGvD,qBAAwBA,GAAK,EAAI,mBAAsBA,EAAI,mBAWhE,IAAIoB,EAAU,EACVtX,EAAOuX,IAIX,KAAOD,GAPc,IAOW,CAC/B7M,EAAM2K,GAAU,CAACE,EAAG2B,EAAGzB,EAAGhZ,EAAGuP,EAAGA,GAAIxJ,GAIpC,MAAMiV,EAAQzZ,KAAKE,IAAIwM,EAAI,GAAKV,GAChC,GAAIyN,EAAQxX,EAAM,CACjB,GAAIwX,GAfY,MAgBf,OAAO/M,EAGRzK,EAAOwX,CACP,CAODP,IAASxM,EAAI,GAAKV,GAAKkN,GAAK,EAAIxM,EAAI,IAEpC6M,GAAW,CACX,CAID,OAAOlC,GAAU,CAACE,EAAG2B,EAAGzB,EAAGhZ,EAAGuP,EAAGA,GAAIxJ,EACtC,CAuDSkV,CAAQJ,EAAKlC,IAErBzO,QAAS,CACRxB,MAAO,CACNZ,GAAI,QACJH,OAAQ,CAAC,qBAAsB,0BAA2B,+BCpJ7D,MAAMwP,GAAU5V,KAAKS,GAAK,IACpBkZ,GAAW,CAAC,EAAM,KAAO,OAO/B,SAASC,GAAcxT,GAMlBA,EAAO,GAAK,IACfA,EAASkT,GAAInQ,SAASmQ,GAAIlQ,OAAOhD,KAMlC,MAAMN,EAAI9F,KAAK6Z,IAAI7Z,KAAKoD,IAAI,EAAIuW,GAAS,GAAKvT,EAAO,GAAKgR,GAAkBR,OAAQ,IAAQ+C,GAAS,GAC/FG,EAAO1T,EAAO,GAAKwP,GACnBrI,EAAIzH,EAAI9F,KAAK0O,IAAIoL,GACjBzN,EAAIvG,EAAI9F,KAAK2O,IAAImL,GAEvB,MAAO,CAAC1T,EAAO,GAAImH,EAAGlB,EACvB,CCde,IAAA0N,GAAA,CACdC,SChBc,SAAmB7S,EAAOgI,GAExC,OAAO6C,GAAS7K,EAAOgI,EAAQ,MAChC,EDcC8K,URLc,SAAU9S,EAAOgI,GAAQ7B,EAACA,EAAI,EAAC7O,EAAEA,EAAI,GAAK,KACvD0I,EAAOgI,GAAUrG,EAAS,CAAC3B,EAAOgI,IAUnC,IAAKI,EAAIC,EAAIC,GAAMpC,EAAI5K,KAAK0E,KACrBuI,EAAIwK,GAAMnM,EAAItL,KAAK4K,EAAK,CAACkC,EAAIC,EAAIC,KACnCE,EAAIC,EAAIC,GAAMxC,EAAI5K,KAAK0M,GACxBW,EAAK/B,EAAItL,KAAK4K,EAAK,CAACsC,EAAIC,EAAIC,IAAK,GAYjCH,EAAK,IACRA,EAAK,GAEFI,EAAK,IACRA,EAAK,GAON,IAAI,EAAKP,EAAKI,EACV,EAAKD,EAAKI,EAOVqK,GALK3K,EAAKI,IAKE,GAJPH,EAAKI,IAIc,EAAM,GAAM,EAmBpCkB,EAAK,KACLxB,GAAM,KACTwB,EAAM,QAAWxB,GAAO,EAAI,OAAUA,IAIvC,IAGI0B,EAHAD,EAAO,MAAStB,GAAO,EAAI,MAASA,GAAO,KAI3ChQ,OAAOC,MAAMua,KAChBA,EAAK,GAILjJ,EADGiJ,GAAM,KAAOA,GAAM,IAClB,IAAOla,KAAKE,IAAI,GAAMF,KAAK0O,KAAKwL,EAAK,KAAOnL,KAG5C,IAAO/O,KAAKE,IAAI,GAAMF,KAAK0O,KAAKwL,EAAK,IAAMnL,KAKhD,IAAIqL,EAAKpa,KAAK4N,IAAI8B,EAAI,GAClB2K,EAAIra,KAAKoO,KAAKgM,GAAMA,EAAK,OAIzBhJ,GAAM,GAAM9D,EAAIyD,KAAQ,EAI5B,OAHAK,IAAO,GAAM3S,EAAIuS,KAAQ,EACzBI,GAAO+I,GALEnJ,GAAOqJ,EAAIpJ,EAAK,EAAIoJ,KAKV,EAEZra,KAAKoO,KAAKgD,EAElB,EQ5FClC,aACAoL,SEZc,SAAUnT,EAAOgI,IAC9BhI,EAAOgI,GAAUrG,EAAS,CAAC3B,EAAOgI,IAKnC,IAAKoL,EAAKC,EAAKC,GAAO1G,GAAOtR,KAAK0E,IAC7BuT,EAAKC,EAAKC,GAAO7G,GAAOtR,KAAK0M,GAI9B,EAAKoL,EAAMG,EACX,EAAKF,EAAMG,EAGVjb,OAAOC,MAAM8a,IAAU/a,OAAOC,MAAMib,IAExCH,EAAM,EACNG,EAAM,GAEElb,OAAOC,MAAM8a,GAErBA,EAAMG,EAEElb,OAAOC,MAAMib,KACrBA,EAAMH,GAGP,IAAI,EAAKA,EAAMG,EACX,EAAK,EAAI5a,KAAKoO,KAAKoM,EAAMG,GAAO3a,KAAK2O,IAAK,EAAK,GAAM3O,KAAKS,GAAK,MAEnE,OAAOT,KAAKoO,KAAK,GAAM,EAAI,GAAM,EAAI,GAAM,EAC5C,EFnBCyM,UGhBc,SAAU1T,EAAOgI,IAC9BhI,EAAOgI,GAAUrG,EAAS,CAAC3B,EAAOgI,IAOnC,IAAM2L,EAAIC,EAAIC,GAAOvG,GAAMhS,KAAK0E,IAC1B8T,EAAIC,EAAIC,GAAO1G,GAAMhS,KAAK0M,GAMhC,OAAO,IAAMnP,KAAKoO,MAAM0M,EAAKG,IAAO,EAAK,KAAQF,EAAKG,IAAO,GAAMF,EAAKG,IAAO,EAChF,EHCCtJ,YACAuJ,UDgBc,SAAUjU,EAAOgI,IAC9BhI,EAAOgI,GAAUrG,EAAS,CAAC3B,EAAOgI,IAEnC,IAAMkM,EAAI7L,EAAIC,GAAOmK,GAAaN,GAAI7W,KAAK0E,KACrCmU,EAAI1L,EAAIC,GAAO+J,GAAaN,GAAI7W,KAAK0M,IAI3C,OAAOnP,KAAKoO,MAAMiN,EAAKC,IAAO,GAAK9L,EAAKI,IAAO,GAAKH,EAAKI,IAAO,EACjE,GKtBA,MAAM0L,GAAa,CAClBjC,IAAO,CACNkC,OAAQ,QACRC,IAAK,EACLC,aAAc,MACdC,gBAAiB,CAAE,GAEpB,YAAa,CACZH,OAAQ,QACRC,IAAK,EACLC,aAAc,MACdC,gBAAiB,CAAEC,QAAS,QAAS1Y,IAAK,EAAGE,IAAK,OAwBrC,SAASyY,GACvB1U,GACAqU,OACCA,EAAS9W,EAASC,cAAauB,MAC/BA,EAAiBwV,aACjBA,EAAe,GAAED,IACjBA,EAAM,EAACE,gBACPA,EAAkB,CAAE,GACjB,CAAE,GAkBN,GAhBAxU,EAAQ2B,EAAS3B,GAEb2U,EAAc1X,UAAU,IAC3B8B,EAAQ9B,UAAU,GAET8B,IACTA,EAAQiB,EAAMjB,OAUXuD,GAAQtC,EAPZjB,EAAQ0B,EAAWmB,IAAI7C,GAOG,CAAE+D,QAAS,IACpC,OAAO9C,EAGR,IAAI4U,EACJ,GAAe,QAAXP,EACHO,EAmIK,SAAqBC,GAAQ9V,MAACA,GAAS,CAAA,GAC7C,MAAM+V,EAAM,IACN,EAAI,KAEVD,EAASlT,EAASkT,GAEb9V,IACJA,EAAQ8V,EAAO9V,OAGhBA,EAAQ0B,EAAWmB,IAAI7C,GACvB,MAAMgW,EAAatU,EAAWmB,IAAI,SAElC,GAAI7C,EAAMsD,YACT,OAAO9G,GAAGsZ,EAAQ9V,GAGnB,MAAMiW,EAAezZ,GAAGsZ,EAAQE,GAChC,IAAIhO,EAAIiO,EAAa/V,OAAO,GAG5B,GAAI8H,GAAK,EAAG,CACX,MAAM7E,EAAQ3G,GAAG0Z,GAAOC,MAAOnW,GAE/B,OADAmD,EAAM1H,MAAQqa,EAAOra,MACde,GAAG2G,EAAOnD,EACjB,CACD,GAAIgI,GAAK,EAAG,CACX,MAAMoO,EAAQ5Z,GAAG0Z,GAAOG,MAAOrW,GAE/B,OADAoW,EAAM3a,MAAQqa,EAAOra,MACde,GAAG4Z,EAAOpW,EACjB,CAED,GAAIuD,GAAQ0S,EAAcjW,EAAO,CAAC+D,QAAS,IAC1C,OAAOvH,GAAGyZ,EAAcjW,GAGzB,SAASsW,EAAMC,GACd,MAAMC,EAAYha,GAAG+Z,EAAQvW,GACvByW,EAAc7d,OAAOqL,OAAOjE,EAAME,QAQxC,OAPAsW,EAAUtW,OAASsW,EAAUtW,OAAOrI,KAAI,CAACsM,EAAOkB,KAC/C,GAAI,UAAWoR,EAAYpR,GAAQ,CAClC,MAAOrI,EAAKE,GAAQuZ,EAAYpR,GAAOxI,MACvC,OAAO6Z,EAAW1Z,EAAKmH,EAAOjH,EAC9B,CACD,OAAOiH,CAAK,IAENqS,CACP,CACD,IAAIxZ,EAAM,EACNE,EAAM+Y,EAAa/V,OAAO,GAC1ByW,GAAc,EACdC,EAAU/K,GAAMoK,GAChBY,EAAUP,EAAKM,GAEfE,EAAInL,GAASkL,EAASD,GAC1B,GAAIE,EAAIf,EACP,OAAOc,EAGR,KAAQ3Z,EAAMF,EAAO,GAAG,CACvB,MAAM+Z,GAAU/Z,EAAME,GAAO,EAE7B,GADA0Z,EAAQ1W,OAAO,GAAK6W,EAChBJ,GAAepT,GAAQqT,EAAS5W,EAAO,CAAC+D,QAAS,IACpD/G,EAAM+Z,OAKN,GAFAF,EAAUP,EAAKM,GACfE,EAAInL,GAASkL,EAASD,GAClBE,EAAIf,EAAK,CACZ,GAAKA,EAAMe,EAAI,EACd,MAGAH,GAAc,EACd3Z,EAAM+Z,CAEP,MAEA7Z,EAAM6Z,CAGR,CACD,OAAOF,CACR,CAtNeG,CAAW/V,EAAO,CAAEjB,cAE7B,CACJ,GAAe,SAAXsV,GAAsB/R,GAAQtC,EAAOjB,GA2ExC6V,EAAarZ,GAAGyE,EAAOjB,OA3EyB,CAE5CpH,OAAOC,UAAUoe,eAAele,KAAKsc,GAAYC,MAClDA,SAAQC,MAAKC,eAAcC,mBAAmBJ,GAAWC,IAI5D,IAAI4B,EAAKlO,EACT,GAAqB,KAAjBwM,EACH,IAAK,IAAI/d,KAAKoc,GACb,GAAI,SAAW2B,EAAavc,gBAAkBxB,EAAEwB,cAAe,CAC9Die,EAAKrD,GAAcpc,GACnB,KACA,CAIH,IAAIof,EAAUlB,GAAQnZ,GAAGyE,EAAOjB,GAAQ,CAAEsV,OAAQ,OAAQtV,UAC1D,GAAIkX,EAAGjW,EAAO4V,GAAWtB,EAAK,CAG7B,GAA4C,IAAxC3c,OAAOqJ,KAAKwT,GAAiB/d,OAAc,CAC9C,IAAIyf,EAAczV,EAAWsD,aAAayQ,EAAgBC,SACtDA,EAAU7S,EAAIrG,GAAGyE,EAAOkW,EAAYnX,OAAQmX,EAAY9W,IAI5D,GAHI+W,EAAY1B,KACfA,EAAU,GAEPA,GAAWD,EAAgBvY,IAC9B,OAAOV,GAAG,CAAEwD,MAAO,UAAWE,OAAQhB,EAAY,KAAK+B,EAAMjB,OAEzD,GAAI0V,GAAWD,EAAgBzY,IACnC,OAAOR,GAAG,CAAEwD,MAAO,UAAWE,OAAQ,CAAC,EAAG,EAAG,IAAMe,EAAMjB,MAE1D,CAGD,IAAIM,EAAYoB,EAAWsD,aAAasQ,GACpC+B,EAAW/W,EAAUN,MACrBoF,EAAU9E,EAAUD,GAEpBiX,EAAc9a,GAAGyE,EAAOoW,GAE5BC,EAAYpX,OAAO/B,SAAQ,CAAC5F,EAAGL,KAC1Bkf,EAAY7e,KACf+e,EAAYpX,OAAOhI,GAAK,EACxB,IAEF,IACI8E,GADSsD,EAAUzD,OAASyD,EAAUO,UACzB,GACb,EA/HR,SAAsB0U,GAGrB,MAAMgC,EAAUhC,EAAWzb,KAAKI,MAAMJ,KAAKC,MAAMD,KAAKE,IAAIub,KAAnC,EAEvB,OAAOzb,KAAKoD,IAAIsa,WAAW,MAAKD,EAAQ,IAAM,KAC/C,CAyHYE,CAAYlC,GAChBmC,EAAM1a,EACN2a,EAAO9U,EAAIyU,EAAalS,GAE5B,KAAOuS,EAAOD,EAAM,GAAG,CACtB,IAAIb,EAAUhL,GAAMyL,GACpBT,EAAUlB,GAAQkB,EAAS,CAAE7W,QAAOsV,OAAQ,SAC/B4B,EAAGI,EAAaT,GAEhBtB,EAAM,EAClBmC,EAAM7U,EAAIyU,EAAalS,GAGvBuS,EAAO9U,EAAIyU,EAAalS,GAGzByB,EAAIyQ,EAAalS,GAAUsS,EAAMC,GAAQ,EACzC,CAED9B,EAAarZ,GAAG8a,EAAatX,EAC7B,MAEA6V,EAAagB,CAEd,CAKD,GAAe,SAAXvB,IAEC/R,GAAQsS,EAAY7V,EAAO,CAAE+D,QAAS,IACzC,CACD,IAAI6T,EAAShf,OAAOqL,OAAOjE,EAAME,QAAQrI,KAAIU,GAAKA,EAAEsE,OAAS,KAE7DgZ,EAAW3V,OAAS2V,EAAW3V,OAAOrI,KAAI,CAACU,EAAGL,KAC7C,IAAK8E,EAAKE,GAAO0a,EAAO1f,GAUxB,YARY4K,IAAR9F,IACHzE,EAAIuB,KAAKoD,IAAIF,EAAKzE,SAGPuK,IAAR5F,IACH3E,EAAIuB,KAAKkD,IAAIzE,EAAG2E,IAGV3E,CAAC,GAET,CACD,CAOD,OALIyH,IAAUiB,EAAMjB,QACnB6V,EAAarZ,GAAGqZ,EAAY5U,EAAMjB,QAGnCiB,EAAMf,OAAS2V,EAAW3V,OACnBe,CACR,CAEA0U,GAAQ5O,QAAU,QAKlB,MAAMmP,GAAS,CACdC,MAAO,CAAEnW,MAAO0L,GAAOxL,OAAQ,CAAC,EAAG,EAAG,IACtCmW,MAAO,CAAErW,MAAO0L,GAAOxL,OAAQ,CAAC,EAAG,EAAG,KC1MxB,SAAS1D,GAAIyE,EAAOjB,GAAOuD,QAACA,GAAW,CAAA,GACrDtC,EAAQ2B,EAAS3B,GAGjB,IAAIf,GAFJF,EAAQ0B,EAAWmB,IAAI7C,IAEJzD,KAAK0E,GACpB3I,EAAM,CAAC0H,QAAOE,SAAQzE,MAAOwF,EAAMxF,OAMvC,OAJI8H,IACHjL,EAAMqd,GAAQrd,GAAiB,IAAZiL,OAAmBT,EAAYS,IAG5CjL,CACR,CCTe,SAASuf,GAAW5W,GAAO7H,UACzCA,EAAYoF,EAASpF,UAAS6G,OAC9BA,EAAS,UACTsD,QAAAA,GAAU,KACPuU,GACA,IACH,IAAIxf,EAIA6J,EAAWlC,EACfA,GAHAgB,EAAQ2B,EAAS3B,IAGFjB,MAAM6B,UAAU5B,IACrBgB,EAAMjB,MAAM6B,UAAU,YACtBH,EAAWqW,eAMrB,IAAI7X,EAASe,EAAMf,OAAO7E,QAS1B,GAPAkI,IAAYtD,EAAO0V,QAEfpS,IAAYyU,GAAa/W,KAE5Bf,EAASyV,GAAQ9J,GAAM5K,IAAoB,IAAZsC,OAAmBT,EAAYS,GAASrD,QAGpD,WAAhBD,EAAOvH,KAAmB,CAG7B,GAFAof,EAAc1e,UAAYA,GAEtB6G,EAAO4X,UAIV,MAAM,IAAIlY,UAAU,UAAUwC,6DAH9B7J,EAAM2H,EAAO4X,UAAU3X,EAAQe,EAAMxF,MAAOqc,EAK7C,KACI,CAEJ,IAAIlc,EAAOqE,EAAOrE,MAAQ,QAEtBqE,EAAO2F,gBACV1F,EAASD,EAAO2F,gBAAgB1F,EAAQ9G,GAGtB,OAAdA,IACH8G,EAASA,EAAOrI,KAAIU,GACZ0f,EAAqB1f,EAAG,CAACa,iBAKnC,IAAI2B,EAAO,IAAImF,GAEf,GAAa,UAATtE,EAAkB,CAErB,IAAI4G,EAAQvC,EAAOI,IAAMJ,EAAOsB,MAAM,IAAMN,EAAMjB,MAAMK,GACxDtF,EAAKmd,QAAQ1V,EACb,CAED,IAAI/G,EAAQwF,EAAMxF,MACA,OAAdrC,IACHqC,EAAQwc,EAAqBxc,EAAO,CAACrC,eAGtC,IAAI+e,EAAWlX,EAAMxF,OAAS,GAAKwE,EAAOmY,QAAU,GAAK,GAAGnY,EAAOoY,OAAS,IAAM,QAAQ5c,IAC1FnD,EAAM,GAAGsD,KAAQb,EAAKwK,KAAKtF,EAAOoY,OAAS,KAAO,OAAOF,IACzD,CAED,OAAO7f,CACR,CD5DAkE,GAAGuK,QAAU,QENE,IAAAuR,GAAA,IAAItS,EAAc,CAChC3F,GAAI,iBACJmC,MAAO,mBACP5G,KAAM,kBACNuH,MAAO,MACRkD,QAlBgB,CACf,CAAE,kBAAoB,mBAAsB,mBAC5C,CAAE,kBAAoB,kBAAsB,oBAC5C,CAAE,EAAoB,oBAAsB,oBAgB7CC,UAZkB,CACjB,CAAG,mBAAqB,kBAAoB,iBAC5C,EAAG,iBAAqB,kBAAoB,mBAC5C,CAAG,kBAAqB,iBAAoB,qBCZ7C,MAAM,GAAI,iBACJ,GAAI,iBAEK,IAAAiS,GAAA,IAAIvS,EAAc,CAChC3F,GAAI,UACJzE,KAAM,WACN0B,KAAMgb,GAENpV,OAAQsV,GACAA,EAAI3gB,KAAI,SAAUoF,GACxB,OAAIA,EAAU,IAAJ,GACFA,EAAM,IAGPnD,KAAK4N,KAAKzK,EAAM,GAAI,GAAK,GAAG,EAAI,IAC1C,IAECgG,SAAUuV,GACFA,EAAI3gB,KAAI,SAAUoF,GACxB,OAAIA,GAAO,GACH,GAAInD,KAAK4N,IAAIzK,EAAK,MAAS,GAAI,GAGhC,IAAMA,CAChB,MCde,IAAAwb,GAAA,IAAIzS,EAAc,CAChC3F,GAAI,YACJmC,MAAO,sBACP5G,KAAM,YACNuH,MAAO,MACRkD,QAjBgB,CACf,CAAC,kBAAoB,mBAAqB,mBAC1C,CAAC,kBAAoB,kBAAqB,kBAC1C,CAAC,EAAoB,mBAAqB,oBAe3CC,UAZkB,CACjB,CAAE,mBAAsB,mBAAqB,oBAC7C,EAAE,kBAAsB,mBAAqB,qBAC7C,CAAE,oBAAsB,mBAAqB,sBCF9C,MAQaA,GAAY,CACxB,CAAG,oBAAsB,mBAAsB,mBAC/C,EAAG,kBAAsB,mBAAsB,oBAC/C,CAAG,oBAAsB,mBAAsB,qBAGjC,IAAAoS,GAAA,IAAI1S,EAAc,CAChC3F,GAAI,cACJzE,KAAM,cACNuH,MAAO,MACRkD,QAlBgB,CACf,CAAE,mBAAqB,iBAAqB,mBAC5C,CAAE,mBAAqB,iBAAqB,oBAC5C,CAAE,mBAAqB,mBAAqB,oBAgB7CC,UAACA,KCpBcqS,GAAA,CACdC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GACpCC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,KAAQ,CAAC,EAAG,EAAG,GACfC,WAAc,CAAC,IAAM,IAAK,EAAG,IAAM,KACnCC,MAAS,CAAC,IAAM,IAAK,EAAG,GACxBC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,OAAU,CAAC,EAAG,IAAM,IAAK,IAAM,KAC/B9C,MAAS,CAAC,EAAG,EAAG,GAChB+C,eAAkB,CAAC,EAAG,IAAM,IAAK,IAAM,KACvCC,KAAQ,CAAC,EAAG,EAAG,GACfC,WAAc,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC1CC,MAAS,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACpCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,WAAc,CAAC,IAAM,IAAK,EAAG,GAC7BC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,MAAS,CAAC,EAAG,IAAM,IAAK,GAAK,KAC7BC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC/CC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,QAAW,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACtCC,KAAQ,CAAC,EAAG,EAAG,GACfC,SAAY,CAAC,EAAG,EAAG,IAAM,KACzBC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KAC7CC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,UAAa,CAAC,EAAG,IAAM,IAAK,GAC5BC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,YAAe,CAAC,IAAM,IAAK,EAAG,IAAM,KACpCC,eAAkB,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KAC7CC,WAAc,CAAC,EAAG,IAAM,IAAK,GAC7BC,WAAc,CAAC,GAAW,GAAK,IAAK,IACpCC,QAAW,CAAC,IAAM,IAAK,EAAG,GAC1BC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,IAAM,KAC5CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,GAAK,KAC3CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,GAAK,KAC3CC,cAAiB,CAAC,EAAG,IAAM,IAAK,IAAM,KACtCC,WAAc,CAAC,IAAM,IAAK,EAAG,IAAM,KACnCC,SAAY,CAAC,EAAG,GAAK,IAAK,IAAM,KAChCC,YAAe,CAAC,EAAG,IAAM,IAAK,GAC9BC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,WAAc,CAAC,GAAK,IAAK,IAAM,IAAK,GACpCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACxCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,YAAe,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KAC1CC,QAAW,CAAC,EAAG,EAAG,GAClBC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,GACrCC,KAAQ,CAAC,EAAG,IAAM,IAAK,GACvBC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,MAAS,CAAC,EAAG,IAAM,IAAK,GACxBC,YAAe,CAAC,IAAM,IAAK,EAAG,GAAK,KACnCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,SAAY,CAAC,IAAM,IAAK,EAAG,IAAM,KACjCC,QAAW,CAAC,EAAG,IAAM,IAAK,IAAM,KAChCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACxCC,OAAU,CAAC,GAAK,IAAK,EAAG,IAAM,KAC9BC,MAAS,CAAC,EAAG,EAAG,IAAM,KACtBC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,cAAiB,CAAC,EAAG,IAAM,IAAK,IAAM,KACtCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GACpCC,aAAgB,CAAC,EAAG,IAAM,IAAK,IAAM,KACrCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,EAAG,GAC5BC,qBAAwB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrDC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,cAAiB,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KAC7CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IACzCC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IACzCC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC/CC,YAAe,CAAC,EAAG,EAAG,IAAM,KAC5BC,KAAQ,CAAC,EAAG,EAAG,GACfC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KACxCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,QAAW,CAAC,EAAG,EAAG,GAClBC,OAAU,CAAC,IAAM,IAAK,EAAG,GACzBC,iBAAoB,CAAC,GAAW,IAAM,IAAK,IAAM,KACjDC,WAAc,CAAC,EAAG,EAAG,IAAM,KAC3BC,aAAgB,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC5CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,eAAkB,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KAC9CC,gBAAmB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAChDC,kBAAqB,CAAC,EAAG,IAAM,IAAK,IAAM,KAC1CC,gBAAmB,CAAC,GAAK,IAAK,IAAM,IAAK,IACzCC,gBAAmB,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC/CC,aAAgB,CAAC,GAAK,IAAK,GAAK,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,EAAG,IAAM,KAClCC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,KAAQ,CAAC,EAAG,EAAG,IAAM,KACrBC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,GAChCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,OAAU,CAAC,EAAG,IAAM,IAAK,GACzBC,UAAa,CAAC,EAAG,GAAK,IAAK,GAC3BC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,WAAc,CAAC,EAAG,IAAM,IAAK,IAAM,KACnCC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACpCC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,OAAU,CAAC,IAAM,IAAK,EAAG,IAAM,KAC/BC,cAAiB,CAAC,GAAW,GAAU,IACvCC,IAAO,CAAC,EAAG,EAAG,GACdC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,YAAe,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KAC1CC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KAC1CC,SAAY,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KACvCC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,OAAU,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACrCC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KACzCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,YAAe,CAAC,EAAG,EAAG,IAAM,KAC5BC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,IAAO,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACpCC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,OAAU,CAAC,EAAG,GAAK,IAAK,GAAK,KAC7BC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCve,MAAS,CAAC,EAAG,EAAG,GAChBwe,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,OAAU,CAAC,EAAG,EAAG,GACjBC,YAAe,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,MCxJ5C,IAAIllB,GAAehF,MAAM,GAAGmqB,KAAK,mCAC7BC,GAAqBpqB,MAAM,GAAGmqB,KAAK,oBAExB,IAAAE,GAAA,IAAIhc,EAAc,CAChC3F,GAAI,OACJzE,KAAM,OACN0B,KAAMob,GACNzV,SAAUsD,GAIFA,EAAI1O,KAAIoF,IACd,IAAIG,EAAOH,EAAM,GAAK,EAAI,EACtBjD,EAAMiD,EAAMG,EAEhB,OAAIpD,EAAM,SACFoD,GAAQ,MAASpD,IAAQ,EAAI,KAAQ,MAGtC,MAAQiD,CAAG,IAGpBiG,OAAQqD,GAIAA,EAAI1O,KAAIoF,IACd,IAAIG,EAAOH,EAAM,GAAK,EAAI,EACtBjD,EAAMiD,EAAMG,EAEhB,OAAIpD,GAAO,OACHiD,EAAM,MAGPG,IAAUpD,EAAM,MAAS,QAAU,GAAI,IAGhDyI,QAAS,CACR8D,IAAO,CACNrG,OAAQvD,IAETslB,WAAc,CACbrmB,KAAM,MACNyc,QAAQ,EACRnY,OAAQ6hB,GACR3J,SAAS,GAEVnX,MAAS,CAAsB,EAC/BihB,KAAQ,CACPhiB,OAAQvD,GACR0b,QAAQ,EACR3V,WAAW,GAEZyf,YAAe,CACdvmB,KAAM,OACNyc,QAAQ,EACRnY,OAAQ6hB,IAETK,IAAO,CACN1pB,KAAM,SACNid,SAAS,EACTra,KAAM7C,GAAO,2BAA2B6C,KAAK7C,GAC7C,KAAAsI,CAAOtI,GACFA,EAAIf,QAAU,IAEjBe,EAAMA,EAAIuC,QAAQ,aAAc,SAGjC,IAAIknB,EAAO,GAKX,OAJAzpB,EAAIuC,QAAQ,iBAAiBqnB,IAC5BH,EAAKvmB,KAAK2mB,SAASD,EAAW,IAAM,IAAI,IAGlC,CACNjgB,QAAS,OACTlC,OAAQgiB,EAAK7mB,MAAM,EAAG,GACtBI,MAAOymB,EAAK7mB,MAAM,GAAG,GAEtB,EACDwc,UAAW,CAAC3X,EAAQzE,GACnB8mB,YAAW,GACR,MACC9mB,EAAQ,GACXyE,EAAOvE,KAAKF,GAGbyE,EAASA,EAAOrI,KAAIU,GAAKuB,KAAK0oB,MAAU,IAAJjqB,KAEpC,IAAIkqB,EAAcF,GAAYriB,EAAOgE,OAAM3L,GAAKA,EAAI,IAAO,IAEvD6pB,EAAMliB,EAAOrI,KAAIU,GAChBkqB,GACKlqB,EAAI,IAAIO,SAAS,IAGnBP,EAAEO,SAAS,IAAI4pB,SAAS,EAAG,OAChCnd,KAAK,IAER,MAAO,IAAM6c,CAAG,GAGlBO,QAAW,CACVjqB,KAAM,SACN4C,KAAM7C,GAAO,YAAY6C,KAAK7C,GAC9B,KAAAsI,CAAOtI,GAEN,IAAIH,EAAM,CAAC8J,QAAS,OAAQlC,OAAQ,KAAMzE,MAAO,GAUjD,GARY,iBAHZhD,EAAMA,EAAIQ,gBAITX,EAAI4H,OAASyY,GAASvC,MACtB9d,EAAImD,MAAQ,GAGZnD,EAAI4H,OAASyY,GAASlgB,GAGnBH,EAAI4H,OACP,OAAO5H,CAER,MCvHWsqB,GAAA,IAAI5c,EAAc,CAChC3F,GAAI,KACJmC,MAAO,aACP5G,KAAM,KACN0B,KAAMmb,GAENxV,SAAU+e,GAAK/e,SACfC,OAAQ8e,GAAK9e,SCEd,IAAI2f,GAEJ,GAJArkB,EAASskB,cAAgBd,GAIN,oBAARe,KAAuBA,IAAIC,SAErC,IAAK,IAAIhjB,IAAS,CAACyH,EAAK8Q,GAASqK,IAAK,CACrC,IAAI1iB,EAASF,EAAM2E,eAEflM,EAAMof,GADE,CAAC7X,QAAOE,SAAQzE,MAAO,IAGnC,GAAIsnB,IAAIC,SAAS,QAASvqB,GAAM,CAC/B+F,EAASskB,cAAgB9iB,EACzB,KACA,CACD,CCnBK,SAASijB,GAAchiB,GAE7B,OAAO4B,EAAI5B,EAAO,CAAC4E,EAAS,KAC7B,CAEO,SAASqd,GAAcjiB,EAAO5E,GAEpCwK,EAAI5F,EAAO,CAAC4E,EAAS,KAAMxJ,EAC5B,+DAEO,SAAmB8mB,GACzBvqB,OAAO6K,eAAe0f,EAAMtqB,UAAW,YAAa,CACnD,GAAAgK,GACC,OAAOogB,GAAa7kB,KACpB,EACD,GAAAyI,CAAKxK,GACJ6mB,GAAa9kB,KAAM/B,EACnB,GAEH,oBClBA,MAMM+mB,GAAU,KACVC,GAAU,MAWhB,SAASC,GAAQC,GAChB,OAAIA,GAAKH,GACDG,EAEDA,GAAKH,GAAUG,IAAMF,EAC7B,CAEA,SAASG,GAAWvmB,GACnB,IAAIG,EAAOH,EAAM,GAAK,EAAI,EACtBjD,EAAMF,KAAKE,IAAIiD,GACnB,OAAOG,EAAOtD,KAAK4N,IAAI1N,EAAK,IAC7B,CChCA,MACM,GAAK,GAAK,IACVkN,GAAI,MAAQ,GAElB,IAAI/D,GAAQjE,EAAOE,IAEJ,IAAAqkB,GAAA,IAAI/hB,EAAW,CAC7BrB,GAAI,UACJzE,KAAM,UACNsE,OAAQ,CACPkH,EAAG,CACFvG,SAAU,CAAC,EAAG,KACdjF,KAAM,aAEPyL,EAAG,CACFxG,SAAU,EAAE,IAAK,MAElBsF,EAAG,CACFtF,SAAU,EAAE,IAAK,OAMpBsC,MAACA,GAEA7F,KAAMuI,EAGN,QAAA5C,CAAUxD,GAET,IAGI8H,EAHM9H,EAAI5H,KAAI,CAACwE,EAAOnE,IAAMmE,EAAQ8G,GAAMjL,KAGlCL,KAAIwE,GAASA,EAlCjB,oBAkC6BvC,KAAK0N,KAAKnL,IAAU6K,GAAI7K,EAAQ,IAAM,MAE3E,MAAO,CACL,IAAMkL,EAAE,GAAM,GACf,KAAOA,EAAE,GAAKA,EAAE,IAChB,KAAOA,EAAE,GAAKA,EAAE,IAEjB,EAID,MAAArE,CAAQuE,GAEP,IAAIF,EAAI,GAaR,OAZAA,EAAE,IAAME,EAAI,GAAK,IAAM,IACvBF,EAAE,GAAKE,EAAI,GAAK,IAAMF,EAAE,GACxBA,EAAE,GAAKA,EAAE,GAAKE,EAAI,GAAK,IAGb,CACTF,EAAE,GAAO,GAAKzN,KAAK4N,IAAIH,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAML,GACrEO,EAAI,GAAK,EAAK3N,KAAK4N,KAAKD,EAAI,GAAK,IAAM,IAAK,GAAKA,EAAI,GAAKP,GAC1DK,EAAE,GAAO,GAAKzN,KAAK4N,IAAIH,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAML,IAI3DrP,KAAI,CAACwE,EAAOnE,IAAMmE,EAAQ8G,GAAMjL,IAC3C,EAEDuK,QAAS,CACR,UAAW,CACVvC,OAAQ,CAAC,0BAA2B,gCAAiC,qCC5DxE,MAAMwjB,GAAyB,GAAnB5pB,KAAK4N,IAAI,EAAG,IAAa,qDF8BtB,SAAuBic,EAAYC,GAIjD,IAAIC,EACAtS,EACAuS,EAGAC,EAAGja,EAAGtS,EARVosB,EAAahhB,EAASghB,GACtBD,EAAa/gB,EAAS+gB,GAStBC,EAAapnB,GAAGonB,EAAY,SAK3BG,EAAGja,EAAGtS,GAAKosB,EAAW1jB,OACvB,IAAI8jB,EAAwB,SAAfR,GAAUO,GAAgC,SAAfP,GAAU1Z,GAAgC,QAAf0Z,GAAUhsB,GAE7EmsB,EAAannB,GAAGmnB,EAAY,SAC3BI,EAAGja,EAAGtS,GAAKmsB,EAAWzjB,OACvB,IAAI+jB,EAAuB,SAAfT,GAAUO,GAAgC,SAAfP,GAAU1Z,GAAgC,QAAf0Z,GAAUhsB,GAGxE0sB,EAAOZ,GAAOU,GACdG,EAAMb,GAAOW,GAGbG,EAAMD,EAAMD,EAgChB,OA3BIpqB,KAAKE,IAAImqB,EAAMD,GAxDF,KAyDhB3S,EAAI,EAGA6S,GAEHP,EAAIM,GAvEQ,IAuEQD,GAtEP,IAuEb3S,EA3Dc,KA2DVsS,IAIJA,EAAIM,GAzEO,IAyEQD,GA1EP,IA2EZ3S,EA9Dc,KA8DVsS,GAILC,EADGhqB,KAAKE,IAAIuX,GAxEC,GAyEN,EAECA,EAAI,EAGLA,EAxEW,KA2EXA,EA3EW,KA8EL,IAAPuS,CACR,mBEzFe,SAA2B/X,EAAQC,GACjDD,EAASnJ,EAASmJ,GAClBC,EAASpJ,EAASoJ,GAElB,IAAIqY,EAAQxhB,EAAIkJ,EAAQ,CAAC0X,GAAS,MAC9Ba,EAAQzhB,EAAImJ,EAAQ,CAACyX,GAAS,MAE9Bc,EAAezqB,KAAKE,IAAIF,KAAK4N,IAAI2c,EAAOX,IAAO5pB,KAAK4N,IAAI4c,EAAOZ,KAE/Dc,EAAW1qB,KAAK4N,IAAI6c,EAAe,EAAIb,IAAQ5pB,KAAK2qB,MAAQ,GAEhE,OAAQD,EAAW,IAAO,EAAMA,CACjC,gBChBe,SAAwBzY,EAAQC,GAC9CD,EAASnJ,EAASmJ,GAClBC,EAASpJ,EAASoJ,GAElB,IAAI3C,EAAKxG,EAAIkJ,EAAQ,CAAC5E,EAAK,MACvBsC,EAAK5G,EAAImJ,EAAQ,CAAC7E,EAAK,MAE3B,OAAOrN,KAAKE,IAAIqP,EAAKI,EACtB,oBCRe,SAA4BsC,EAAQC,GAClDD,EAASnJ,EAASmJ,GAClBC,EAASpJ,EAASoJ,GAElB,IAAI0Y,EAAK5qB,KAAKoD,IAAI+lB,GAAalX,GAAS,GACpC4Y,EAAK7qB,KAAKoD,IAAI+lB,GAAajX,GAAS,GAEpC2Y,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,IAGjB,IAAIE,EAASF,EAAKC,EAClB,OAAiB,IAAVC,EAAc,GAAKF,EAAKC,GAAMC,CACtC,iBCde,SAAyB7Y,EAAQC,GAC/CD,EAASnJ,EAASmJ,GAClBC,EAASpJ,EAASoJ,GAElB,IAAI0Y,EAAK5qB,KAAKoD,IAAI+lB,GAAalX,GAAS,GACpC4Y,EAAK7qB,KAAKoD,IAAI+lB,GAAajX,GAAS,GAMxC,OAJI2Y,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,KAGTA,EAAK,MAAQC,EAAK,IAC3B,gBCLe,SAAwB5Y,EAAQC,GAC9CD,EAASnJ,EAASmJ,GAClBC,EAASpJ,EAASoJ,GAElB,IAAI0Y,EAAK5qB,KAAKoD,IAAI+lB,GAAalX,GAAS,GACpC4Y,EAAK7qB,KAAKoD,IAAI+lB,GAAajX,GAAS,GAMxC,OAJI2Y,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,IAGH,IAAPC,EAbI,KAacD,EAAKC,GAAMA,CACrC,ICtBO,SAASE,GAAI5jB,GAEnB,IAAK6jB,EAAGvB,EAAGwB,GAAKre,EAAOzF,EAAO4E,GAC1B+e,EAAQE,EAAI,GAAKvB,EAAI,EAAIwB,EAC7B,MAAO,CAAC,EAAID,EAAIF,EAAO,EAAIrB,EAAIqB,EAChC,CAEO,SAASI,GAAI/jB,GAEnB,IAAK6jB,EAAGvB,EAAGwB,GAAKre,EAAOzF,EAAO4E,GACzBof,EAAMH,EAAIvB,EAAIwB,EACnB,MAAO,CAACD,EAAIG,EAAK1B,EAAI0B,EACtB,+CAEO,SAAmB9B,GAGzBvqB,OAAO6K,eAAe0f,EAAMtqB,UAAW,KAAM,CAC5C,GAAAgK,GACC,OAAOgiB,GAAGzmB,KACV,IAGFxF,OAAO6K,eAAe0f,EAAMtqB,UAAW,KAAM,CAC5C,GAAAgK,GACC,OAAOmiB,GAAG5mB,KACV,GAEH,gBC5Be,SAASM,GAAQ2N,EAAIC,EAAI3T,EAAI,CAAA,GACvCH,EAASG,KACZA,EAAI,CAAC2c,OAAQ3c,IAGd,IAAI2c,OAACA,EAAS9W,EAASE,UAAWwmB,GAAQvsB,EAE1C,IAAK,IAAIlB,KAAKoc,GACb,GAAI,SAAWyB,EAAOrc,gBAAkBxB,EAAEwB,cACzC,OAAO4a,GAAcpc,GAAG4U,EAAIC,EAAI4Y,GAIlC,MAAM,IAAIvlB,UAAU,0BAA0B2V,IAC/C,6CCTO,SAAiBrU,EAAOkkB,EAAS,KAGvC,OAAOte,EAAI5F,EADK,CADJS,EAAWmB,IAAI,QAAS,OACZ,MACKuE,GAAKA,GAAK,EAAI+d,IAC5C,UAVO,SAAkBlkB,EAAOkkB,EAAS,KAGxC,OAAOte,EAAI5F,EADK,CADJS,EAAWmB,IAAI,QAAS,OACZ,MACKuE,GAAKA,GAAK,EAAI+d,IAC5C,ICmBO,SAASC,GAAK/Y,EAAIC,EAAIvU,EAAI,GAAIY,EAAI,IAQxC,OAPC0T,EAAIC,GAAM,CAAC1J,EAASyJ,GAAKzJ,EAAS0J,IAEnB,WAAZ5T,EAAKX,MACPA,EAAGY,GAAK,CAAC,GAAIZ,IAGP8E,GAAMwP,EAAIC,EAAI3T,EACfsN,CAAElO,EACV,CASO,SAASstB,GAAOhZ,EAAIC,EAAI5M,EAAU,CAAA,GACxC,IAAI4lB,EAEAC,GAAQlZ,MAEViZ,EAAY5lB,GAAW,CAAC2M,EAAIC,IAC5BD,EAAIC,GAAMgZ,EAAWE,UAAUC,QAGjC,IAAIC,UACHA,EAASlQ,aAAEA,EAAY6P,MACvBA,EAAQ,EAACM,SAAEA,EAAW,OACnBC,GACAlmB,EAEC4lB,KACHjZ,EAAIC,GAAM,CAAC1J,EAASyJ,GAAKzJ,EAAS0J,IACnCgZ,EAAazoB,GAAMwP,EAAIC,EAAIsZ,IAG5B,IAAIC,EAAannB,GAAO2N,EAAIC,GACxBwZ,EAAcJ,EAAY,EAAI5rB,KAAKoD,IAAImoB,EAAOvrB,KAAKisB,KAAKF,EAAaH,GAAa,GAAKL,EACvF/sB,EAAM,GAMV,QAJiBwK,IAAb6iB,IACHG,EAAchsB,KAAKkD,IAAI8oB,EAAaH,IAGjB,IAAhBG,EACHxtB,EAAM,CAAC,CAACP,EAAG,GAAIkJ,MAAOqkB,EAAW,UAE7B,CACJ,IAAIU,EAAO,GAAKF,EAAc,GAC9BxtB,EAAMX,MAAM4E,KAAK,CAAC7E,OAAQouB,IAAc,CAAC7tB,EAAGC,KAC3C,IAAIH,EAAIG,EAAI8tB,EACZ,MAAO,CAACjuB,IAAGkJ,MAAOqkB,EAAWvtB,GAAG,GAEjC,CAED,GAAI2tB,EAAY,EAAG,CAElB,IAAIO,EAAW3tB,EAAI6T,QAAO,CAACC,EAAK8Z,EAAKhuB,KACpC,GAAU,IAANA,EACH,OAAO,EAGR,IAAI,EAAKwG,GAAOwnB,EAAIjlB,MAAO3I,EAAIJ,EAAI,GAAG+I,MAAOuU,GAC7C,OAAO1b,KAAKoD,IAAIkP,EAAK,EAAG,GACtB,GAEH,KAAO6Z,EAAWP,GAAW,CAG5BO,EAAW,EAEX,IAAK,IAAI/tB,EAAI,EAAIA,EAAII,EAAIZ,QAAYY,EAAIZ,OAASiuB,EAAWztB,IAAK,CACjE,IAAIiuB,EAAO7tB,EAAIJ,EAAI,GACfguB,EAAM5tB,EAAIJ,GAEVH,GAAKmuB,EAAInuB,EAAIouB,EAAKpuB,GAAK,EACvBkJ,EAAQqkB,EAAWvtB,GACvBkuB,EAAWnsB,KAAKoD,IAAI+oB,EAAUvnB,GAAOuC,EAAOklB,EAAKllB,OAAQvC,GAAOuC,EAAOilB,EAAIjlB,QAC3E3I,EAAI8tB,OAAOluB,EAAG,EAAG,CAACH,IAAGkJ,MAAOqkB,EAAWvtB,KACvCG,GACA,CACD,CACD,CAID,OAFAI,EAAMA,EAAIT,KAAIwP,GAAKA,EAAEpG,QAEd3I,CACR,CASO,SAASuE,GAAOkP,EAAQC,EAAQtM,EAAU,CAAA,GAChD,GAAI6lB,GAAQxZ,GAAS,CAEpB,IAAK9F,EAAGvG,GAAW,CAACqM,EAAQC,GAE5B,OAAOnP,MAASoJ,EAAEuf,UAAUC,OAAQ,IAAIxf,EAAEuf,UAAU9lB,WAAYA,GAChE,CAED,IAAIM,MAACA,EAAKqmB,YAAEA,EAAWC,YAAEA,EAAWC,cAAEA,GAAiB7mB,EAEvDqM,EAASnJ,EAASmJ,GAClBC,EAASpJ,EAASoJ,GAGlBD,EAASF,GAAME,GACfC,EAASH,GAAMG,GAEf,IAAIwZ,EAAY,CAACC,OAAQ,CAAC1Z,EAAQC,GAAStM,WAoB3C,GAjBCM,EADGA,EACK0B,EAAWmB,IAAI7C,GAGf0B,EAAWa,SAAS/D,EAASgoB,qBAAuBza,EAAO/L,MAGpEqmB,EAAcA,EAAc3kB,EAAWmB,IAAIwjB,GAAermB,EAE1D+L,EAASvP,GAAGuP,EAAQ/L,GACpBgM,EAASxP,GAAGwP,EAAQhM,GAGpB+L,EAAS4J,GAAQ5J,GACjBC,EAAS2J,GAAQ3J,GAIbhM,EAAME,OAAO4H,GAA6B,UAAxB9H,EAAME,OAAO4H,EAAEpP,KAAkB,CACtD,IAAI+tB,EAAM/mB,EAAQqI,IAAMrI,EAAQqI,KAAO,UAEnCA,EAAM,CAAC/H,EAAO,MACb,EAAI,GAAM,CAAC6C,EAAIkJ,EAAQhE,GAAMlF,EAAImJ,EAAQjE,IAI1CtO,MAAM,KAAQA,MAAM,GACvB,EAAK,EAEGA,MAAM,KAAQA,MAAM,KAC5B,EAAK,IAEL,EAAI,G1C3KA,SAAiBgtB,EAAKC,GAC5B,GAAY,QAARD,EACH,OAAOC,EAGR,IAAKpd,EAAII,GAAMgd,EAAO7uB,IAAI8P,GAEtBgf,EAAYjd,EAAKJ,EA+BrB,MA7BY,eAARmd,EACCE,EAAY,IACfjd,GAAM,KAGS,eAAR+c,EACJE,EAAY,IACfrd,GAAM,KAGS,WAARmd,GACH,IAAME,GAAaA,EAAY,MAC/BA,EAAY,EACfrd,GAAM,IAGNI,GAAM,KAIQ,YAAR+c,IACJE,EAAY,IACfrd,GAAM,IAEEqd,GAAa,MACrBjd,GAAM,MAID,CAACJ,EAAII,EACb,C0CoIakd,CAAcH,EAAK,CAAC,EAAI,IACnC5f,EAAIkF,EAAQhE,EAAK,GACjBlB,EAAImF,EAAQjE,EAAK,EACjB,CAQD,OANIwe,IAEHxa,EAAO7L,OAAS6L,EAAO7L,OAAOrI,KAAIU,GAAKA,EAAIwT,EAAOtQ,QAClDuQ,EAAO9L,OAAS8L,EAAO9L,OAAOrI,KAAIU,GAAKA,EAAIyT,EAAOvQ,SAG5C7C,OAAOsJ,QAAOnK,IACpBA,EAAIuuB,EAAcA,EAAYvuB,GAAKA,EACnC,IAAImI,EAAS6L,EAAO7L,OAAOrI,KAAI,CAACqE,EAAOhE,IAE/B+D,EAAYC,EADT8P,EAAO9L,OAAOhI,GACOH,KAG5B0D,EAAQQ,EAAY8P,EAAOtQ,MAAOuQ,EAAOvQ,MAAO1D,GAChDO,EAAM,CAAC0H,QAAOE,SAAQzE,SAW1B,OATI8qB,IAEHjuB,EAAI4H,OAAS5H,EAAI4H,OAAOrI,KAAIU,GAAKA,EAAIkD,KAGlC4qB,IAAgBrmB,IACnB1H,EAAMkE,GAAGlE,EAAK+tB,IAGR/tB,CAAG,GACR,CACFktB,aAEF,CAEO,SAASD,GAAStoB,GACxB,MAAqB,aAAdvE,EAAKuE,MAAyBA,EAAIuoB,SAC1C,CAEAhnB,EAASgoB,mBAAqB,+EAEvB,SAAmBrD,GACzBA,EAAM0D,eAAe,MAAOzB,GAAK,CAACre,QAAS,UAC3Coc,EAAM0D,eAAe,QAAShqB,GAAO,CAACkK,QAAS,oBAC/Coc,EAAM0D,eAAe,QAASxB,GAAO,CAACte,QAAS,gBAChD,aC1Ne+f,GAAA,IAAIplB,EAAW,CAC7BrB,GAAI,MACJzE,KAAM,MACNsE,OAAQ,CACP4H,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,OAEP4J,EAAG,CACF3I,MAAO,CAAC,EAAG,KACXjB,KAAM,cAEPwL,EAAG,CACFvK,MAAO,CAAC,EAAG,KACXjB,KAAM,cAIR0B,KAAM0kB,GAGN/e,SAAUsD,IACT,IAAIrJ,EAAMpD,KAAKoD,OAAOqJ,GAClBvJ,EAAMlD,KAAKkD,OAAOuJ,IACjBN,EAAGC,EAAGC,GAAKI,GACXuB,EAAGtC,EAAG4B,GAAK,CAAC7L,IAAK,GAAIyB,EAAME,GAAO,GACnCO,EAAIP,EAAMF,EAEd,GAAU,IAANS,EAAS,CAGZ,OAFA+H,EAAW,IAAN4B,GAAiB,IAANA,EAAW,GAAKlK,EAAMkK,GAAKtN,KAAKkD,IAAIoK,EAAG,EAAIA,GAEnDlK,GACP,KAAK+I,EAAG6B,GAAK5B,EAAIC,GAAK1I,GAAKyI,EAAIC,EAAI,EAAI,GAAI,MAC3C,KAAKD,EAAG4B,GAAK3B,EAAIF,GAAKxI,EAAI,EAAG,MAC7B,KAAK0I,EAAG2B,GAAK7B,EAAIC,GAAKzI,EAAI,EAG3BqK,GAAQ,EACR,CAcD,OATItC,EAAI,IACPsC,GAAK,IACLtC,EAAI1L,KAAKE,IAAIwL,IAGVsC,GAAK,MACRA,GAAK,KAGC,CAACA,EAAO,IAAJtC,EAAa,IAAJ4B,EAAQ,EAI7BlE,OAAQ6jB,IACP,IAAKjf,EAAGtC,EAAG4B,GAAK2f,EAUhB,SAASxf,EAAGpO,GACX,IAAI6tB,GAAK7tB,EAAI2O,EAAI,IAAM,GACnBT,EAAI7B,EAAI1L,KAAKkD,IAAIoK,EAAG,EAAIA,GAC5B,OAAOA,EAAIC,EAAIvN,KAAKoD,KAAK,EAAGpD,KAAKkD,IAAIgqB,EAAI,EAAG,EAAIA,EAAG,GACnD,CAED,OAfAlf,GAAQ,IAEJA,EAAI,IACPA,GAAK,KAGNtC,GAAK,IACL4B,GAAK,IAQE,CAACG,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAG,EAG1B9E,QAAS,CACRskB,IAAO,CACN7mB,OAAQ,CAAC,qBAAsB,eAAgB,iBAEhD+mB,KAAQ,CACP/mB,OAAQ,CAAC,qBAAsB,eAAgB,gBAC/CmY,QAAQ,EACR3V,WAAW,MC/ECwkB,GAAA,IAAIxlB,EAAW,CAC7BrB,GAAI,MACJzE,KAAM,MACNsE,OAAQ,CACP4H,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,OAEP4J,EAAG,CACF3I,MAAO,CAAC,EAAG,KACXjB,KAAM,cAEP4Q,EAAG,CACF3P,MAAO,CAAC,EAAG,KACXjB,KAAM,UAIR0B,KAAMwpB,GAEN,QAAA7jB,CAAU8jB,GACT,IAAKjf,EAAGtC,EAAG4B,GAAK2f,EAChBvhB,GAAK,IACL4B,GAAK,IAEL,IAAIoF,EAAIpF,EAAI5B,EAAI1L,KAAKkD,IAAIoK,EAAG,EAAIA,GAEhC,MAAO,CACNU,EACM,IAAN0E,EAAU,EAAI,KAAO,EAAIpF,EAAIoF,GAC7B,IAAMA,EAEP,EAED,MAAAtJ,CAAQikB,GACP,IAAKrf,EAAGtC,EAAGgH,GAAK2a,EAEhB3hB,GAAK,IACLgH,GAAK,IAEL,IAAIpF,EAAIoF,GAAK,EAAIhH,EAAI,GAErB,MAAO,CACNsC,EACO,IAANV,GAAiB,IAANA,EAAW,GAAMoF,EAAIpF,GAAKtN,KAAKkD,IAAIoK,EAAG,EAAIA,GAAM,IACxD,IAAJA,EAED,EAED3E,QAAS,CACRxB,MAAO,CACNZ,GAAI,QACJH,OAAQ,CAAC,qBAAsB,0BAA2B,+BCrD9CknB,GAAA,IAAI1lB,EAAW,CAC7BrB,GAAI,MACJzE,KAAM,MACNsE,OAAQ,CACP4H,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,OAEPyrB,EAAG,CACFxqB,MAAO,CAAC,EAAG,KACXjB,KAAM,aAEPuK,EAAG,CACFtJ,MAAO,CAAC,EAAG,KACXjB,KAAM,cAIR0B,KAAM4pB,GACN,QAAAjkB,CAAUkkB,GACT,IAAKrf,EAAGtC,EAAGgH,GAAK2a,EAEhB,MAAO,CAACrf,EAAG0E,GAAK,IAAMhH,GAAK,IAAK,IAAMgH,EACtC,EACD,MAAAtJ,CAAQkkB,GACP,IAAKtf,EAAGuf,EAAGlhB,GAAKihB,EAGhBC,GAAK,IACLlhB,GAAK,IAGL,IAAI8e,EAAMoC,EAAIlhB,EACd,GAAI8e,GAAO,EAAG,CAEb,MAAO,CAACnd,EAAG,EAAU,KADVuf,EAAIpC,GAEf,CAED,IAAIzY,EAAK,EAAIrG,EAEb,MAAO,CAAC2B,EAAO,KADA,IAAN0E,EAAW,EAAI,EAAI6a,EAAI7a,GACR,IAAJA,EACpB,EAED/J,QAAS,CACR2kB,IAAO,CACNlnB,OAAQ,CAAC,qBAAsB,0BAA2B,+BClC9C,IAAAonB,GAAA,IAAIthB,EAAc,CAChC3F,GAAI,gBACJmC,MAAO,mBACP5G,KAAM,kCACNuH,MAAO,MACRkD,QAjBgB,CACf,CAAE,kBAAsB,kBAAsB,mBAC9C,CAAE,mBAAsB,kBAAsB,oBAC9C,CAAE,mBAAsB,mBAAsB,oBAe/CC,UAZkB,CACjB,CAAG,oBAAwB,mBAAuB,oBAClD,EAAG,kBAAwB,mBAAuB,oBAClD,CAAG,qBAAwB,mBAAuB,uBCdpCihB,GAAA,IAAIvhB,EAAc,CAChC3F,GAAI,SACJmC,MAAO,UACP5G,KAAM,2BACN0B,KAAMgqB,GACNpkB,OAAQsV,GAAOA,EAAI3gB,KAAIoF,GAAOnD,KAAK4N,IAAI5N,KAAKE,IAAIiD,GAAM,IAAM,KAAOnD,KAAKsD,KAAKH,KAC7EgG,SAAUuV,GAAOA,EAAI3gB,KAAIoF,GAAOnD,KAAK4N,IAAI5N,KAAKE,IAAIiD,GAAM,IAAM,KAAOnD,KAAKsD,KAAKH,OCUjE,IAAAuqB,GAAA,IAAIxhB,EAAc,CAChC3F,GAAI,kBACJmC,MAAO,wBACP5G,KAAM,kBACNuH,MAAO,MACP7F,KAAM0J,EACPX,QAlBgB,CACf,CAAE,kBAAsB,mBAAsB,mBAC9C,CAAE,kBAAsB,iBAAsB,mBAC9C,CAAE,EAAsB,EAAsB,oBAgB/CC,UAbkB,CACjB,CAAG,oBAAsB,oBAAsB,oBAC/C,EAAG,kBAAsB,mBAAsB,oBAC/C,CAAG,EAAsB,EAAsB,uBCVjC,IAAAmhB,GAAA,IAAIzhB,EAAc,CAChC3F,GAAI,WACJmC,MAAO,eACP5G,KAAM,WACN0B,KAAMkqB,GACNtkB,OAAQsV,GAEAA,EAAI3gB,KAAI2U,GAAKA,EATV,OASoBA,EAAI,GAAKA,GAAK,MAE7CvJ,SAAUuV,GACFA,EAAI3gB,KAAI2U,GAAKA,GAbX,WAaqBA,IAAM,EAAI,KAAO,GAAKA,MCZvCkb,GAAA,IAAIhmB,EAAW,CAC7BrB,GAAI,QACJzE,KAAM,QACNsE,OAAQ,CACPkH,EAAG,CACFvG,SAAU,CAAC,EAAG,GACdjF,KAAM,aAEPrD,EAAG,CACFsI,SAAU,CAAC,EAAG,IACdjF,KAAM,UAEPkM,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,QAGRuH,MAAO,MAEP7F,KAAMiO,GACN,QAAAtI,CAAUyI,GAET,IACI5D,GADCE,EAAGX,EAAGlB,GAAKuF,EAEhB,MAAM,EAAI,KASV,OANC5D,EADGhO,KAAKE,IAAIqN,GAAK,GAAKvN,KAAKE,IAAImM,GAAK,EAChC5K,IAGmB,IAAnBzB,KAAKmO,MAAM9B,EAAGkB,GAAWvN,KAAKS,GAG5B,CACNyN,EACAlO,KAAKoO,KAAKb,GAAK,EAAIlB,GAAK,GACxBgC,EAAeL,GAEhB,EAED,MAAA5E,CAAQwkB,GACP,IACIrgB,EAAGlB,GADF6B,EAAGuJ,EAAGzJ,GAAK4f,EAahB,OATIjuB,MAAMqO,IACTT,EAAI,EACJlB,EAAI,IAGJkB,EAAIkK,EAAIzX,KAAK0O,IAAIV,EAAIhO,KAAKS,GAAK,KAC/B4L,EAAIoL,EAAIzX,KAAK2O,IAAIX,EAAIhO,KAAKS,GAAK,MAGzB,CAAEyN,EAAGX,EAAGlB,EACf,EAED1D,QAAS,CACRilB,MAAS,CACRxnB,OAAQ,CAAC,0BAA2B,+BAAgC,0BC1DvE,IAAIiD,GAAQjE,EAAOE,IAEnB,MACM8H,GAAI,MAAQ,IACXygB,GAAeC,IAAiB/C,GAAG,CAAC7kB,MAAO6F,EAAS3F,OAAQiD,KAEpD,IAAA0kB,GAAA,IAAInmB,EAAW,CAC7BrB,GAAI,MACJzE,KAAM,MACNsE,OAAQ,CACPkH,EAAG,CACFvG,SAAU,CAAC,EAAG,KACdjF,KAAM,aAGPksB,EAAG,CACFjnB,SAAU,EAAE,IAAK,MAElB2L,EAAG,CACF3L,SAAU,EAAE,IAAK,OAInBsC,MAAOA,GACP7F,KAAMuI,EAIN,QAAA5C,CAAUxD,GACT,IAAI+G,EAAM,CAAC7M,EAAS8F,EAAI,IAAK9F,EAAS8F,EAAI,IAAK9F,EAAS8F,EAAI,KACxDqG,EAAIU,EAAI,IAEPuhB,EAAIC,GAAMnD,GAAG,CAAC7kB,MAAO6F,EAAS3F,OAAQsG,IAG3C,IAAKhN,OAAOyuB,SAASF,KAAQvuB,OAAOyuB,SAASD,GAC5C,MAAO,CAAC,EAAG,EAAG,GAGf,IAAIhgB,EAAIlC,GArCA,oBAqCSoB,GAAIpB,EAAI,IAAMhM,KAAK0N,KAAK1B,GAAK,GAC9C,MAAO,CACNkC,EACA,GAAKA,GAAK+f,EAAKJ,IACf,GAAK3f,GAAKggB,EAAKJ,IAEhB,EAID,MAAA1kB,CAAQ2kB,GACP,IAAK7f,EAAG8f,EAAGtb,GAAKqb,EAGhB,GAAU,IAAN7f,GAAW1O,EAAO0O,GACrB,MAAO,CAAC,EAAG,EAAG,GAGf8f,EAAInuB,EAASmuB,GACbtb,EAAI7S,EAAS6S,GAEb,IAAIub,EAAMD,GAAK,GAAK9f,GAAM2f,GACtBK,EAAMxb,GAAK,GAAKxE,GAAM4f,GAEtB9hB,EAAIkC,GAAK,EAAIA,EAAId,GAAIpN,KAAK4N,KAAKM,EAAI,IAAM,IAAK,GAElD,MAAO,CACNlC,GAAM,EAAIiiB,GAAO,EAAIC,IACrBliB,EACAA,IAAM,GAAK,EAAIiiB,EAAK,GAAKC,IAAO,EAAIA,IAErC,EAEDvlB,QAAS,CACRxB,MAAO,CACNZ,GAAI,QACJH,OAAQ,CAAC,0BAA2B,gCAAiC,qCC7EzDgoB,GAAA,IAAIxmB,EAAW,CAC7BrB,GAAI,QACJzE,KAAM,QACNsE,OAAQ,CACPkH,EAAG,CACFvG,SAAU,CAAC,EAAG,KACdjF,KAAM,aAEPrD,EAAG,CACFsI,SAAU,CAAC,EAAG,KACdjF,KAAM,UAEPkM,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,QAIR0B,KAAMuqB,GACN,QAAA5kB,CAAU4kB,GAET,IACI9f,GADCC,EAAG8f,EAAGtb,GAAKqb,EAWhB,OANC9f,EADGjO,KAAKE,IAAI8tB,GAFH,KAEahuB,KAAKE,IAAIwS,GAFtB,IAGHjR,IAGmB,IAAnBzB,KAAKmO,MAAMuE,EAAGsb,GAAWhuB,KAAKS,GAG9B,CACNyN,EACAlO,KAAKoO,KAAK4f,GAAK,EAAItb,GAAK,GACxBrE,EAAeJ,GAEhB,EACD,MAAA7E,CAAQkF,GAEP,IAAKC,EAAWC,EAAQC,GAAOH,EAS/B,OAPIE,EAAS,IACZA,EAAS,GAGN7O,MAAM8O,KACTA,EAAM,GAEA,CACNF,EACAC,EAASxO,KAAK0O,IAAID,EAAMzO,KAAKS,GAAK,KAClC+N,EAASxO,KAAK2O,IAAIF,EAAMzO,KAAKS,GAAK,KAEnC,EAEDkI,QAAS,CACRxB,MAAO,CACNZ,GAAI,UACJH,OAAQ,CAAC,0BAA2B,0BAA2B,0BClClE,MAGMioB,GAAO7hB,GAAU,GAAG,GACpB8hB,GAAO9hB,GAAU,GAAG,GACpB+hB,GAAO/hB,GAAU,GAAG,GACpBgiB,GAAOhiB,GAAU,GAAG,GACpBiiB,GAAOjiB,GAAU,GAAG,GACpBkiB,GAAOliB,GAAU,GAAG,GACpBmiB,GAAOniB,GAAU,GAAG,GACpBoiB,GAAOpiB,GAAU,GAAG,GACpBqiB,GAAOriB,GAAU,GAAG,GAE1B,SAASsiB,GAAyBC,EAAOC,EAAWlhB,GACnD,MAAMnK,EAAIqrB,GAAahvB,KAAK2O,IAAIb,GAASihB,EAAQ/uB,KAAK0O,IAAIZ,IAC1D,OAAOnK,EAAI,EAAI6V,IAAW7V,CAC3B,CAEO,SAASsrB,GAAwB3hB,GACvC,MAAM4hB,EAAOlvB,KAAK4N,IAAIN,EAAI,GAAI,GAAK,QAC7B6hB,EAAOD,EApBJ,oBAoBeA,EAAO5hB,EAnBtB,kBAoBH8hB,EAAMD,GAAQ,OAASd,GAAO,MAAQE,IACtCc,EAAMF,GAAQ,OAASZ,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMH,GAAQ,OAASZ,GAAO,OAASD,IACvCiB,EAAMJ,GAAQ,OAASX,GAAO,MAAQE,IACtCc,EAAML,GAAQ,OAAST,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMN,GAAQ,OAAST,GAAO,OAASD,IACvCiB,EAAMP,GAAQ,OAASR,GAAO,MAAQE,IACtCc,EAAMR,GAAQ,OAASN,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMT,GAAQ,OAASN,GAAO,OAASD,IAE7C,MAAO,CACNiB,IAAKT,EAAME,EACXQ,IAAKT,EAAM/hB,EAAIgiB,EACfS,IAAKX,GAAOE,EAAM,QAClBU,KAAMX,EAAM,QAAU/hB,GAAKgiB,EAAM,QACjCW,IAAKV,EAAME,EACXS,IAAKV,EAAMliB,EAAImiB,EACfU,IAAKZ,GAAOE,EAAM,QAClBW,KAAMZ,EAAM,QAAUliB,GAAKmiB,EAAM,QACjCY,IAAKX,EAAME,EACXU,IAAKX,EAAMriB,EAAIsiB,EACfW,IAAKb,GAAOE,EAAM,QAClBY,KAAMb,EAAM,QAAUriB,GAAKsiB,EAAM,QAEnC,CAEA,SAASa,GAAoBC,EAAO1iB,GACnC,MAAM2iB,EAAS3iB,EAAI,IAAMhO,KAAKS,GAAK,EAC7BmwB,EAAK9B,GAAwB4B,EAAMb,IAAKa,EAAMZ,IAAKa,GACnDE,EAAK/B,GAAwB4B,EAAMX,IAAKW,EAAMV,IAAKW,GACnDG,EAAKhC,GAAwB4B,EAAMT,IAAKS,EAAMR,IAAKS,GACnDI,EAAKjC,GAAwB4B,EAAMP,IAAKO,EAAMN,IAAKO,GACnDK,EAAKlC,GAAwB4B,EAAML,IAAKK,EAAMJ,IAAKK,GACnDlhB,EAAKqf,GAAwB4B,EAAMH,IAAKG,EAAMF,IAAKG,GAEzD,OAAO3wB,KAAKkD,IAAI0tB,EAAIC,EAAIC,EAAIC,EAAIC,EAAIvhB,EACrC,CAEe,IAAAwhB,GAAA,IAAIrpB,EAAW,CAC7BrB,GAAI,QACJzE,KAAM,QACNsE,OAAQ,CACP4H,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,OAEP4J,EAAG,CACF3I,MAAO,CAAC,EAAG,KACXjB,KAAM,cAEPwL,EAAG,CACFvK,MAAO,CAAC,EAAG,KACXjB,KAAM,cAIR0B,KAAM4qB,GACN9kB,WAAY4e,GAGZ,QAAA/e,CAAU4E,GACT,IACIrC,GADC4B,EAAG7O,EAAGuP,GAAK,CAACnO,EAASkO,EAAI,IAAKlO,EAASkO,EAAI,IAAKlO,EAASkO,EAAI,KAGlE,GAAIT,EAAI,WACP5B,EAAI,EACJ4B,EAAI,SAEA,GAAIA,EAAI,KACZ5B,EAAI,EACJ4B,EAAI,MAEA,CAGJ5B,EAAIjN,EADMgyB,GADExB,GAAuB3hB,GACCU,GACtB,GACd,CAED,MAAO,CAACA,EAAGtC,EAAG4B,EACd,EAGD,MAAAlE,CAAQ6jB,GACP,IACIxuB,GADCuP,EAAGtC,EAAG4B,GAAK,CAACzN,EAASotB,EAAI,IAAKptB,EAASotB,EAAI,IAAKptB,EAASotB,EAAI,KAGlE,GAAI3f,EAAI,WACPA,EAAI,IACJ7O,EAAI,OAEA,GAAI6O,EAAI,KACZA,EAAI,EACJ7O,EAAI,MAEA,CAGJA,EADUgyB,GADExB,GAAuB3hB,GACCU,GAC1B,IAAMtC,CAChB,CAED,MAAO,CAAC4B,EAAG7O,EAAGuP,EACd,EAEDrF,QAAS,CACRxB,MAAO,CACNZ,GAAI,UACJH,OAAQ,CAAC,qBAAsB,0BAA2B,+BCnH7D,SAAS8qB,GAAoBnC,EAAOC,GACnC,OAAOhvB,KAAKE,IAAI8uB,GAAahvB,KAAKoO,KAAKpO,KAAK4N,IAAImhB,EAAO,GAAK,EAC7D,CAEA,SAASoC,GAAoBT,GAC5B,IAAIE,EAAKM,GAAmBR,EAAMb,IAAKa,EAAMZ,KACzCe,EAAKK,GAAmBR,EAAMX,IAAKW,EAAMV,KACzCc,EAAKI,GAAmBR,EAAMT,IAAKS,EAAMR,KACzCa,EAAKG,GAAmBR,EAAMP,IAAKO,EAAMN,KACzCY,EAAKE,GAAmBR,EAAML,IAAKK,EAAMJ,KACzC7gB,EAAKyhB,GAAmBR,EAAMH,IAAKG,EAAMF,KAE7C,OAAOxwB,KAAKkD,IAAI0tB,EAAIC,EAAIC,EAAIC,EAAIC,EAAIvhB,EACrC,CAvBajD,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GAiBX,IAAA4kB,GAAA,IAAIxpB,EAAW,CAC7BrB,GAAI,QACJzE,KAAM,QACNsE,OAAQ,CACP4H,EAAG,CACFjH,SAAU,CAAC,EAAG,KACdnI,KAAM,QACNkD,KAAM,OAEP4J,EAAG,CACF3I,MAAO,CAAC,EAAG,KACXjB,KAAM,cAEPwL,EAAG,CACFvK,MAAO,CAAC,EAAG,KACXjB,KAAM,cAIR0B,KAAM4qB,GACN9kB,WAAY,OAGZ,QAAAH,CAAU4E,GACT,IACIrC,GADC4B,EAAG7O,EAAGuP,GAAK,CAACnO,EAASkO,EAAI,IAAKlO,EAASkO,EAAI,IAAKlO,EAASkO,EAAI,KAGlE,GAAIT,EAAI,WACP5B,EAAI,EACJ4B,EAAI,SAEA,GAAIA,EAAI,KACZ5B,EAAI,EACJ4B,EAAI,MAEA,CAGJ5B,EAAIjN,EADM0yB,GADElC,GAAuB3hB,IAErB,GACd,CACD,MAAO,CAACU,EAAGtC,EAAG4B,EACd,EAGD,MAAAlE,CAAQ6jB,GACP,IACIxuB,GADCuP,EAAGtC,EAAG4B,GAAK,CAACzN,EAASotB,EAAI,IAAKptB,EAASotB,EAAI,IAAKptB,EAASotB,EAAI,KAGlE,GAAI3f,EAAI,WACPA,EAAI,IACJ7O,EAAI,OAEA,GAAI6O,EAAI,KACZA,EAAI,EACJ7O,EAAI,MAEA,CAGJA,EADU0yB,GADElC,GAAuB3hB,IAEzB,IAAM5B,CAChB,CAED,MAAO,CAAC4B,EAAG7O,EAAGuP,EACd,EAEDrF,QAAS,CACRxB,MAAO,CACNZ,GAAI,UACJH,OAAQ,CAAC,qBAAsB,0BAA2B,+BC3H7D,MACM/G,GAAI,KAAI,MAGRgyB,GAAO,GAAW,KAClB9e,GAAK,SACLC,GAAK,KAAI,IACTI,GAAK,QAEI,IAAA0e,GAAA,IAAIplB,EAAc,CAChC3F,GAAI,YACJmC,MAAO,aACP5G,KAAM,cACN0B,KAAMgb,GACNpV,OAAQsV,GAGAA,EAAI3gB,KAAI,SAAUoF,GAExB,OAAY,KADFnD,KAAKoD,IAAMD,GAAOkuB,GAAQ9e,GAAK,IAAMC,GAAMI,GAAMzP,GAAOkuB,MAhBxD,kBAFF,GAoBX,IAECloB,SAAUuV,GAGFA,EAAI3gB,KAAI,SAAUoF,GACxB,IAAInF,EAAIgC,KAAKoD,IA1BL,IA0BSD,EAAW,IAAO,GAInC,QAHWoP,GAAMC,GAAMxU,GAAKqB,KACf,EAAKuT,GAAM5U,GAAKqB,MAzBtB,QA4BV,MC7BA,MAAMkO,GAAI,UACJlB,GAAI,UACJ5N,GAAI,UAEJ8yB,GAAQ,OAEC,IAAAC,GAAA,IAAItlB,EAAc,CAChC3F,GAAI,aACJmC,MAAO,cACP5G,KAAM,eACN4H,SAAU,QAEVlG,KAAMgb,GACNpV,OAAQsV,GAGAA,EAAI3gB,KAAI,SAAUoF,GAKxB,OAAIA,GAAO,GACFA,GAAO,EAAK,EAAIouB,IAEhBvxB,KAAKyD,KAAKN,EAAM1E,IAAK8O,IAAKlB,IAAK,GAAMklB,EACjD,IAECpoB,SAAUuV,GAIFA,EAAI3gB,KAAI,SAAUoF,GAMxB,OAJAA,GAAOouB,KAII,EAAI,GACPvxB,KAAKoO,KAAK,EAAIjL,GAEfoK,GAAIvN,KAAK6Z,IAAI,GAAK1W,EAAMkJ,IAAK5N,EACvC,MC1CO,MAAMgzB,GAAO,CAAA,EAcb,SAASC,IAAWnrB,GAACA,EAAEorB,SAAEA,EAAQC,WAAEA,IAEzCH,GAAKlrB,GAAMnC,UAAU,EACtB,CAEO,SAASoB,GAAOC,EAAIC,EAAIa,EAAK,YAKnC,IAAIiV,EAASiW,GAAKlrB,IAEb,EAAI,EAAI,GAAM/I,EAAiBge,EAAOmW,SAAUlsB,IAChD,EAAI,EAAI,GAAMjI,EAAiBge,EAAOmW,SAAUjsB,GAUjDmsB,EAAgBr0B,EAPR,CACX,CAAC,EAAK,EAAK,EAAU,GACrB,CAAC,EAAU,EAAK,EAAK,GACrB,CAAC,EAAU,EAAU,EAAK,IAIiBge,EAAOmW,UAGnD,OAFcn0B,EAAiBge,EAAOoW,WAAYC,EAGnD,CAvCA7tB,EAAMC,IAAI,8BAA8BO,IACnCA,EAAIoB,QAAQ4V,SACfhX,EAAIsB,EAAIN,GAAMhB,EAAIiB,GAAIjB,EAAIkB,GAAIlB,EAAIoB,QAAQ4V,QAC1C,IAGFxX,EAAMC,IAAI,4BAA4BO,IAChCA,EAAIsB,IACRtB,EAAIsB,EAAIN,GAAMhB,EAAIiB,GAAIjB,EAAIkB,GAAIlB,EAAIoB,QAAQ4V,QAC1C,IAgCFkW,GAAU,CACTnrB,GAAI,YACJorB,SAAU,CACT,CAAG,OAAY,OAAY,QAC3B,EAAG,MAAY,QAAY,OAC3B,CAAG,EAAY,EAAY,SAE5BC,WAAY,CACX,CAAE,oBAAqB,mBAAsB,oBAC7C,CAAE,kBAAqB,mBAAsB,sBAC7C,CAAE,EAAqB,EAAsB,uBAI/CF,GAAU,CACTnrB,GAAI,WAGJorB,SAAU,CACT,CAAG,MAAY,OAAY,OAC3B,EAAG,MAAY,OAAY,OAC3B,CAAG,OAAY,MAAY,SAG5BC,WAAY,CACX,CAAG,mBAAqB,mBAAqB,oBAC7C,CAAG,kBAAqB,kBAAqB,qBAC7C,EAAG,mBAAqB,mBAAqB,oBAI/CF,GAAU,CACTnrB,GAAI,QAEJorB,SAAU,CACT,CAAG,MAAY,OAAY,OAC3B,EAAG,MAAY,OAAY,OAC3B,CAAG,KAAY,MAAY,QAE5BC,WAAY,CACX,CAAG,oBAAuB,mBAAqB,oBAC/C,CAAG,kBAAuB,kBAAqB,oBAC/C,EAAG,qBAAuB,mBAAqB,uBAIjDF,GAAU,CACTnrB,GAAI,QACJorB,SAAU,CACT,CAAG,QAAW,SAAW,SACzB,EAAG,QAAW,SAAW,SACzB,EAAG,QAAW,QAAW,UAG1BC,WAAY,CACX,CAAG,mBAAsB,mBAAqB,oBAC9C,CAAG,kBAAsB,mBAAqB,qBAC9C,EAAG,oBAAsB,mBAAqB,uBAIhD9yB,OAAOsJ,OAAOhD,EAAQ,CAIrB3H,EAAK,CAAC,OAAS,EAAS,QAGxBga,EAAK,CAAC,OAAS,EAAU,SAKzBqa,IAAK,CAAC,OAAS,EAAS,QACxBC,IAAK,CAAC,OAAS,EAAS,SAGxB/U,EAAK,CAAC,EAAS,EAAS,GAGxBgV,GAAK,CAAC,OAAS,EAAS,QACxBC,GAAK,CAAC,OAAS,EAAS,SACxBC,IAAK,CAAC,QAAS,EAAS,SCzHzB9sB,EAAO+sB,KAAO,CAAC,OAAU,OAAS,EAAS,OAAgC,QAc5D,IAAAC,GAAA,IAAIlmB,EAAc,CAChC3F,GAAI,SACJmC,MAAO,WACP5G,KAAM,SAKNsE,OAAQ,CACP+F,EAAG,CACFpJ,MAAO,CAAC,EAAG,OACXjB,KAAM,OAEPsK,EAAG,CACFrJ,MAAO,CAAC,EAAG,OACXjB,KAAM,SAEPuK,EAAG,CACFtJ,MAAO,CAAC,EAAG,OACXjB,KAAM,SAIR4H,SAAU,QAEVL,MAAOjE,EAAO+sB,KAEd5lB,QAtCe,CACf,CAAG,kBAAsB,mBAAsB,mBAC/C,CAAG,mBAAsB,kBAAsB,oBAC/C,EAAG,oBAAsB,oBAAsB,qBAoC/CC,UAlCiB,CACjB,CAAG,oBAAuB,iBAAsB,oBAChD,EAAG,kBAAuB,mBAAsB,qBAChD,CAAG,qBAAuB,oBAAsB,sBCfjD,MAAM,GAAI,IAAM,GAIV6lB,IAAoB,UAGpBC,IAAetyB,KAAKuyB,KAAK,OAAS,MAAQ,MAEjC,IAAAC,GAAA,IAAItmB,EAAc,CAChC3F,GAAI,SACJmC,MAAO,WACP5G,KAAM,SASNsE,OAAQ,CACP+F,EAAG,CACFpJ,MAAO,CAACsvB,GAAkBC,IAC1BxwB,KAAM,OAEPsK,EAAG,CACFrJ,MAAO,CAACsvB,GAAkBC,IAC1BxwB,KAAM,SAEPuK,EAAG,CACFtJ,MAAO,CAACsvB,GAAkBC,IAC1BxwB,KAAM,SAGR4H,SAAU,QAEVlG,KAAM4uB,GAENhpB,OAAQsV,GAGAA,EAAI3gB,KAAI,SAAUoF,GACxB,OAAIA,IAHO,kBAIiC,GAAnC,IAAa,MAANA,EAAe,MAAQ,IAE9BA,EAAMmvB,GACP,IAAa,MAANnvB,EAAe,MAGtB,KAEX,IAICgG,SAAUuV,GACFA,EAAI3gB,KAAI,SAAUoF,GACxB,OAAIA,GAAO,GACFnD,KAAKuyB,KAAK,IAAK,MAAQ,MAEvBpvB,EAAM,IACLnD,KAAKuyB,KAAK,GAAU,GAANpvB,GAAa,MAAQ,OAGnCnD,KAAKuyB,KAAKpvB,GAAO,MAAQ,KAEtC,mZC3Ce,MAAMkmB,GAUpB,WAAApgB,IAAgBhI,GACf,IAAIkG,EAMAjB,EAAOE,EAAQzE,EAJC,IAAhBV,EAAKrD,SACRuJ,EAAQ2B,EAAS7H,EAAK,KAKnBkG,GACHjB,EAAQiB,EAAMjB,OAASiB,EAAMmB,QAC7BlC,EAASe,EAAMf,OACfzE,EAAQwF,EAAMxF,QAIbuE,EAAOE,EAAQzE,GAASV,EAG1BnC,OAAO6K,eAAerF,KAAM,QAAS,CACpC/B,MAAOqF,EAAWmB,IAAI7C,GACtB4D,UAAU,EACVC,YAAY,EACZC,cAAc,IAGf1F,KAAK8B,OAASA,EAASA,EAAO7E,QAAU,CAAC,EAAG,EAAG,GAG/C+C,KAAK3C,MAAQA,EAAQ,QAAeqH,IAAVrH,EAAsB,EAAKA,EAAQ,EAAI,EAAIA,EAGrE,IAAK,IAAIvD,EAAI,EAAGA,EAAIkG,KAAK8B,OAAOxI,OAAQQ,IAChB,QAAnBkG,KAAK8B,OAAOhI,KACfkG,KAAK8B,OAAOhI,GAAKqD,KAKnB,IAAK,IAAI8E,KAAMjC,KAAK4B,MAAME,OACzBtH,OAAO6K,eAAerF,KAAMiC,EAAI,CAC/BwC,IAAK,IAAMzE,KAAKyE,IAAIxC,GACpBwG,IAAKxK,GAAS+B,KAAKyI,IAAIxG,EAAIhE,IAG7B,CAED,WAAI+F,GACH,OAAOhE,KAAK4B,MAAMK,EAClB,CAED,KAAAwL,GACC,OAAO,IAAIsX,GAAM/kB,KAAK4B,MAAO5B,KAAK8B,OAAQ9B,KAAK3C,MAC/C,CAED,MAAA8wB,GACC,MAAO,CACNnqB,QAAShE,KAAKgE,QACdlC,OAAQ9B,KAAK8B,OACbzE,MAAO2C,KAAK3C,MAEb,CAED,OAAA+wB,IAAYzxB,GACX,IAAIzC,E9B7DS,SAAkB2I,GAAOjB,MAACA,EAAQxB,EAASskB,iBAAkBpjB,GAAW,IACtF,IAAIpH,EAAMuf,GAAU5W,EAAOvB,GAE3B,GAAmB,oBAARqjB,KAAuBA,IAAIC,SAAS,QAAS1qB,KAASkG,EAASskB,cACzExqB,EAAM,IAAIwE,OAAOxE,GACjBA,EAAI2I,MAAQA,MAER,CAEJ,IAAIwrB,EAAgBxrB,EAKpB,IAFcA,EAAMf,OAAOwsB,KAAKpzB,IAAWA,EAAO2H,EAAMxF,WAIjDonB,KAAiBE,IAAIC,SAAS,QAAS,wBAE5CyJ,EAAgB5gB,GAAM5K,GACtBwrB,EAAcvsB,OAASusB,EAAcvsB,OAAOrI,IAAI8B,GAChD8yB,EAAchxB,MAAQ9B,EAAS8yB,EAAchxB,OAE7CnD,EAAMuf,GAAU4U,EAAe/sB,GAE3BqjB,IAAIC,SAAS,QAAS1qB,IAIzB,OAFAA,EAAM,IAAIwE,OAAOxE,GACjBA,EAAI2I,MAAQwrB,EACLn0B,EAOVm0B,EAAgBjwB,GAAGiwB,EAAezsB,GAClC1H,EAAM,IAAIwE,OAAO+a,GAAU4U,EAAe/sB,IAC1CpH,EAAI2I,MAAQwrB,CACZ,CAED,OAAOn0B,CACR,C8BoBYk0B,CAAQpuB,QAASrD,GAK3B,OAFAzC,EAAI2I,MAAQ,IAAIkiB,GAAM7qB,EAAI2I,OAEnB3I,CACP,CAMD,UAAOuK,CAAK5B,KAAUlG,GACrB,OAAIkG,aAAiBkiB,GACbliB,EAGD,IAAIkiB,GAAMliB,KAAUlG,EAC3B,CAED,qBAAO8rB,CAAgBjrB,EAAM+wB,EAAMh0B,EAAIg0B,GACtC,IAAIC,SAACA,GAAW,EAAI7lB,QAAEA,GAAWpO,EAE7Bk0B,EAAO,YAAa9xB,GACvB,IAAIzC,EAAMq0B,KAAQ5xB,GAElB,GAAgB,UAAZgM,EACHzO,EAAM6qB,GAAMtgB,IAAIvK,QAEZ,GAAgB,oBAAZyO,EAA+B,CACvC,IAAIQ,EAAIjP,EACRA,EAAM,YAAayC,GAClB,IAAIzC,EAAMiP,KAAKxM,GACf,OAAOooB,GAAMtgB,IAAIvK,EACtB,EAEIM,OAAOsJ,OAAO5J,EAAKiP,EACnB,KACoB,iBAAZR,IACRzO,EAAMA,EAAIT,KAAIU,GAAK4qB,GAAMtgB,IAAItK,MAG9B,OAAOD,CACV,EAEQsD,KAAQunB,KACbA,GAAMvnB,GAAQixB,GAGXD,IACHzJ,GAAMtqB,UAAU+C,GAAQ,YAAab,GACpC,OAAO8xB,EAAKzuB,QAASrD,EACzB,EAEE,CAED,sBAAO+xB,CAAiBn0B,GACvB,IAAK,IAAIiD,KAAQjD,EAChBwqB,GAAM0D,eAAejrB,EAAMjD,EAAEiD,GAAOjD,EAAEiD,GAEvC,CAED,aAAOmxB,CAAQC,GACd,GAAIA,EAAQnoB,SACXmoB,EAAQnoB,SAASse,SAIjB,IAAK,IAAIvnB,KAAQoxB,EAChB7J,GAAM0D,eAAejrB,EAAMoxB,EAAQpxB,GAGrC,EAGFunB,GAAM2J,gBAAgB,CACrBjqB,MACA6D,SACAG,MACAD,SACApK,MACAwH,OCrLc,SAAiB+H,EAAQC,GAIvC,OAHAD,EAASnJ,EAASmJ,GAClBC,EAASpJ,EAASoJ,GAEXD,EAAO/L,QAAUgM,EAAOhM,OACrB+L,EAAOtQ,QAAUuQ,EAAOvQ,OACxBsQ,EAAO7L,OAAOgE,OAAM,CAAC3L,EAAGL,IAAMK,IAAMyT,EAAO9L,OAAOhI,IAC7D,ED+KCqL,WACAoS,WACA7J,YACAhT,SAAU+e,KAGXjf,OAAOsJ,OAAOihB,GAAO,CACpB8J,OACAnvB,QACAoB,SACAguB,MAAOxrB,EACPyrB,OAAQzrB,EAAWa,SACnBxB,QAGAvC,aElMD,IAAK,IAAI4uB,KAAOx0B,OAAOqJ,KAAKkrB,IAC3BzrB,EAAWmD,SAASsoB,GAAOC,ICG5B,IAAK,IAAI/sB,KAAMqB,EAAWa,SACzB8qB,GAAkBhtB,EAAIqB,EAAWa,SAASlC,IAW3C,SAASgtB,GAAmBhtB,EAAIL,GAC/B,IAAIstB,EAASjtB,EAAGrF,QAAQ,KAAM,KAE9BpC,OAAO6K,eAAe0f,GAAMtqB,UAAWy0B,EAAQ,CAI9C,GAAAzqB,GACC,IAAIvK,EAAM8F,KAAKsI,OAAOrG,GAEtB,MAAqB,oBAAVktB,MAEHj1B,EAID,IAAIi1B,MAAMj1B,EAAK,CACrBmI,IAAK,CAAC+sB,EAAKC,KACV,IAEC,OADA/rB,EAAWsD,aAAa,CAAChF,EAAOytB,KACzB,CACP,CACD,MAAOle,GAAK,CAEZ,OAAOme,QAAQjtB,IAAI+sB,EAAKC,EAAS,EAElC5qB,IAAK,CAAC2qB,EAAKC,EAAUE,KACpB,GAAIF,GAAgC,iBAAbA,KAA2BA,KAAYD,GAAM,CACnE,IAAInoB,MAACA,GAAS3D,EAAWsD,aAAa,CAAChF,EAAOytB,IAE9C,GAAIpoB,GAAS,EACZ,OAAOmoB,EAAInoB,EAEZ,CAED,OAAOqoB,QAAQ7qB,IAAI2qB,EAAKC,EAAUE,EAAS,EAE5C9mB,IAAK,CAAC2mB,EAAKC,EAAUpxB,EAAOsxB,KAC3B,GAAIF,GAAgC,iBAAbA,KAA2BA,KAAYD,IAAQC,GAAY,EAAG,CACpF,IAAIpoB,MAACA,GAAS3D,EAAWsD,aAAa,CAAChF,EAAOytB,IAE9C,GAAIpoB,GAAS,EAMZ,OALAmoB,EAAInoB,GAAShJ,EAGb+B,KAAKwI,OAAOvG,EAAImtB,IAET,CAER,CAED,OAAOE,QAAQ7mB,IAAI2mB,EAAKC,EAAUpxB,EAAOsxB,EAAS,GAGpD,EAID,GAAA9mB,CAAK3G,GACJ9B,KAAKwI,OAAOvG,EAAIH,EAChB,EACD4D,cAAc,EACdD,YAAY,GAEd,QAvEA/F,EAAMC,IAAI,uBAAuBiC,IAChCqtB,GAAkBrtB,EAAMK,GAAIL,GAC5BA,EAAMgD,SAAS7E,SAAQ2G,IACtBuoB,GAAkBvoB,EAAO9E,EAAM,GAC9B,ICRHmjB,GAAM4J,OAAOlZ,IACbsP,GAAM4J,OAAO,CAACruB,YACd9F,OAAOsJ,OAAOihB,GAAO,CAACtP,mBAItBsP,GAAM4J,OAAOa,IAGbzK,GAAM4J,OAAO,CAACvI,SCdC,SAAmBb,EAAYC,EAAYjrB,EAAI,CAAA,GACzDH,EAASG,KACZA,EAAI,CAACk1B,UAAWl1B,IAGjB,IAAIk1B,UAACA,KAAc3I,GAAQvsB,EAE3B,IAAKk1B,EAAW,CACf,IAAIC,EAAal1B,OAAOqJ,KAAK8rB,IAAoBl2B,KAAIwP,GAAKA,EAAErM,QAAQ,YAAa,MAAKuK,KAAK,MAC3F,MAAM,IAAI5F,UAAU,0EAA0EmuB,IAC9F,CAEDnK,EAAa/gB,EAAS+gB,GACtBC,EAAahhB,EAASghB,GAEtB,IAAK,IAAIvc,KAAK0mB,GACb,GAAI,WAAaF,EAAU50B,gBAAkBoO,EAAEpO,cAC9C,OAAO80B,GAAmB1mB,GAAGsc,EAAYC,EAAYsB,GAIvD,MAAM,IAAIvlB,UAAU,+BAA+BkuB,IACpD,IDLA1K,GAAM4J,OAAOiB,IAGb7K,GAAM4J,OAAOkB,IAGb9K,GAAM4J,OAAOmB,IAGb/K,GAAM4J,OAAOoB"}