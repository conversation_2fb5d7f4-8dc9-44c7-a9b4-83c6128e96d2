{"version": 3, "file": "color-fn.cjs", "sources": ["../src/multiply-matrices.js", "../src/util.js", "../src/hooks.js", "../src/adapt.js", "../src/defaults.js", "../src/parse.js", "../src/getColor.js", "../src/space.js", "../src/spaces/xyz-d65.js", "../src/rgbspace.js", "../src/getAll.js", "../src/get.js", "../src/setAll.js", "../src/set.js", "../src/spaces/xyz-d50.js", "../src/spaces/lab.js", "../src/angles.js", "../src/spaces/lch.js", "../src/deltaE/deltaE2000.js", "../src/spaces/oklab.js", "../src/deltaE/deltaEOK.js", "../src/inGamut.js", "../src/clone.js", "../src/distance.js", "../src/deltaE/deltaE76.js", "../src/deltaE/deltaECMC.js", "../src/spaces/xyz-abs-d65.js", "../src/spaces/jzazbz.js", "../src/spaces/jzczhz.js", "../src/deltaE/deltaEJz.js", "../src/spaces/ictcp.js", "../src/deltaE/deltaEITP.js", "../src/spaces/cam16.js", "../src/spaces/hct.js", "../src/deltaE/deltaEHCT.js", "../src/deltaE/index.js", "../src/toGamut.js", "../src/to.js", "../src/serialize.js", "../src/spaces/rec2020-linear.js", "../src/spaces/rec2020.js", "../src/spaces/p3-linear.js", "../src/spaces/srgb-linear.js", "../src/keywords.js", "../src/spaces/srgb.js", "../src/spaces/p3.js", "../src/display.js", "../src/equals.js", "../src/luminance.js", "../src/contrast/WCAG21.js", "../src/contrast/APCA.js", "../src/contrast/Michelson.js", "../src/contrast/Weber.js", "../src/contrast/Lstar.js", "../src/spaces/lab-d65.js", "../src/contrast/deltaPhi.js", "../src/contrast.js", "../src/chromaticity.js", "../src/deltaE.js", "../src/variations.js", "../src/interpolation.js", "../src/spaces/hsl.js", "../src/spaces/hsv.js", "../src/spaces/hwb.js", "../src/spaces/a98rgb-linear.js", "../src/spaces/a98rgb.js", "../src/spaces/prophoto-linear.js", "../src/spaces/prophoto.js", "../src/spaces/oklch.js", "../src/spaces/luv.js", "../src/spaces/lchuv.js", "../src/spaces/hsluv.js", "../src/spaces/hpluv.js", "../src/spaces/rec2100-pq.js", "../src/spaces/rec2100-hlg.js", "../src/CATs.js", "../src/spaces/acescg.js", "../src/spaces/acescc.js"], "sourcesContent": ["// A is m x n. B is n x p. product is m x p.\nexport default function multiplyMatrices (A, B) {\n\tlet m = A.length;\n\n\tif (!Array.isArray(A[0])) {\n\t\t// A is vector, convert to [[a, b, c, ...]]\n\t\tA = [A];\n\t}\n\n\tif (!Array.isArray(B[0])) {\n\t\t// B is vector, convert to [[a], [b], [c], ...]]\n\t\tB = B.map(x => [x]);\n\t}\n\n\tlet p = B[0].length;\n\tlet B_cols = B[0].map((_, i) => B.map(x => x[i])); // transpose B\n\tlet product = A.map(row => B_cols.map(col => {\n\t\tlet ret = 0;\n\n\t\tif (!Array.isArray(row)) {\n\t\t\tfor (let c of col) {\n\t\t\t\tret += row * c;\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tfor (let i = 0; i < row.length; i++) {\n\t\t\tret += row[i] * (col[i] || 0);\n\t\t}\n\n\t\treturn ret;\n\t}));\n\n\tif (m === 1) {\n\t\tproduct = product[0]; // Avoid [[a, b, c, ...]]\n\t}\n\n\tif (p === 1) {\n\t\treturn product.map(x => x[0]); // Avoid [[a], [b], [c], ...]]\n\t}\n\n\treturn product;\n}\n", "/**\n * Various utility functions\n */\n\nexport {default as multiplyMatrices} from \"./multiply-matrices.js\";\n\n/**\n * Check if a value is a string (including a String object)\n * @param {*} str - Value to check\n * @returns {boolean}\n */\nexport function isString (str) {\n\treturn type(str) === \"string\";\n}\n\n/**\n * Determine the internal JavaScript [[Class]] of an object.\n * @param {*} o - Value to check\n * @returns {string}\n */\nexport function type (o) {\n\tlet str = Object.prototype.toString.call(o);\n\n\treturn (str.match(/^\\[object\\s+(.*?)\\]$/)[1] || \"\").toLowerCase();\n}\n\nexport function serializeNumber (n, {precision, unit }) {\n\tif (isNone(n)) {\n\t\treturn \"none\";\n\t}\n\n\treturn toPrecision(n, precision) + (unit ?? \"\");\n}\n\n/**\n * Check if a value corresponds to a none argument\n * @param {*} n - Value to check\n * @returns {boolean}\n */\nexport function isNone (n) {\n\treturn Number.isNaN(n) || (n instanceof Number && n?.none);\n}\n\n/**\n * Replace none values with 0\n */\nexport function skipNone (n) {\n\treturn isNone(n) ? 0 : n;\n}\n\n/**\n * Round a number to a certain number of significant digits\n * @param {number} n - The number to round\n * @param {number} precision - Number of significant digits\n */\nexport function toPrecision (n, precision) {\n\tif (n === 0) {\n\t\treturn 0;\n\t}\n\tlet integer = ~~n;\n\tlet digits = 0;\n\tif (integer && precision) {\n\t\tdigits = ~~Math.log10(Math.abs(integer)) + 1;\n\t}\n\tconst multiplier = 10.0 ** (precision - digits);\n\treturn Math.floor(n * multiplier + 0.5) / multiplier;\n}\n\nconst angleFactor = {\n\tdeg: 1,\n\tgrad: 0.9,\n\trad: 180 / Math.PI,\n\tturn: 360,\n};\n\n/**\n* Parse a CSS function, regardless of its name and arguments\n* @param String str String to parse\n* @return {{name, args, rawArgs}}\n*/\nexport function parseFunction (str) {\n\tif (!str) {\n\t\treturn;\n\t}\n\n\tstr = str.trim();\n\n\tconst isFunctionRegex = /^([a-z]+)\\((.+?)\\)$/i;\n\tconst isNumberRegex = /^-?[\\d.]+$/;\n\tconst unitValueRegex = /%|deg|g?rad|turn$/;\n\tconst singleArgument = /\\/?\\s*(none|[-\\w.]+(?:%|deg|g?rad|turn)?)/g;\n\tlet parts = str.match(isFunctionRegex);\n\n\tif (parts) {\n\t\t// It is a function, parse args\n\t\tlet args = [];\n\t\tparts[2].replace(singleArgument, ($0, rawArg) => {\n\t\t\tlet match = rawArg.match(unitValueRegex);\n\t\t\tlet arg = rawArg;\n\n\t\t\tif (match) {\n\t\t\t\tlet unit = match[0];\n\t\t\t\t// Drop unit from value\n\t\t\t\tlet unitlessArg = arg.slice(0, -unit.length);\n\n\t\t\t\tif (unit === \"%\") {\n\t\t\t\t\t// Convert percentages to 0-1 numbers\n\t\t\t\t\targ = new Number(unitlessArg / 100);\n\t\t\t\t\targ.type = \"<percentage>\";\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// Multiply angle by appropriate factor for its unit\n\t\t\t\t\targ = new Number(unitlessArg * angleFactor[unit]);\n\t\t\t\t\targ.type = \"<angle>\";\n\t\t\t\t\targ.unit = unit;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (isNumberRegex.test(arg)) {\n\t\t\t\t// Convert numerical args to numbers\n\t\t\t\targ = new Number(arg);\n\t\t\t\targ.type = \"<number>\";\n\t\t\t}\n\t\t\telse if (arg === \"none\") {\n\t\t\t\targ = new Number(NaN);\n\t\t\t\targ.none = true;\n\t\t\t}\n\n\t\t\tif ($0.startsWith(\"/\")) {\n\t\t\t\t// It's alpha\n\t\t\t\targ = arg instanceof Number ? arg : new Number(arg);\n\t\t\t\targ.alpha = true;\n\t\t\t}\n\n\t\t\tif (typeof arg === \"object\" && arg instanceof Number) {\n\t\t\t\targ.raw = rawArg;\n\t\t\t}\n\n\t\t\targs.push(arg);\n\t\t});\n\n\t\treturn {\n\t\t\tname: parts[1].toLowerCase(),\n\t\t\trawName: parts[1],\n\t\t\trawArgs: parts[2],\n\t\t\t// An argument could be (as of css-color-4):\n\t\t\t// a number, percentage, degrees (hue), ident (in color())\n\t\t\targs,\n\t\t};\n\t}\n}\n\nexport function last (arr) {\n\treturn arr[arr.length - 1];\n}\n\nexport function interpolate (start, end, p) {\n\tif (isNaN(start)) {\n\t\treturn end;\n\t}\n\n\tif (isNaN(end)) {\n\t\treturn start;\n\t}\n\n\treturn start + (end - start) * p;\n}\n\nexport function interpolateInv (start, end, value) {\n\treturn (value - start) / (end - start);\n}\n\nexport function mapRange (from, to, value) {\n\treturn interpolate(to[0], to[1], interpolateInv(from[0], from[1], value));\n}\n\nexport function parseCoordGrammar (coordGrammars) {\n\treturn coordGrammars.map(coordGrammar => {\n\t\treturn coordGrammar.split(\"|\").map(type => {\n\t\t\ttype = type.trim();\n\t\t\tlet range = type.match(/^(<[a-z]+>)\\[(-?[.\\d]+),\\s*(-?[.\\d]+)\\]?$/);\n\n\t\t\tif (range) {\n\t\t\t\tlet ret = new String(range[1]);\n\t\t\t\tret.range = [+range[2], +range[3]];\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn type;\n\t\t});\n\t});\n}\n\n/**\n * Clamp value between the minimum and maximum\n * @param {number} min minimum value to return\n * @param {number} val the value to return if it is between min and max\n * @param {number} max maximum value to return\n * @returns number\n */\nexport function clamp (min, val, max) {\n\treturn Math.max(Math.min(max, val), min);\n}\n\n/**\n * Copy sign of one value to another.\n * @param {number} - to number to copy sign to\n * @param {number} - from number to copy sign from\n * @returns number\n */\nexport function copySign (to, from) {\n\treturn Math.sign(to) === Math.sign(from) ? to : -to;\n}\n\n/**\n * Perform pow on a signed number and copy sign to result\n * @param {number} - base the base number\n * @param {number} - exp the exponent\n * @returns number\n */\nexport function spow (base, exp) {\n\treturn copySign(Math.abs(base) ** exp, base);\n}\n\n/**\n * Perform a divide, but return zero if the numerator is zero\n * @param {number} n - the numerator\n * @param {number} d - the denominator\n * @returns number\n */\nexport function zdiv (n, d) {\n\treturn (d === 0) ? 0 : n / d;\n}\n\n/**\n * Perform a bisect on a sorted list and locate the insertion point for\n * a value in arr to maintain sorted order.\n * @param {number[]} arr - array of sorted numbers\n * @param {number} value - value to find insertion point for\n * @param {number} lo - used to specify a the low end of a subset of the list\n * @param {number} hi - used to specify a the high end of a subset of the list\n * @returns number\n */\nexport function bisectLeft (arr, value, lo = 0, hi = arr.length) {\n\twhile (lo < hi) {\n\t\tconst mid = (lo + hi) >> 1;\n\t\tif (arr[mid] < value) {\n\t\t\tlo = mid + 1;\n\t\t}\n\t\telse {\n\t\t\thi = mid;\n\t\t}\n\t}\n\treturn lo;\n}\n", "/**\n * A class for adding deep extensibility to any piece of JS code\n */\nexport class Hooks {\n\tadd (name, callback, first) {\n\t\tif (typeof arguments[0] != \"string\") {\n\t\t\t// Multiple hooks\n\t\t\tfor (var name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\tthis[name] = this[name] || [];\n\n\t\t\tif (callback) {\n\t\t\t\tthis[name][first ? \"unshift\" : \"push\"](callback);\n\t\t\t}\n\t\t}, this);\n\t}\n\n\trun (name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * The instance of {@link Hooks} used throughout Color.js\n */\nconst hooks = new Hooks();\n\nexport default hooks;\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\n\nexport const WHITES = {\n\t// for compatibility, the four-digit chromaticity-derived ones everyone else uses\n\tD50: [0.3457 / 0.3585, 1.00000, (1.0 - 0.3457 - 0.3585) / 0.3585],\n\tD65: [0.3127 / 0.3290, 1.00000, (1.0 - 0.3127 - 0.3290) / 0.3290],\n};\n\nexport function getWhite (name) {\n\tif (Array.isArray(name)) {\n\t\treturn name;\n\t}\n\n\treturn WHITES[name];\n}\n\n// Adapt XYZ from white point W1 to W2\nexport default function adapt (W1, W2, XYZ, options = {}) {\n\tW1 = getWhite(W1);\n\tW2 = getWhite(W2);\n\n\tif (!W1 || !W2) {\n\t\tthrow new TypeError(`Missing white point to convert ${!W1 ? \"from\" : \"\"}${!W1 && !W2 ? \"/\" : \"\"}${!W2 ? \"to\" : \"\"}`);\n\t}\n\n\tif (W1 === W2) {\n\t\t// Same whitepoints, no conversion needed\n\t\treturn XYZ;\n\t}\n\n\tlet env = {W1, W2, XYZ, options};\n\n\thooks.run(\"chromatic-adaptation-start\", env);\n\n\tif (!env.M) {\n\t\tif (env.W1 === WHITES.D65 && env.W2 === WHITES.D50) {\n\t\t\tenv.M = [\n\t\t\t\t[ 1.0479297925449969, 0.022946870601609652, -0.05019226628920524 ],\n\t\t\t\t[ 0.02962780877005599, 0.9904344267538799, -0.017073799063418826 ],\n\t\t\t\t[ -0.009243040646204504, 0.015055191490298152, 0.7518742814281371 ],\n\t\t\t];\n\t\t}\n\t\telse if (env.W1 === WHITES.D50 && env.W2 === WHITES.D65) {\n\n\t\t\tenv.M = [\n\t\t\t\t[ 0.955473421488075, -0.02309845494876471, 0.06325924320057072 ],\n\t\t\t\t[ -0.0283697093338637, 1.0099953980813041, 0.021041441191917323 ],\n\t\t\t\t[ 0.012314014864481998, -0.020507649298898964, 1.330365926242124 ],\n\t\t\t];\n\t\t}\n\t}\n\n\thooks.run(\"chromatic-adaptation-end\", env);\n\n\tif (env.M) {\n\t\treturn multiplyMatrices(env.M, env.XYZ);\n\t}\n\telse {\n\t\tthrow new TypeError(\"Only Bradford CAT with white points D50 and D65 supported for now.\");\n\t}\n}\n", "// Global defaults one may want to configure\nexport default {\n\tgamut_mapping: \"css\",\n\tprecision: 5,\n\tdeltaE: \"76\", // Default deltaE method\n\tverbose: globalThis?.process?.env?.NODE_ENV?.toLowerCase() !== \"test\",\n\twarn: function warn (msg) {\n\t\tif (this.verbose) {\n\t\t\tglobalThis?.console?.warn?.(msg);\n\t\t}\n\t},\n};\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\n\nconst noneTypes = new Set([\"<number>\", \"<percentage>\", \"<angle>\"]);\n\n/**\n * Validates the coordinates of a color against a format's coord grammar and\n * maps the coordinates to the range or refRange of the coordinates.\n * @param {ColorSpace} space - Colorspace the coords are in\n * @param {object} format - the format object to validate against\n * @param {string} name - the name of the color function. e.g. \"oklab\" or \"color\"\n * @returns {object[]} - an array of type metadata for each coordinate\n */\nfunction coerceCoords (space, format, name, coords) {\n\tlet types = Object.entries(space.coords).map(([id, coordMeta], i) => {\n\t\tlet coordGrammar = format.coordGrammar[i];\n\t\tlet arg = coords[i];\n\t\tlet providedType = arg?.type;\n\n\t\t// Find grammar alternative that matches the provided type\n\t\t// Non-strict equals is intentional because we are comparing w/ string objects\n\t\tlet type;\n\t\tif (arg.none) {\n\t\t\ttype = coordGrammar.find(c => noneTypes.has(c));\n\t\t}\n\t\telse {\n\t\t\ttype = coordGrammar.find(c => c == providedType);\n\t\t}\n\n\t\t// Check that each coord conforms to its grammar\n\t\tif (!type) {\n\t\t\t// Type does not exist in the grammar, throw\n\t\t\tlet coordName = coordMeta.name || id;\n\t\t\tthrow new TypeError(`${providedType ?? arg.raw} not allowed for ${coordName} in ${name}()`);\n\t\t}\n\n\t\tlet fromRange = type.range;\n\n\t\tif (providedType === \"<percentage>\") {\n\t\t\tfromRange ||= [0, 1];\n\t\t}\n\n\t\tlet toRange = coordMeta.range || coordMeta.refRange;\n\n\t\tif (fromRange && toRange) {\n\t\t\tcoords[i] = util.mapRange(fromRange, toRange, coords[i]);\n\t\t}\n\n\t\treturn type;\n\t});\n\n\treturn types;\n}\n\n\n/**\n * Convert a CSS Color string to a color object\n * @param {string} str\n * @param {object} [options]\n * @param {object} [options.meta] - Object for additional information about the parsing\n * @returns {Color}\n */\nexport default function parse (str, {meta} = {}) {\n\tlet env = {\"str\": String(str)?.trim()};\n\thooks.run(\"parse-start\", env);\n\n\tif (env.color) {\n\t\treturn env.color;\n\t}\n\n\tenv.parsed = util.parseFunction(env.str);\n\n\tif (env.parsed) {\n\t\t// Is a functional syntax\n\t\tlet name = env.parsed.name;\n\n\t\tif (name === \"color\") {\n\t\t\t// color() function\n\t\t\tlet id = env.parsed.args.shift();\n\t\t\t// Check against both <dashed-ident> and <ident> versions\n\t\t\tlet alternateId = id.startsWith(\"--\") ? id.substring(2) : `--${id}`;\n\t\t\tlet ids = [id, alternateId];\n\t\t\tlet alpha = env.parsed.rawArgs.indexOf(\"/\") > 0 ? env.parsed.args.pop() : 1;\n\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\tlet colorSpec = space.getFormat(\"color\");\n\n\t\t\t\tif (colorSpec) {\n\t\t\t\t\tif (ids.includes(colorSpec.id) || colorSpec.ids?.filter((specId) => ids.includes(specId)).length) {\n\t\t\t\t\t\t// From https://drafts.csswg.org/css-color-4/#color-function\n\t\t\t\t\t\t// If more <number>s or <percentage>s are provided than parameters that the colorspace takes, the excess <number>s at the end are ignored.\n\t\t\t\t\t\t// If less <number>s or <percentage>s are provided than parameters that the colorspace takes, the missing parameters default to 0. (This is particularly convenient for multichannel printers where the additional inks are spot colors or varnishes that most colors on the page won’t use.)\n\t\t\t\t\t\tconst coords = Object.keys(space.coords).map((_, i) => env.parsed.args[i] || 0);\n\n\t\t\t\t\t\tlet types;\n\n\t\t\t\t\t\tif (colorSpec.coordGrammar) {\n\t\t\t\t\t\t\ttypes = coerceCoords(space, colorSpec, \"color\", coords);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (meta) {\n\t\t\t\t\t\t\tObject.assign(meta, {formatId: \"color\", types});\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (colorSpec.id.startsWith(\"--\") && !id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a non-standard space and not currently supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use prefixed color(${colorSpec.id}) instead of color(${id}).`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (id.startsWith(\"--\") && !colorSpec.id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a standard space and supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use color(${colorSpec.id}) instead of prefixed color(${id}).`);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {spaceId: space.id, coords, alpha};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Not found\n\t\t\tlet didYouMean = \"\";\n\t\t\tlet registryId = id in ColorSpace.registry ? id : alternateId;\n\t\t\tif (registryId in ColorSpace.registry) {\n\t\t\t\t// Used color space id instead of color() id, these are often different\n\t\t\t\tlet cssId = ColorSpace.registry[registryId].formats?.color?.id;\n\n\t\t\t\tif (cssId) {\n\t\t\t\t\tdidYouMean = `Did you mean color(${cssId})?`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthrow new TypeError(`Cannot parse color(${id}). ` + (didYouMean || \"Missing a plugin?\"));\n\t\t}\n\t\telse {\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\t// color space specific function\n\t\t\t\tlet format = space.getFormat(name);\n\t\t\t\tif (format && format.type === \"function\") {\n\t\t\t\t\tlet alpha = 1;\n\n\t\t\t\t\tif (format.lastAlpha || util.last(env.parsed.args).alpha) {\n\t\t\t\t\t\talpha = env.parsed.args.pop();\n\t\t\t\t\t}\n\n\t\t\t\t\tlet coords = env.parsed.args;\n\n\t\t\t\t\tlet types;\n\n\t\t\t\t\tif (format.coordGrammar) {\n\t\t\t\t\t\ttypes = coerceCoords(space, format, name, coords);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tObject.assign(meta, {formatId: format.name, types});\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tspaceId: space.id,\n\t\t\t\t\t\tcoords, alpha,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\telse {\n\t\t// Custom, colorspace-specific format\n\t\tfor (let space of ColorSpace.all) {\n\t\t\tfor (let formatId in space.formats) {\n\t\t\t\tlet format = space.formats[formatId];\n\n\t\t\t\tif (format.type !== \"custom\") {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (format.test && !format.test(env.str)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tlet color = format.parse(env.str);\n\n\t\t\t\tif (color) {\n\t\t\t\t\tcolor.alpha ??= 1;\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tmeta.formatId = formatId;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t// If we're here, we couldn't parse\n\tthrow new TypeError(`Could not parse ${str} as a color. Missing a plugin?`);\n}\n", "import ColorSpace from \"./space.js\";\nimport {isString} from \"./util.js\";\nimport parse from \"./parse.js\";\n\n/**\n * Resolves a color reference (object or string) to a plain color object\n * @param {Color | {space, coords, alpha} | string | Array<Color | {space, coords, alpha} | string> } color\n * @returns {{space, coords, alpha} | Array<{space, coords, alpha}}>\n */\nexport default function getColor (color) {\n\tif (Array.isArray(color)) {\n\t\treturn color.map(getColor);\n\t}\n\n\tif (!color) {\n\t\tthrow new TypeError(\"Empty color reference\");\n\t}\n\n\tif (isString(color)) {\n\t\tcolor = parse(color);\n\t}\n\n\t// Object fixup\n\tlet space = color.space || color.spaceId;\n\n\tif (!(space instanceof ColorSpace)) {\n\t\t// Convert string id to color space object\n\t\tcolor.space = ColorSpace.get(space);\n\t}\n\n\tif (color.alpha === undefined) {\n\t\tcolor.alpha = 1;\n\t}\n\n\treturn color;\n}\n", "import {type, parseCoordGrammar, serialize<PERSON><PERSON>ber, mapRange} from \"./util.js\";\nimport {getWhite} from \"./adapt.js\";\nimport hooks from \"./hooks.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Class to represent a color space\n */\nexport default class ColorSpace {\n\tconstructor (options) {\n\t\tthis.id = options.id;\n\t\tthis.name = options.name;\n\t\tthis.base = options.base ? ColorSpace.get(options.base) : null;\n\t\tthis.aliases = options.aliases;\n\n\t\tif (this.base) {\n\t\t\tthis.fromBase = options.fromBase;\n\t\t\tthis.toBase = options.toBase;\n\t\t}\n\n\t\t// Coordinate metadata\n\n\t\tlet coords = options.coords ?? this.base.coords;\n\n\t\tfor (let name in coords) {\n\t\t\tif (!(\"name\" in coords[name])) {\n\t\t\t\tcoords[name].name = name;\n\t\t\t}\n\t\t}\n\t\tthis.coords = coords;\n\n\t\t// White point\n\n\t\tlet white = options.white ?? this.base.white ?? \"D65\";\n\t\tthis.white = getWhite(white);\n\n\t\t// Sort out formats\n\n\t\tthis.formats = options.formats ?? {};\n\n\t\tfor (let name in this.formats) {\n\t\t\tlet format = this.formats[name];\n\t\t\tformat.type ||= \"function\";\n\t\t\tformat.name ||= name;\n\t\t}\n\n\t\tif (!this.formats.color?.id) {\n\t\t\tthis.formats.color = {\n\t\t\t\t...this.formats.color ?? {},\n\t\t\t\tid: options.cssId || this.id,\n\t\t\t};\n\t\t}\n\n\t\t// Gamut space\n\n\t\tif (options.gamutSpace) {\n\t\t\t// Gamut space explicitly specified\n\t\t\tthis.gamutSpace = options.gamutSpace === \"self\" ? this : ColorSpace.get(options.gamutSpace);\n\t\t}\n\t\telse {\n\t\t\t// No gamut space specified, calculate a sensible default\n\t\t\tif (this.isPolar) {\n\t\t\t\t// Do not check gamut through polar coordinates\n\t\t\t\tthis.gamutSpace = this.base;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.gamutSpace =  this;\n\t\t\t}\n\t\t}\n\n\t\t// Optimize inGamut for unbounded spaces\n\t\tif (this.gamutSpace.isUnbounded) {\n\t\t\tthis.inGamut = (coords, options) => {\n\t\t\t\treturn true;\n\t\t\t};\n\t\t}\n\n\t\t// Other stuff\n\t\tthis.referred = options.referred;\n\n\t\t// Compute ancestors and store them, since they will never change\n\t\tObject.defineProperty(this, \"path\", {\n\t\t\tvalue: getPath(this).reverse(),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t});\n\n\t\thooks.run(\"colorspace-init-end\", this);\n\t}\n\n\tinGamut (coords, {epsilon = ε} = {}) {\n\t\tif (!this.equals(this.gamutSpace)) {\n\t\t\tcoords = this.to(this.gamutSpace, coords);\n\t\t\treturn this.gamutSpace.inGamut(coords, {epsilon});\n\t\t}\n\n\t\tlet coordMeta = Object.values(this.coords);\n\n\t\treturn coords.every((c, i) => {\n\t\t\tlet meta = coordMeta[i];\n\n\t\t\tif (meta.type !== \"angle\" && meta.range) {\n\t\t\t\tif (Number.isNaN(c)) {\n\t\t\t\t\t// NaN is always in gamut\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tlet [min, max] = meta.range;\n\t\t\t\treturn (min === undefined || c >= min - epsilon)\n\t\t\t\t    && (max === undefined || c <= max + epsilon);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t});\n\t}\n\n\tget isUnbounded () {\n\t\treturn Object.values(this.coords).every(coord => !(\"range\" in coord));\n\t}\n\n\tget cssId () {\n\t\treturn this.formats?.color?.id || this.id;\n\t}\n\n\tget isPolar () {\n\t\tfor (let id in this.coords) {\n\t\t\tif (this.coords[id].type === \"angle\") {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tgetFormat (format) {\n\t\tif (typeof format === \"object\") {\n\t\t\tformat = processFormat(format, this);\n\t\t\treturn format;\n\t\t}\n\n\t\tlet ret;\n\t\tif (format === \"default\") {\n\t\t\t// Get first format\n\t\t\tret = Object.values(this.formats)[0];\n\t\t}\n\t\telse {\n\t\t\tret = this.formats[format];\n\t\t}\n\n\t\tif (ret) {\n\t\t\tret = processFormat(ret, this);\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Check if this color space is the same as another color space reference.\n\t * Allows proxying color space objects and comparing color spaces with ids.\n\t * @param {string | ColorSpace} space ColorSpace object or id to compare to\n\t * @returns {boolean}\n\t */\n\tequals (space) {\n\t\tif (!space) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this === space || this.id === space || this.id === space.id;\n\t}\n\n\tto (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (this.equals(space)) {\n\t\t\t// Same space, no change needed\n\t\t\treturn coords;\n\t\t}\n\n\t\t// Convert NaN to 0, which seems to be valid in every coordinate of every color space\n\t\tcoords = coords.map(c => Number.isNaN(c) ? 0 : c);\n\n\t\t// Find connection space = lowest common ancestor in the base tree\n\t\tlet myPath = this.path;\n\t\tlet otherPath = space.path;\n\n\t\tlet connectionSpace, connectionSpaceIndex;\n\n\t\tfor (let i = 0; i < myPath.length; i++) {\n\t\t\tif (myPath[i].equals(otherPath[i])) {\n\t\t\t\tconnectionSpace = myPath[i];\n\t\t\t\tconnectionSpaceIndex = i;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (!connectionSpace) {\n\t\t\t// This should never happen\n\t\t\tthrow new Error(`Cannot convert between color spaces ${this} and ${space}: no connection space was found`);\n\t\t}\n\n\t\t// Go up from current space to connection space\n\t\tfor (let i = myPath.length - 1; i > connectionSpaceIndex; i--) {\n\t\t\tcoords = myPath[i].toBase(coords);\n\t\t}\n\n\t\t// Go down from connection space to target space\n\t\tfor (let i = connectionSpaceIndex + 1; i < otherPath.length; i++) {\n\t\t\tcoords = otherPath[i].fromBase(coords);\n\t\t}\n\n\t\treturn coords;\n\t}\n\n\tfrom (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\treturn space.to(this, coords);\n\t}\n\n\ttoString () {\n\t\treturn `${this.name} (${this.id})`;\n\t}\n\n\tgetMinCoords () {\n\t\tlet ret = [];\n\n\t\tfor (let id in this.coords) {\n\t\t\tlet meta = this.coords[id];\n\t\t\tlet range = meta.range || meta.refRange;\n\t\t\tret.push(range?.min ?? 0);\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tstatic registry = {};\n\n\t// Returns array of unique color spaces\n\tstatic get all () {\n\t\treturn [...new Set(Object.values(ColorSpace.registry))];\n\t}\n\n\tstatic register (id, space) {\n\t\tif (arguments.length === 1) {\n\t\t\tspace = arguments[0];\n\t\t\tid = space.id;\n\t\t}\n\n\t\tspace = this.get(space);\n\n\t\tif (this.registry[id] && this.registry[id] !== space) {\n\t\t\tthrow new Error(`Duplicate color space registration: '${id}'`);\n\t\t}\n\t\tthis.registry[id] = space;\n\n\t\t// Register aliases when called without an explicit ID.\n\t\tif (arguments.length === 1 && space.aliases) {\n\t\t\tfor (let alias of space.aliases) {\n\t\t\t\tthis.register(alias, space);\n\t\t\t}\n\t\t}\n\n\t\treturn space;\n\t}\n\n\t/**\n\t * Lookup ColorSpace object by name\n\t * @param {ColorSpace | string} name\n\t */\n\tstatic get (space, ...alternatives) {\n\t\tif (!space || space instanceof ColorSpace) {\n\t\t\treturn space;\n\t\t}\n\n\t\tlet argType = type(space);\n\n\t\tif (argType === \"string\") {\n\t\t\t// It's a color space id\n\t\t\tlet ret = ColorSpace.registry[space.toLowerCase()];\n\n\t\t\tif (!ret) {\n\t\t\t\tthrow new TypeError(`No color space found with id = \"${space}\"`);\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tif (alternatives.length) {\n\t\t\treturn ColorSpace.get(...alternatives);\n\t\t}\n\n\t\tthrow new TypeError(`${space} is not a valid color space`);\n\t}\n\n\t/**\n\t * Get metadata about a coordinate of a color space\n\t *\n\t * @static\n\t * @param {Array | string} ref\n\t * @param {ColorSpace | string} [workingSpace]\n\t * @return {Object}\n\t */\n\tstatic resolveCoord (ref, workingSpace) {\n\t\tlet coordType = type(ref);\n\t\tlet space, coord;\n\n\t\tif (coordType === \"string\") {\n\t\t\tif (ref.includes(\".\")) {\n\t\t\t\t// Absolute coordinate\n\t\t\t\t[space, coord] = ref.split(\".\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Relative coordinate\n\t\t\t\t[space, coord] = [, ref];\n\t\t\t}\n\t\t}\n\t\telse if (Array.isArray(ref)) {\n\t\t\t[space, coord] = ref;\n\t\t}\n\t\telse {\n\t\t\t// Object\n\t\t\tspace = ref.space;\n\t\t\tcoord = ref.coordId;\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (!space) {\n\t\t\tspace = workingSpace;\n\t\t}\n\n\t\tif (!space) {\n\t\t\tthrow new TypeError(`Cannot resolve coordinate reference ${ref}: No color space specified and relative references are not allowed here`);\n\t\t}\n\n\t\tcoordType = type(coord);\n\n\t\tif (coordType === \"number\" || coordType === \"string\" && coord >= 0) {\n\t\t\t// Resolve numerical coord\n\t\t\tlet meta = Object.entries(space.coords)[coord];\n\n\t\t\tif (meta) {\n\t\t\t\treturn {space, id: meta[0], index: coord, ...meta[1]};\n\t\t\t}\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tlet normalizedCoord = coord.toLowerCase();\n\n\t\tlet i = 0;\n\t\tfor (let id in space.coords) {\n\t\t\tlet meta = space.coords[id];\n\n\t\t\tif (id.toLowerCase() === normalizedCoord || meta.name?.toLowerCase() === normalizedCoord) {\n\t\t\t\treturn {space, id, index: i, ...meta};\n\t\t\t}\n\n\t\t\ti++;\n\t\t}\n\n\t\tthrow new TypeError(`No \"${coord}\" coordinate found in ${space.name}. Its coordinates are: ${Object.keys(space.coords).join(\", \")}`);\n\t}\n\n\tstatic DEFAULT_FORMAT = {\n\t\ttype: \"functions\",\n\t\tname: \"color\",\n\t};\n}\n\nfunction getPath (space) {\n\tlet ret = [space];\n\n\tfor (let s = space; s = s.base;) {\n\t\tret.push(s);\n\t}\n\n\treturn ret;\n}\n\nfunction processFormat (format, {coords} = {}) {\n\tif (format.coords && !format.coordGrammar) {\n\t\tformat.type ||= \"function\";\n\t\tformat.name ||= \"color\";\n\n\t\t// Format has not been processed\n\t\tformat.coordGrammar = parseCoordGrammar(format.coords);\n\n\t\tlet coordFormats = Object.entries(coords).map(([id, coordMeta], i) => {\n\t\t\t// Preferred format for each coord is the first one\n\t\t\tlet outputType = format.coordGrammar[i][0];\n\n\t\t\tlet fromRange = coordMeta.range || coordMeta.refRange;\n\t\t\tlet toRange = outputType.range, suffix = \"\";\n\n\t\t\t// Non-strict equals intentional since outputType could be a string object\n\t\t\tif (outputType == \"<percentage>\") {\n\t\t\t\ttoRange = [0, 100];\n\t\t\t\tsuffix = \"%\";\n\t\t\t}\n\t\t\telse if (outputType == \"<angle>\") {\n\t\t\t\tsuffix = \"deg\";\n\t\t\t}\n\n\t\t\treturn  {fromRange, toRange, suffix};\n\t\t});\n\n\t\tformat.serializeCoords = (coords, precision) => {\n\t\t\treturn coords.map((c, i) => {\n\t\t\t\tlet {fromRange, toRange, suffix} = coordFormats[i];\n\n\t\t\t\tif (fromRange && toRange) {\n\t\t\t\t\tc = mapRange(fromRange, toRange, c);\n\t\t\t\t}\n\n\t\t\t\tc = serializeNumber(c, {precision, unit: suffix});\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t};\n\t}\n\n\treturn format;\n}\n", "import ColorSpace from \"../space.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d65\",\n\tname: \"XYZ D65\",\n\tcoords: {\n\t\tx: {name: \"X\"},\n\t\ty: {name: \"Y\"},\n\t\tz: {name: \"Z\"},\n\t},\n\twhite: \"D65\",\n\tformats: {\n\t\tcolor: {\n\t\t\tids: [\"xyz-d65\", \"xyz\"],\n\t\t},\n\t},\n\taliases: [\"xyz\"],\n});\n", "import ColorSpace from \"./space.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport adapt from \"./adapt.js\";\nimport XYZ_D65 from \"./spaces/xyz-d65.js\";\n\n/**\n * Convenience class for RGB color spaces\n * @extends {ColorSpace}\n */\nexport default class RGBColorSpace extends ColorSpace {\n\t/**\n\t * Creates a new RGB ColorSpace.\n\t * If coords are not specified, they will use the default RGB coords.\n\t * Instead of `fromBase()` and `toBase()` functions,\n\t * you can specify to/from XYZ matrices and have `toBase()` and `fromBase()` automatically generated.\n\t * @param {*} options - Same options as {@link ColorSpace} plus:\n\t * @param {number[][]} options.toXYZ_M - Matrix to convert to XYZ\n\t * @param {number[][]} options.fromXYZ_M - Matrix to convert from XYZ\n\t */\n\tconstructor (options) {\n\t\tif (!options.coords) {\n\t\t\toptions.coords = {\n\t\t\t\tr: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"Red\",\n\t\t\t\t},\n\t\t\t\tg: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t\tb: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif (!options.base) {\n\t\t\toptions.base = XYZ_D65;\n\t\t}\n\n\t\tif (options.toXYZ_M && options.fromXYZ_M) {\n\t\t\toptions.toBase ??= rgb => {\n\t\t\t\tlet xyz = multiplyMatrices(options.toXYZ_M, rgb);\n\n\t\t\t\tif (this.white !== this.base.white) {\n\t\t\t\t\t// Perform chromatic adaptation\n\t\t\t\t\txyz = adapt(this.white, this.base.white, xyz);\n\t\t\t\t}\n\n\t\t\t\treturn xyz;\n\t\t\t};\n\n\t\t\toptions.fromBase ??= xyz => {\n\t\t\t\txyz = adapt(this.base.white, this.white, xyz);\n\t\t\t\treturn multiplyMatrices(options.fromXYZ_M, xyz);\n\t\t\t};\n\t\t}\n\n\t\toptions.referred ??= \"display\";\n\n\t\tsuper(options);\n\t}\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\n/**\n * Get the coordinates of a color in any color space\n * @param {Color} color\n * @param {string | ColorSpace} [space = color.space] The color space to convert to. Defaults to the color's current space\n * @returns {number[]} The color coordinates in the given color space\n */\nexport default function getAll (color, space) {\n\tcolor = getColor(color);\n\n\tif (!space || color.space.equals(space)) {\n\t\t// No conversion needed\n\t\treturn color.coords.slice();\n\t}\n\n\tspace = ColorSpace.get(space);\n\treturn space.from(color);\n}\n", "import ColorSpace from \"./space.js\";\nimport getAll from \"./getAll.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function get (color, prop) {\n\tcolor = getColor(color);\n\n\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\tlet coords = getAll(color, space);\n\treturn coords[index];\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function setAll (color, space, coords) {\n\tcolor = getColor(color);\n\n\tspace = ColorSpace.get(space);\n\tcolor.coords = space.to(color.space, coords);\n\treturn color;\n}\n\nsetAll.returns = \"color\";\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\nimport get from \"./get.js\";\nimport getAll from \"./getAll.js\";\nimport setAll from \"./setAll.js\";\nimport {type} from \"./util.js\";\n\n// Set properties and return current instance\nexport default function set (color, prop, value) {\n\tcolor = getColor(color);\n\n\tif (arguments.length === 2 && type(arguments[1]) === \"object\") {\n\t\t// Argument is an object literal\n\t\tlet object = arguments[1];\n\t\tfor (let p in object) {\n\t\t\tset(color, p, object[p]);\n\t\t}\n\t}\n\telse {\n\t\tif (typeof value === \"function\") {\n\t\t\tvalue = value(get(color, prop));\n\t\t}\n\n\t\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\t\tlet coords = getAll(color, space);\n\t\tcoords[index] = value;\n\t\tsetAll(color, space, coords);\n\t}\n\n\treturn color;\n}\n\nset.returns = \"color\";\n", "import ColorSpace from \"../space.js\";\nimport adapt from \"../adapt.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d50\",\n\tname: \"XYZ D50\",\n\twhite: \"D50\",\n\tbase: XYZ_D65,\n\tfromBase: coords => adapt(XYZ_D65.white, \"D50\", coords),\n\ttoBase: coords => adapt(\"D50\", XYZ_D65.white, coords),\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d50 from \"./xyz-d50.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D50;\n\nexport default new ColorSpace({\n\tid: \"lab\",\n\tname: \"Lab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D50, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d50,\n\t// Convert D50-adapted XYX to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D50-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "export function constrain (angle) {\n\treturn ((angle % 360) + 360) % 360;\n}\n\nexport function adjust (arc, angles) {\n\tif (arc === \"raw\") {\n\t\treturn angles;\n\t}\n\n\tlet [a1, a2] = angles.map(constrain);\n\n\tlet angleDiff = a2 - a1;\n\n\tif (arc === \"increasing\") {\n\t\tif (angleDiff < 0) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\telse if (arc === \"decreasing\") {\n\t\tif (angleDiff > 0) {\n\t\t\ta1 += 360;\n\t\t}\n\t}\n\telse if (arc === \"longer\") {\n\t\tif (-180 < angleDiff && angleDiff < 180) {\n\t\t\tif (angleDiff > 0) {\n\t\t\t\ta1 += 360;\n\t\t\t}\n\t\t\telse {\n\t\t\t\ta2 += 360;\n\t\t\t}\n\t\t}\n\t}\n\telse if (arc === \"shorter\") {\n\t\tif (angleDiff > 180) {\n\t\t\ta1 += 360;\n\t\t}\n\t\telse if (angleDiff < -180) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\n\treturn [a1, a2];\n}\n", "import ColorSpace from \"../space.js\";\nimport Lab from \"./lab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lch\",\n\tname: \"<PERSON><PERSON>\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 150],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Lab,\n\tfromBase (Lab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = Lab;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // a\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // b\n\t\t];\n\t},\n\n\tformats: {\n\t\t\"lch\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import defaults from \"../defaults.js\";\nimport lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// deltaE2000 is a statistically significant improvement\n// and is recommended by the CIE and Idealliance\n// especially for color differences less than 10 deltaE76\n// but is wicked complicated\n// and many implementations have small errors!\n// DeltaE2000 is also discontinuous; in case this\n// matters to you, use deltaECMC instead.\n\nconst Gfactor = 25 ** 7;\nconst π = Math.PI;\nconst r2d = 180 / π;\nconst d2r = π / 180;\n\nfunction pow7 (x) {\n\t// Faster than x ** 7 or Math.pow(x, 7)\n\n\tconst x2 = x * x;\n\tconst x7 = x2 * x2 * x2 * x;\n\n\treturn x7;\n}\n\nexport default function (color, sample, {kL = 1, kC = 1, kH = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and the function parameter as the sample,\n\t// calculate deltaE 2000.\n\n\t// This implementation assumes the parametric\n\t// weighting factors kL, kC and kH\n\t// for the influence of viewing conditions\n\t// are all 1, as sadly seems typical.\n\t// kL should be increased for lightness texture or noise\n\t// and kC increased for chroma noise\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet C1 = lch.from(lab, [L1, a1, b1])[1];\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\tlet Cbar = (C1 + C2) / 2; // mean Chroma\n\n\t// calculate a-axis asymmetry factor from mean Chroma\n\t// this turns JND ellipses for near-neutral colors back into circles\n\tlet C7 = pow7(Cbar);\n\n\tlet G = 0.5 * (1 - Math.sqrt(C7 / (C7 + Gfactor)));\n\n\t// scale a axes by asymmetry factor\n\t// this by the way is why there is no Lab2000 colorspace\n\tlet adash1 = (1 + G) * a1;\n\tlet adash2 = (1 + G) * a2;\n\n\t// calculate new Chroma from scaled a and original b axes\n\tlet Cdash1 = Math.sqrt(adash1 ** 2 + b1 ** 2);\n\tlet Cdash2 = Math.sqrt(adash2 ** 2 + b2 ** 2);\n\n\t// calculate new hues, with zero hue for true neutrals\n\t// and in degrees, not radians\n\n\tlet h1 = (adash1 === 0 && b1 === 0) ? 0 : Math.atan2(b1, adash1);\n\tlet h2 = (adash2 === 0 && b2 === 0) ? 0 : Math.atan2(b2, adash2);\n\n\tif (h1 < 0) {\n\t\th1 += 2 * π;\n\t}\n\tif (h2 < 0) {\n\t\th2 += 2 * π;\n\t}\n\n\th1 *= r2d;\n\th2 *= r2d;\n\n\t// Lightness and Chroma differences; sign matters\n\tlet ΔL = L2 - L1;\n\tlet ΔC = Cdash2 - Cdash1;\n\n\t// Hue difference, getting the sign correct\n\tlet hdiff = h2 - h1;\n\tlet hsum = h1 + h2;\n\tlet habs = Math.abs(hdiff);\n\tlet Δh;\n\n\tif (Cdash1 * Cdash2 === 0) {\n\t\tΔh = 0;\n\t}\n\telse if (habs <= 180) {\n\t\tΔh = hdiff;\n\t}\n\telse if (hdiff > 180) {\n\t\tΔh = hdiff - 360;\n\t}\n\telse if (hdiff < -180) {\n\t\tΔh = hdiff + 360;\n\t}\n\telse {\n\t\tdefaults.warn(\"the unthinkable has happened\");\n\t}\n\n\t// weighted Hue difference, more for larger Chroma\n\tlet ΔH = 2 * Math.sqrt(Cdash2 * Cdash1) * Math.sin(Δh * d2r / 2);\n\n\t// calculate mean Lightness and Chroma\n\tlet Ldash = (L1 + L2) / 2;\n\tlet Cdash = (Cdash1 + Cdash2) / 2;\n\tlet Cdash7 = pow7(Cdash);\n\n\t// Compensate for non-linearity in the blue region of Lab.\n\t// Four possibilities for hue weighting factor,\n\t// depending on the angles, to get the correct sign\n\tlet hdash;\n\tif (Cdash1 * Cdash2 === 0) {\n\t\thdash = hsum;   // which should be zero\n\t}\n\telse if (habs <= 180) {\n\t\thdash = hsum / 2;\n\t}\n\telse if (hsum < 360) {\n\t\thdash = (hsum + 360) / 2;\n\t}\n\telse {\n\t\thdash = (hsum - 360) / 2;\n\t}\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor\n\t// a background with L=50 is assumed\n\tlet lsq = (Ldash - 50) ** 2;\n\tlet SL = 1 + ((0.015 * lsq) / Math.sqrt(20 + lsq));\n\n\t// SC Chroma factor, similar to those in CMC and deltaE 94 formulae\n\tlet SC = 1 + 0.045 * Cdash;\n\n\t// Cross term T for blue non-linearity\n\tlet T = 1;\n\tT -= (0.17 * Math.cos((     hdash - 30)  * d2r));\n\tT += (0.24 * Math.cos(  2 * hdash        * d2r));\n\tT += (0.32 * Math.cos(((3 * hdash) + 6)  * d2r));\n\tT -= (0.20 * Math.cos(((4 * hdash) - 63) * d2r));\n\n\t// SH Hue factor depends on Chroma,\n\t// as well as adjusted hue angle like deltaE94.\n\tlet SH = 1 + 0.015 * Cdash * T;\n\n\t// RT Hue rotation term compensates for rotation of JND ellipses\n\t// and Munsell constant hue lines\n\t// in the medium-high Chroma blue region\n\t// (Hue 225 to 315)\n\tlet Δθ = 30 * Math.exp(-1 * (((hdash - 275) / 25) ** 2));\n\tlet RC = 2 * Math.sqrt(Cdash7 / (Cdash7 + Gfactor));\n\tlet RT = -1 * Math.sin(2 * Δθ * d2r) * RC;\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (kL * SL)) ** 2;\n\tdE += (ΔC / (kC * SC)) ** 2;\n\tdE += (ΔH / (kH * SH)) ** 2;\n\tdE += RT * (ΔC / (kC * SC)) * (ΔH / (kH * SH));\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\n// Recalculated for consistent reference white\n// see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484\nconst XY<PERSON>toLMS_M = [\n\t[ 0.8190224379967030, 0.3619062600528904, -0.1288737815209879 ],\n\t[ 0.0329836539323885, 0.9292868615863434,  0.0361446663506424 ],\n\t[ 0.0481771893596242, 0.2642395317527308,  0.6335478284694309 ],\n];\n// inverse of XYZtoLMS_M\nconst LMStoXYZ_M = [\n\t[  1.2268798758459243, -0.5578149944602171,  0.2813910456659647 ],\n\t[ -0.0405757452148008,  1.1122868032803170, -0.0717110580655164 ],\n\t[ -0.0763729366746601, -0.4214933324022432,  1.5869240198367816 ],\n];\nconst LMStoLab_M = [\n\t[ 0.2104542683093140,  0.7936177747023054, -0.0040720430116193 ],\n\t[ 1.9779985324311684, -2.4285922420485799,  0.4505937096174110 ],\n\t[ 0.0259040424655478,  0.7827717124575296, -0.8086757549230774 ],\n];\n// LMStoIab_M inverted\nconst LabtoLMS_M = [\n\t[ 1.0000000000000000,  0.3963377773761749,  0.2158037573099136 ],\n\t[ 1.0000000000000000, -0.1055613458156586, -0.0638541728258133 ],\n\t[ 1.0000000000000000, -0.0894841775298119, -1.2914855480194092 ],\n];\n\nexport default new ColorSpace({\n\tid: \"oklab\",\n\tname: \"Oklab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t},\n\n\t// Note that XYZ is relative to D65\n\twhite: \"D65\",\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\t// non-linearity\n\t\tlet LMSg = LMS.map(val => Math.cbrt(val));\n\n\t\treturn multiplyMatrices(LMStoLab_M, LMSg);\n\n\t},\n\ttoBase (OKLab) {\n\t\t// move to LMS cone domain\n\t\tlet LMSg = multiplyMatrices(LabtoLMS_M, OKLab);\n\n\t\t// restore linearity\n\t\tlet LMS = LMSg.map(val => val ** 3);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n\n\tformats: {\n\t\t\"oklab\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in CIE Lab\n\nimport oklab from \"../spaces/oklab.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaEOK, term by term as root sum of squares\n\tlet [L1, a1, b1] = oklab.from(color);\n\tlet [L2, a2, b2] = oklab.from(sample);\n\tlet ΔL = L1 - L2;\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\treturn Math.sqrt(ΔL ** 2 + Δa ** 2 + Δb ** 2);\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Check if a color is in gamut of either its own or another color space\n * @return {Boolean} Is the color in gamut?\n */\nexport default function inGamut (color, space, {epsilon = ε} = {}) {\n\tcolor = getColor(color);\n\n\tif (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tlet coords = color.coords;\n\n\tif (space !== color.space) {\n\t\tcoords = space.from(color);\n\t}\n\n\treturn space.inGamut(coords, {epsilon});\n}\n", "export default function clone (color) {\n\treturn {\n\t\tspace: color.space,\n\t\tcoords: color.coords.slice(),\n\t\talpha: color.alpha,\n\t};\n}\n", "import ColorSpace from \"./space.js\";\n\n/**\n * Euclidean distance of colors in an arbitrary color space\n */\nexport default function distance (color1, color2, space = \"lab\") {\n\tspace = ColorSpace.get(space);\n\n\t// Assume getColor() is called on color in space.from()\n\tlet coords1 = space.from(color1);\n\tlet coords2 = space.from(color2);\n\n\treturn Math.sqrt(coords1.reduce((acc, c1, i) => {\n\t\tlet c2 = coords2[i];\n\t\tif (isNaN(c1) || isNaN(c2)) {\n\t\t\treturn acc;\n\t\t}\n\n\t\treturn acc + (c2 - c1) ** 2;\n\t}, 0));\n}\n", "import distance from \"../distance.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function deltaE76 (color, sample) {\n\t// Assume getColor() is called in the distance function\n\treturn distance(color, sample, \"lab\");\n}\n", "import lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// CMC by the Color Measurement Committee of the\n// Bradford Society of Dyeists and Colorsts, 1994.\n// Uses LCH rather than Lab,\n// with different weights for L, C and H differences\n// A nice increase in accuracy for modest increase in complexity\nconst π = Math.PI;\nconst d2r = π / 180;\n\nexport default function (color, sample, {l = 2, c = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE CMC.\n\n\t// This implementation assumes the parametric\n\t// weighting factors l:c are 2:1\n\t// which is typical for non-textile uses.\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet [, C1, H1] = lch.from(lab, [L1, a1, b1]);\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// let [L1, a1, b1] = color.getAll(lab);\n\t// let C1 = color.get(\"lch.c\");\n\t// let H1 = color.get(\"lch.h\");\n\t// let [L2, a2, b2] = sample.getAll(lab);\n\t// let C2 = sample.get(\"lch.c\");\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\t// we don't need H2 as ΔH is calculated from Δa, Δb and ΔC\n\n\t// Lightness and Chroma differences\n\t// These are (color - sample), unlike deltaE2000\n\tlet ΔL = L1 - L2;\n\tlet ΔC = C1 - C2;\n\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\n\t// weighted Hue difference, less for larger Chroma difference\n\n\tlet H2 = (Δa ** 2) + (Δb ** 2) - (ΔC ** 2);\n\t// due to roundoff error it is possible that, for zero a and b,\n\t// ΔC > Δa + Δb is 0, resulting in attempting\n\t// to take the square root of a negative number\n\n\t// trying instead the equation from Industrial Color Physics\n\t// By Georg A. Klein\n\n\t// let ΔH = ((a1 * b2) - (a2 * b1)) / Math.sqrt(0.5 * ((C2 * C1) + (a2 * a1) + (b2 * b1)));\n\t// console.log({ΔH});\n\t// This gives the same result to 12 decimal places\n\t// except it sometimes NaNs when trying to root a negative number\n\n\t// let ΔH = Math.sqrt(H2); we never actually use the root, it gets squared again!!\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor, depends entirely on L1 not L2\n\tlet SL = 0.511;\t// linear portion of the Y to L transfer function\n\tif (L1 >= 16) {\t// cubic portion\n\t\tSL = (0.040975 * L1) / (1 + 0.01765 * L1);\n\t}\n\n\t// SC Chroma factor\n\tlet SC = ((0.0638 * C1) / (1 + 0.0131 * C1)) + 0.638;\n\n\t// Cross term T for blue non-linearity\n\tlet T;\n\tif (Number.isNaN(H1)) {\n\t\tH1 = 0;\n\t}\n\n\tif (H1 >= 164 && H1 <= 345) {\n\t\tT = 0.56 + Math.abs(0.2 * Math.cos((H1 + 168) * d2r));\n\t}\n\telse {\n\t\tT = 0.36 + Math.abs(0.4 * Math.cos((H1 + 35) * d2r));\n\t}\n\t// console.log({T});\n\n\t// SH Hue factor also depends on C1,\n\tlet C4 = Math.pow(C1, 4);\n\tlet F = Math.sqrt(C4 / (C4 + 1900));\n\tlet SH = SC * ((F * T) + 1 - F);\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (l * SL)) ** 2;\n\tdE += (ΔC / (c * SC)) ** 2;\n\tdE += (H2 / (SH ** 2));\n\t// dE += (ΔH / SH)  ** 2;\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nconst Yw = 203;\t// absolute luminance of media white\n\nexport default new ColorSpace({\n// Absolute CIE XYZ, with a D65 whitepoint,\n// as used in most HDR colorspaces as a starting point.\n// SDR spaces are converted per BT.2048\n// so that diffuse, media white is 203 cd/m²\n\tid: \"xyz-abs-d65\",\n\tcssId: \"--xyz-abs-d65\",\n\tname: \"Absolute XYZ D65\",\n\tcoords: {\n\t\tx: {\n\t\t\trefRange: [0, 9504.7],\n\t\t\tname: \"Xa\",\n\t\t},\n\t\ty: {\n\t\t\trefRange: [0, 10000],\n\t\t\tname: \"Ya\",\n\t\t},\n\t\tz: {\n\t\t\trefRange: [0, 10888.3],\n\t\t\tname: \"Za\",\n\t\t},\n\t},\n\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// Make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\treturn XYZ.map (v => Math.max(v * Yw, 0));\n\t},\n\ttoBase (AbsXYZ) {\n\t\t// Convert to media-white relative XYZ\n\t\treturn AbsXYZ.map(v => Math.max(v / Yw, 0));\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst b = 1.15;\nconst g = 0.66;\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\nconst p = 1.7 * 2523 / (2 ** 5);\nconst pinv = (2 ** 5) / (1.7 * 2523);\nconst d = -0.56;\nconst d0 = 1.6295499532821566E-11;\n\nconst XYZtoCone_M = [\n\t[  0.41478972, 0.579999,  0.0146480 ],\n\t[ -0.2015100,  1.120649,  0.0531008 ],\n\t[ -0.0166008,  0.264800,  0.6684799 ],\n];\n// XYZtoCone_M inverted\nconst Coneto<PERSON>Y<PERSON>_M = [\n\t[  1.9242264357876067,  -1.0047923125953657,  0.037651404030618   ],\n\t[  0.35031676209499907,  0.7264811939316552, -0.06538442294808501 ],\n\t[ -0.09098281098284752, -0.3127282905230739,  1.5227665613052603  ],\n];\nconst ConetoIab_M = [\n\t[  0.5,       0.5,       0        ],\n\t[  3.524000, -4.066708,  0.542708 ],\n\t[  0.199076,  1.096799, -1.295875 ],\n];\n// ConetoIab_M inverted\nconst IabtoCone_M = [\n\t[ 1,                   0.1386050432715393,   0.05804731615611886 ],\n\t[ 0.9999999999999999, -0.1386050432715393,  -0.05804731615611886 ],\n\t[ 0.9999999999999998, -0.09601924202631895, -0.8118918960560388  ],\n];\n\nexport default new ColorSpace({\n\tid: \"jzazbz\",\n\tname: \"Jzazbz\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Jz\",\n\t\t},\n\t\taz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t\tbz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// First make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\t// BT.2048 says media white Y=203 at PQ 58\n\n\t\tlet [ Xa, Ya, Za ] = XYZ;\n\n\t\t// modify X and Y\n\t\tlet Xm = (b * Xa) - ((b - 1) * Za);\n\t\tlet Ym = (g * Ya) - ((g - 1) * Xa);\n\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoCone_M, [ Xm, Ym, Za ]);\n\n\t\t// PQ-encode LMS\n\t\tlet PQLMS = LMS.map (function (val) {\n\t\t\tlet num = c1 + (c2 * ((val / 10000) ** n));\n\t\t\tlet denom = 1 + (c3 * ((val / 10000) ** n));\n\n\t\t\treturn (num / denom)  ** p;\n\t\t});\n\n\t\t// almost there, calculate Iz az bz\n\t\tlet [ Iz, az, bz] = multiplyMatrices(ConetoIab_M, PQLMS);\n\t\t// console.log({Iz, az, bz});\n\n\t\tlet Jz = ((1 + d) * Iz) / (1 + (d * Iz)) - d0;\n\t\treturn [Jz, az, bz];\n\t},\n\ttoBase (Jzazbz) {\n\t\tlet [Jz, az, bz] = Jzazbz;\n\t\tlet Iz = (Jz + d0) / (1 + d - d * (Jz + d0));\n\n\t\t// bring into LMS cone domain\n\t\tlet PQLMS = multiplyMatrices(IabtoCone_M, [ Iz, az, bz ]);\n\n\t\t// convert from PQ-coded to linear-light\n\t\tlet LMS = PQLMS.map(function (val) {\n\t\t\tlet num = (c1 - (val ** pinv));\n\t\t\tlet denom = (c3 * (val ** pinv)) - c2;\n\t\t\tlet x = 10000 * ((num / denom) ** ninv);\n\n\t\t\treturn (x); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\n\t\t// modified abs XYZ\n\t\tlet [ Xm, Ym, Za ] = multiplyMatrices(ConetoXYZ_M, LMS);\n\n\t\t// restore standard D50 relative XYZ, relative to media white\n\t\tlet Xa = (Xm + ((b - 1) * Za)) / b;\n\t\tlet Ya = (Ym + ((g - 1) * Xa)) / g;\n\t\treturn [ Xa, Ya, Za ];\n\t},\n\n\tformats: {\n\t\t// https://drafts.csswg.org/css-color-hdr/#Jzazbz\n\t\t\"color\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport <PERSON><PERSON><PERSON>b<PERSON> from \"./jzazbz.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"jzczhz\",\n\tname: \"J<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\tcz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\thz: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Jzazbz,\n\tfromBase (jzazbz) {\n\t\t// Convert to polar form\n\t\tlet [Jz, az, bz] = jzazbz;\n\t\tlet hue;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(az) < ε && Math.abs(bz) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(bz, az) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tJz, // Jz is still Jz\n\t\t\tMath.sqrt(az ** 2 + bz ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (jzczhz) {\n\t\t// Convert from polar form\n\t\t// debugger;\n\t\treturn [\n\t\t\tjzczhz[0], // Jz is still Jz\n\t\t\tjzczhz[1] * Math.cos(jzczhz[2] * Math.PI / 180), // az\n\t\t\tjzczhz[1] * Math.sin(jzczhz[2] * Math.PI / 180),  // bz\n\t\t];\n\t},\n});\n", "import jzczhz from \"../spaces/jzczhz.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// Uses JzCzHz, which has improved perceptual uniformity\n// and thus a simple Euclidean root-sum of ΔL² ΔC² ΔH²\n// gives good results.\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in JzCzHz.\n\tlet [Jz1, Cz1, Hz1] = jzczhz.from(color);\n\tlet [Jz2, Cz2, Hz2] = jzczhz.from(sample);\n\n\t// Lightness and Chroma differences\n\t// sign does not matter as they are squared.\n\tlet ΔJ = Jz1 - Jz2;\n\tlet ΔC = Cz1 - Cz2;\n\n\t// length of chord for ΔH\n\tif ((Number.isNaN(Hz1)) && (Number.isNaN(Hz2))) {\n\t\t// both undefined hues\n\t\tHz1 = 0;\n\t\tHz2 = 0;\n\t}\n\telse if (Number.isNaN(Hz1)) {\n\t\t// one undefined, set to the defined hue\n\t\tHz1 = Hz2;\n\t}\n\telse if (Number.isNaN(Hz2)) {\n\t\tHz2 = Hz1;\n\t}\n\n\tlet Δh = Hz1 - Hz2;\n\tlet ΔH = 2 * Math.sqrt(Cz1 * Cz2) * Math.sin((Δh / 2) * (Math.PI / 180));\n\n\treturn Math.sqrt(ΔJ ** 2 + ΔC ** 2 + ΔH ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst c1 = 3424 / 4096;\nconst c2 = 2413 / 128;\nconst c3 = 2392 / 128;\nconst m1 = 2610 / 16384;\nconst m2 = 2523 / 32;\nconst im1 = 16384 / 2610;\nconst im2 = 32 / 2523;\n\n// The matrix below includes the 4% crosstalk components\n// and is from the Dolby \"What is ICtCp\" paper\"\nconst XYZtoLMS_M = [\n\t[  0.3592832590121217,  0.6976051147779502, -0.0358915932320290 ],\n\t[ -0.1920808463704993,  1.1004767970374321,  0.0753748658519118 ],\n\t[  0.0070797844607479,  0.0748396662186362,  0.8433265453898765 ],\n];\n// linear-light Rec.2020 to LMS, again with crosstalk\n// rational terms from <PERSON>,\n// Encoding High Dynamic Range andWide Color Gamut Imagery, p.97\n// and ITU-R BT.2124-0 p.2\n/*\nconst Rec2020toLMS_M = [\n\t[ 1688 / 4096,  2146 / 4096,   262 / 4096 ],\n\t[  683 / 4096,  2951 / 4096,   462 / 4096 ],\n\t[   99 / 4096,   309 / 4096,  3688 / 4096 ]\n];\n*/\n// this includes the Ebner LMS coefficients,\n// the rotation, and the scaling to [-0.5,0.5] range\n// rational terms from Fröhlich p.97\n// and ITU-R BT.2124-0 pp.2-3\nconst LMStoIPT_M = [\n\t[  2048 / 4096,   2048 / 4096,       0      ],\n\t[  6610 / 4096, -13613 / 4096,  7003 / 4096 ],\n\t[ 17933 / 4096, -17390 / 4096,  -543 / 4096 ],\n];\n\n// inverted matrices, calculated from the above\nconst IPTtoLMS_M = [\n\t[ 0.9999999999999998,  0.0086090370379328,  0.1110296250030260 ],\n\t[ 0.9999999999999998, -0.0086090370379328, -0.1110296250030259 ],\n\t[ 0.9999999999999998,  0.5600313357106791, -0.3206271749873188 ],\n];\n/*\nconst LMStoRec2020_M = [\n\t[ 3.4375568932814012112,   -2.5072112125095058195,   0.069654319228104608382],\n\t[-0.79142868665644156125,   1.9838372198740089874,  -0.19240853321756742626 ],\n\t[-0.025646662911506476363, -0.099240248643945566751, 1.1248869115554520431  ]\n];\n*/\nconst LMStoXYZ_M = [\n\t[  2.0701522183894223, -1.3263473389671563,  0.2066510476294053 ],\n\t[  0.3647385209748072,  0.6805660249472273, -0.0453045459220347 ],\n\t[ -0.0497472075358123, -0.0492609666966131,  1.1880659249923042 ],\n];\n\n// Only the PQ form of ICtCp is implemented here. There is also an HLG form.\n// from Dolby, \"WHAT IS ICTCP?\"\n// https://professional.dolby.com/siteassets/pdfs/ictcp_dolbywhitepaper_v071.pdf\n// and\n// Dolby, \"Perceptual Color Volume\n// Measuring the Distinguishable Colors of HDR and WCG Displays\"\n// https://professional.dolby.com/siteassets/pdfs/dolby-vision-measuring-perceptual-color-volume-v7.1.pdf\nexport default new ColorSpace({\n\tid: \"ictcp\",\n\tname: \"ICTCP\",\n\t// From BT.2100-2 page 7:\n\t// During production, signal values are expected to exceed the\n\t// range E′ = [0.0 : 1.0]. This provides processing headroom and avoids\n\t// signal degradation during cascaded processing. Such values of E′,\n\t// below 0.0 or exceeding 1.0, should not be clipped during production\n\t// and exchange.\n\t// Values below 0.0 should not be clipped in reference displays (even\n\t// though they represent “negative” light) to allow the black level of\n\t// the signal (LB) to be properly set using test signals known as “PLUGE”\n\tcoords: {\n\t\ti: {\n\t\t\trefRange: [0, 1],\t// Constant luminance,\n\t\t\tname: \"I\",\n\t\t},\n\t\tct: {\n\t\t\trefRange: [-0.5, 0.5],\t// Full BT.2020 gamut in range [-0.5, 0.5]\n\t\t\tname: \"CT\",\n\t\t},\n\t\tcp: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t\tname: \"CP\",\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\treturn LMStoICtCp(LMS);\n\t},\n\ttoBase (ICtCp) {\n\t\tlet LMS = ICtCptoLMS(ICtCp);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n});\n\nfunction LMStoICtCp (LMS) {\n\t// apply the PQ EOTF\n\t// we can't ever be dividing by zero because of the \"1 +\" in the denominator\n\tlet PQLMS = LMS.map (function (val) {\n\t\tlet num = c1 + (c2 * ((val / 10000) ** m1));\n\t\tlet denom = 1 + (c3 * ((val / 10000) ** m1));\n\n\t\treturn (num / denom)  ** m2;\n\t});\n\n\t// LMS to IPT, with rotation for Y'C'bC'r compatibility\n\treturn multiplyMatrices(LMStoIPT_M, PQLMS);\n}\n\nfunction ICtCptoLMS (ICtCp) {\n\tlet PQLMS = multiplyMatrices(IPTtoLMS_M, ICtCp);\n\n\t// From BT.2124-0 Annex 2 Conversion 3\n\tlet LMS = PQLMS.map (function (val) {\n\t\tlet num  = Math.max((val ** im2) - c1, 0);\n\t\tlet denom = (c2 - (c3 * (val ** im2)));\n\t\treturn 10000 * ((num / denom) ** im1);\n\t});\n\n\treturn LMS;\n}\n", "import ictcp from \"../spaces/ictcp.js\";\nimport getColor from \"../getColor.js\";\n\n// Delta E in ICtCp space,\n// which the ITU calls Delta E ITP, which is shorter\n// formulae from ITU Rec. ITU-R BT.2124-0\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in ICtCp\n\t// which is simply the Euclidean distance\n\n\tlet [ I1, T1, P1 ] = ictcp.from(color);\n\tlet [ I2, T2, P2 ] = ictcp.from(sample);\n\n\t// the 0.25 factor is to undo the encoding scaling in Ct\n\t// the 720 is so that 1 deltaE = 1 JND\n\t// per  ITU-R BT.2124-0 p.3\n\n\treturn 720 * Math.sqrt((I1 - I2) ** 2 + (0.25 * (T1 - T2) ** 2) + (P1 - P2) ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices, interpolate, copySign, spow, zdiv, bisectLeft} from \"../util.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst adaptedCoef = 0.42;\nconst adaptedCoefInv = 1 / adaptedCoef;\nconst tau = 2 * Math.PI;\n\nconst cat16 = [\n\t[  0.401288,  0.650173, -0.051461 ],\n\t[ -0.250268,  1.204414,  0.045854 ],\n\t[ -0.002079,  0.048952,  0.953127 ],\n];\n\nconst cat16Inv = [\n\t[1.8620678550872327, -1.0112546305316843, 0.14918677544445175],\n\t[0.38752654323613717, 0.6214474419314753, -0.008973985167612518],\n\t[-0.015841498849333856, -0.03412293802851557, 1.0499644368778496],\n];\n\nconst m1 = [\n\t[460.0, 451.0, 288.0],\n\t[460.0, -891.0, -261.0],\n\t[460.0, -220.0, -6300.0],\n];\n\nconst surroundMap = {\n\tdark: [0.8, 0.525, 0.8],\n\tdim: [0.9, 0.59, 0.9],\n\taverage: [1, 0.69, 1],\n};\n\nconst hueQuadMap = {\n\t// Red, Yellow, Green, Blue, Red\n\th: [20.14, 90.00, 164.25, 237.53, 380.14],\n\te: [0.8, 0.7, 1.0, 1.2, 0.8],\n\tH: [0.0, 100.0, 200.0, 300.0, 400.0],\n};\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\n\nexport function adapt (coords, fl) {\n\tconst temp = coords.map(c => {\n\t\tconst x = spow(fl * Math.abs(c) * 0.01, adaptedCoef);\n\t\treturn 400 * copySign(x, c) / (x + 27.13);\n\t});\n\treturn temp;\n}\n\nexport function unadapt (adapted, fl) {\n\tconst constant = 100 / fl * (27.13 ** adaptedCoefInv);\n\treturn adapted.map(c => {\n\t\tconst cabs = Math.abs(c);\n\t\treturn copySign(constant * spow(cabs / (400 - cabs), adaptedCoefInv), c);\n\t});\n}\n\nexport function hueQuadrature (h) {\n\tlet hp = constrain(h);\n\tif (hp <= hueQuadMap.h[0]) {\n\t\thp += 360;\n\t}\n\n\tconst i = bisectLeft(hueQuadMap.h, hp) - 1;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\tconst Hi = hueQuadMap.H[i];\n\n\tconst t = (hp - hi) / ei;\n\treturn Hi + (100 * t) / (t + (hii - hp) / eii);\n}\n\nexport function invHueQuadrature (H) {\n\tlet Hp = ((H % 400 + 400) % 400);\n\tconst i = Math.floor(0.01 * Hp);\n\tHp = Hp % 100;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\n\treturn constrain(\n\t\t(Hp * (eii * hi - ei * hii) - 100 * hi * eii) /\n\t\t(Hp * (eii - ei) - 100 * eii),\n\t);\n}\n\nexport function environment (\n\trefWhite,\n\tadaptingLuminance,\n\tbackgroundLuminance,\n\tsurround,\n\tdiscounting,\n) {\n\n\tconst env = {};\n\n\tenv.discounting = discounting;\n\tenv.refWhite = refWhite;\n\tenv.surround = surround;\n\tconst xyzW = refWhite.map(c => {\n\t\treturn c * 100;\n\t});\n\n\t// The average luminance of the environment in `cd/m^2cd/m` (a.k.a. nits)\n\tenv.la = adaptingLuminance;\n\t// The relative luminance of the nearby background\n\tenv.yb = backgroundLuminance;\n\t// Absolute luminance of the reference white.\n\tconst yw = xyzW[1];\n\n\t// Cone response for reference white\n\tconst rgbW = multiplyMatrices(cat16, xyzW);\n\n\t// Surround: dark, dim, and average\n\tsurround = surroundMap[env.surround];\n\tconst f = surround[0];\n\tenv.c = surround[1];\n\tenv.nc = surround[2];\n\n\tconst k = 1 / (5 * env.la + 1);\n\tconst k4 = k ** 4;\n\n\t// Factor of luminance level adaptation\n\tenv.fl = (k4 * env.la + 0.1 * (1 - k4) * (1 - k4) * Math.cbrt(5 * env.la));\n\tenv.flRoot = env.fl ** 0.25;\n\n\tenv.n = env.yb / yw;\n\tenv.z = 1.48 + Math.sqrt(env.n);\n\tenv.nbb = 0.725 * (env.n ** -0.2);\n\tenv.ncb = env.nbb;\n\n\t// Degree of adaptation calculating if not discounting\n\t// illuminant (assumed eye is fully adapted)\n\tconst d = (discounting) ?\n\t\t1 :\n\t\tMath.max(\n\t\t\tMath.min(f * (1 - 1 / 3.6 * Math.exp((-env.la - 42) / 92)), 1),\n\t\t\t0,\n\t\t);\n\tenv.dRgb = rgbW.map(c => {\n\t\treturn interpolate(1, yw / c, d);\n\t});\n\tenv.dRgbInv = env.dRgb.map(c => {\n\t\treturn 1 / c;\n\t});\n\n\t// Achromatic response\n\tconst rgbCW = rgbW.map((c, i) => {\n\t\treturn c * env.dRgb[i];\n\t});\n\tconst rgbAW = adapt(rgbCW, env.fl);\n\tenv.aW = env.nbb * (2 * rgbAW[0] + rgbAW[1] + 0.05 * rgbAW[2]);\n\n\t// console.log(env);\n\n\treturn env;\n}\n\n// Pre-calculate everything we can with the viewing conditions\nconst viewingConditions = environment(\n\twhite,\n\t64 / Math.PI * 0.2, 20,\n\t\"average\",\n\tfalse,\n);\n\nexport function fromCam16 (cam16, env) {\n\n\t// These check ensure one, and only one attribute for a\n\t// given category is provided.\n\tif (!((cam16.J !== undefined) ^ (cam16.Q !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'J' or 'Q'\");\n\t}\n\n\tif (!((cam16.C !== undefined) ^ (cam16.M !== undefined) ^ (cam16.s !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'C', 'M' or 's'\");\n\t}\n\n\t// Hue is absolutely required\n\tif (!((cam16.h !== undefined) ^ (cam16.H !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'h' or 'H'\");\n\t}\n\n\t// Black\n\tif (cam16.J === 0.0 || cam16.Q === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Break hue into Cartesian components\n\tlet hRad = 0.0;\n\tif (cam16.h !== undefined) {\n\t\thRad = constrain(cam16.h) * deg2rad;\n\t}\n\telse {\n\t\thRad = invHueQuadrature(cam16.H) * deg2rad;\n\t}\n\n\tconst cosh = Math.cos(hRad);\n\tconst sinh = Math.sin(hRad);\n\n\t// Calculate `Jroot` from one of the lightness derived coordinates.\n\tlet Jroot = 0.0;\n\tif (cam16.J !== undefined) {\n\t\tJroot = spow(cam16.J, 1 / 2) * 0.1;\n\t}\n\telse if (cam16.Q !== undefined) {\n\t\tJroot = 0.25 * env.c * cam16.Q / ((env.aW + 4) * env.flRoot);\n\t}\n\n\t// Calculate the `t` value from one of the chroma derived coordinates\n\tlet alpha = 0.0;\n\tif (cam16.C !== undefined) {\n\t\talpha = cam16.C / Jroot;\n\t}\n\telse if (cam16.M !== undefined) {\n\t\talpha = (cam16.M / env.flRoot) / Jroot;\n\t}\n\telse if (cam16.s !== undefined) {\n\t\talpha = 0.0004 * (cam16.s ** 2) * (env.aW + 4) / env.c;\n\t}\n\tconst t = spow(\n\t\talpha * Math.pow(1.64 - Math.pow(0.29, env.n), -0.73),\n\t\t10 / 9,\n\t);\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\t// Achromatic response\n\tconst A = env.aW * spow(Jroot, 2 / env.c / env.z);\n\n\t// Calculate red-green and yellow-blue components\n\tconst p1 = 5e4 / 13 * env.nc * env.ncb * et;\n\tconst p2 = A / env.nbb;\n\tconst r = (\n\t\t23 * (p2 + 0.305) *\n\t\tzdiv(t, 23 * p1 + t * (11 * cosh + 108 * sinh))\n\t);\n\tconst a = r * cosh;\n\tconst b = r * sinh;\n\n\t// Calculate back from cone response to XYZ\n\tconst rgb_c = unadapt(\n\t\tmultiplyMatrices(m1, [p2, a, b]).map(c => {\n\t\t\treturn c * 1 / 1403;\n\t\t}),\n\t\tenv.fl,\n\t);\n\treturn multiplyMatrices(\n\t\tcat16Inv,\n\t\trgb_c.map((c, i) => {\n\t\t\treturn c * env.dRgbInv[i];\n\t\t}),\n\t).map(c => {\n\t\treturn c / 100;\n\t});\n}\n\n\nexport function toCam16 (xyzd65, env) {\n\t// Cone response\n\tconst xyz100 = xyzd65.map(c => {\n\t\treturn c * 100;\n\t});\n\tconst rgbA = adapt(\n\t\tmultiplyMatrices(cat16, xyz100).map((c, i) => {\n\t\t\treturn c * env.dRgb[i];\n\t\t}),\n\t\tenv.fl,\n\t);\n\n\t// Calculate hue from red-green and yellow-blue components\n\tconst a = rgbA[0] + (-12 * rgbA[1] + rgbA[2]) / 11;\n\tconst b = (rgbA[0] + rgbA[1] - 2 * rgbA[2]) / 9;\n\tconst hRad = ((Math.atan2(b, a) % tau) + tau) % tau;\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\tconst t = (\n\t\t5e4 / 13 * env.nc * env.ncb *\n\t\tzdiv(\n\t\t\tet * Math.sqrt(a ** 2 + b ** 2),\n\t\t\trgbA[0] + rgbA[1] + 1.05 * rgbA[2] + 0.305,\n\t\t)\n\t);\n\tconst alpha = spow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, env.n), 0.73);\n\n\t// Achromatic response\n\tconst A = env.nbb * (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]);\n\n\tconst Jroot = spow(A / env.aW, 0.5 * env.c * env.z);\n\n\t// Lightness\n\tconst J = 100 * spow(Jroot, 2);\n\n\t// Brightness\n\tconst Q = (4 / env.c * Jroot * (env.aW + 4) * env.flRoot);\n\n\t// Chroma\n\tconst C = alpha * Jroot;\n\n\t// Colorfulness\n\tconst M = C * env.flRoot;\n\n\t// Hue\n\tconst h = constrain(hRad * rad2deg);\n\n\t// Hue quadrature\n\tconst H = hueQuadrature(h);\n\n\t// Saturation\n\tconst s = 50 * spow(env.c * alpha / (env.aW + 4), 1 / 2);\n\n\t// console.log({J: J, C: C, h: h, s: s, Q: Q, M: M, H: H});\n\n\treturn {J: J, C: C, h: h, s: s, Q: Q, M: M, H: H};\n}\n\n\n// Provided as a way to directly evaluate the CAM16 model\n// https://observablehq.com/@jrus/cam16: reference implementation\n// https://arxiv.org/pdf/1802.06067.pdf: Nico Schlömer\n// https://onlinelibrary.wiley.com/doi/pdf/10.1002/col.22324: hue quadrature\n// https://www.researchgate.net/publication/318152296_Comprehensive_color_solutions_CAM16_CAT16_and_CAM16-UCS\n// Results compared against: https://github.com/colour-science/colour\nexport default new ColorSpace({\n\tid: \"cam16-jmh\",\n\tcssId: \"--cam16-jmh\",\n\tname: \"CAM16-JMh\",\n\tcoords: {\n\t\tj: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"J\",\n\t\t},\n\t\tm: {\n\t\t\trefRange: [0, 105.0],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\tconst cam16 = toCam16(xyz, viewingConditions);\n\t\treturn [cam16.J, cam16.M, cam16.h];\n\t},\n\ttoBase (cam16) {\n\t\treturn fromCam16(\n\t\t\t{J: cam16[0], M: cam16[1], h: cam16[2]},\n\t\t\tviewingConditions,\n\t\t);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {fromCam16, toCam16, environment} from \"./cam16.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nfunction toLstar (y) {\n\t// Convert XYZ Y to L*\n\n\tconst fy = (y > ε) ? Math.cbrt(y) : (κ * y + 16) / 116;\n\treturn (116.0 * fy) - 16.0;\n}\n\nfunction fromLstar (lstar) {\n\t// Convert L* back to XYZ Y\n\n\treturn (lstar > 8) ?  Math.pow((lstar + 16) / 116, 3) : lstar / κ;\n}\n\nfunction fromHct (coords, env) {\n\t// Use Newton's method to try and converge as quick as possible or\n\t// converge as close as we can. While the requested precision is achieved\n\t// most of the time, it may not always be achievable. Especially past the\n\t// visible spectrum, the algorithm will likely struggle to get the same\n\t// precision. If, for whatever reason, we cannot achieve the accuracy we\n\t// seek in the allotted iterations, just return the closest we were able to\n\t// get.\n\n\tlet [h, c, t] = coords;\n\tlet xyz = [];\n\tlet j = 0;\n\n\t// Shortcut out for black\n\tif (t === 0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Calculate the Y we need to target\n\tlet y = fromLstar(t);\n\n\t// A better initial guess yields better results. Polynomials come from\n\t// curve fitting the T vs J response.\n\tif (t > 0) {\n\t\tj = 0.00379058511492914 * t ** 2 + 0.608983189401032 * t + 0.9155088574762233;\n\t}\n\telse {\n\t\tj = 9.514440756550361e-06 * t ** 2 + 0.08693057439788597 * t - 21.928975842194614;\n\t}\n\n\t// Threshold of how close is close enough, and max number of attempts.\n\t// More precision and more attempts means more time spent iterating. Higher\n\t// required precision gives more accuracy but also increases the chance of\n\t// not hitting the goal. 2e-12 allows us to convert round trip with\n\t// reasonable accuracy of six decimal places or more.\n\tconst threshold = 2e-12;\n\tconst max_attempts = 15;\n\n\tlet attempt = 0;\n\tlet last = Infinity;\n\tlet best = j;\n\n\t// Try to find a J such that the returned y matches the returned y of the L*\n\twhile (attempt <= max_attempts) {\n\t\txyz = fromCam16({J: j, C: c, h: h}, env);\n\n\t\t// If we are within range, return XYZ\n\t\t// If we are closer than last time, save the values\n\t\tconst delta = Math.abs(xyz[1] - y);\n\t\tif (delta < last) {\n\t\t\tif (delta <= threshold) {\n\t\t\t\treturn xyz;\n\t\t\t}\n\t\t\tbest = j;\n\t\t\tlast = delta;\n\t\t}\n\n\t\t// f(j_root) = (j ** (1 / 2)) * 0.1\n\t\t// f(j) = ((f(j_root) * 100) ** 2) / j - 1 = 0\n\t\t// f(j_root) = Y = y / 100\n\t\t// f(j) = (y ** 2) / j - 1\n\t\t// f'(j) = (2 * y) / j\n\t\tj = j - (xyz[1] - y) * j / (2 * xyz[1]);\n\n\t\tattempt += 1;\n\t}\n\n\t// We could not acquire the precision we desired,\n\t// return our closest attempt.\n\treturn fromCam16({J: j, C: c, h: h}, env);\n}\n\nfunction toHct (xyz, env) {\n\t// Calculate HCT by taking the L* of CIE LCh D65 and CAM16 chroma and hue.\n\n\tconst t = toLstar(xyz[1]);\n\tif (t === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\tconst cam16 = toCam16(xyz, viewingConditions);\n\treturn [constrain(cam16.h), cam16.C, t];\n}\n\n// Pre-calculate everything we can with the viewing conditions\nexport const viewingConditions = environment(\n\twhite, 200 / Math.PI * fromLstar(50.0),\n\tfromLstar(50.0) * 100,\n\t\"average\",\n\tfalse,\n);\n\n// https://material.io/blog/science-of-color-design\n// This is not a port of the material-color-utilities,\n// but instead implements the full color space as described,\n// combining CAM16 JCh and Lab D65. This does not clamp conversion\n// to HCT to specific chroma bands and provides support for wider\n// gamuts than Google currently supports and does so at a greater\n// precision (> 8 bits back to sRGB).\n// This implementation comes from https://github.com/facelessuser/coloraide\n// which is licensed under MIT.\nexport default new ColorSpace({\n\tid: \"hct\",\n\tname: \"HCT\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 145],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\tt: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Tone\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\treturn toHct(xyz, viewingConditions);\n\t},\n\ttoBase (hct) {\n\t\treturn fromHct(hct, viewingConditions);\n\t},\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hct\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import hct from \"../spaces/hct.js\";\nimport {viewingConditions} from \"../spaces/hct.js\";\nimport getColor from \"../getColor.js\";\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\nconst ucsCoeff = [1.00, 0.007, 0.0228];\n\n/**\n* Convert HCT chroma and hue (CAM16 JMh colorfulness and hue) using UCS logic for a and b.\n* @param {number[]} coords - HCT coordinates.\n* @return {number[]}\n*/\nfunction convertUcsAb (coords) {\n\t// We want the distance between the actual color.\n\t// If chroma is negative, it will throw off our calculations.\n\t// Normally, converting back to the base and forward will correct it.\n\t// If we have a negative chroma after this, then we have a color that\n\t// cannot resolve to positive chroma.\n\tif (coords[1] < 0) {\n\t\tcoords = hct.fromBase(hct.toBase(coords));\n\t}\n\n\t// Only in extreme cases (usually outside the visible spectrum)\n\t// can the input value for log become negative.\n\t// Avoid domain error by forcing a zero result via \"max\" if necessary.\n\tconst M = Math.log(Math.max(1 + ucsCoeff[2] * coords[1] * viewingConditions.flRoot, 1.0)) / ucsCoeff[2];\n\tconst hrad = coords[0] * deg2rad;\n\tconst a = M * Math.cos(hrad);\n\tconst b = M * Math.sin(hrad);\n\n\treturn [coords[2], a, b];\n}\n\n\n/**\n* Color distance using HCT.\n* @param {Color} color - Color to compare.\n* @param {Color} sample - Color to compare.\n* @return {number[]}\n*/\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\tlet [ t1, a1, b1 ] = convertUcsAb(hct.from(color));\n\tlet [ t2, a2, b2 ] = convertUcsAb(hct.from(sample));\n\n\t// Use simple euclidean distance with a and b using UCS conversion\n\t// and LCh lightness (HCT tone).\n\treturn Math.sqrt((t1 - t2) ** 2 + (a1 - a2) ** 2 + (b1 - b2) ** 2);\n}\n", "import deltaE76 from \"./deltaE76.js\";\nimport deltaECMC from \"./deltaECMC.js\";\nimport deltaE2000 from \"./deltaE2000.js\";\nimport deltaEJz from \"./deltaEJz.js\";\nimport deltaEITP from \"./deltaEITP.js\";\nimport deltaE<PERSON> from \"./deltaEOK.js\";\nimport deltaEHCT from \"./deltaEHCT.js\";\n\nexport {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n\nexport default {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport deltaE2000 from \"./deltaE/deltaE2000.js\";\nimport deltaEOK from \"./deltaE/deltaEOK.js\";\nimport inGamut from \"./inGamut.js\";\nimport to from \"./to.js\";\nimport get from \"./get.js\";\nimport oklab from \"./spaces/oklab.js\";\nimport set from \"./set.js\";\nimport clone from \"./clone.js\";\nimport getColor from \"./getColor.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\nimport {WHITES} from \"./adapt.js\";\n\n/**\n * Calculate the epsilon to 2 degrees smaller than the specified JND.\n * @param {Number} jnd - The target \"just noticeable difference\".\n * @returns {Number}\n */\nfunction calcEpsilon (jnd) {\n\t// Calculate the epsilon to 2 degrees smaller than the specified JND.\n\n\tconst order = (!jnd) ? 0 : Math.floor(Math.log10(Math.abs(jnd)));\n\t// Limit to an arbitrary value to ensure value is never too small and causes infinite loops.\n\treturn Math.max(parseFloat(`1e${order - 2}`), 1e-6);\n}\n\nconst GMAPPRESET = {\n\t\"hct\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 2,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: {},\n\t},\n\t\"hct-tonal\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 0,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: { channel: \"hct.t\", min: 0, max: 100 },\n\t},\n};\n\n/**\n * Force coordinates to be in gamut of a certain color space.\n * Mutates the color it is passed.\n * @param {Object|string} options object or spaceId string\n * @param {string} options.method - How to force into gamut.\n *        If \"clip\", coordinates are just clipped to their reference range.\n *        If \"css\", coordinates are reduced according to the CSS 4 Gamut Mapping Algorithm.\n *        If in the form [colorSpaceId].[coordName], that coordinate is reduced\n *        until the color is in gamut. Please note that this may produce nonsensical\n *        results for certain coordinates (e.g. hue) or infinite loops if reducing the coordinate never brings the color in gamut.\n * @param {ColorSpace|string} options.space - The space whose gamut we want to map to\n * @param {string} options.deltaEMethod - The delta E method to use while performing gamut mapping.\n *        If no method is specified, delta E 2000 is used.\n * @param {Number} options.jnd - The \"just noticeable difference\" to target.\n * @param {Object} options.blackWhiteClamp - Used to configure SDR black and clamping.\n *        \"channel\" indicates the \"space.channel\" to use for determining when to clamp.\n *        \"min\" indicates the lower limit for black clamping and \"max\" indicates the upper\n *        limit for white clamping.\n */\n\nexport default function toGamut (\n\tcolor,\n\t{\n\t\tmethod = defaults.gamut_mapping,\n\t\tspace = undefined,\n\t\tdeltaEMethod = \"\",\n\t\tjnd = 2,\n\t\tblackWhiteClamp = {},\n\t} = {},\n) {\n\tcolor = getColor(color);\n\n\tif (util.isString(arguments[1])) {\n\t\tspace = arguments[1];\n\t}\n\telse if (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\n\t// 3 spaces:\n\t// color.space: current color space\n\t// space: space whose gamut we are mapping to\n\t// mapSpace: space with the coord we're reducing\n\n\tif (inGamut(color, space, { epsilon: 0 })) {\n\t\treturn color;\n\t}\n\n\tlet spaceColor;\n\tif (method === \"css\") {\n\t\tspaceColor = toGamutCSS(color, { space });\n\t}\n\telse {\n\t\tif (method !== \"clip\" && !inGamut(color, space)) {\n\n\t\t\tif (Object.prototype.hasOwnProperty.call(GMAPPRESET, method)) {\n\t\t\t\t({method, jnd, deltaEMethod, blackWhiteClamp} = GMAPPRESET[method]);\n\t\t\t}\n\n\t\t\t// Get the correct delta E method\n\t\t\tlet de = deltaE2000;\n\t\t\tif (deltaEMethod !== \"\") {\n\t\t\t\tfor (let m in deltaEMethods) {\n\t\t\t\t\tif (\"deltae\" + deltaEMethod.toLowerCase() === m.toLowerCase()) {\n\t\t\t\t\t\tde = deltaEMethods[m];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet clipped = toGamut(to(color, space), { method: \"clip\", space });\n\t\t\tif (de(color, clipped) > jnd) {\n\n\t\t\t\t// Clamp to SDR white and black if required\n\t\t\t\tif (Object.keys(blackWhiteClamp).length === 3) {\n\t\t\t\t\tlet channelMeta = ColorSpace.resolveCoord(blackWhiteClamp.channel);\n\t\t\t\t\tlet channel = get(to(color, channelMeta.space), channelMeta.id);\n\t\t\t\t\tif (util.isNone(channel)) {\n\t\t\t\t\t\tchannel = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (channel >= blackWhiteClamp.max) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: WHITES[\"D65\"] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t\telse if (channel <= blackWhiteClamp.min) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: [0, 0, 0] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Reduce a coordinate of a certain color space until the color is in gamut\n\t\t\t\tlet coordMeta = ColorSpace.resolveCoord(method);\n\t\t\t\tlet mapSpace = coordMeta.space;\n\t\t\t\tlet coordId = coordMeta.id;\n\n\t\t\t\tlet mappedColor = to(color, mapSpace);\n\t\t\t\t// If we were already in the mapped color space, we need to resolve undefined channels\n\t\t\t\tmappedColor.coords.forEach((c, i) => {\n\t\t\t\t\tif (util.isNone(c)) {\n\t\t\t\t\t\tmappedColor.coords[i] = 0;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tlet bounds = coordMeta.range || coordMeta.refRange;\n\t\t\t\tlet min = bounds[0];\n\t\t\t\tlet ε = calcEpsilon(jnd);\n\t\t\t\tlet low = min;\n\t\t\t\tlet high = get(mappedColor, coordId);\n\n\t\t\t\twhile (high - low > ε) {\n\t\t\t\t\tlet clipped = clone(mappedColor);\n\t\t\t\t\tclipped = toGamut(clipped, { space, method: \"clip\" });\n\t\t\t\t\tlet deltaE = de(mappedColor, clipped);\n\n\t\t\t\t\tif (deltaE - jnd < ε) {\n\t\t\t\t\t\tlow = get(mappedColor, coordId);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thigh = get(mappedColor, coordId);\n\t\t\t\t\t}\n\n\t\t\t\t\tset(mappedColor, coordId, (low + high) / 2);\n\t\t\t\t}\n\n\t\t\t\tspaceColor = to(mappedColor, space);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tspaceColor = clipped;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tspaceColor = to(color, space);\n\t\t}\n\n\t\tif (method === \"clip\" // Dumb coord clipping\n\t\t\t// finish off smarter gamut mapping with clip to get rid of ε, see #17\n\t\t\t|| !inGamut(spaceColor, space, { epsilon: 0 })\n\t\t) {\n\t\t\tlet bounds = Object.values(space.coords).map(c => c.range || []);\n\n\t\t\tspaceColor.coords = spaceColor.coords.map((c, i) => {\n\t\t\t\tlet [min, max] = bounds[i];\n\n\t\t\t\tif (min !== undefined) {\n\t\t\t\t\tc = Math.max(min, c);\n\t\t\t\t}\n\n\t\t\t\tif (max !== undefined) {\n\t\t\t\t\tc = Math.min(c, max);\n\t\t\t\t}\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t}\n\t}\n\n\tif (space !== color.space) {\n\t\tspaceColor = to(spaceColor, color.space);\n\t}\n\n\tcolor.coords = spaceColor.coords;\n\treturn color;\n}\n\ntoGamut.returns = \"color\";\n\n// The reference colors to be used if lightness is out of the range 0-1 in the\n// `Oklch` space. These are created in the `Oklab` space, as it is used by the\n// DeltaEOK calculation, so it is guaranteed to be imported.\nconst COLORS = {\n\tWHITE: { space: oklab, coords: [1, 0, 0] },\n\tBLACK: { space: oklab, coords: [0, 0, 0] },\n};\n\n/**\n * Given a color `origin`, returns a new color that is in gamut using\n * the CSS Gamut Mapping Algorithm. If `space` is specified, it will be in gamut\n * in `space`, and returned in `space`. Otherwise, it will be in gamut and\n * returned in the color space of `origin`.\n * @param {Object} origin\n * @param {Object} options\n * @param {ColorSpace|string} options.space\n * @returns {Color}\n */\nexport function toGamutCSS (origin, {space} = {}) {\n\tconst JND = 0.02;\n\tconst ε = 0.0001;\n\n\torigin = getColor(origin);\n\n\tif (!space) {\n\t\tspace = origin.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tconst oklchSpace = ColorSpace.get(\"oklch\");\n\n\tif (space.isUnbounded) {\n\t\treturn to(origin, space);\n\t}\n\n\tconst origin_OKLCH = to(origin, oklchSpace);\n\tlet L = origin_OKLCH.coords[0];\n\n\t// return media white or black, if lightness is out of range\n\tif (L >= 1) {\n\t\tconst white = to(COLORS.WHITE, space);\n\t\twhite.alpha = origin.alpha;\n\t\treturn to(white, space);\n\t}\n\tif (L <= 0) {\n\t\tconst black = to(COLORS.BLACK, space);\n\t\tblack.alpha = origin.alpha;\n\t\treturn to(black, space);\n\t}\n\n\tif (inGamut(origin_OKLCH, space, {epsilon: 0})) {\n\t\treturn to(origin_OKLCH, space);\n\t}\n\n\tfunction clip (_color) {\n\t\tconst destColor = to(_color, space);\n\t\tconst spaceCoords = Object.values(space.coords);\n\t\tdestColor.coords = destColor.coords.map((coord, index) => {\n\t\t\tif (\"range\" in spaceCoords[index]) {\n\t\t\t\tconst [min, max] =  spaceCoords[index].range;\n\t\t\t\treturn util.clamp(min, coord, max);\n\t\t\t}\n\t\t\treturn coord;\n\t\t});\n\t\treturn destColor;\n\t}\n\tlet min = 0;\n\tlet max = origin_OKLCH.coords[1];\n\tlet min_inGamut = true;\n\tlet current = clone(origin_OKLCH);\n\tlet clipped = clip(current);\n\n\tlet E = deltaEOK(clipped, current);\n\tif (E < JND) {\n\t\treturn clipped;\n\t}\n\n\twhile ((max - min) > ε) {\n\t\tconst chroma = (min + max) / 2;\n\t\tcurrent.coords[1] = chroma;\n\t\tif (min_inGamut && inGamut(current, space, {epsilon: 0})) {\n\t\t\tmin = chroma;\n\t\t}\n\t\telse {\n\t\t\tclipped = clip(current);\n\t\t\tE = deltaEOK(clipped, current);\n\t\t\tif (E < JND) {\n\t\t\t\tif ((JND - E < ε)) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tmin_inGamut = false;\n\t\t\t\t\tmin = chroma;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tmax = chroma;\n\t\t\t}\n\t\t}\n\t}\n\treturn clipped;\n}\n", "import getColor from \"./getColor.js\";\nimport ColorSpace from \"./space.js\";\nimport toGamut from \"./toGamut.js\";\n\n/**\n * Convert to color space and return a new color\n * @param {Object|string} space - Color space object or id\n * @param {Object} options\n * @param {boolean} options.inGamut - Whether to force resulting color in gamut\n * @returns {Color}\n */\nexport default function to (color, space, {inGamut} = {}) {\n\tcolor = getColor(color);\n\tspace = ColorSpace.get(space);\n\n\tlet coords = space.from(color);\n\tlet ret = {space, coords, alpha: color.alpha};\n\n\tif (inGamut) {\n\t\tret = toGamut(ret, inGamut === true ? undefined : inGamut);\n\t}\n\n\treturn ret;\n}\n\nto.returns = \"color\";\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport getColor from \"./getColor.js\";\nimport checkInGamut from \"./inGamut.js\";\nimport toGamut from \"./toGamut.js\";\nimport clone from \"./clone.js\";\n\n/**\n * Generic toString() method, outputs a color(spaceId ...coords) function, a functional syntax, or custom formats defined by the color space\n * @param {Object} options\n * @param {number} options.precision - Significant digits\n * @param {boolean} options.inGamut - Adjust coordinates to fit in gamut first? [default: false]\n */\nexport default function serialize (color, {\n\tprecision = defaults.precision,\n\tformat = \"default\",\n\tinGamut = true,\n\t...customOptions\n} = {}) {\n\tlet ret;\n\n\tcolor = getColor(color);\n\n\tlet formatId = format;\n\tformat = color.space.getFormat(format)\n\t       ?? color.space.getFormat(\"default\")\n\t       ?? ColorSpace.DEFAULT_FORMAT;\n\n\t// The assignment to coords and inGamut needs to stay in the order they are now\n\t// The order of the assignment was changed as a workaround for a bug in Next.js\n\t// See this issue for details: https://github.com/color-js/color.js/issues/260\n\n\tlet coords = color.coords.slice(); // clone so we can manipulate it\n\n\tinGamut ||= format.toGamut;\n\n\tif (inGamut && !checkInGamut(color)) {\n\t\t// FIXME what happens if the color contains NaNs?\n\t\tcoords = toGamut(clone(color), inGamut === true ? undefined : inGamut).coords;\n\t}\n\n\tif (format.type === \"custom\") {\n\t\tcustomOptions.precision = precision;\n\n\t\tif (format.serialize) {\n\t\t\tret = format.serialize(coords, color.alpha, customOptions);\n\t\t}\n\t\telse {\n\t\t\tthrow new TypeError(`format ${formatId} can only be used to parse colors, not for serialization`);\n\t\t}\n\t}\n\telse {\n\t\t// Functional syntax\n\t\tlet name = format.name || \"color\";\n\n\t\tif (format.serializeCoords) {\n\t\t\tcoords = format.serializeCoords(coords, precision);\n\t\t}\n\t\telse {\n\t\t\tif (precision !== null) {\n\t\t\t\tcoords = coords.map(c => {\n\t\t\t\t\treturn util.serializeNumber(c, {precision});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tlet args = [...coords];\n\n\t\tif (name === \"color\") {\n\t\t\t// If output is a color() function, add colorspace id as first argument\n\t\t\tlet cssId = format.id || format.ids?.[0] || color.space.id;\n\t\t\targs.unshift(cssId);\n\t\t}\n\n\t\tlet alpha = color.alpha;\n\t\tif (precision !== null) {\n\t\t\talpha = util.serializeNumber(alpha, {precision});\n\t\t}\n\n\t\tlet strAlpha = color.alpha >= 1 || format.noAlpha ? \"\" : `${format.commas ? \",\" : \" /\"} ${alpha}`;\n\t\tret = `${name}(${args.join(format.commas ? \", \" : \" \")}${strAlpha})`;\n\t}\n\n\treturn ret;\n}\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light rec2020 values to CIE XYZ\n// using  D65 (no chromatic adaptation)\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// 0 is actually calculated as  4.994106574466076e-17\nconst toXYZ_M = [\n\t[ 0.6369580483012914, 0.14461690358620832,  0.1688809751641721  ],\n\t[ 0.2627002120112671, 0.6779980715188708,   0.05930171646986196 ],\n\t[ 0.000000000000000,  0.028072693049087428, 1.060985057710791   ],\n];\n\n// from ITU-R BT.2124-0 Annex 2 p.3\nconst fromXYZ_M = [\n\t[  1.716651187971268,  -0.355670783776392, -0.253366281373660  ],\n\t[ -0.666684351832489,   1.616481236634939,  0.0157685458139111 ],\n\t[  0.017639857445311,  -0.042770613257809,  0.942103121235474  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"rec2020-linear\",\n\tcssId: \"--rec2020-linear\",\n\tname: \"Linear REC.2020\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n// import sRGB from \"./srgb.js\";\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\n\nexport default new RGBColorSpace({\n\tid: \"rec2020\",\n\tname: \"REC.2020\",\n\tbase: REC2020Linear,\n\t// Non-linear transfer function from Rec. ITU-R BT.2020-2 table 4\n\ttoBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val < β * 4.5) {\n\t\t\t\treturn val / 4.5;\n\t\t\t}\n\n\t\t\treturn Math.pow((val + α - 1) / α, 1 / 0.45);\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val >= β) {\n\t\t\t\treturn α * Math.pow(val, 0.45) - (α - 1);\n\t\t\t}\n\n\t\t\treturn 4.5 * val;\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\nconst toXYZ_M = [\n\t[0.4865709486482162, 0.26566769316909306, 0.1982172852343625],\n\t[0.2289745640697488, 0.6917385218365064,  0.079286914093745],\n\t[0.0000000000000000, 0.04511338185890264, 1.043944368900976],\n];\n\nconst fromXYZ_M = [\n\t[ 2.493496911941425,   -0.9313836179191239, -0.40271078445071684],\n\t[-0.8294889695615747,   1.7626640603183463,  0.023624685841943577],\n\t[ 0.03584583024378447, -0.07617238926804182, 0.9568845240076872],\n];\n\nexport default new RGBColorSpace({\n\tid: \"p3-linear\",\n\tcssId: \"--display-p3-linear\",\n\tname: \"Linear P3\",\n\twhite: \"D65\",\n\tto<PERSON><PERSON><PERSON>_<PERSON>,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// This is the linear-light version of sRGB\n// as used for example in SVG filters\n// or in Canvas\n\n// This matrix was calculated directly from the RGB and white chromaticities\n// when rounded to 8 decimal places, it agrees completely with the official matrix\n// see https://github.com/w3c/csswg-drafts/issues/5922\nconst toXYZ_M = [\n\t[ 0.41239079926595934, 0.357584339383878,   0.1804807884018343  ],\n\t[ 0.21263900587151027, 0.715168678767756,   0.07219231536073371 ],\n\t[ 0.01933081871559182, 0.11919477979462598, 0.9505321522496607  ],\n];\n\n// This matrix is the inverse of the above;\n// again it agrees with the official definition when rounded to 8 decimal places\nexport const fromXYZ_M = [\n\t[  3.2409699419045226,  -1.537383177570094,   -0.4986107602930034  ],\n\t[ -0.9692436362808796,   1.8759675015077202,   0.04155505740717559 ],\n\t[  0.05563007969699366, -0.20397695888897652,  1.0569715142428786  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"srgb-linear\",\n\tname: \"Linear sRGB\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "/* List of CSS color keywords\n * Note that this does not include currentColor, transparent,\n * or system colors\n */\n\n// To produce: Visit https://www.w3.org/TR/css-color-4/#named-colors\n// and run in the console:\n// copy($$(\"tr\", $(\".named-color-table tbody\")).map(tr => `\"${tr.cells[2].textContent.trim()}\": [${tr.cells[4].textContent.trim().split(/\\s+/).map(c => c === \"0\"? \"0\" : c === \"255\"? \"1\" : c + \" / 255\").join(\", \")}]`).join(\",\\n\"))\nexport default {\n\t\"aliceblue\": [240 / 255, 248 / 255, 1],\n\t\"antiquewhite\": [250 / 255, 235 / 255, 215 / 255],\n\t\"aqua\": [0, 1, 1],\n\t\"aquamarine\": [127 / 255, 1, 212 / 255],\n\t\"azure\": [240 / 255, 1, 1],\n\t\"beige\": [245 / 255, 245 / 255, 220 / 255],\n\t\"bisque\": [1, 228 / 255, 196 / 255],\n\t\"black\": [0, 0, 0],\n\t\"blanchedalmond\": [1, 235 / 255, 205 / 255],\n\t\"blue\": [0, 0, 1],\n\t\"blueviolet\": [138 / 255, 43 / 255, 226 / 255],\n\t\"brown\": [165 / 255, 42 / 255, 42 / 255],\n\t\"burlywood\": [222 / 255, 184 / 255, 135 / 255],\n\t\"cadetblue\": [95 / 255, 158 / 255, 160 / 255],\n\t\"chartreuse\": [127 / 255, 1, 0],\n\t\"chocolate\": [210 / 255, 105 / 255, 30 / 255],\n\t\"coral\": [1, 127 / 255, 80 / 255],\n\t\"cornflowerblue\": [100 / 255, 149 / 255, 237 / 255],\n\t\"cornsilk\": [1, 248 / 255, 220 / 255],\n\t\"crimson\": [220 / 255, 20 / 255, 60 / 255],\n\t\"cyan\": [0, 1, 1],\n\t\"darkblue\": [0, 0, 139 / 255],\n\t\"darkcyan\": [0, 139 / 255, 139 / 255],\n\t\"darkgoldenrod\": [184 / 255, 134 / 255, 11 / 255],\n\t\"darkgray\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkgreen\": [0, 100 / 255, 0],\n\t\"darkgrey\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkkhaki\": [189 / 255, 183 / 255, 107 / 255],\n\t\"darkmagenta\": [139 / 255, 0, 139 / 255],\n\t\"darkolivegreen\": [85 / 255, 107 / 255, 47 / 255],\n\t\"darkorange\": [1, 140 / 255, 0],\n\t\"darkorchid\": [153 / 255, 50 / 255, 204 / 255],\n\t\"darkred\": [139 / 255, 0, 0],\n\t\"darksalmon\": [233 / 255, 150 / 255, 122 / 255],\n\t\"darkseagreen\": [143 / 255, 188 / 255, 143 / 255],\n\t\"darkslateblue\": [72 / 255, 61 / 255, 139 / 255],\n\t\"darkslategray\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkslategrey\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkturquoise\": [0, 206 / 255, 209 / 255],\n\t\"darkviolet\": [148 / 255, 0, 211 / 255],\n\t\"deeppink\": [1, 20 / 255, 147 / 255],\n\t\"deepskyblue\": [0, 191 / 255, 1],\n\t\"dimgray\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dimgrey\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dodgerblue\": [30 / 255, 144 / 255, 1],\n\t\"firebrick\": [178 / 255, 34 / 255, 34 / 255],\n\t\"floralwhite\": [1, 250 / 255, 240 / 255],\n\t\"forestgreen\": [34 / 255, 139 / 255, 34 / 255],\n\t\"fuchsia\": [1, 0, 1],\n\t\"gainsboro\": [220 / 255, 220 / 255, 220 / 255],\n\t\"ghostwhite\": [248 / 255, 248 / 255, 1],\n\t\"gold\": [1, 215 / 255, 0],\n\t\"goldenrod\": [218 / 255, 165 / 255, 32 / 255],\n\t\"gray\": [128 / 255, 128 / 255, 128 / 255],\n\t\"green\": [0, 128 / 255, 0],\n\t\"greenyellow\": [173 / 255, 1, 47 / 255],\n\t\"grey\": [128 / 255, 128 / 255, 128 / 255],\n\t\"honeydew\": [240 / 255, 1, 240 / 255],\n\t\"hotpink\": [1, 105 / 255, 180 / 255],\n\t\"indianred\": [205 / 255, 92 / 255, 92 / 255],\n\t\"indigo\": [75 / 255, 0, 130 / 255],\n\t\"ivory\": [1, 1, 240 / 255],\n\t\"khaki\": [240 / 255, 230 / 255, 140 / 255],\n\t\"lavender\": [230 / 255, 230 / 255, 250 / 255],\n\t\"lavenderblush\": [1, 240 / 255, 245 / 255],\n\t\"lawngreen\": [124 / 255, 252 / 255, 0],\n\t\"lemonchiffon\": [1, 250 / 255, 205 / 255],\n\t\"lightblue\": [173 / 255, 216 / 255, 230 / 255],\n\t\"lightcoral\": [240 / 255, 128 / 255, 128 / 255],\n\t\"lightcyan\": [224 / 255, 1, 1],\n\t\"lightgoldenrodyellow\": [250 / 255, 250 / 255, 210 / 255],\n\t\"lightgray\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightgreen\": [144 / 255, 238 / 255, 144 / 255],\n\t\"lightgrey\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightpink\": [1, 182 / 255, 193 / 255],\n\t\"lightsalmon\": [1, 160 / 255, 122 / 255],\n\t\"lightseagreen\": [32 / 255, 178 / 255, 170 / 255],\n\t\"lightskyblue\": [135 / 255, 206 / 255, 250 / 255],\n\t\"lightslategray\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightslategrey\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightsteelblue\": [176 / 255, 196 / 255, 222 / 255],\n\t\"lightyellow\": [1, 1, 224 / 255],\n\t\"lime\": [0, 1, 0],\n\t\"limegreen\": [50 / 255, 205 / 255, 50 / 255],\n\t\"linen\": [250 / 255, 240 / 255, 230 / 255],\n\t\"magenta\": [1, 0, 1],\n\t\"maroon\": [128 / 255, 0, 0],\n\t\"mediumaquamarine\": [102 / 255, 205 / 255, 170 / 255],\n\t\"mediumblue\": [0, 0, 205 / 255],\n\t\"mediumorchid\": [186 / 255, 85 / 255, 211 / 255],\n\t\"mediumpurple\": [147 / 255, 112 / 255, 219 / 255],\n\t\"mediumseagreen\": [60 / 255, 179 / 255, 113 / 255],\n\t\"mediumslateblue\": [123 / 255, 104 / 255, 238 / 255],\n\t\"mediumspringgreen\": [0, 250 / 255, 154 / 255],\n\t\"mediumturquoise\": [72 / 255, 209 / 255, 204 / 255],\n\t\"mediumvioletred\": [199 / 255, 21 / 255, 133 / 255],\n\t\"midnightblue\": [25 / 255, 25 / 255, 112 / 255],\n\t\"mintcream\": [245 / 255, 1, 250 / 255],\n\t\"mistyrose\": [1, 228 / 255, 225 / 255],\n\t\"moccasin\": [1, 228 / 255, 181 / 255],\n\t\"navajowhite\": [1, 222 / 255, 173 / 255],\n\t\"navy\": [0, 0, 128 / 255],\n\t\"oldlace\": [253 / 255, 245 / 255, 230 / 255],\n\t\"olive\": [128 / 255, 128 / 255, 0],\n\t\"olivedrab\": [107 / 255, 142 / 255, 35 / 255],\n\t\"orange\": [1, 165 / 255, 0],\n\t\"orangered\": [1, 69 / 255, 0],\n\t\"orchid\": [218 / 255, 112 / 255, 214 / 255],\n\t\"palegoldenrod\": [238 / 255, 232 / 255, 170 / 255],\n\t\"palegreen\": [152 / 255, 251 / 255, 152 / 255],\n\t\"paleturquoise\": [175 / 255, 238 / 255, 238 / 255],\n\t\"palevioletred\": [219 / 255, 112 / 255, 147 / 255],\n\t\"papayawhip\": [1, 239 / 255, 213 / 255],\n\t\"peachpuff\": [1, 218 / 255, 185 / 255],\n\t\"peru\": [205 / 255, 133 / 255, 63 / 255],\n\t\"pink\": [1, 192 / 255, 203 / 255],\n\t\"plum\": [221 / 255, 160 / 255, 221 / 255],\n\t\"powderblue\": [176 / 255, 224 / 255, 230 / 255],\n\t\"purple\": [128 / 255, 0, 128 / 255],\n\t\"rebeccapurple\": [102 / 255, 51 / 255, 153 / 255],\n\t\"red\": [1, 0, 0],\n\t\"rosybrown\": [188 / 255, 143 / 255, 143 / 255],\n\t\"royalblue\": [65 / 255, 105 / 255, 225 / 255],\n\t\"saddlebrown\": [139 / 255, 69 / 255, 19 / 255],\n\t\"salmon\": [250 / 255, 128 / 255, 114 / 255],\n\t\"sandybrown\": [244 / 255, 164 / 255, 96 / 255],\n\t\"seagreen\": [46 / 255, 139 / 255, 87 / 255],\n\t\"seashell\": [1, 245 / 255, 238 / 255],\n\t\"sienna\": [160 / 255, 82 / 255, 45 / 255],\n\t\"silver\": [192 / 255, 192 / 255, 192 / 255],\n\t\"skyblue\": [135 / 255, 206 / 255, 235 / 255],\n\t\"slateblue\": [106 / 255, 90 / 255, 205 / 255],\n\t\"slategray\": [112 / 255, 128 / 255, 144 / 255],\n\t\"slategrey\": [112 / 255, 128 / 255, 144 / 255],\n\t\"snow\": [1, 250 / 255, 250 / 255],\n\t\"springgreen\": [0, 1, 127 / 255],\n\t\"steelblue\": [70 / 255, 130 / 255, 180 / 255],\n\t\"tan\": [210 / 255, 180 / 255, 140 / 255],\n\t\"teal\": [0, 128 / 255, 128 / 255],\n\t\"thistle\": [216 / 255, 191 / 255, 216 / 255],\n\t\"tomato\": [1, 99 / 255, 71 / 255],\n\t\"turquoise\": [64 / 255, 224 / 255, 208 / 255],\n\t\"violet\": [238 / 255, 130 / 255, 238 / 255],\n\t\"wheat\": [245 / 255, 222 / 255, 179 / 255],\n\t\"white\": [1, 1, 1],\n\t\"whitesmoke\": [245 / 255, 245 / 255, 245 / 255],\n\t\"yellow\": [1, 1, 0],\n\t\"yellowgreen\": [154 / 255, 205 / 255, 50 / 255],\n};\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport sRGBLinear from \"./srgb-linear.js\";\nimport KEYWORDS from \"../keywords.js\";\n\nlet coordGrammar = Array(3).fill(\"<percentage> | <number>[0, 255]\");\nlet coordGrammarNumber = Array(3).fill(\"<number>[0, 255]\");\n\nexport default new RGBColorSpace({\n\tid: \"srgb\",\n\tname: \"sRGB\",\n\tbase: sRGBLinear,\n\tfromBase: rgb => {\n\t\t// convert an array of linear-light sRGB values in the range 0.0-1.0\n\t\t// to gamma corrected form\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs > 0.0031308) {\n\t\t\t\treturn sign * (1.055 * (abs ** (1 / 2.4)) - 0.055);\n\t\t\t}\n\n\t\t\treturn 12.92 * val;\n\t\t});\n\t},\n\ttoBase: rgb => {\n\t\t// convert an array of sRGB values in the range 0.0 - 1.0\n\t\t// to linear light (un-companded) form.\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs <= 0.04045) {\n\t\t\t\treturn val / 12.92;\n\t\t\t}\n\n\t\t\treturn sign * (((abs + 0.055) / 1.055) ** 2.4);\n\t\t});\n\t},\n\tformats: {\n\t\t\"rgb\": {\n\t\t\tcoords: coordGrammar,\n\t\t},\n\t\t\"rgb_number\": {\n\t\t\tname: \"rgb\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t\tnoAlpha: true,\n\t\t},\n\t\t\"color\": { /* use defaults */ },\n\t\t\"rgba\": {\n\t\t\tcoords: coordGrammar,\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t\t\"rgba_number\": {\n\t\t\tname: \"rgba\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t},\n\t\t\"hex\": {\n\t\t\ttype: \"custom\",\n\t\t\ttoGamut: true,\n\t\t\ttest: str => /^#([a-f0-9]{3,4}){1,2}$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tif (str.length <= 5) {\n\t\t\t\t\t// #rgb or #rgba, duplicate digits\n\t\t\t\t\tstr = str.replace(/[a-f0-9]/gi, \"$&$&\");\n\t\t\t\t}\n\n\t\t\t\tlet rgba = [];\n\t\t\t\tstr.replace(/[a-f0-9]{2}/gi, component => {\n\t\t\t\t\trgba.push(parseInt(component, 16) / 255);\n\t\t\t\t});\n\n\t\t\t\treturn {\n\t\t\t\t\tspaceId: \"srgb\",\n\t\t\t\t\tcoords: rgba.slice(0, 3),\n\t\t\t\t\talpha: rgba.slice(3)[0],\n\t\t\t\t};\n\t\t\t},\n\t\t\tserialize: (coords, alpha, {\n\t\t\t\tcollapse = true, // collapse to 3-4 digit hex when possible?\n\t\t\t} = {}) => {\n\t\t\t\tif (alpha < 1) {\n\t\t\t\t\tcoords.push(alpha);\n\t\t\t\t}\n\n\t\t\t\tcoords = coords.map(c => Math.round(c * 255));\n\n\t\t\t\tlet collapsible = collapse && coords.every(c => c % 17 === 0);\n\n\t\t\t\tlet hex = coords.map(c => {\n\t\t\t\t\tif (collapsible) {\n\t\t\t\t\t\treturn (c / 17).toString(16);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn c.toString(16).padStart(2, \"0\");\n\t\t\t\t}).join(\"\");\n\n\t\t\t\treturn \"#\" + hex;\n\t\t\t},\n\t\t},\n\t\t\"keyword\": {\n\t\t\ttype: \"custom\",\n\t\t\ttest: str => /^[a-z]+$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tstr = str.toLowerCase();\n\t\t\t\tlet ret = {spaceId: \"srgb\", coords: null, alpha: 1};\n\n\t\t\t\tif (str === \"transparent\") {\n\t\t\t\t\tret.coords = KEYWORDS.black;\n\t\t\t\t\tret.alpha = 0;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tret.coords = KEYWORDS[str];\n\t\t\t\t}\n\n\t\t\t\tif (ret.coords) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport P3Linear from \"./p3-linear.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new RGBColorSpace({\n\tid: \"p3\",\n\tcssId: \"display-p3\",\n\tname: \"P3\",\n\tbase: P3Linear,\n\t// Gamma encoding/decoding is the same as sRGB\n\tfromBase: sRGB.fromBase,\n\ttoBase: sRGB.toBase,\n});\n", "import { isNone, skipNone } from \"./util.js\";\nimport defaults from \"./defaults.js\";\nimport to from \"./to.js\";\nimport serialize from \"./serialize.js\";\nimport clone from \"./clone.js\";\nimport REC2020 from \"./spaces/rec2020.js\";\nimport P3 from \"./spaces/p3.js\";\nimport Lab from \"./spaces/lab.js\";\nimport sRGB from \"./spaces/srgb.js\";\n\n// Default space for CSS output. Code in Color.js makes this wider if there's a DOM available\ndefaults.display_space = sRGB;\n\nlet supportsNone;\n\nif (typeof CSS !== \"undefined\" && CSS.supports) {\n\t// Find widest supported color space for CSS\n\tfor (let space of [Lab, REC2020, P3]) {\n\t\tlet coords = space.getMinCoords();\n\t\tlet color = {space, coords, alpha: 1};\n\t\tlet str = serialize(color);\n\n\t\tif (CSS.supports(\"color\", str)) {\n\t\t\tdefaults.display_space = space;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\n/**\n * Returns a serialization of the color that can actually be displayed in the browser.\n * If the default serialization can be displayed, it is returned.\n * Otherwise, the color is converted to Lab, REC2020, or P3, whichever is the widest supported.\n * In Node.js, this is basically equivalent to `serialize()` but returns a `String` object instead.\n *\n * @export\n * @param {{space, coords} | Color | string} color\n * @param {*} [options={}] Options to be passed to serialize()\n * @param {ColorSpace | string} [options.space = defaults.display_space] Color space to use for serialization if default is not supported\n * @returns {String} String object containing the serialized color with a color property containing the converted color (or the original, if no conversion was necessary)\n */\nexport default function display (color, {space = defaults.display_space, ...options} = {}) {\n\tlet ret = serialize(color, options);\n\n\tif (typeof CSS === \"undefined\" || CSS.supports(\"color\", ret) || !defaults.display_space) {\n\t\tret = new String(ret);\n\t\tret.color = color;\n\t}\n\telse {\n\t\t// If we're here, what we were about to output is not supported\n\t\tlet fallbackColor = color;\n\n\t\t// First, check if the culprit is none values\n\t\tlet hasNone = color.coords.some(isNone) || isNone(color.alpha);\n\n\t\tif (hasNone) {\n\t\t\t// Does the browser support none values?\n\t\t\tif (!(supportsNone ??= CSS.supports(\"color\", \"hsl(none 50% 50%)\"))) {\n\t\t\t\t// Nope, try again without none\n\t\t\t\tfallbackColor = clone(color);\n\t\t\t\tfallbackColor.coords = fallbackColor.coords.map(skipNone);\n\t\t\t\tfallbackColor.alpha = skipNone(fallbackColor.alpha);\n\n\t\t\t\tret = serialize(fallbackColor, options);\n\n\t\t\t\tif (CSS.supports(\"color\", ret)) {\n\t\t\t\t\t// We're done, now it's supported\n\t\t\t\t\tret = new String(ret);\n\t\t\t\t\tret.color = fallbackColor;\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// If we're here, the color function is not supported\n\t\t// Fall back to fallback space\n\t\tfallbackColor = to(fallbackColor, space);\n\t\tret = new String(serialize(fallbackColor, options));\n\t\tret.color = fallbackColor;\n\t}\n\n\treturn ret;\n}\n", "import getColor from \"./getColor.js\";\n\nexport default function equals (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\treturn color1.space === color2.space\n\t       && color1.alpha === color2.alpha\n\t       && color1.coords.every((c, i) => c === color2.coords[i]);\n}\n", "/**\n * Relative luminance\n */\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport xyz_d65 from \"./spaces/xyz-d65.js\";\n\nexport function getLuminance (color) {\n\t// Assume getColor() is called on color in get()\n\treturn get(color, [xyz_d65, \"y\"]);\n}\n\nexport function setLuminance (color, value) {\n\t// Assume getColor() is called on color in set()\n\tset(color, [xyz_d65, \"y\"], value);\n}\n\nexport function register (Color) {\n\tObject.defineProperty(Color.prototype, \"luminance\", {\n\t\tget () {\n\t\t\treturn getLuminance(this);\n\t\t},\n\t\tset (value) {\n\t\t\tsetLuminance(this, value);\n\t\t},\n\t});\n}\n", "// WCAG 2.0 contrast https://www.w3.org/TR/WCAG20-TECHS/G18.html\n// Simple contrast, with fixed 5% viewing flare contribution\n// Symmetric, does not matter which is foreground and which is background\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrastWCAG21 (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn (Y1 + .05) / (Y2 + .05);\n}\n", "// APCA 0.0.98G\n// https://github.com/Myndex/apca-w3\n// see also https://github.com/w3c/silver/issues/643\n\nimport getColor from \"../getColor.js\";\nimport to from \"../to.js\";\n\n// exponents\nconst normBG = 0.56;\nconst normTXT = 0.57;\nconst revTXT = 0.62;\nconst revBG = 0.65;\n\n// clamps\nconst blkThrs = 0.022;\nconst blkClmp = 1.414;\nconst loClip = 0.1;\nconst deltaYmin = 0.0005;\n\n// scalers\n// see https://github.com/w3c/silver/issues/645\nconst scaleBoW = 1.14;\nconst loBoWoffset = 0.027;\nconst scaleWoB = 1.14;\nconst loWoBoffset = 0.027;\n\nfunction fclamp (Y) {\n\tif (Y >= blkThrs) {\n\t\treturn Y;\n\t}\n\treturn Y + (blkThrs - Y) ** blkClmp;\n}\n\nfunction linearize (val) {\n\tlet sign = val < 0 ? -1 : 1;\n\tlet abs = Math.abs(val);\n\treturn sign * Math.pow(abs, 2.4);\n}\n\n// Not symmetric, requires a foreground (text) color, and a background color\nexport default function contrastAPCA (background, foreground) {\n\tforeground = getColor(foreground);\n\tbackground = getColor(background);\n\n\tlet S;\n\tlet C;\n\tlet Sapc;\n\n\t// Myndex as-published, assumes sRGB inputs\n\tlet R, G, B;\n\n\tforeground = to(foreground, \"srgb\");\n\t// Should these be clamped to in-gamut values?\n\n\t// Calculates \"screen luminance\" with non-standard simple gamma EOTF\n\t// weights should be from CSS Color 4, not the ones here which are via Myndex and copied from Lindbloom\n\t[R, G, B] = foreground.coords;\n\tlet lumTxt = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\tbackground = to(background, \"srgb\");\n\t[R, G, B] = background.coords;\n\tlet lumBg = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\t// toe clamping of very dark values to account for flare\n\tlet Ytxt = fclamp(lumTxt);\n\tlet Ybg = fclamp(lumBg);\n\n\t// are we \"Black on White\" (dark on light), or light on dark?\n\tlet BoW = Ybg > Ytxt;\n\n\t// why is this a delta, when Y is not perceptually uniform?\n\t// Answer: it is a noise gate, see\n\t// https://github.com/LeaVerou/color.js/issues/208\n\tif (Math.abs(Ybg - Ytxt) < deltaYmin) {\n\t\tC = 0;\n\t}\n\telse {\n\t\tif (BoW) {\n\t\t\t// dark text on light background\n\t\t\tS = Ybg ** normBG - Ytxt ** normTXT;\n\t\t\tC = S * scaleBoW;\n\t\t}\n\t\telse {\n\t\t\t// light text on dark background\n\t\t\tS = Ybg ** revBG - Ytxt ** revTXT;\n\t\t\tC = S * scaleWoB;\n\t\t}\n\t}\n\tif (Math.abs(C) < loClip) {\n\t\tSapc = 0;\n\t}\n\telse if (C > 0) {\n\t\t// not clear whether Woffset is loBoWoffset or loWoBoffset\n\t\t// but they have the same value\n\t\tSapc = C - loBoWoffset;\n\t}\n\telse {\n\t\tSapc = C + loBoWoffset;\n\t}\n\n\treturn Sapc * 100;\n}\n", "// Michelson  luminance contrast\n// the relation between the spread and the sum of the two luminances\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrast<PERSON><PERSON><PERSON> (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\tlet denom = (Y1 + Y2);\n\treturn denom === 0 ? 0 : (Y1 - Y2) / denom;\n}\n", "// Weber luminance contrast\n// The difference between the two luminances divided by the lower luminance\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\n// the darkest sRGB color above black is #000001 and this produces\n// a plain Weber contrast of ~45647.\n// So, setting the divide-by-zero result at 50000 is a reasonable\n// max clamp for the plain Weber\nconst max = 50000;\n\nexport default function contrastWeber (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn Y2 === 0 ? max : (Y1 - Y2) / Y2;\n}\n", "// CIE Lightness difference, as used by Google Material Design\n// Google HCT Tone is the same as CIE Lightness\n// https://material.io/blog/science-of-color-design\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab from \"../spaces/lab.js\";\n\nexport default function contrastLstar (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet L1 = get(color1, [lab, \"l\"]);\n\tlet L2 = get(color2, [lab, \"l\"]);\n\n\treturn Math.abs(L1 - L2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D65;\n\nexport default new ColorSpace({\n\tid: \"lab-d65\",\n\tname: \"Lab D65\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D65, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d65,\n\t// Convert D65-adapted XYZ to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D65-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab-d65\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// Delta Phi Star perceptual lightness contrast\n// See https://github.com/Myndex/deltaphistar\n// The (difference between two Lstars each raised to phi) raised to (1/phi)\n// Symmetric, does not matter which is foreground and which is background\n\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab_d65 from \"../spaces/lab-d65.js\";\n\nconst phi = Math.pow(5, 0.5) * 0.5 + 0.5; // Math.phi can be used if Math.js\n\nexport default function contrastDeltaPhi (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Lstr1 = get(color1, [lab_d65, \"l\"]);\n\tlet Lstr2 = get(color2, [lab_d65, \"l\"]);\n\n\tlet deltaPhiStar = Math.abs(Math.pow(Lstr1, phi) - Math.pow(Lstr2, phi));\n\n\tlet contrast = Math.pow(deltaPhiStar, (1 / phi)) * Math.SQRT2 - 40;\n\n\treturn (contrast < 7.5) ? 0.0 : contrast ;\n}\n", "import getColor from \"./getColor.js\";\n// import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport * as contrastAlgorithms from \"./contrast/index.js\";\n\nexport default function contrast (background, foreground, o = {}) {\n\tif (isString(o)) {\n\t\to = {algorithm: o};\n\t}\n\n\tlet {algorithm, ...rest} = o;\n\n\tif (!algorithm) {\n\t\tlet algorithms = Object.keys(contrastAlgorithms).map(a => a.replace(/^contrast/, \"\")).join(\", \");\n\t\tthrow new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${algorithms}`);\n\t}\n\n\tbackground = getColor(background);\n\tforeground = getColor(foreground);\n\n\tfor (let a in contrastAlgorithms) {\n\t\tif (\"contrast\" + algorithm.toLowerCase() === a.toLowerCase()) {\n\t\t\treturn contrastAlgorithms[a](background, foreground, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown contrast algorithm: ${algorithm}`);\n}\n", "import xyz_d65 from \"./spaces/xyz-d65.js\";\nimport getAll from \"./getAll.js\";\n\n// Chromaticity coordinates\nexport function uv (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet denom = X + 15 * Y + 3 * Z;\n\treturn [4 * X / denom, 9 * Y / denom];\n}\n\nexport function xy (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet  sum = X + Y + Z;\n\treturn [X / sum, Y / sum];\n}\n\nexport function register (Color) {\n\t// no setters, as lightness information is lost\n\t// when converting color to chromaticity\n\tObject.defineProperty(Color.prototype, \"uv\", {\n\t\tget () {\n\t\t\treturn uv(this);\n\t\t},\n\t});\n\n\tObject.defineProperty(Color.prototype, \"xy\", {\n\t\tget () {\n\t\t\treturn xy(this);\n\t\t},\n\t});\n}\n", "import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nexport default function deltaE (c1, c2, o = {}) {\n\tif (isString(o)) {\n\t\to = {method: o};\n\t}\n\n\tlet {method = defaults.deltaE, ...rest} = o;\n\n\tfor (let m in deltaEMethods) {\n\t\tif (\"deltae\" + method.toLowerCase() === m.toLowerCase()) {\n\t\t\treturn deltaEMethods[m](c1, c2, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown deltaE method: ${method}`);\n}\n", "import ColorSpace from \"./space.js\";\nimport set from \"./set.js\";\n\nexport function lighten (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 + amount));\n}\n\nexport function darken (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 - amount));\n}\n", "/**\n * Functions related to color interpolation\n */\nimport ColorSpace from \"./space.js\";\nimport {type, interpolate} from \"./util.js\";\nimport getColor from \"./getColor.js\";\nimport clone from \"./clone.js\";\nimport to from \"./to.js\";\nimport toGamut from \"./toGamut.js\";\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport defaults from \"./defaults.js\";\nimport * as angles from \"./angles.js\";\nimport deltaE from \"./deltaE.js\";\n\n/**\n * Return an intermediate color between two colors\n * Signatures: mix(c1, c2, p, options)\n *             mix(c1, c2, options)\n *             mix(color)\n * @param {Color | string} c1 The first color\n * @param {Color | string} [c2] The second color\n * @param {number} [p=.5] A 0-1 percentage where 0 is c1 and 1 is c2\n * @param {Object} [o={}]\n * @return {Color}\n */\nexport function mix (c1, c2, p = .5, o = {}) {\n\t[c1, c2] = [getColor(c1), getColor(c2)];\n\n\tif (type(p) === \"object\") {\n\t\t[p, o] = [.5, p];\n\t}\n\n\tlet r = range(c1, c2, o);\n\treturn r(p);\n}\n\n/**\n *\n * @param {Color | string | Function} c1 The first color or a range\n * @param {Color | string} [c2] The second color if c1 is not a range\n * @param {Object} [options={}]\n * @return {Color[]}\n */\nexport function steps (c1, c2, options = {}) {\n\tlet colorRange;\n\n\tif (isRange(c1)) {\n\t\t// Tweaking existing range\n\t\t[colorRange, options] = [c1, c2];\n\t\t[c1, c2] = colorRange.rangeArgs.colors;\n\t}\n\n\tlet {\n\t\tmaxDeltaE, deltaEMethod,\n\t\tsteps = 2, maxSteps = 1000,\n\t\t...rangeOptions\n\t} = options;\n\n\tif (!colorRange) {\n\t\t[c1, c2] = [getColor(c1), getColor(c2)];\n\t\tcolorRange = range(c1, c2, rangeOptions);\n\t}\n\n\tlet totalDelta = deltaE(c1, c2);\n\tlet actualSteps = maxDeltaE > 0 ? Math.max(steps, Math.ceil(totalDelta / maxDeltaE) + 1) : steps;\n\tlet ret = [];\n\n\tif (maxSteps !== undefined) {\n\t\tactualSteps = Math.min(actualSteps, maxSteps);\n\t}\n\n\tif (actualSteps === 1) {\n\t\tret = [{p: .5, color: colorRange(.5)}];\n\t}\n\telse {\n\t\tlet step = 1 / (actualSteps - 1);\n\t\tret = Array.from({length: actualSteps}, (_, i) => {\n\t\t\tlet p = i * step;\n\t\t\treturn {p, color: colorRange(p)};\n\t\t});\n\t}\n\n\tif (maxDeltaE > 0) {\n\t\t// Iterate over all stops and find max deltaE\n\t\tlet maxDelta = ret.reduce((acc, cur, i) => {\n\t\t\tif (i === 0) {\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tlet ΔΕ = deltaE(cur.color, ret[i - 1].color, deltaEMethod);\n\t\t\treturn Math.max(acc, ΔΕ);\n\t\t}, 0);\n\n\t\twhile (maxDelta > maxDeltaE) {\n\t\t\t// Insert intermediate stops and measure maxDelta again\n\t\t\t// We need to do this for all pairs, otherwise the midpoint shifts\n\t\t\tmaxDelta = 0;\n\n\t\t\tfor (let i = 1; (i < ret.length) && (ret.length < maxSteps); i++) {\n\t\t\t\tlet prev = ret[i - 1];\n\t\t\t\tlet cur = ret[i];\n\n\t\t\t\tlet p = (cur.p + prev.p) / 2;\n\t\t\t\tlet color = colorRange(p);\n\t\t\t\tmaxDelta = Math.max(maxDelta, deltaE(color, prev.color), deltaE(color, cur.color));\n\t\t\t\tret.splice(i, 0, {p, color: colorRange(p)});\n\t\t\t\ti++;\n\t\t\t}\n\t\t}\n\t}\n\n\tret = ret.map(a => a.color);\n\n\treturn ret;\n}\n\n/**\n * Interpolate to color2 and return a function that takes a 0-1 percentage\n * @param {Color | string | Function} color1 The first color or an existing range\n * @param {Color | string} [color2] If color1 is a color, this is the second color\n * @param {Object} [options={}]\n * @returns {Function} A function that takes a 0-1 percentage and returns a color\n */\nexport function range (color1, color2, options = {}) {\n\tif (isRange(color1)) {\n\t\t// Tweaking existing range\n\t\tlet [r, options] = [color1, color2];\n\n\t\treturn range(...r.rangeArgs.colors, {...r.rangeArgs.options, ...options});\n\t}\n\n\tlet {space, outputSpace, progression, premultiplied} = options;\n\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\t// Make sure we're working on copies of these colors\n\tcolor1 = clone(color1);\n\tcolor2 = clone(color2);\n\n\tlet rangeArgs = {colors: [color1, color2], options};\n\n\tif (space) {\n\t\tspace = ColorSpace.get(space);\n\t}\n\telse {\n\t\tspace = ColorSpace.registry[defaults.interpolationSpace] || color1.space;\n\t}\n\n\toutputSpace = outputSpace ? ColorSpace.get(outputSpace) : space;\n\n\tcolor1 = to(color1, space);\n\tcolor2 = to(color2, space);\n\n\t// Gamut map to avoid areas of flat color\n\tcolor1 = toGamut(color1);\n\tcolor2 = toGamut(color2);\n\n\t// Handle hue interpolation\n\t// See https://github.com/w3c/csswg-drafts/issues/4735#issuecomment-635741840\n\tif (space.coords.h && space.coords.h.type === \"angle\") {\n\t\tlet arc = options.hue = options.hue || \"shorter\";\n\n\t\tlet hue = [space, \"h\"];\n\t\tlet [θ1, θ2] = [get(color1, hue), get(color2, hue)];\n\t\t// Undefined hues must be evaluated before hue fix-up to properly\n\t\t// calculate hue arcs between undefined and defined hues.\n\t\t// See https://github.com/w3c/csswg-drafts/issues/9436#issuecomment-1746957545\n\t\tif (isNaN(θ1) && !isNaN(θ2)) {\n\t\t\tθ1 = θ2;\n\t\t}\n\t\telse if (isNaN(θ2) && !isNaN(θ1)) {\n\t\t\tθ2 = θ1;\n\t\t}\n\t\t[θ1, θ2] = angles.adjust(arc, [θ1, θ2]);\n\t\tset(color1, hue, θ1);\n\t\tset(color2, hue, θ2);\n\t}\n\n\tif (premultiplied) {\n\t\t// not coping with polar spaces yet\n\t\tcolor1.coords = color1.coords.map(c => c * color1.alpha);\n\t\tcolor2.coords = color2.coords.map(c => c * color2.alpha);\n\t}\n\n\treturn Object.assign(p => {\n\t\tp = progression ? progression(p) : p;\n\t\tlet coords = color1.coords.map((start, i) => {\n\t\t\tlet end = color2.coords[i];\n\t\t\treturn interpolate(start, end, p);\n\t\t});\n\n\t\tlet alpha = interpolate(color1.alpha, color2.alpha, p);\n\t\tlet ret = {space, coords, alpha};\n\n\t\tif (premultiplied) {\n\t\t\t// undo premultiplication\n\t\t\tret.coords = ret.coords.map(c => c / alpha);\n\t\t}\n\n\t\tif (outputSpace !== space) {\n\t\t\tret = to(ret, outputSpace);\n\t\t}\n\n\t\treturn ret;\n\t}, {\n\t\trangeArgs,\n\t});\n}\n\nexport function isRange (val) {\n\treturn type(val) === \"function\" && !!val.rangeArgs;\n}\n\ndefaults.interpolationSpace = \"lab\";\n\nexport function register (Color) {\n\tColor.defineFunction(\"mix\", mix, {returns: \"color\"});\n\tColor.defineFunction(\"range\", range, {returns: \"function<color>\"});\n\tColor.defineFunction(\"steps\", steps, {returns: \"array<color>\"});\n}\n", "import ColorSpace from \"../space.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new ColorSpace({\n\tid: \"hsl\",\n\tname: \"H<PERSON>\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: sRGB,\n\n\t// Adapted from https://drafts.csswg.org/css-color-4/better-rgbToHsl.js\n\tfromBase: rgb => {\n\t\tlet max = Math.max(...rgb);\n\t\tlet min = Math.min(...rgb);\n\t\tlet [r, g, b] = rgb;\n\t\tlet [h, s, l] = [NaN, 0, (min + max) / 2];\n\t\tlet d = max - min;\n\n\t\tif (d !== 0) {\n\t\t\ts = (l === 0 || l === 1) ? 0 : (max - l) / Math.min(l, 1 - l);\n\n\t\t\tswitch (max) {\n\t\t\t\tcase r: h = (g - b) / d + (g < b ? 6 : 0); break;\n\t\t\t\tcase g: h = (b - r) / d + 2; break;\n\t\t\t\tcase b: h = (r - g) / d + 4;\n\t\t\t}\n\n\t\t\th = h * 60;\n\t\t}\n\n\t\t// Very out of gamut colors can produce negative saturation\n\t\t// If so, just rotate the hue by 180 and use a positive saturation\n\t\t// see https://github.com/w3c/csswg-drafts/issues/9222\n\t\tif (s < 0) {\n\t\t\th += 180;\n\t\t\ts = Math.abs(s);\n\t\t}\n\n\t\tif (h >= 360) {\n\t\t\th -= 360;\n\t\t}\n\n\t\treturn [h, s * 100, l * 100];\n\t},\n\n\t// Adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_RGB_alternative\n\ttoBase: hsl => {\n\t\tlet [h, s, l] = hsl;\n\t\th = h % 360;\n\n\t\tif (h < 0) {\n\t\t\th += 360;\n\t\t}\n\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tfunction f (n) {\n\t\t\tlet k = (n + h / 30) % 12;\n\t\t\tlet a = s * Math.min(l, 1 - l);\n\t\t\treturn l - a * Math.max(-1, Math.min(k - 3, 9 - k, 1));\n\t\t}\n\n\t\treturn [f(0), f(8), f(4)];\n\t},\n\n\tformats: {\n\t\t\"hsl\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t},\n\t\t\"hsla\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSL from \"./hsl.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hsv\",\n\tname: \"HSV\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tv: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Value\",\n\t\t},\n\t},\n\n\tbase: HSL,\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\tfromBase (hsl) {\n\t\tlet [h, s, l] = hsl;\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tlet v = l + s * Math.min(l, 1 - l);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\tv === 0 ? 0 : 200 * (1 - l / v), // s\n\t\t\t100 * v,\n\t\t];\n\t},\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\ttoBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\ts /= 100;\n\t\tv /= 100;\n\n\t\tlet l = v * (1 - s / 2);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\t(l === 0 || l === 1) ? 0 : ((v - l) / Math.min(l, 1 - l)) * 100,\n\t\t\tl * 100,\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSV from \"./hsv.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hwb\",\n\tname: \"HWB\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\tw: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Whiteness\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Blackness\",\n\t\t},\n\t},\n\n\tbase: HSV,\n\tfromBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\treturn [h, v * (100 - s) / 100, 100 - v];\n\t},\n\ttoBase (hwb) {\n\t\tlet [h, w, b] = hwb;\n\n\t\t// Now convert percentages to [0..1]\n\t\tw /= 100;\n\t\tb /= 100;\n\n\t\t// Achromatic check (white plus black >= 1)\n\t\tlet sum = w + b;\n\t\tif (sum >= 1) {\n\t\t\tlet gray = w / sum;\n\t\t\treturn [h, 0, gray * 100];\n\t\t}\n\n\t\tlet v = (1 - b);\n\t\tlet s = (v === 0) ? 0 : 1 - w / v;\n\t\treturn [h, s * 100, v * 100];\n\t},\n\n\tformats: {\n\t\t\"hwb\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light a98-rgb values to CIE XYZ\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// has greater numerical precision than section ******* of\n// https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf\n// but the values below were calculated from first principles\n// from the chromaticity coordinates of R G B W\nconst toXYZ_M = [\n\t[ 0.5766690429101305,   0.1855582379065463,   0.1882286462349947  ],\n\t[ 0.29734497525053605,  0.6273635662554661,   0.07529145849399788 ],\n\t[ 0.02703136138641234,  0.07068885253582723,  0.9913375368376388  ],\n];\n\nconst fromXYZ_M = [\n\t[  2.0415879038107465,    -0.5650069742788596,   -0.34473135077832956 ],\n\t[ -0.9692436362808795,     1.8759675015077202,    0.04155505740717557 ],\n\t[  0.013444280632031142,  -0.11836239223101838,   1.0151749943912054  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb-linear\",\n\tcssId: \"--a98-rgb-linear\",\n\tname: \"Linear Adobe® 98 RGB compatible\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport A98Linear from \"./a98rgb-linear.js\";\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb\",\n\tcssId: \"a98-rgb\",\n\tname: \"Adobe® 98 RGB compatible\",\n\tbase: A98Linear,\n\ttoBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 563 / 256) * Math.sign(val)),\n\tfromBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 256 / 563) * Math.sign(val)),\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport XYZ_D50 from \"./xyz-d50.js\";\n\n// convert an array of  prophoto-rgb values to CIE XYZ\n// using  D50 (so no chromatic adaptation needed afterwards)\n// matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy\n// see https://github.com/w3c/csswg-drafts/issues/7675\nconst toXYZ_M = [\n\t[ 0.79776664490064230,  0.13518129740053308,  0.03134773412839220 ],\n\t[ 0.28807482881940130,  0.71183523424187300,  0.00008993693872564 ],\n\t[ 0.00000000000000000,  0.00000000000000000,  0.82510460251046020 ],\n];\n\nconst fromXYZ_M = [\n\t[  1.34578688164715830, -0.25557208737979464, -0.05110186497554526 ],\n\t[ -0.54463070512490190,  1.50824774284514680,  0.02052744743642139 ],\n\t[  0.00000000000000000,  0.00000000000000000,  1.21196754563894520 ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"prophoto-linear\",\n\tcssId: \"--prophoto-rgb-linear\",\n\tname: \"Linear ProPhoto\",\n\twhite: \"D50\",\n\tbase: XYZ_D50,\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport ProPhotoLinear from \"./prophoto-linear.js\";\n\nconst Et = 1 / 512;\nconst Et2 = 16 / 512;\n\nexport default new RGBColorSpace({\n\tid: \"prophoto\",\n\tcssId: \"prophoto-rgb\",\n\tname: \"ProPhoto\",\n\tbase: ProPhotoLinear,\n\ttoBase (RGB) {\n\t\t// Transfer curve is gamma 1.8 with a small linear portion\n\t\treturn RGB.map(v => v < Et2 ? v / 16 : v ** 1.8);\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(v => v >= Et ? v ** (1 / 1.8) : 16 * v);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport OKLab from \"./oklab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"oklch\",\n\tname: \"Oklch\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 0.4],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\twhite: \"D65\",\n\n\tbase: OKLab,\n\tfromBase (oklab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = oklab;\n\t\tlet h;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\th = NaN;\n\t\t}\n\t\telse {\n\t\t\th = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // OKLab L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(h), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\t// Convert from polar form\n\ttoBase (oklch) {\n\t\tlet [L, C, h] = oklch;\n\t\tlet a, b;\n\n\t\t// check for NaN hue\n\t\tif (isNaN(h)) {\n\t\t\ta = 0;\n\t\t\tb = 0;\n\t\t}\n\t\telse {\n\t\t\ta = C * Math.cos(h * Math.PI / 180);\n\t\t\tb = C * Math.sin(h * Math.PI / 180);\n\t\t}\n\n\t\treturn [ L, a, b ];\n\t},\n\n\tformats: {\n\t\t\"oklch\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[0,1]\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {uv} from \"../chromaticity.js\";\nimport {isNone, skipNone} from \"../util.js\";\n\nlet white = WHITES.D65;\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\nconst [U_PRIME_WHITE, V_PRIME_WHITE] = uv({space: xyz_d65, coords: white});\n\nexport default new ColorSpace({\n\tid: \"luv\",\n\tname: \"Luv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\t// Reference ranges from https://facelessuser.github.io/coloraide/colors/luv/\n\t\tu: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t\tv: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t},\n\n\twhite: white,\n\tbase: xyz_d65,\n\n\t// Convert D65-adapted XYZ to Luv\n\t// https://en.wikipedia.org/wiki/CIELUV#The_forward_transformation\n\tfromBase (XYZ) {\n\t\tlet xyz = [skipNone(XYZ[0]), skipNone(XYZ[1]), skipNone(XYZ[2])];\n\t\tlet y = xyz[1];\n\n\t\tlet [up, vp] = uv({space: xyz_d65, coords: xyz});\n\n\t\t// Protect against XYZ of [0, 0, 0]\n\t\tif (!Number.isFinite(up) || !Number.isFinite(vp)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tlet L = y <= ε ? κ * y : 116 * Math.cbrt(y) - 16;\n\t\treturn [\n\t\t\tL,\n\t\t\t13 * L * (up - U_PRIME_WHITE),\n\t\t\t13 * L * (vp - V_PRIME_WHITE),\n\t\t ];\n\t},\n\n\t// Convert Luv to D65-adapted XYZ\n\t// https://en.wikipedia.org/wiki/CIELUV#The_reverse_transformation\n\ttoBase (Luv) {\n\t\tlet [L, u, v] = Luv;\n\n\t\t// Protect against division by zero and NaN Lightness\n\t\tif (L === 0 || isNone(L)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tu = skipNone(u);\n\t\tv = skipNone(v);\n\n\t\tlet up = (u / (13 * L)) + U_PRIME_WHITE;\n\t\tlet vp = (v / (13 * L)) + V_PRIME_WHITE;\n\n\t\tlet y = L <= 8 ? L / κ : Math.pow((L + 16) / 116, 3);\n\n\t\treturn [\n\t\t\ty * ((9 * up) / (4 * vp)),\n\t\t\ty,\n\t\t\ty * ((12 - 3 * up - 20 * vp) / (4 * vp)),\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--luv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport Luv from \"./luv.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lchuv\",\n\tname: \"<PERSON>Chuv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 220],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Luv,\n\tfromBase (Luv) {\n\t\t// Convert to polar form\n\t\tlet [L, u, v] = Luv;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(u) < ε && Math.abs(v) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(v, u) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(u ** 2 + v ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // u\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // v\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--lchuv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport sRGB from \"./srgb.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOriginAngle (slope, intercept, angle) {\n\tconst d = intercept / (Math.sin(angle) - slope * Math.cos(angle));\n\treturn d < 0 ? Infinity : d;\n}\n\nexport function calculateBoundingLines (l) {\n\tconst sub1 = Math.pow(l + 16, 3) / 1560896;\n\tconst sub2 = sub1 > ε ? sub1 : l / κ;\n\tconst s1r = sub2 * (284517 * m_r0 - 94839 * m_r2);\n\tconst s2r = sub2 * (838422 * m_r2 + 769860 * m_r1 + 731718 * m_r0);\n\tconst s3r = sub2 * (632260 * m_r2 - 126452 * m_r1);\n\tconst s1g = sub2 * (284517 * m_g0 - 94839 * m_g2);\n\tconst s2g = sub2 * (838422 * m_g2 + 769860 * m_g1 + 731718 * m_g0);\n\tconst s3g = sub2 * (632260 * m_g2 - 126452 * m_g1);\n\tconst s1b = sub2 * (284517 * m_b0 - 94839 * m_b2);\n\tconst s2b = sub2 * (838422 * m_b2 + 769860 * m_b1 + 731718 * m_b0);\n\tconst s3b = sub2 * (632260 * m_b2 - 126452 * m_b1);\n\n\treturn {\n\t\tr0s: s1r / s3r,\n\t\tr0i: s2r * l / s3r,\n\t\tr1s: s1r / (s3r + 126452),\n\t\tr1i: (s2r - 769860) * l / (s3r + 126452),\n\t\tg0s: s1g / s3g,\n\t\tg0i: s2g * l / s3g,\n\t\tg1s: s1g / (s3g + 126452),\n\t\tg1i: (s2g - 769860) * l / (s3g + 126452),\n\t\tb0s: s1b / s3b,\n\t\tb0i: s2b * l / s3b,\n\t\tb1s: s1b / (s3b + 126452),\n\t\tb1i: (s2b - 769860) * l / (s3b + 126452),\n\t};\n}\n\nfunction calcMaxChromaHsluv (lines, h) {\n\tconst hueRad = h / 360 * Math.PI * 2;\n\tconst r0 = distanceFromOriginAngle(lines.r0s, lines.r0i, hueRad);\n\tconst r1 = distanceFromOriginAngle(lines.r1s, lines.r1i, hueRad);\n\tconst g0 = distanceFromOriginAngle(lines.g0s, lines.g0i, hueRad);\n\tconst g1 = distanceFromOriginAngle(lines.g1s, lines.g1i, hueRad);\n\tconst b0 = distanceFromOriginAngle(lines.b0s, lines.b0i, hueRad);\n\tconst b1 = distanceFromOriginAngle(lines.b1s, lines.b1i, hueRad);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hsluv\",\n\tname: \"HSLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: sRGB,\n\n\t// Convert LCHuv to HSLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\ts = c / max * 100;\n\t\t}\n\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HSLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\nimport {calculateBoundingLines} from \"./hsluv.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOrigin (slope, intercept) {\n\treturn Math.abs(intercept) / Math.sqrt(Math.pow(slope, 2) + 1);\n}\n\nfunction calcMaxChromaHpluv (lines) {\n\tlet r0 = distanceFromOrigin(lines.r0s, lines.r0i);\n\tlet r1 = distanceFromOrigin(lines.r1s, lines.r1i);\n\tlet g0 = distanceFromOrigin(lines.g0s, lines.g0i);\n\tlet g1 = distanceFromOrigin(lines.g1s, lines.g1i);\n\tlet b0 = distanceFromOrigin(lines.b0s, lines.b0i);\n\tlet b1 = distanceFromOrigin(lines.b1s, lines.b1i);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hpluv\",\n\tname: \"HPLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: \"self\",\n\n\t// Convert LCHuv to HPLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines);\n\t\t\ts = c / max * 100;\n\t\t}\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HPLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hpluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\nconst Yw = 203;\t// absolute luminance of media white, cd/m²\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst m = 2523 / (2 ** 5);\nconst minv = (2 ** 5) / 2523;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\n\nexport default new RGBColorSpace({\n\tid: \"rec2100pq\",\n\tcssId: \"rec2100-pq\",\n\tname: \"REC.2100-PQ\",\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given PQ encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = ((Math.max(((val ** minv) - c1), 0) / (c2 - (c3 * (val ** minv)))) ** ninv);\n\t\t\treturn (x * 10000 / Yw); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// returnPQ encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = Math.max(val * Yw / 10000, 0); \t// absolute luminance of peak white is 10,000 cd/m².\n\t\t\tlet num = (c1 + (c2 * (x ** n)));\n\t\t\tlet denom = (1 + (c3 * (x ** n)));\n\n\t\t\treturn ((num / denom)  ** m);\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\n// FIXME see https://github.com/LeaVerou/color.js/issues/190\n\nconst a = 0.17883277;\nconst b = 0.28466892; // 1 - (4 * a)\nconst c = 0.55991073; // 0.5 - a * Math.log(4 *a)\n\nconst scale = 3.7743;\t// Place 18% grey at HLG 0.38, so media white at 0.75\n\nexport default new RGBColorSpace({\n\tid: \"rec2100hlg\",\n\tcssId: \"rec2100-hlg\",\n\tname: \"REC.2100-HLG\",\n\treferred: \"scene\",\n\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given HLG encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\t// first the HLG EOTF\n\t\t\t// ITU-R BT.2390-10 p.30 section\n\t\t\t// 6.3 The hybrid log-gamma electro-optical transfer function (EOTF)\n\t\t\t// Then scale by 3 so media white is 1.0\n\t\t\tif (val <= 0.5) {\n\t\t\t\treturn (val ** 2) / 3 * scale;\n\t\t\t}\n\t\t\treturn ((Math.exp((val - c) / a) + b) / 12) * scale;\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// where diffuse white is 1.0,\n\t\t// return HLG encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\t// first scale to put linear-light media white at 1/3\n\t\t\tval /= scale;\n\t\t\t// now the HLG OETF\n\t\t\t// ITU-R BT.2390-10 p.23\n\t\t\t// 6.1 The hybrid log-gamma opto-electronic transfer function (OETF)\n\t\t\tif (val <= 1 / 12) {\n\t\t\t\treturn Math.sqrt(3 * val);\n\t\t\t}\n\t\t\treturn a * Math.log(12 * val - b) + c;\n\t\t});\n\t},\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport {WHITES} from \"./adapt.js\";\n\nexport const CATs = {};\n\nhooks.add(\"chromatic-adaptation-start\", env => {\n\tif (env.options.method) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nhooks.add(\"chromatic-adaptation-end\", env => {\n\tif (!env.M) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nexport function defineCAT ({id, toCone_M, fromCone_M}) {\n\t// Use id, toCone_M, fromCone_M like variables\n\tCATs[id] = arguments[0];\n}\n\nexport function adapt (W1, W2, id = \"Bradford\") {\n\t// adapt from a source whitepoint or illuminant W1\n\t// to a destination whitepoint or illuminant W2,\n\t// using the given chromatic adaptation transform (CAT)\n\t// debugger;\n\tlet method = CATs[id];\n\n\tlet [ρs, γs, βs] = multiplyMatrices(method.toCone_M, W1);\n\tlet [ρd, γd, βd] = multiplyMatrices(method.toCone_M, W2);\n\n\t// all practical illuminants have non-zero XYZ so no division by zero can occur below\n\tlet scale = [\n\t\t[ρd / ρs,  0,        0      ],\n\t\t[0,        γd / γs,  0      ],\n\t\t[0,        0,        βd / βs],\n\t];\n\t// console.log({scale});\n\n\tlet scaled_cone_M = multiplyMatrices(scale, method.toCone_M);\n\tlet adapt_M\t= multiplyMatrices(method.fromCone_M, scaled_cone_M);\n\t// console.log({scaled_cone_M, adapt_M});\n\treturn adapt_M;\n}\n\ndefineCAT({\n\tid: \"von Kries\",\n\ttoCone_M: [\n\t\t[  0.4002400,  0.7076000, -0.0808100 ],\n\t\t[ -0.2263000,  1.1653200,  0.0457000 ],\n\t\t[  0.0000000,  0.0000000,  0.9182200 ],\n\t],\n\tfromCone_M: [\n\t\t[ 1.8599363874558397, -1.1293816185800916,   0.21989740959619328     ],\n\t\t[ 0.3611914362417676,  0.6388124632850422,  -0.000006370596838649899 ],\n\t\t[ 0,                   0,                    1.0890636230968613      ],\n\t],\n});\n\ndefineCAT({\n\tid: \"Bradford\",\n\t// Convert an array of XYZ values in the range 0.0 - 1.0\n\t// to cone fundamentals\n\ttoCone_M: [\n\t\t[  0.8951000,  0.2664000, -0.1614000 ],\n\t\t[ -0.7502000,  1.7135000,  0.0367000 ],\n\t\t[  0.0389000, -0.0685000,  1.0296000 ],\n\t],\n\t// and back\n\tfromCone_M: [\n\t\t[  0.9869929054667121, -0.14705425642099013, 0.15996265166373122  ],\n\t\t[  0.4323052697233945,  0.5183602715367774,  0.049291228212855594 ],\n\t\t[ -0.00852866457517732, 0.04004282165408486, 0.96848669578755     ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT02\",\n\t// with complete chromatic adaptation to W2, so D = 1.0\n\ttoCone_M: [\n\t\t[  0.7328000,  0.4296000, -0.1624000 ],\n\t\t[ -0.7036000,  1.6975000,  0.0061000 ],\n\t\t[  0.0030000,  0.0136000,  0.9834000 ],\n\t],\n\tfromCone_M: [\n\t\t[  1.0961238208355142,   -0.27886900021828726, 0.18274517938277307 ],\n\t\t[  0.4543690419753592,    0.4735331543074117,  0.07209780371722911 ],\n\t\t[ -0.009627608738429355, -0.00569803121611342, 1.0153256399545427  ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT16\",\n\ttoCone_M: [\n\t\t[  0.401288,  0.650173, -0.051461 ],\n\t\t[ -0.250268,  1.204414,  0.045854 ],\n\t\t[ -0.002079,  0.048952,  0.953127 ],\n\t],\n\t// the extra precision is needed to avoid roundtripping errors\n\tfromCone_M: [\n\t\t[  1.862067855087233,   -1.0112546305316845,  0.14918677544445172  ],\n\t\t[  0.3875265432361372,   0.6214474419314753, -0.008973985167612521 ],\n\t\t[ -0.01584149884933386, -0.03412293802851557, 1.0499644368778496   ],\n\t],\n});\n\nObject.assign(WHITES, {\n\t// whitepoint values from ASTM E308-01 with 10nm spacing, 1931 2 degree observer\n\t// all normalized to Y (luminance) = 1.00000\n\t// Illuminant A is a tungsten electric light, giving a very warm, orange light.\n\tA:   [1.09850, 1.00000, 0.35585],\n\n\t// Illuminant C was an early approximation to daylight: illuminant A with a blue filter.\n\tC:   [0.98074, 1.000000, 1.18232],\n\n\t// The daylight series of illuminants simulate natural daylight.\n\t// The color temperature (in degrees Kelvin/100) ranges from\n\t// cool, overcast daylight (D50) to bright, direct sunlight (D65).\n\tD55: [0.95682, 1.00000, 0.92149],\n\tD75: [0.94972, 1.00000, 1.22638],\n\n\t// Equal-energy illuminant, used in two-stage CAT16\n\tE:   [1.00000, 1.00000, 1.00000],\n\n\t// The F series of illuminants represent fluorescent lights\n\tF2:  [0.99186, 1.00000, 0.67393],\n\tF7:  [0.95041, 1.00000, 1.08747],\n\tF11: [1.00962, 1.00000, 0.64350],\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport {WHITES} from \"../adapt.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\n\n// The ACES whitepoint\n// see TB-2018-001 Derivation of the ACES White Point CIE Chromaticity Coordinates\n// also https://github.com/ampas/aces-dev/blob/master/documents/python/TB-2018-001/aces_wp.py\n// Similar to D60\nWHITES.ACES = [0.32168 / 0.33767, 1.00000, (1.00000 - 0.32168 - 0.33767) / 0.33767];\n\n// convert an array of linear-light ACEScc values to CIE XYZ\nconst toXYZ_M = [\n\t[  0.6624541811085053,   0.13400420645643313,  0.1561876870049078  ],\n\t[  0.27222871678091454,  0.6740817658111484,   0.05368951740793705 ],\n\t[ -0.005574649490394108, 0.004060733528982826, 1.0103391003129971  ],\n];\nconst fromXYZ_M = [\n\t[  1.6410233796943257,   -0.32480329418479,    -0.23642469523761225  ],\n\t[ -0.6636628587229829,    1.6153315916573379,   0.016756347685530137 ],\n\t[  0.011721894328375376, -0.008284441996237409, 0.9883948585390215   ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"acescg\",\n\tcssId: \"--acescg\",\n\tname: \"ACEScg\",\n\n\t// ACEScg – A scene-referred, linear-light encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescg/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\tcoords: {\n\t\tr: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\n\treferred: \"scene\",\n\n\twhite: WHITES.ACES,\n\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n\n// export default Color;\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\nimport ACEScg from \"./acescg.js\";\n\nconst ε = 2 ** -16;\n\n// the smallest value which, in the 32bit IEEE 754 float encoding,\n// decodes as a non-negative value\nconst ACES_min_nonzero = -0.35828683;\n\n// brightest encoded value, decodes to 65504\nconst ACES_cc_max = (Math.log2(65504) + 9.72) / 17.52; // 1.468\n\nexport default new RGBColorSpace({\n\tid: \"acescc\",\n\tcssId: \"--acescc\",\n\tname: \"ACEScc\",\n\t// see S-2014-003 ACEScc – A Logarithmic Encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescc/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\n\t// Appendix A: \"Very small ACES scene referred values below 7 1/4 stops\n\t// below 18% middle gray are encoded as negative ACEScc values.\n\t// These values should be preserved per the encoding in Section 4.4\n\t// so that all positive ACES values are maintained.\"\n\tcoords: {\n\t\tr: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\treferred: \"scene\",\n\n\tbase: ACEScg,\n\t// from section 4.4.2 Decoding Function\n\ttoBase (RGB) {\n\t\tconst low = (9.72 - 15) / 17.52; // -0.3014\n\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= low) {\n\t\t\t\treturn (2 ** ((val * 17.52) - 9.72) - ε) * 2; // very low values, below -0.3014\n\t\t\t}\n\t\t\telse if (val < ACES_cc_max) {\n\t\t\t\treturn 2 ** ((val * 17.52) - 9.72);\n\t\t\t}\n\t\t\telse { // val >= ACES_cc_max\n\t\t\t\treturn 65504;\n\t\t\t}\n\t\t});\n\t},\n\n\t// Non-linear encoding function from S-2014-003, section 4.4.1 Encoding Function\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= 0) {\n\t\t\t\treturn (Math.log2(ε) + 9.72) / 17.52; // -0.3584\n\t\t\t}\n\t\t\telse if (val < ε) {\n\t\t\t\treturn  (Math.log2(ε + val * 0.5) + 9.72) / 17.52;\n\t\t\t}\n\t\t\telse { // val >= ε\n\t\t\t\treturn  (Math.log2(val) + 9.72) / 17.52;\n\t\t\t}\n\t\t});\n\t},\n\t// encoded media white (rgb 1,1,1) => linear  [ 222.861, 222.861, 222.861 ]\n\t// encoded media black (rgb 0,0,0) => linear [ 0.0011857, 0.0011857, 0.0011857]\n});\n"], "names": ["adapt", "util.mapRange", "util.parseFunction", "util.last", "ε", "XYZ_D65", "ε3", "κ", "white", "xyz_d50", "Lab", "constrainAngle", "π", "d2r", "XYZtoLMS_M", "LMStoXYZ_M", "oklab", "Yw", "b", "n", "ninv", "c1", "c2", "c3", "m1", "deg2rad", "viewingConditions", "util.isString", "util.isNone", "util.clamp", "inGamut", "checkInGamut", "util.serializeNumber", "toXYZ_M", "fromXYZ_M", "angles.adjust"], "mappings": ";;AAAA;AACe,SAAS,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE;AAChD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAClB;AACA,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC3B;AACA,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACV,EAAE;AACF;AACA,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC3B;AACA,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE;AACF;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACrB,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI;AAC9C,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACd;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC3B,GAAG,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE;AACtB,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;AACnB,IAAI;AACJ;AACA,GAAG,OAAO,GAAG,CAAC;AACd,GAAG;AACH;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACjC,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,EAAE,CAAC,CAAC,CAAC;AACL;AACA,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACd,EAAE,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACvB,EAAE;AACF;AACA,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACd,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,EAAE;AACF;AACA,CAAC,OAAO,OAAO,CAAC;AAChB;;AC3CA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,EAAE,GAAG,EAAE;AAC/B,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC;AAC/B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,EAAE,CAAC,EAAE;AACzB,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC;AACnE,CAAC;AACD;AACO,SAAS,eAAe,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE;AACxD,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;AAChB,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACF;AACA,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,EAAE,CAAC,EAAE;AAC3B,CAAC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5D,CAAC;AACD;AACA;AACA;AACA;AACO,SAAS,QAAQ,EAAE,CAAC,EAAE;AAC7B,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE;AAC3C,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACd,EAAE,OAAO,CAAC,CAAC;AACX,EAAE;AACF,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAChB,CAAC,IAAI,OAAO,IAAI,SAAS,EAAE;AAC3B,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AAC/C,EAAE;AACF,CAAC,MAAM,UAAU,GAAG,IAAI,KAAK,SAAS,GAAG,MAAM,CAAC,CAAC;AACjD,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,GAAG,CAAC,GAAG,UAAU,CAAC;AACtD,CAAC;AACD;AACA,MAAM,WAAW,GAAG;AACpB,CAAC,GAAG,EAAE,CAAC;AACP,CAAC,IAAI,EAAE,GAAG;AACV,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE;AACnB,CAAC,IAAI,EAAE,GAAG;AACV,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,EAAE,GAAG,EAAE;AACpC,CAAC,IAAI,CAAC,GAAG,EAAE;AACX,EAAE,OAAO;AACT,EAAE;AACF;AACA,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AAClB;AACA,CAAC,MAAM,eAAe,GAAG,sBAAsB,CAAC;AAChD,CAAC,MAAM,aAAa,GAAG,YAAY,CAAC;AACpC,CAAC,MAAM,cAAc,GAAG,mBAAmB,CAAC;AAC5C,CAAC,MAAM,cAAc,GAAG,4CAA4C,CAAC;AACrE,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AACxC;AACA,CAAC,IAAI,KAAK,EAAE;AACZ;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,MAAM,KAAK;AACnD,GAAG,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC5C,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC;AACpB;AACA,GAAG,IAAI,KAAK,EAAE;AACd,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACxB;AACA,IAAI,IAAI,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjD;AACA,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE;AACtB;AACA,KAAK,GAAG,GAAG,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;AACzC,KAAK,GAAG,CAAC,IAAI,GAAG,cAAc,CAAC;AAC/B,KAAK;AACL,SAAS;AACT;AACA,KAAK,GAAG,GAAG,IAAI,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AACvD,KAAK,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC;AAC1B,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,KAAK;AACL,IAAI;AACJ,QAAQ,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACrC;AACA,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1B,IAAI,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC;AAC1B,IAAI;AACJ,QAAQ,IAAI,GAAG,KAAK,MAAM,EAAE;AAC5B,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1B,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB,IAAI;AACJ;AACA,GAAG,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC3B;AACA,IAAI,GAAG,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AACxD,IAAI,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,IAAI;AACJ;AACA,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,MAAM,EAAE;AACzD,IAAI,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC;AACrB,IAAI;AACJ;AACA,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB,GAAG,CAAC,CAAC;AACL;AACA,EAAE,OAAO;AACT,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;AAC/B,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AACpB,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AACpB;AACA;AACA,GAAG,IAAI;AACP,GAAG,CAAC;AACJ,EAAE;AACF,CAAC;AACD;AACO,SAAS,IAAI,EAAE,GAAG,EAAE;AAC3B,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5B,CAAC;AACD;AACO,SAAS,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;AAC5C,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;AACnB,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;AACjB,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACF;AACA,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC;AAClC,CAAC;AACD;AACO,SAAS,cAAc,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACnD,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;AACxC,CAAC;AACD;AACO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE;AAC3C,CAAC,OAAO,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3E,CAAC;AACD;AACO,SAAS,iBAAiB,EAAE,aAAa,EAAE;AAClD,CAAC,OAAO,aAAa,CAAC,GAAG,CAAC,YAAY,IAAI;AAC1C,EAAE,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI;AAC7C,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACtB,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;AACvE;AACA,GAAG,IAAI,KAAK,EAAE;AACd,IAAI,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,IAAI,OAAO,GAAG,CAAC;AACf,IAAI;AACJ;AACA,GAAG,OAAO,IAAI,CAAC;AACf,GAAG,CAAC,CAAC;AACL,EAAE,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE;AACpC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;AACrD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE;AACjC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC;AAC9C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;AAC5B,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE;AACjE,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;AACjB,EAAE,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAC7B,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE;AACxB,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;AAChB,GAAG;AACH,OAAO;AACP,GAAG,EAAE,GAAG,GAAG,CAAC;AACZ,GAAG;AACH,EAAE;AACF,CAAC,OAAO,EAAE,CAAC;AACX;;AC7PA;AACA;AACA;AACO,MAAM,KAAK,CAAC;AACnB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;AAC7B,EAAE,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;AACvC;AACA,GAAG,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;AAClC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,IAAI;AACJ;AACA,GAAG,OAAO;AACV,GAAG;AACH;AACA,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,UAAU,IAAI,EAAE;AAChE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACjC;AACA,GAAG,IAAI,QAAQ,EAAE;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrD,IAAI;AACJ,GAAG,EAAE,IAAI,CAAC,CAAC;AACX,EAAE;AACF;AACA,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE;AACjB,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AAChC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;AACzC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9D,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC;AACD;AACA;AACA;AACA;AACK,MAAC,KAAK,GAAG,IAAI,KAAK;;AC/BhB,MAAM,MAAM,GAAG;AACtB;AACA,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC;AAClE,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC;AAClE,CAAC,CAAC;AACF;AACO,SAAS,QAAQ,EAAE,IAAI,EAAE;AAChC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC1B,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;AACrB,CAAC;AACD;AACA;AACe,SAASA,OAAK,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE;AAC1D,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AACnB,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AACnB;AACA,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE;AACjB,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC,+BAA+B,EAAE,CAAC,EAAE,GAAG,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACvH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;AAChB;AACA,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAClC;AACA,CAAC,KAAK,CAAC,GAAG,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;AAC9C;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AACb,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE;AACtD,GAAG,GAAG,CAAC,CAAC,GAAG;AACX,IAAI,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,CAAC,mBAAmB,EAAE;AACtE,IAAI,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,oBAAoB,EAAE;AACtE,IAAI,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE;AACvE,IAAI,CAAC;AACL,GAAG;AACH,OAAO,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE;AAC3D;AACA,GAAG,GAAG,CAAC,CAAC,GAAG;AACX,IAAI,EAAE,iBAAiB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE;AACpE,IAAI,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE;AACrE,IAAI,EAAE,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE;AACtE,IAAI,CAAC;AACL,GAAG;AACH,EAAE;AACF;AACA,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;AAC5C;AACA,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE;AACZ,EAAE,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C,EAAE;AACF,MAAM;AACN,EAAE,MAAM,IAAI,SAAS,CAAC,oEAAoE,CAAC,CAAC;AAC5F,EAAE;AACF;;AC7DA;AACA,eAAe;AACf,CAAC,aAAa,EAAE,KAAK;AACrB,CAAC,SAAS,EAAE,CAAC;AACb,CAAC,MAAM,EAAE,IAAI;AACb,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,MAAM;AACtE,CAAC,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,EAAE;AAC3B,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE;AACpB,GAAG,UAAU,EAAE,OAAO,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACpC,GAAG;AACH,EAAE;AACF,CAAC;;ACND,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;AACpD,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK;AACtE,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AAC5C,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE,IAAI,YAAY,GAAG,GAAG,EAAE,IAAI,CAAC;AAC/B;AACA;AACA;AACA,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE;AAChB,GAAG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,GAAG;AACH,OAAO;AACP,GAAG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,CAAC;AACpD,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,IAAI,EAAE;AACb;AACA,GAAG,IAAI,SAAS,GAAG,SAAS,CAAC,IAAI,IAAI,EAAE,CAAC;AACxC,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,EAAE,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,iBAAiB,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/F,GAAG;AACH;AACA,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B;AACA,EAAE,IAAI,YAAY,KAAK,cAAc,EAAE;AACvC,GAAG,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,CAAC;AACtD;AACA,EAAE,IAAI,SAAS,IAAI,OAAO,EAAE;AAC5B,GAAG,MAAM,CAAC,CAAC,CAAC,GAAGC,QAAa,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,EAAE,CAAC,CAAC;AACJ;AACA,CAAC,OAAO,KAAK,CAAC;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAAS,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;AACjD,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AACxC,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;AAC/B;AACA,CAAC,IAAI,GAAG,CAAC,KAAK,EAAE;AAChB,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC;AACnB,EAAE;AACF;AACA,CAAC,GAAG,CAAC,MAAM,GAAGC,aAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C;AACA,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;AACjB;AACA,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AAC7B;AACA,EAAE,IAAI,IAAI,KAAK,OAAO,EAAE;AACxB;AACA,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AACpC;AACA,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACvE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;AAC/B,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/E;AACA,GAAG,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,EAAE;AACrC,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC7C;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE;AACvG;AACA;AACA;AACA,MAAM,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACtF;AACA,MAAM,IAAI,KAAK,CAAC;AAChB;AACA,MAAM,IAAI,SAAS,CAAC,YAAY,EAAE;AAClC,OAAO,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC/D,OAAO;AACP;AACA,MAAM,IAAI,IAAI,EAAE;AAChB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AACvD,OAAO;AACP;AACA,MAAM,IAAI,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACjE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,sEAAsE,CAAC;AAC1G,qBAAqB,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,CAAC,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrF,OAAO;AACP,MAAM,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACjE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,oDAAoD,CAAC;AACxF,qBAAqB,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,CAAC,4BAA4B,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrF,OAAO;AACP;AACA,MAAM,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAChD,MAAM;AACN,KAAK;AACL,IAAI;AACJ;AACA;AACA,GAAG,IAAI,UAAU,GAAG,EAAE,CAAC;AACvB,GAAG,IAAI,UAAU,GAAG,EAAE,IAAI,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG,WAAW,CAAC;AACjE,GAAG,IAAI,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE;AAC1C;AACA,IAAI,IAAI,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC;AACnE;AACA,IAAI,IAAI,KAAK,EAAE;AACf,KAAK,UAAU,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AAClD,KAAK;AACL,IAAI;AACJ;AACA,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,mBAAmB,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,UAAU,IAAI,mBAAmB,CAAC,CAAC,CAAC;AAC5F,GAAG;AACH,OAAO;AACP,GAAG,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,EAAE;AACrC;AACA,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACvC,IAAI,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;AAC9C,KAAK,IAAI,KAAK,GAAG,CAAC,CAAC;AACnB;AACA,KAAK,IAAI,MAAM,CAAC,SAAS,IAAIC,IAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;AAC/D,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACpC,MAAM;AACN;AACA,KAAK,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AAClC;AACA,KAAK,IAAI,KAAK,CAAC;AACf;AACA,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;AAC9B,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACxD,MAAM;AACN;AACA,KAAK,IAAI,IAAI,EAAE;AACf,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AAC1D,MAAM;AACN;AACA,KAAK,OAAO;AACZ,MAAM,OAAO,EAAE,KAAK,CAAC,EAAE;AACvB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,CAAC;AACP,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE;AACF,MAAM;AACN;AACA,EAAE,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,EAAE;AACpC,GAAG,KAAK,IAAI,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AACvC,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACzC;AACA,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AAClC,KAAK,SAAS;AACd,KAAK;AACL;AACA,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC9C,KAAK,SAAS;AACd,KAAK;AACL;AACA,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtC;AACA,IAAI,IAAI,KAAK,EAAE;AACf,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;AACvB;AACA,KAAK,IAAI,IAAI,EAAE;AACf,MAAM,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,MAAM;AACN;AACA,KAAK,OAAO,KAAK,CAAC;AAClB,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA;AACA;AACA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,8BAA8B,CAAC,CAAC,CAAC;AAC7E;;ACjMA;AACA;AACA;AACA;AACA;AACe,SAAS,QAAQ,EAAE,KAAK,EAAE;AACzC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC3B,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC7B,EAAE;AACF;AACA,CAAC,IAAI,CAAC,KAAK,EAAE;AACb,EAAE,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;AAC/C,EAAE;AACF;AACA,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACtB,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AACvB,EAAE;AACF;AACA;AACA,CAAC,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC;AAC1C;AACA,CAAC,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,EAAE;AACrC;AACA,EAAE,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC,EAAE;AACF;AACA,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE;AAChC,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAClB,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC;AACd;;AC9BA,MAAMC,GAAC,GAAG,OAAO,CAAC;AAClB;AACA;AACA;AACA;AACe,MAAM,UAAU,CAAC;AAChC,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE;AACvB,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;AACvB,EAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC3B,EAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACjE,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACjC;AACA,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;AACjB,GAAG,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACpC,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAClD;AACA,EAAE,KAAK,IAAI,IAAI,IAAI,MAAM,EAAE;AAC3B,GAAG,IAAI,EAAE,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAClC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7B,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACvB;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC;AACxD,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC/B;AACA;AACA;AACA,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;AACvC;AACA,EAAE,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACjC,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnC,GAAG,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;AAC9B,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE;AAC/B,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;AACxB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;AAC/B,IAAI,EAAE,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE;AAChC,IAAI,CAAC;AACL,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE;AAC1B;AACA,GAAG,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,KAAK,MAAM,GAAG,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC/F,GAAG;AACH,OAAO;AACP;AACA,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;AACrB;AACA,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;AAChC,IAAI;AACJ,QAAQ;AACR,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC;AAC5B,IAAI;AACJ,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;AACnC,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,KAAK;AACvC,IAAI,OAAO,IAAI,CAAC;AAChB,IAAI,CAAC;AACL,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACnC;AACA;AACA,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;AACtC,GAAG,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE;AACjC,GAAG,QAAQ,EAAE,KAAK;AAClB,GAAG,UAAU,EAAE,IAAI;AACnB,GAAG,YAAY,EAAE,IAAI;AACrB,GAAG,CAAC,CAAC;AACL;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;AACzC,EAAE;AACF;AACA,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,GAAGA,GAAC,CAAC,GAAG,EAAE,EAAE;AACtC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACrC,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAC7C,GAAG,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AACrD,GAAG;AACH;AACA,EAAE,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7C;AACA,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAChC,GAAG,IAAI,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC3B;AACA,GAAG,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;AAC5C,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AACzB;AACA,KAAK,OAAO,IAAI,CAAC;AACjB,KAAK;AACL;AACA,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAChC,IAAI,OAAO,CAAC,GAAG,KAAK,SAAS,IAAI,CAAC,IAAI,GAAG,GAAG,OAAO;AACnD,YAAY,GAAG,KAAK,SAAS,IAAI,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,CAAC;AACrD,IAAI;AACJ;AACA,GAAG,OAAO,IAAI,CAAC;AACf,GAAG,CAAC,CAAC;AACL,EAAE;AACF;AACA,CAAC,IAAI,WAAW,CAAC,GAAG;AACpB,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC;AACxE,EAAE;AACF;AACA,CAAC,IAAI,KAAK,CAAC,GAAG;AACd,EAAE,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;AAC5C,EAAE;AACF;AACA,CAAC,IAAI,OAAO,CAAC,GAAG;AAChB,EAAE,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAC9B,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;AACzC,IAAI,OAAO,IAAI,CAAC;AAChB,IAAI;AACJ,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACF;AACA,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE;AACpB,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAClC,GAAG,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,GAAG,OAAO,MAAM,CAAC;AACjB,GAAG;AACH;AACA,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,MAAM,KAAK,SAAS,EAAE;AAC5B;AACA,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,GAAG;AACH,OAAO;AACP,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,IAAI,GAAG,EAAE;AACX,GAAG,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAClC,GAAG,OAAO,GAAG,CAAC;AACd,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;AAChB,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,GAAG,OAAO,KAAK,CAAC;AAChB,GAAG;AACH;AACA,EAAE,OAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;AACrE,EAAE;AACF;AACA,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE;AACpB,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,GAAG,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACjC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AACjD,GAAG;AACH;AACA,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC;AACA,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AAC1B;AACA,GAAG,OAAO,MAAM,CAAC;AACjB,GAAG;AACH;AACA;AACA,EAAE,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACpD;AACA;AACA,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;AAC7B;AACA,EAAE,IAAI,eAAe,EAAE,oBAAoB,CAAC;AAC5C;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;AACvC,IAAI,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAChC,IAAI,oBAAoB,GAAG,CAAC,CAAC;AAC7B,IAAI;AACJ,QAAQ;AACR,IAAI,MAAM;AACV,IAAI;AACJ,GAAG;AACH;AACA,EAAE,IAAI,CAAC,eAAe,EAAE;AACxB;AACA,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;AAC9G,GAAG;AACH;AACA;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,EAAE,CAAC,EAAE,EAAE;AACjE,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACrC,GAAG;AACH;AACA;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,oBAAoB,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpE,GAAG,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACF;AACA,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE;AACtB,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,GAAG,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACjC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AACjD,GAAG;AACH;AACA,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC;AACA,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAChC,EAAE;AACF;AACA,CAAC,QAAQ,CAAC,GAAG;AACb,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrC,EAAE;AACF;AACA,CAAC,YAAY,CAAC,GAAG;AACjB,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf;AACA,EAAE,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAC9B,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC9B,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC;AAC3C,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACF;AACA,CAAC,OAAO,QAAQ,GAAG,EAAE,CAAC;AACtB;AACA;AACA,CAAC,WAAW,GAAG,CAAC,GAAG;AACnB,EAAE,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC1D,EAAE;AACF;AACA,CAAC,OAAO,QAAQ,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE;AAC7B,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,GAAG,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACxB,GAAG,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;AACjB,GAAG;AACH;AACA,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B;AACA,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,KAAK,EAAE;AACxD,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;AAC5B;AACA;AACA,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE;AAC/C,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAChC,IAAI;AACJ,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,YAAY,EAAE;AACrC,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,YAAY,UAAU,EAAE;AAC7C,GAAG,OAAO,KAAK,CAAC;AAChB,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B;AACA,EAAE,IAAI,OAAO,KAAK,QAAQ,EAAE;AAC5B;AACA,GAAG,IAAI,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;AACtD;AACA,GAAG,IAAI,CAAC,GAAG,EAAE;AACb,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,IAAI;AACJ;AACA,GAAG,OAAO,GAAG,CAAC;AACd,GAAG;AACH;AACA,EAAE,IAAI,YAAY,CAAC,MAAM,EAAE;AAC3B,GAAG,OAAO,UAAU,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;AAC7D,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,OAAO,YAAY,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE;AACzC,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,EAAE,IAAI,KAAK,EAAE,KAAK,CAAC;AACnB;AACA,EAAE,IAAI,SAAS,KAAK,QAAQ,EAAE;AAC9B,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC1B;AACA,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,IAAI;AACJ,QAAQ;AACR;AACA,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAC7B,IAAI;AACJ,GAAG;AACH,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC/B,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,GAAG;AACH,OAAO;AACP;AACA,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AACrB,GAAG,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC;AACvB,GAAG;AACH;AACA,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC;AACA,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,GAAG,KAAK,GAAG,YAAY,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,uEAAuE,CAAC,CAAC,CAAC;AAC5I,GAAG;AACH;AACA,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B;AACA,EAAE,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,IAAI,CAAC,EAAE;AACtE;AACA,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;AAClD;AACA,GAAG,IAAI,IAAI,EAAE;AACb,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,IAAI;AACJ,GAAG;AACH;AACA,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC;AACA,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;AAC5C;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE;AAC/B,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC/B;AACA,GAAG,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,eAAe,IAAI,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,eAAe,EAAE;AAC7F,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;AAC1C,IAAI;AACJ;AACA,GAAG,CAAC,EAAE,CAAC;AACP,GAAG;AACH;AACA,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACvI,EAAE;AACF;AACA,CAAC,OAAO,cAAc,GAAG;AACzB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,CAAC;AACH,CAAC;AACD;AACA,SAAS,OAAO,EAAE,KAAK,EAAE;AACzB,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACnB;AACA,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG;AAClC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACd,EAAE;AACF;AACA,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC;AACD;AACA,SAAS,aAAa,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE;AAC/C,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;AAC5C,EAAE,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;AAC7B,EAAE,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC;AAC1B;AACA;AACA,EAAE,MAAM,CAAC,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACzD;AACA,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK;AACxE;AACA,GAAG,IAAI,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C;AACA,GAAG,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,CAAC;AACzD,GAAG,IAAI,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC;AAC/C;AACA;AACA,GAAG,IAAI,UAAU,IAAI,cAAc,EAAE;AACrC,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACvB,IAAI,MAAM,GAAG,GAAG,CAAC;AACjB,IAAI;AACJ,QAAQ,IAAI,UAAU,IAAI,SAAS,EAAE;AACrC,IAAI,MAAM,GAAG,KAAK,CAAC;AACnB,IAAI;AACJ;AACA,GAAG,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AACxC,GAAG,CAAC,CAAC;AACL;AACA,EAAE,MAAM,CAAC,eAAe,GAAG,CAAC,MAAM,EAAE,SAAS,KAAK;AAClD,GAAG,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC/B,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACvD;AACA,IAAI,IAAI,SAAS,IAAI,OAAO,EAAE;AAC9B,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AACzC,KAAK;AACL;AACA,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AACtD;AACA,IAAI,OAAO,CAAC,CAAC;AACb,IAAI,CAAC,CAAC;AACN,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,OAAO,MAAM,CAAC;AACf;;ACrbA,cAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,SAAS;AACd,CAAC,IAAI,EAAE,SAAS;AAChB,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAChB,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAChB,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAChB,EAAE;AACF,CAAC,KAAK,EAAE,KAAK;AACb,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,GAAG,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;AAC1B,GAAG;AACH,EAAE;AACF,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;AACjB,CAAC,CAAC;;ACZF;AACA;AACA;AACA;AACe,MAAM,aAAa,SAAS,UAAU,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE;AACvB,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACvB,GAAG,OAAO,CAAC,MAAM,GAAG;AACpB,IAAI,CAAC,EAAE;AACP,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAClB,KAAK,IAAI,EAAE,KAAK;AAChB,KAAK;AACL,IAAI,CAAC,EAAE;AACP,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAClB,KAAK,IAAI,EAAE,OAAO;AAClB,KAAK;AACL,IAAI,CAAC,EAAE;AACP,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAClB,KAAK,IAAI,EAAE,MAAM;AACjB,KAAK;AACL,IAAI,CAAC;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACrB,GAAG,OAAO,CAAC,IAAI,GAAGC,OAAO,CAAC;AAC1B,GAAG;AACH;AACA,EAAE,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE;AAC5C,GAAG,OAAO,CAAC,MAAM,KAAK,GAAG,IAAI;AAC7B,IAAI,IAAI,GAAG,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACxC;AACA,KAAK,GAAG,GAAGL,OAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACnD,KAAK;AACL;AACA,IAAI,OAAO,GAAG,CAAC;AACf,IAAI,CAAC;AACL;AACA,GAAG,OAAO,CAAC,QAAQ,KAAK,GAAG,IAAI;AAC/B,IAAI,GAAG,GAAGA,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAClD,IAAI,OAAO,gBAAgB,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AACpD,IAAI,CAAC;AACL,GAAG;AACH;AACA,EAAE,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;AACjC;AACA,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AACjB,EAAE;AACF;;AC5DA;AACA;AACA;AACA;AACA;AACA;AACe,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE;AAC9C,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;AACA,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AAC1C;AACA,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AAC9B,EAAE;AACF;AACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B;;ACfe,SAAS,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AAC1C,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;AACA,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACnC,CAAC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB;;ACPe,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;AACtD,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;AACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC,OAAO,KAAK,CAAC;AACd,CAAC;AACD;AACA,MAAM,CAAC,OAAO,GAAG,OAAO;;ACJxB;AACe,SAAS,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;AACjD,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;AACA,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAChE;AACA,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC5B,EAAE,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;AACxB,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,GAAG;AACH,EAAE;AACF,MAAM;AACN,EAAE,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;AACnC,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACnC,GAAG;AACH;AACA,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAClE,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACpC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACxB,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAC/B,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC;AACd,CAAC;AACD;AACA,GAAG,CAAC,OAAO,GAAG,OAAO;;AC5BrB,cAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,SAAS;AACd,CAAC,IAAI,EAAE,SAAS;AAChB,CAAC,KAAK,EAAE,KAAK;AACb,CAAC,IAAI,EAAEK,OAAO;AACd,CAAC,QAAQ,EAAE,MAAM,IAAIL,OAAK,CAACK,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AACxD,CAAC,MAAM,EAAE,MAAM,IAAIL,OAAK,CAAC,KAAK,EAAEK,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;AACtD,CAAC,CAAC;;ACPF;AACA,MAAMD,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACtB,MAAME,IAAE,GAAG,EAAE,GAAG,GAAG,CAAC;AACpB,MAAMC,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB;AACA,IAAIC,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC;AACvB;AACA,UAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,KAAK;AACV,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE;AACF;AACA;AACA;AACA,QAACA,OAAK;AACN;AACA,CAAC,IAAI,EAAEC,OAAO;AACd;AACA;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,GAAGD,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD;AACA;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,GAAGJ,GAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAACG,GAAC,GAAG,KAAK,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;AAClF;AACA,EAAE,OAAO;AACT,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;AACpB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE;AACF;AACA;AACA;AACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd;AACA,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAC7B;AACA;AACA,EAAE,IAAI,GAAG,GAAG;AACZ,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKD,IAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIC,GAAC;AACzE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAC;AAC9D,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKD,IAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIC,GAAC;AACzE,GAAG,CAAC;AACJ;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,GAAGC,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;AACxG,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACzEK,SAAS,SAAS,EAAE,KAAK,EAAE;AAClC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACpC,CAAC;AACD;AACO,SAAS,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;AACrC,CAAC,IAAI,GAAG,KAAK,KAAK,EAAE;AACpB,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACF;AACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACtC;AACA,CAAC,IAAI,SAAS,GAAG,EAAE,GAAG,EAAE,CAAC;AACzB;AACA,CAAC,IAAI,GAAG,KAAK,YAAY,EAAE;AAC3B,EAAE,IAAI,SAAS,GAAG,CAAC,EAAE;AACrB,GAAG,EAAE,IAAI,GAAG,CAAC;AACb,GAAG;AACH,EAAE;AACF,MAAM,IAAI,GAAG,KAAK,YAAY,EAAE;AAChC,EAAE,IAAI,SAAS,GAAG,CAAC,EAAE;AACrB,GAAG,EAAE,IAAI,GAAG,CAAC;AACb,GAAG;AACH,EAAE;AACF,MAAM,IAAI,GAAG,KAAK,QAAQ,EAAE;AAC5B,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS,IAAI,SAAS,GAAG,GAAG,EAAE;AAC3C,GAAG,IAAI,SAAS,GAAG,CAAC,EAAE;AACtB,IAAI,EAAE,IAAI,GAAG,CAAC;AACd,IAAI;AACJ,QAAQ;AACR,IAAI,EAAE,IAAI,GAAG,CAAC;AACd,IAAI;AACJ,GAAG;AACH,EAAE;AACF,MAAM,IAAI,GAAG,KAAK,SAAS,EAAE;AAC7B,EAAE,IAAI,SAAS,GAAG,GAAG,EAAE;AACvB,GAAG,EAAE,IAAI,GAAG,CAAC;AACb,GAAG;AACH,OAAO,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE;AAC7B,GAAG,EAAE,IAAI,GAAG,CAAC;AACb,GAAG;AACH,EAAE;AACF;AACA,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACjB;;ACvCA,UAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,KAAK;AACV,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,QAAQ;AACjB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAEE,GAAG;AACV,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;AACjB;AACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC1C,GAAG,GAAG,GAAG,GAAG,CAAC;AACb,GAAG;AACH,OAAO;AACP,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,OAAO;AACT,GAAG,CAAC;AACJ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,GAAGC,SAAc,CAAC,GAAG,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd;AACA,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;AACrC;AACA,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;AAClB,GAAG,MAAM,GAAG,CAAC,CAAC;AACd,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;AAClB,GAAG,GAAG,GAAG,CAAC,CAAC;AACX,GAAG;AACH,EAAE,OAAO;AACT,GAAG,SAAS;AACZ,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AACzC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AACzC,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,oBAAoB,CAAC;AACvF,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;AC7DF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,OAAO,GAAG,EAAE,IAAI,CAAC,CAAC;AACxB,MAAMC,GAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAClB,MAAM,GAAG,GAAG,GAAG,GAAGA,GAAC,CAAC;AACpB,MAAMC,KAAG,GAAGD,GAAC,GAAG,GAAG,CAAC;AACpB;AACA,SAAS,IAAI,EAAE,CAAC,EAAE;AAClB;AACA;AACA,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC7B;AACA,CAAC,OAAO,EAAE,CAAC;AACX,CAAC;AACD;AACe,mBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;AACvE,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;AACb,EAAE,EAAE,GAAG,CAAC,CAAC;AACT,EAAE;AACF,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;AACb,EAAE,EAAE,GAAG,CAAC,CAAC;AACT,EAAE;AACF;AACA,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC1B;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AACrB;AACA,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACpD;AACA;AACA;AACA,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AAC3B,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AAC3B;AACA;AACA,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/C,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAClE,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAClE;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;AACb,EAAE,EAAE,IAAI,CAAC,GAAGA,GAAC,CAAC;AACd,EAAE;AACF,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;AACb,EAAE,EAAE,IAAI,CAAC,GAAGA,GAAC,CAAC;AACd,EAAE;AACF;AACA,CAAC,EAAE,IAAI,GAAG,CAAC;AACX,CAAC,EAAE,IAAI,GAAG,CAAC;AACX;AACA;AACA,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB,CAAC,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC;AAC1B;AACA;AACA,CAAC,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AACpB,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC,IAAI,EAAE,CAAC;AACR;AACA,CAAC,IAAI,MAAM,GAAG,MAAM,KAAK,CAAC,EAAE;AAC5B,EAAE,EAAE,GAAG,CAAC,CAAC;AACT,EAAE;AACF,MAAM,IAAI,IAAI,IAAI,GAAG,EAAE;AACvB,EAAE,EAAE,GAAG,KAAK,CAAC;AACb,EAAE;AACF,MAAM,IAAI,KAAK,GAAG,GAAG,EAAE;AACvB,EAAE,EAAE,GAAG,KAAK,GAAG,GAAG,CAAC;AACnB,EAAE;AACF,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE;AACxB,EAAE,EAAE,GAAG,KAAK,GAAG,GAAG,CAAC;AACnB,EAAE;AACF,MAAM;AACN,EAAE,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;AAChD,EAAE;AACF;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGC,KAAG,GAAG,CAAC,CAAC,CAAC;AAClE;AACA;AACA,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3B,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;AACnC,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA,CAAC,IAAI,KAAK,CAAC;AACX,CAAC,IAAI,MAAM,GAAG,MAAM,KAAK,CAAC,EAAE;AAC5B,EAAE,KAAK,GAAG,IAAI,CAAC;AACf,EAAE;AACF,MAAM,IAAI,IAAI,IAAI,GAAG,EAAE;AACvB,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;AACnB,EAAE;AACF,MAAM,IAAI,IAAI,GAAG,GAAG,EAAE;AACtB,EAAE,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC;AAC3B,EAAE;AACF,MAAM;AACN,EAAE,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC;AAC3B,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;AAC7B,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AACpD;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AAC5B;AACA;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE,KAAKA,KAAG,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,UAAUA,KAAG,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,KAAKA,KAAG,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,EAAE,IAAIA,KAAG,CAAC,CAAC,CAAC;AAClD;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;AACrD,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAG,CAAC,GAAG,EAAE,CAAC;AAC3C;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAChD,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB;AACA;;AC9KA;AACA;AACA,MAAMC,YAAU,GAAG;AACnB,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AAChE,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,GAAG,kBAAkB,EAAE;AAChE,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,GAAG,kBAAkB,EAAE;AAChE,CAAC,CAAC;AACF;AACA,MAAMC,YAAU,GAAG;AACnB,CAAC,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;AAClE,CAAC,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AAClE,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;AAClE,CAAC,CAAC;AACF,MAAM,UAAU,GAAG;AACnB,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AACjE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;AACjE,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AACjE,CAAC,CAAC;AACF;AACA,MAAM,UAAU,GAAG;AACnB,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,EAAE;AACjE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AACjE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AACjE,CAAC,CAAC;AACF;AACA,YAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,OAAO;AACZ,CAAC,IAAI,EAAE,OAAO;AACd,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE;AACF;AACA;AACA,CAAC,KAAK,EAAE,KAAK;AACb,CAAC,IAAI,EAAEV,OAAO;AACd,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA,EAAE,IAAI,GAAG,GAAG,gBAAgB,CAACS,YAAU,EAAE,GAAG,CAAC,CAAC;AAC9C;AACA;AACA,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C;AACA,EAAE,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC5C;AACA,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;AAChB;AACA,EAAE,IAAI,IAAI,GAAG,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACjD;AACA;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;AACtC;AACA,EAAE,OAAO,gBAAgB,CAACC,YAAU,EAAE,GAAG,CAAC,CAAC;AAC3C,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,OAAO,EAAE;AACX,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;AACxG,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACzEF;AACA;AACA;AAGA;AACe,iBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;AACxC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/C;;ACfA,MAAMZ,GAAC,GAAG,OAAO,CAAC;AAClB;AACA;AACA;AACA;AACA;AACe,SAAS,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,OAAO,GAAGA,GAAC,CAAC,GAAG,EAAE,EAAE;AACnE,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;AACA,CAAC,IAAI,CAAC,KAAK,EAAE;AACb,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AACtB,EAAE;AACF;AACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC3B;AACA,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5B,EAAE,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7B,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AACzC;;ACxBe,SAAS,KAAK,EAAE,KAAK,EAAE;AACtC,CAAC,OAAO;AACR,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK;AACpB,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE;AAC9B,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK;AACpB,EAAE,CAAC;AACH;;ACJA;AACA;AACA;AACe,SAAS,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE;AACjE,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B;AACA;AACA,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC;AACA,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,KAAK;AACjD,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;AAC9B,GAAG,OAAO,GAAG,CAAC;AACd,GAAG;AACH;AACA,EAAE,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9B,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACR;;ACjBe,SAAS,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;AACjD;AACA,CAAC,OAAO,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACvC;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAClB,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AACpB;AACe,kBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;AAC7D,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9C,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;AACb,EAAE,EAAE,GAAG,CAAC,CAAC;AACT,EAAE;AACF,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;AACb,EAAE,EAAE,GAAG,CAAC,CAAC;AACT,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB;AACA,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClB;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;AAChB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;AACf,EAAE,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;AAC5C,EAAE;AACF;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC;AACtD;AACA;AACA,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AACvB,EAAE,EAAE,GAAG,CAAC,CAAC;AACT,EAAE;AACF;AACA,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AAC7B,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;AACxD,EAAE;AACF,MAAM;AACN,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;AACvD,EAAE;AACF;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC1B,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AACrC,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC;AACA;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB;AACA,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB;AACA;;AC9GA,MAAMa,IAAE,GAAG,GAAG,CAAC;AACf;AACA,kBAAe,IAAI,UAAU,CAAC;AAC9B;AACA;AACA;AACA;AACA,CAAC,EAAE,EAAE,aAAa;AAClB,CAAC,KAAK,EAAE,eAAe;AACvB,CAAC,IAAI,EAAE,kBAAkB;AACzB,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;AACxB,GAAG,IAAI,EAAE,IAAI;AACb,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AACvB,GAAG,IAAI,EAAE,IAAI;AACb,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;AACzB,GAAG,IAAI,EAAE,IAAI;AACb,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAEZ,OAAO;AACd,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGY,IAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5C,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;AACjB;AACA,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGA,IAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9C,EAAE;AACF,CAAC,CAAC;;ACnCF,MAAMC,GAAC,GAAG,IAAI,CAAC;AACf,MAAM,CAAC,GAAG,IAAI,CAAC;AACf,MAAMC,GAAC,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3B,MAAMC,MAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC;AAC9B,MAAMC,IAAE,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5B,MAAMC,IAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3B,MAAMC,IAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3B,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC;AACrC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AAChB,MAAM,EAAE,GAAG,sBAAsB,CAAC;AAClC;AACA,MAAM,WAAW,GAAG;AACpB,CAAC,GAAG,UAAU,EAAE,QAAQ,GAAG,SAAS,EAAE;AACtC,CAAC,EAAE,CAAC,SAAS,GAAG,QAAQ,GAAG,SAAS,EAAE;AACtC,CAAC,EAAE,CAAC,SAAS,GAAG,QAAQ,GAAG,SAAS,EAAE;AACtC,CAAC,CAAC;AACF;AACA,MAAM,WAAW,GAAG;AACpB,CAAC,GAAG,kBAAkB,GAAG,CAAC,kBAAkB,GAAG,iBAAiB,IAAI;AACpE,CAAC,GAAG,mBAAmB,GAAG,kBAAkB,EAAE,CAAC,mBAAmB,EAAE;AACpE,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,GAAG;AACpE,CAAC,CAAC;AACF,MAAM,WAAW,GAAG;AACpB,CAAC,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,SAAS;AACpC,CAAC,GAAG,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,EAAE;AACpC,CAAC,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC,QAAQ,EAAE;AACpC,CAAC,CAAC;AACF;AACA,MAAM,WAAW,GAAG;AACpB,CAAC,EAAE,CAAC,oBAAoB,kBAAkB,IAAI,mBAAmB,EAAE;AACnE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,CAAC,mBAAmB,EAAE;AACnE,CAAC,EAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,GAAG;AACnE,CAAC,CAAC;AACF;AACA,aAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,QAAQ;AACb,CAAC,IAAI,EAAE,QAAQ;AACf,CAAC,MAAM,EAAE;AACT,EAAE,EAAE,EAAE;AACN,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnB,GAAG,IAAI,EAAE,IAAI;AACb,GAAG;AACH,EAAE,EAAE,EAAE;AACN,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE,EAAE,EAAE;AACN,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,WAAW;AAClB,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC;AAC3B;AACA;AACA,EAAE,IAAI,EAAE,GAAG,CAACL,GAAC,GAAG,EAAE,KAAK,CAACA,GAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AACrC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AACrC;AACA;AACA,EAAE,IAAI,GAAG,GAAG,gBAAgB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1D;AACA;AACA,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;AACtC,GAAG,IAAI,GAAG,GAAGG,IAAE,IAAIC,IAAE,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKH,GAAC,CAAC,CAAC,CAAC;AAC9C,GAAG,IAAI,KAAK,GAAG,CAAC,IAAII,IAAE,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKJ,GAAC,CAAC,CAAC,CAAC;AAC/C;AACA,GAAG,OAAO,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,CAAC;AAC9B,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAC3D;AACA;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;AAChD,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;AACjB,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/C;AACA;AACA,EAAE,IAAI,KAAK,GAAG,gBAAgB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC5D;AACA;AACA,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AACrC,GAAG,IAAI,GAAG,IAAIE,IAAE,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;AAClC,GAAG,IAAI,KAAK,GAAG,CAACE,IAAE,IAAI,GAAG,IAAI,IAAI,CAAC,IAAID,IAAE,CAAC;AACzC,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKF,MAAI,CAAC,CAAC;AAC3C;AACA,GAAG,QAAQ,CAAC,EAAE;AACd,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,gBAAgB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AAC1D;AACA;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAACF,GAAC,GAAG,CAAC,IAAI,EAAE,CAAC,IAAIA,GAAC,CAAC;AACrC,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;AACrC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACxB,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV;AACA,EAAE,OAAO,EAAE;AACX,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;AACxG,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACjHF,aAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,QAAQ;AACb,CAAC,IAAI,EAAE,QAAQ;AACf,CAAC,MAAM,EAAE;AACT,EAAE,EAAE,EAAE;AACN,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnB,GAAG,IAAI,EAAE,IAAI;AACb,GAAG;AACH,EAAE,EAAE,EAAE;AACN,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnB,GAAG,IAAI,EAAE,QAAQ;AACjB,GAAG;AACH,EAAE,EAAE,EAAE;AACN,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,MAAM;AACb,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;AACnB;AACA,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC;AAC5B,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC;AACnB;AACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;AAC5C,GAAG,GAAG,GAAG,GAAG,CAAC;AACb,GAAG;AACH,OAAO;AACP,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,OAAO;AACT,GAAG,EAAE;AACL,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC/B,GAAGP,SAAc,CAAC,GAAG,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;AACjB;AACA;AACA,EAAE,OAAO;AACT,GAAG,MAAM,CAAC,CAAC,CAAC;AACZ,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AAClD,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AAClD,GAAG,CAAC;AACJ,EAAE;AACF,CAAC,CAAC;;ACjDF;AACA;AACA;AACA;AACA;AACA;AACA;AACe,iBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;AACxC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1C,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC3C;AACA;AACA;AACA,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AACpB,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AACpB;AACA;AACA,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;AACjD;AACA,EAAE,GAAG,GAAG,CAAC,CAAC;AACV,EAAE,GAAG,GAAG,CAAC,CAAC;AACV,EAAE;AACF,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC7B;AACA,EAAE,GAAG,GAAG,GAAG,CAAC;AACZ,EAAE;AACF,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC7B,EAAE,GAAG,GAAG,GAAG,CAAC;AACZ,EAAE;AACF;AACA,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AACpB,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1E;AACA,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/C;;ACtCA,MAAMU,IAAE,GAAG,IAAI,GAAG,IAAI,CAAC;AACvB,MAAMC,IAAE,GAAG,IAAI,GAAG,GAAG,CAAC;AACtB,MAAMC,IAAE,GAAG,IAAI,GAAG,GAAG,CAAC;AACtB,MAAMC,IAAE,GAAG,IAAI,GAAG,KAAK,CAAC;AACxB,MAAM,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC;AACrB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC;AACzB,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;AACtB;AACA;AACA;AACA,MAAM,UAAU,GAAG;AACnB,CAAC,GAAG,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AAClE,CAAC,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,EAAE;AAClE,CAAC,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,EAAE;AAClE,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,GAAG;AACnB,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,CAAC,OAAO;AAC9C,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;AAC9C,CAAC,EAAE,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE;AAC9C,CAAC,CAAC;AACF;AACA;AACA,MAAM,UAAU,GAAG;AACnB,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,EAAE;AACjE,CAAC,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AACjE,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AACjE,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,GAAG;AACnB,CAAC,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;AAClE,CAAC,GAAG,kBAAkB,GAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAE;AAClE,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,kBAAkB,EAAE;AAClE,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,OAAO;AACZ,CAAC,IAAI,EAAE,OAAO;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnB,GAAG,IAAI,EAAE,GAAG;AACZ,GAAG;AACH,EAAE,EAAE,EAAE;AACN,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG,IAAI,EAAE,IAAI;AACb,GAAG;AACH,EAAE,EAAE,EAAE;AACN,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG,IAAI,EAAE,IAAI;AACb,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,WAAW;AAClB,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA,EAAE,IAAI,GAAG,GAAG,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC9C;AACA,EAAE,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;AACzB,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;AAChB,EAAE,IAAI,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAC9B;AACA,EAAE,OAAO,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC3C,EAAE;AACF,CAAC,CAAC,CAAC;AACH;AACA,SAAS,UAAU,EAAE,GAAG,EAAE;AAC1B;AACA;AACA,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;AACrC,EAAE,IAAI,GAAG,GAAGH,IAAE,IAAIC,IAAE,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKE,IAAE,CAAC,CAAC,CAAC;AAC9C,EAAE,IAAI,KAAK,GAAG,CAAC,IAAID,IAAE,IAAI,CAAC,GAAG,GAAG,KAAK,KAAKC,IAAE,CAAC,CAAC,CAAC;AAC/C;AACA,EAAE,OAAO,CAAC,GAAG,GAAG,KAAK,MAAM,EAAE,CAAC;AAC9B,EAAE,CAAC,CAAC;AACJ;AACA;AACA,CAAC,OAAO,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAC5C,CAAC;AACD;AACA,SAAS,UAAU,EAAE,KAAK,EAAE;AAC5B,CAAC,IAAI,KAAK,GAAG,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACjD;AACA;AACA,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;AACrC,EAAE,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAIH,IAAE,EAAE,CAAC,CAAC,CAAC;AAC5C,EAAE,IAAI,KAAK,IAAIC,IAAE,IAAIC,IAAE,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,EAAE,OAAO,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC;AACxC,EAAE,CAAC,CAAC;AACJ;AACA,CAAC,OAAO,GAAG,CAAC;AACZ;;ACjIA;AACA;AACA;AACA;AACe,kBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;AACxC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACnF;;ACjBA,MAAMf,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC;AACzB,MAAM,WAAW,GAAG,IAAI,CAAC;AACzB,MAAM,cAAc,GAAG,CAAC,GAAG,WAAW,CAAC;AACvC,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACxB;AACA,MAAM,KAAK,GAAG;AACd,CAAC,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC,QAAQ,EAAE;AACpC,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE;AACpC,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE;AACpC,CAAC,CAAC;AACF;AACA,MAAM,QAAQ,GAAG;AACjB,CAAC,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;AAC/D,CAAC,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,oBAAoB,CAAC;AACjE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;AAClE,CAAC,CAAC;AACF;AACA,MAAM,EAAE,GAAG;AACX,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACtB,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC;AACxB,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC;AACzB,CAAC,CAAC;AACF;AACA,MAAM,WAAW,GAAG;AACpB,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC;AACxB,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;AACtB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AACtB,CAAC,CAAC;AACF;AACA,MAAM,UAAU,GAAG;AACnB;AACA,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;AAC1C,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACrC,CAAC,CAAC;AACF;AACA,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;AAC9B,MAAMiB,SAAO,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AAC9B;AACO,SAASzB,OAAK,EAAE,MAAM,EAAE,EAAE,EAAE;AACnC,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;AAC9B,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,WAAW,CAAC,CAAC;AACvD,EAAE,OAAO,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AAC5C,EAAE,CAAC,CAAC;AACJ,CAAC,OAAO,IAAI,CAAC;AACb,CAAC;AACD;AACO,SAAS,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;AACtC,CAAC,MAAM,QAAQ,GAAG,GAAG,GAAG,EAAE,IAAI,KAAK,IAAI,cAAc,CAAC,CAAC;AACvD,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI;AACzB,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,EAAE,OAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,EAAE,CAAC,CAAC;AACJ,CAAC;AACD;AACO,SAAS,aAAa,EAAE,CAAC,EAAE;AAClC,CAAC,IAAI,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,IAAI,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC5B,EAAE,EAAE,IAAI,GAAG,CAAC;AACZ,EAAE;AACF;AACA,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AAC5C,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B;AACA,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAC1B,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;AAChD,CAAC;AACD;AACO,SAAS,gBAAgB,EAAE,CAAC,EAAE;AACrC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;AAClC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;AACjC,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;AACf,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD;AACA,CAAC,OAAO,SAAS;AACjB,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG;AAC9C,GAAG,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,EAAE,CAAC;AACH,CAAC;AACD;AACO,SAAS,WAAW;AAC3B,CAAC,QAAQ;AACT,CAAC,iBAAiB;AAClB,CAAC,mBAAmB;AACpB,CAAC,QAAQ;AACT,CAAC,WAAW;AACZ,EAAE;AACF;AACA,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;AAChB;AACA,CAAC,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,CAAC,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;AAChC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;AACjB,EAAE,CAAC,CAAC;AACJ;AACA;AACA,CAAC,GAAG,CAAC,EAAE,GAAG,iBAAiB,CAAC;AAC5B;AACA,CAAC,GAAG,CAAC,EAAE,GAAG,mBAAmB,CAAC;AAC9B;AACA,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB;AACA;AACA,CAAC,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC5C;AACA;AACA,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACtC,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACtB;AACA,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAChC,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACnB;AACA;AACA,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5E,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC;AAC7B;AACA,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACrB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACnB;AACA;AACA;AACA,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW;AACvB,EAAE,CAAC;AACH,EAAE,IAAI,CAAC,GAAG;AACV,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACjE,GAAG,CAAC;AACJ,GAAG,CAAC;AACJ,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI;AAC1B,EAAE,OAAO,WAAW,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,EAAE,CAAC,CAAC;AACJ,CAAC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI;AACjC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACf,EAAE,CAAC,CAAC;AACJ;AACA;AACA,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAClC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,CAAC,CAAC;AACJ,CAAC,MAAM,KAAK,GAAGA,OAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AACpC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE;AACA;AACA;AACA,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC;AACD;AACA;AACA,MAAM0B,mBAAiB,GAAG,WAAW;AACrC,CAAClB,OAAK;AACN,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE;AACvB,CAAC,SAAS;AACV,CAAC,KAAK;AACN,CAAC,CAAC;AACF;AACO,SAAS,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE;AACvC;AACA;AACA;AACA,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE;AAC3D,EAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACtE,EAAE;AACF;AACA,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE;AACrF,EAAE,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AAC3E,EAAE;AACF;AACA;AACA,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE;AAC3D,EAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACtE,EAAE;AACF;AACA;AACA,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,KAAK,GAAG,EAAE;AACzC,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,EAAE;AACF;AACA;AACA,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC;AAChB,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;AAC5B,EAAE,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGiB,SAAO,CAAC;AACtC,EAAE;AACF,MAAM;AACN,EAAE,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGA,SAAO,CAAC;AAC7C,EAAE;AACF;AACA,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B;AACA;AACA,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC;AACjB,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;AAC5B,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACrC,EAAE;AACF,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;AACjC,EAAE,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;AAC/D,EAAE;AACF;AACA;AACA,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC;AACjB,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;AAC5B,EAAE,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;AAC1B,EAAE;AACF,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;AACjC,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,CAAC;AACzC,EAAE;AACF,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;AACjC,EAAE,KAAK,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACzD,EAAE;AACF,CAAC,MAAM,CAAC,GAAG,IAAI;AACf,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;AACvD,EAAE,EAAE,GAAG,CAAC;AACR,EAAE,CAAC;AACH;AACA;AACA,CAAC,MAAM,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9C;AACA;AACA,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;AACA;AACA,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC7C,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC;AACxB,CAAC,MAAM,CAAC;AACR,EAAE,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AACnB,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;AACjD,EAAE,CAAC;AACH,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACpB,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACpB;AACA;AACA,CAAC,MAAM,KAAK,GAAG,OAAO;AACtB,EAAE,gBAAgB,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI;AAC5C,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACvB,GAAG,CAAC;AACJ,EAAE,GAAG,CAAC,EAAE;AACR,EAAE,CAAC;AACH,CAAC,OAAO,gBAAgB;AACxB,EAAE,QAAQ;AACV,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACtB,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI;AACZ,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;AACjB,EAAE,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACO,SAAS,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE;AACtC;AACA,CAAC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;AAChC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;AACjB,EAAE,CAAC,CAAC;AACJ,CAAC,MAAM,IAAI,GAAGzB,OAAK;AACnB,EAAE,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAChD,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,GAAG,CAAC,EAAE;AACR,EAAE,CAAC;AACH;AACA;AACA,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACpD,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACjD,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACrD;AACA;AACA,CAAC,MAAM,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9C;AACA,CAAC,MAAM,CAAC;AACR,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG;AAC7B,EAAE,IAAI;AACN,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK;AAC7C,GAAG;AACH,EAAE,CAAC;AACH,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC3E;AACA;AACA,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D;AACA,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD;AACA;AACA,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAChC;AACA;AACA,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;AAC3D;AACA;AACA,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AACzB;AACA;AACA,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;AAC1B;AACA;AACA,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;AACrC;AACA;AACA,CAAC,MAAM,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5B;AACA;AACA,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D;AACA;AACA;AACA,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,WAAW;AAChB,CAAC,KAAK,EAAE,aAAa;AACrB,CAAC,IAAI,EAAE,WAAW;AAClB,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,GAAG;AACZ,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AACvB,GAAG,IAAI,EAAE,cAAc;AACvB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,OAAO;AACd;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE0B,mBAAiB,CAAC,CAAC;AAChD,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACrC,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;AAChB,EAAE,OAAO,SAAS;AAClB,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1C,GAAGA,mBAAiB;AACpB,GAAG,CAAC;AACJ,EAAE;AACF,CAAC,CAAC;;ACnWF,MAAMlB,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC;AACzB,MAAMJ,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACtB,MAAMG,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB;AACA,SAAS,OAAO,EAAE,CAAC,EAAE;AACrB;AACA;AACA,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAGH,GAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAACG,GAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AACxD,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,IAAI,IAAI,CAAC;AAC5B,CAAC;AACD;AACA,SAAS,SAAS,EAAE,KAAK,EAAE;AAC3B;AACA;AACA,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGA,GAAC,CAAC;AACnE,CAAC;AACD;AACA,SAAS,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;AACxB,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;AACd,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACX;AACA;AACA,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACd,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,EAAE;AACF;AACA;AACA,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACtB;AACA;AACA;AACA,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACZ,EAAE,CAAC,GAAG,mBAAmB,GAAG,CAAC,IAAI,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAChF,EAAE;AACF,MAAM;AACN,EAAE,CAAC,GAAG,qBAAqB,GAAG,CAAC,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AACpF,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC;AACzB,CAAC,MAAM,YAAY,GAAG,EAAE,CAAC;AACzB;AACA,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC;AACjB,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;AAErB;AACA;AACA,CAAC,OAAO,OAAO,IAAI,YAAY,EAAE;AACjC,EAAE,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3C;AACA;AACA;AACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC,EAAE,IAAI,KAAK,GAAG,IAAI,EAAE;AACpB,GAAG,IAAI,KAAK,IAAI,SAAS,EAAE;AAC3B,IAAI,OAAO,GAAG,CAAC;AACf,IAAI;AAEJ,GAAG,IAAI,GAAG,KAAK,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C;AACA,EAAE,OAAO,IAAI,CAAC,CAAC;AACf,EAAE;AACF;AACA;AACA;AACA,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3C,CAAC;AACD;AACA,SAAS,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;AAC1B;AACA;AACA,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;AAChB,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,EAAE;AACF,CAAC,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;AAC/C,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,CAAC;AACD;AACA;AACO,MAAM,iBAAiB,GAAG,WAAW;AAC5C,CAACC,OAAK,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC;AACvC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG;AACtB,CAAC,SAAS;AACV,CAAC,KAAK;AACN,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,KAAK;AACV,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,cAAc;AACvB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,MAAM;AACf,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,OAAO;AACd;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,OAAO,KAAK,CAAC,GAAsB,CAAC,CAAC;AACvC,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;AACzC,EAAE;AACF,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,EAAE,EAAE,OAAO;AACd,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;AACvF,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACvJF,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AAC9B,MAAM,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,EAAE,MAAM,EAAE;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACpB,EAAE,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5C,EAAE;AACF;AACA;AACA;AACA;AACA,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzG,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAClC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B;AACA,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,kBAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;AACxC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACpD,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACrD;AACA;AACA;AACA,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACpE;;AChCA,oBAAe;AACf,CAAC,QAAQ;AACT,CAAC,SAAS;AACV,CAAC,UAAU;AACX,CAAC,QAAQ;AACT,CAAC,SAAS;AACV,CAAC,QAAQ;AACT,CAAC,SAAS;AACV,CAAC;;ACXD;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,EAAE,GAAG,EAAE;AAC3B;AACA;AACA,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClE;AACA,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACrD,CAAC;AACD;AACA,MAAM,UAAU,GAAG;AACnB,CAAC,KAAK,EAAE;AACR,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,YAAY,EAAE,KAAK;AACrB,EAAE,eAAe,EAAE,EAAE;AACrB,EAAE;AACF,CAAC,WAAW,EAAE;AACd,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,YAAY,EAAE,KAAK;AACrB,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;AACzD,EAAE;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAAS,OAAO;AAC/B,CAAC,KAAK;AACN,CAAC;AACD,EAAE,MAAM,GAAG,QAAQ,CAAC,aAAa;AACjC,EAAE,KAAK,GAAG,SAAS;AACnB,EAAE,YAAY,GAAG,EAAE;AACnB,EAAE,GAAG,GAAG,CAAC;AACT,EAAE,eAAe,GAAG,EAAE;AACtB,EAAE,GAAG,EAAE;AACP,EAAE;AACF,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;AACA,CAAC,IAAImB,QAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;AAClC,EAAE,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACvB,EAAE;AACF,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AACtB,EAAE;AACF;AACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE;AAC5C,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACF;AACA,CAAC,IAAI,UAAU,CAAC;AAChB,CAAC,IAAI,MAAM,KAAK,KAAK,EAAE;AACvB,EAAE,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AAC5C,EAAE;AACF,MAAM;AACN,EAAE,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;AACnD;AACA,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;AACjE,IAAI,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,eAAe,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,EAAE;AACxE,IAAI;AACJ;AACA;AACA,GAAG,IAAI,EAAE,GAAG,UAAU,CAAC;AACvB,GAAG,IAAI,YAAY,KAAK,EAAE,EAAE;AAC5B,IAAI,KAAK,IAAI,CAAC,IAAI,aAAa,EAAE;AACjC,KAAK,IAAI,QAAQ,GAAG,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;AACpE,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,MAAM;AACZ,MAAM;AACN,KAAK;AACL,IAAI;AACJ;AACA,GAAG,IAAI,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AACtE,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,GAAG,EAAE;AACjC;AACA;AACA,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACnD,KAAK,IAAI,WAAW,GAAG,UAAU,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACxE,KAAK,IAAI,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;AACrE,KAAK,IAAIC,MAAW,CAAC,OAAO,CAAC,EAAE;AAC/B,MAAM,OAAO,GAAG,CAAC,CAAC;AAClB,MAAM;AACN,KAAK,IAAI,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE;AACzC,MAAM,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1E,MAAM;AACN,UAAU,IAAI,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE;AAC9C,MAAM,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACtE,MAAM;AACN,KAAK;AACL;AACA;AACA,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACpD,IAAI,IAAI,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;AACnC,IAAI,IAAI,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC;AAC/B;AACA,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC1C;AACA,IAAI,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACzC,KAAK,IAAIA,MAAW,CAAC,CAAC,CAAC,EAAE;AACzB,MAAM,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAChC,MAAM;AACN,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,MAAM,GAAG,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,CAAC;AACvD,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAClB,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AACzC;AACA,IAAI,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE;AAC3B,KAAK,IAAI,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;AACtC,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;AAC3D,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC3C;AACA,KAAK,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE;AAC3B,MAAM,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AACtC,MAAM;AACN,UAAU;AACV,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AACvC,MAAM;AACN;AACA,KAAK,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;AACjD,KAAK;AACL;AACA,IAAI,UAAU,GAAG,EAAE,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AACxC,IAAI;AACJ,QAAQ;AACR,IAAI,UAAU,GAAG,OAAO,CAAC;AACzB,IAAI;AACJ,GAAG;AACH,OAAO;AACP,GAAG,UAAU,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACjC,GAAG;AACH;AACA,EAAE,IAAI,MAAM,KAAK,MAAM;AACvB;AACA,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AACjD,IAAI;AACJ,GAAG,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACpE;AACA,GAAG,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACvD,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;AAC3B,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC1B,KAAK;AACL;AACA,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;AAC3B,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1B,KAAK;AACL;AACA,IAAI,OAAO,CAAC,CAAC;AACb,IAAI,CAAC,CAAC;AACN,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5B,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAC3C,EAAE;AACF;AACA,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AAClC,CAAC,OAAO,KAAK,CAAC;AACd,CAAC;AACD;AACA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1B;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAG;AACf,CAAC,KAAK,EAAE,EAAE,KAAK,EAAEZ,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;AAC3C,CAAC,KAAK,EAAE,EAAE,KAAK,EAAEA,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;AAC3C,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE;AAClD,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC;AAClB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAClB;AACA,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;AACA,CAAC,IAAI,CAAC,KAAK,EAAE;AACb,EAAE,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACvB,EAAE;AACF;AACA,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC5C;AACA,CAAC,IAAI,KAAK,CAAC,WAAW,EAAE;AACxB,EAAE,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3B,EAAE;AACF;AACA,CAAC,MAAM,YAAY,GAAG,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAC7C,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChC;AACA;AACA,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACb,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxC,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1B,EAAE;AACF,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACb,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxC,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1B,EAAE;AACF;AACA,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;AACjD,EAAE,OAAO,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AACjC,EAAE;AACF;AACA,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE;AACxB,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACtC,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAClD,EAAE,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;AAC5D,GAAG,IAAI,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AACtC,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACjD,IAAI,OAAOa,KAAU,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACvC,IAAI;AACJ,GAAG,OAAO,KAAK,CAAC;AAChB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,SAAS,CAAC;AACnB,EAAE;AACF,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AACb,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC;AACxB,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AACnC,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B;AACA,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACpC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;AACd,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF;AACA,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE;AACzB,EAAE,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;AACjC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AAC7B,EAAE,IAAI,WAAW,IAAI,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;AAC5D,GAAG,GAAG,GAAG,MAAM,CAAC;AAChB,GAAG;AACH,OAAO;AACP,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3B,GAAG,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAClC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE;AAChB,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG;AACvB,KAAK,MAAM;AACX,KAAK;AACL,SAAS;AACT,KAAK,WAAW,GAAG,KAAK,CAAC;AACzB,KAAK,GAAG,GAAG,MAAM,CAAC;AAClB,KAAK;AACL,IAAI;AACJ,QAAQ;AACR,IAAI,GAAG,GAAG,MAAM,CAAC;AACjB,IAAI;AACJ,GAAG;AACH,EAAE;AACF,CAAC,OAAO,OAAO,CAAC;AAChB;;ACjTA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAAS,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE;AAC1D,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B;AACA,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/C;AACA,CAAC,IAAI,OAAO,EAAE;AACd,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,OAAO,KAAK,IAAI,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC;AAC7D,EAAE;AACF;AACA,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC;AACD;AACA,EAAE,CAAC,OAAO,GAAG,OAAO;;ACjBpB;AACA;AACA;AACA;AACA;AACA;AACe,SAAS,SAAS,EAAE,KAAK,EAAE;AAC1C,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS;AAC/B,CAAC,MAAM,GAAG,SAAS;AACnB,UAACC,SAAO,GAAG,IAAI;AACf,CAAC,GAAG,aAAa;AACjB,CAAC,GAAG,EAAE,EAAE;AACR,CAAC,IAAI,GAAG,CAAC;AACT;AACA,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB;AACA,CAAC,IAAI,QAAQ,GAAG,MAAM,CAAC;AACvB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;AACvC,WAAW,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC;AAC3C,WAAW,UAAU,CAAC,cAAc,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACnC;AACA,CAACA,SAAO,KAAK,MAAM,CAAC,OAAO,CAAC;AAC5B;AACA,CAAC,IAAIA,SAAO,IAAI,CAACC,OAAY,CAAC,KAAK,CAAC,EAAE;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAED,SAAO,KAAK,IAAI,GAAG,SAAS,GAAGA,SAAO,CAAC,CAAC,MAAM,CAAC;AAChF,EAAE;AACF;AACA,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC/B,EAAE,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;AACtC;AACA,EAAE,IAAI,MAAM,CAAC,SAAS,EAAE;AACxB,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AAC9D,GAAG;AACH,OAAO;AACP,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,wDAAwD,CAAC,CAAC,CAAC;AACrG,GAAG;AACH,EAAE;AACF,MAAM;AACN;AACA,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACpC;AACA,EAAE,IAAI,MAAM,CAAC,eAAe,EAAE;AAC9B,GAAG,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACtD,GAAG;AACH,OAAO;AACP,GAAG,IAAI,SAAS,KAAK,IAAI,EAAE;AAC3B,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;AAC7B,KAAK,OAAOE,eAAoB,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;AACjD,KAAK,CAAC,CAAC;AACP,IAAI;AACJ,GAAG;AACH;AACA,EAAE,IAAI,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AACzB;AACA,EAAE,IAAI,IAAI,KAAK,OAAO,EAAE;AACxB;AACA,GAAG,IAAI,KAAK,GAAG,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;AAC9D,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvB,GAAG;AACH;AACA,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC1B,EAAE,IAAI,SAAS,KAAK,IAAI,EAAE;AAC1B,GAAG,KAAK,GAAGA,eAAoB,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;AACpD,GAAG;AACH;AACA,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,GAAG,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACpG,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvE,EAAE;AACF;AACA,CAAC,OAAO,GAAG,CAAC;AACZ;;ACnFA;AACA;AACA;AACA;AACA,MAAMC,SAAO,GAAG;AAChB,CAAC,EAAE,kBAAkB,EAAE,mBAAmB,GAAG,kBAAkB,GAAG;AAClE,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE;AAClE,CAAC,EAAE,iBAAiB,GAAG,oBAAoB,EAAE,iBAAiB,IAAI;AAClE,CAAC,CAAC;AACF;AACA;AACA,MAAMC,WAAS,GAAG;AAClB,CAAC,GAAG,iBAAiB,GAAG,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,GAAG;AACjE,CAAC,EAAE,CAAC,iBAAiB,IAAI,iBAAiB,GAAG,kBAAkB,EAAE;AACjE,CAAC,GAAG,iBAAiB,GAAG,CAAC,iBAAiB,GAAG,iBAAiB,GAAG;AACjE,CAAC,CAAC;AACF;AACA,oBAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,gBAAgB;AACrB,CAAC,KAAK,EAAE,kBAAkB;AAC1B,CAAC,IAAI,EAAE,iBAAiB;AACxB,CAAC,KAAK,EAAE,KAAK;AACb,UAACD,SAAO;AACR,YAACC,WAAS;AACV,CAAC,CAAC;;ACxBF;AACA;AACA,MAAM,CAAC,GAAG,gBAAgB,CAAC;AAC3B,MAAM,CAAC,GAAG,iBAAiB,CAAC;AAC5B;AACA,cAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,SAAS;AACd,CAAC,IAAI,EAAE,UAAU;AACjB,CAAC,IAAI,EAAE,aAAa;AACpB;AACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AAChC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE;AACtB,IAAI,OAAO,GAAG,GAAG,GAAG,CAAC;AACrB,IAAI;AACJ;AACA,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AAChD,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AAChC,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE;AACjB,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,IAAI;AACJ;AACA,GAAG,OAAO,GAAG,GAAG,GAAG,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,CAAC;;AC5BF,MAAMD,SAAO,GAAG;AAChB,CAAC,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,kBAAkB,CAAC;AAC9D,CAAC,CAAC,kBAAkB,EAAE,kBAAkB,GAAG,iBAAiB,CAAC;AAC7D,CAAC,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;AAC7D,CAAC,CAAC;AACF;AACA,MAAMC,WAAS,GAAG;AAClB,CAAC,EAAE,iBAAiB,IAAI,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,CAAC;AAClE,CAAC,CAAC,CAAC,kBAAkB,IAAI,kBAAkB,GAAG,oBAAoB,CAAC;AACnE,CAAC,EAAE,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;AACjE,CAAC,CAAC;AACF;AACA,eAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,WAAW;AAChB,CAAC,KAAK,EAAE,qBAAqB;AAC7B,CAAC,IAAI,EAAE,WAAW;AAClB,CAAC,KAAK,EAAE,KAAK;AACb,UAACD,SAAO;AACR,YAACC,WAAS;AACV,CAAC,CAAC;;ACnBF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,SAAO,GAAG;AAChB,CAAC,EAAE,mBAAmB,EAAE,iBAAiB,IAAI,kBAAkB,GAAG;AAClE,CAAC,EAAE,mBAAmB,EAAE,iBAAiB,IAAI,mBAAmB,EAAE;AAClE,CAAC,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,kBAAkB,GAAG;AAClE,CAAC,CAAC;AACF;AACA;AACA;AACO,MAAMC,WAAS,GAAG;AACzB,CAAC,GAAG,kBAAkB,GAAG,CAAC,iBAAiB,IAAI,CAAC,kBAAkB,GAAG;AACrE,CAAC,EAAE,CAAC,kBAAkB,IAAI,kBAAkB,IAAI,mBAAmB,EAAE;AACrE,CAAC,GAAG,mBAAmB,EAAE,CAAC,mBAAmB,GAAG,kBAAkB,GAAG;AACrE,CAAC,CAAC;AACF;AACA,iBAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,aAAa;AAClB,CAAC,IAAI,EAAE,aAAa;AACpB,CAAC,KAAK,EAAE,KAAK;AACb,UAACD,SAAO;AACR,YAACC,WAAS;AACV,CAAC,CAAC;;AC7BF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACvC,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClD,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACxC,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC3C,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACpC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnB,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC5C,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AACzC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAChC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC9C,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAClC,CAAC,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACpD,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACtC,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC3C,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9B,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACtC,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAClD,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9C,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAC/B,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACzC,CAAC,gBAAgB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAClD,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAChC,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAChD,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClD,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACjD,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAChD,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAChD,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC3C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACxC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACrC,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACjC,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC7C,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC7C,CAAC,YAAY,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACvC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC7C,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACzC,CAAC,aAAa,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC/C,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrB,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACxC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAC1B,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC9C,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC1C,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAC3B,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC;AACxC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC1C,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACtC,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACrC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC7C,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACnC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AAC3B,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC3C,CAAC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9C,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC3C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACvC,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC1C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAChD,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/B,CAAC,sBAAsB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC1D,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAChD,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACvC,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACzC,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClD,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClD,CAAC,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACpD,CAAC,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACpD,CAAC,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACpD,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACjC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC7C,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC3C,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrB,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5B,CAAC,kBAAkB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACtD,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AAChC,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACjD,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClD,CAAC,gBAAgB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACnD,CAAC,iBAAiB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACrD,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,iBAAiB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACpD,CAAC,iBAAiB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACpD,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAChD,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACvC,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACvC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACtC,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACzC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AAC1B,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC7C,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACnC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC9C,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAC5B,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC;AAC9B,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC5C,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACnD,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACnD,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACnD,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACxC,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACvC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AACzC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC1C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAChD,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACpC,CAAC,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClD,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACjB,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9C,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC/C,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC5C,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC/C,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC5C,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACtC,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAC1C,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC5C,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC7C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC/C,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClC,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;AACjC,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9C,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AACzC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAClC,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC7C,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAClC,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC9C,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC5C,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAC3C,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnB,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;AAChD,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACpB,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;AAChD,CAAC;;ACzJD,IAAI,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;AACpE,IAAI,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC3D;AACA,WAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,MAAM;AACX,CAAC,IAAI,EAAE,MAAM;AACb,CAAC,IAAI,EAAE,UAAU;AACjB,CAAC,QAAQ,EAAE,GAAG,IAAI;AAClB;AACA;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;AACxB,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC/B,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACxB;AACA,GAAG,IAAI,GAAG,GAAG,SAAS,EAAE;AACxB,IAAI,OAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACvD,IAAI;AACJ;AACA,GAAG,OAAO,KAAK,GAAG,GAAG,CAAC;AACtB,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,MAAM,EAAE,GAAG,IAAI;AAChB;AACA;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;AACxB,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC/B,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACxB;AACA,GAAG,IAAI,GAAG,IAAI,OAAO,EAAE;AACvB,IAAI,OAAO,GAAG,GAAG,KAAK,CAAC;AACvB,IAAI;AACJ;AACA,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,MAAM,EAAE,YAAY;AACvB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG,MAAM,EAAE,IAAI;AACf,GAAG,MAAM,EAAE,kBAAkB;AAC7B,GAAG,OAAO,EAAE,IAAI;AAChB,GAAG;AACH,EAAE,OAAO,EAAE,sBAAsB;AACjC,EAAE,MAAM,EAAE;AACV,GAAG,MAAM,EAAE,YAAY;AACvB,GAAG,MAAM,EAAE,IAAI;AACf,GAAG,SAAS,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,GAAG,IAAI,EAAE,MAAM;AACf,GAAG,MAAM,EAAE,IAAI;AACf,GAAG,MAAM,EAAE,kBAAkB;AAC7B,GAAG;AACH,EAAE,KAAK,EAAE;AACT,GAAG,IAAI,EAAE,QAAQ;AACjB,GAAG,OAAO,EAAE,IAAI;AAChB,GAAG,IAAI,EAAE,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,GAAG,CAAC;AACpD,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE;AACf,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;AACzB;AACA,KAAK,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;AAC7C,KAAK;AACL;AACA,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,SAAS,IAAI;AAC9C,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9C,KAAK,CAAC,CAAC;AACP;AACA,IAAI,OAAO;AACX,KAAK,OAAO,EAAE,MAAM;AACpB,KAAK,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7B,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,KAAK,CAAC;AACN,IAAI;AACJ,GAAG,SAAS,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE;AAC9B,IAAI,QAAQ,GAAG,IAAI;AACnB,IAAI,GAAG,EAAE,KAAK;AACd,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;AACnB,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,KAAK;AACL;AACA,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClD;AACA,IAAI,IAAI,WAAW,GAAG,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AAClE;AACA,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;AAC9B,KAAK,IAAI,WAAW,EAAE;AACtB,MAAM,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;AACnC,MAAM;AACN;AACA,KAAK,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5C,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAChB;AACA,IAAI,OAAO,GAAG,GAAG,GAAG,CAAC;AACrB,IAAI;AACJ,GAAG;AACH,EAAE,SAAS,EAAE;AACb,GAAG,IAAI,EAAE,QAAQ;AACjB,GAAG,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC;AACrC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE;AACf,IAAI,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;AAC5B,IAAI,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AACxD;AACA,IAAI,IAAI,GAAG,KAAK,aAAa,EAAE;AAC/B,KAAK,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,KAAK;AACL,SAAS;AACT,KAAK,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AAChC,KAAK;AACL;AACA,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;AACpB,KAAK,OAAO,GAAG,CAAC;AAChB,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;AC1HF,SAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,IAAI;AACT,CAAC,KAAK,EAAE,YAAY;AACpB,CAAC,IAAI,EAAE,IAAI;AACX,CAAC,IAAI,EAAE,QAAQ;AACf;AACA,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACxB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;AACpB,CAAC,CAAC;;ACFF;AACA,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B;AACA,IAAI,YAAY,CAAC;AACjB;AACA,IAAI,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,CAAC,QAAQ,EAAE;AAChD;AACA,CAAC,KAAK,IAAI,KAAK,IAAI,CAACxB,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE;AACvC,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;AACpC,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AACxC,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7B;AACA,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;AAClC,GAAG,QAAQ,CAAC,aAAa,GAAG,KAAK,CAAC;AAClC,GAAG,MAAM;AACT,GAAG;AACH,EAAE;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAAS,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,EAAE;AAC3F,CAAC,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACrC;AACA,CAAC,IAAI,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;AAC1F,EAAE,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;AACpB,EAAE;AACF,MAAM;AACN;AACA,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC;AAC5B;AACA;AACA,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACjE;AACA,EAAE,IAAI,OAAO,EAAE;AACf;AACA,GAAG,IAAI,EAAE,YAAY,KAAK,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,EAAE;AACvE;AACA,IAAI,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC9D,IAAI,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACxD;AACA,IAAI,GAAG,GAAG,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAC5C;AACA,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;AACpC;AACA,KAAK,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AAC3B,KAAK,GAAG,CAAC,KAAK,GAAG,aAAa,CAAC;AAC/B,KAAK,OAAO,GAAG,CAAC;AAChB,KAAK;AACL,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA,EAAE,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC3C,EAAE,GAAG,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC;AACtD,EAAE,GAAG,CAAC,KAAK,GAAG,aAAa,CAAC;AAC5B,EAAE;AACF;AACA,CAAC,OAAO,GAAG,CAAC;AACZ;;AChFe,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAChD,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;AACA,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;AACrC,WAAW,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;AACxC,WAAW,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE;;ACTA;AACA;AACA;AAIA;AACO,SAAS,YAAY,EAAE,KAAK,EAAE;AACrC;AACA,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;AACnC,CAAC;AACD;AACO,SAAS,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE;AAC5C;AACA,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AACnC;;ACfA;AACA;AACA;AACA;AAGA;AACe,SAAS,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE;AACxD,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;AACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C;AACA,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;AACd,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB,EAAE;AACF;AACA,CAAC,OAAO,CAAC,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;AAChC;;ACnBA;AACA;AACA;AACA;AAGA;AACA;AACA,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,MAAM,OAAO,GAAG,IAAI,CAAC;AACrB,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB;AACA;AACA,MAAM,OAAO,GAAG,KAAK,CAAC;AACtB,MAAM,OAAO,GAAG,KAAK,CAAC;AACtB,MAAM,MAAM,GAAG,GAAG,CAAC;AACnB,MAAM,SAAS,GAAG,MAAM,CAAC;AACzB;AACA;AACA;AACA,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,MAAM,WAAW,GAAG,KAAK,CAAC;AAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC;AAEtB;AACA,SAAS,MAAM,EAAE,CAAC,EAAE;AACpB,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE;AACnB,EAAE,OAAO,CAAC,CAAC;AACX,EAAE;AACF,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,KAAK,OAAO,CAAC;AACrC,CAAC;AACD;AACA,SAAS,SAAS,EAAE,GAAG,EAAE;AACzB,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AACD;AACA;AACe,SAAS,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE;AAC9D,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;AACnC,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;AACnC;AACA,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,CAAC,CAAC;AACP,CAAC,IAAI,IAAI,CAAC;AACV;AACA;AACA,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACb;AACA,CAAC,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;AAC/B,CAAC,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AAC7F;AACA,CAAC,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;AAC/B,CAAC,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AAC5F;AACA;AACA,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACzB;AACA;AACA,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACtB;AACA;AACA;AACA;AACA,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,SAAS,EAAE;AACvC,EAAE,CAAC,GAAG,CAAC,CAAC;AACR,EAAE;AACF,MAAM;AACN,EAAE,IAAI,GAAG,EAAE;AACX;AACA,GAAG,CAAC,GAAG,GAAG,IAAI,MAAM,GAAG,IAAI,IAAI,OAAO,CAAC;AACvC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AACpB,GAAG;AACH,OAAO;AACP;AACA,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI,MAAM,CAAC;AACrC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AACpB,GAAG;AACH,EAAE;AACF,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE;AAC3B,EAAE,IAAI,GAAG,CAAC,CAAC;AACX,EAAE;AACF,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;AACjB;AACA;AACA,EAAE,IAAI,GAAG,CAAC,GAAG,WAAW,CAAC;AACzB,EAAE;AACF,MAAM;AACN,EAAE,IAAI,GAAG,CAAC,GAAG,WAAW,CAAC;AACzB,EAAE;AACF;AACA,CAAC,OAAO,IAAI,GAAG,GAAG,CAAC;AACnB;;ACrGA;AACA;AACA;AACA;AACA;AAGA;AACe,SAAS,iBAAiB,EAAE,MAAM,EAAE,MAAM,EAAE;AAC3D,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;AACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C;AACA,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;AACd,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB,EAAE;AACF;AACA,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;AACvB,CAAC,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,KAAK,CAAC;AAC5C;;ACrBA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA,MAAM,GAAG,GAAG,KAAK,CAAC;AAClB;AACe,SAAS,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE;AACvD,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;AACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C;AACA,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;AACd,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB,EAAE;AACF;AACA,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AACxC;;AC1BA;AACA;AACA;AACA;AAIA;AACe,SAAS,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE;AACvD,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;AACA,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAClC,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAClC;AACA,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1B;;ACZA;AACA,MAAMN,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACtB,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;AACpB,MAAMG,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB;AACA,IAAIC,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC;AACvB;AACA,cAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,SAAS;AACd,CAAC,IAAI,EAAE,SAAS;AAChB,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE;AACF;AACA;AACA;AACA,QAACA,OAAK;AACN;AACA,CAAC,IAAI,EAAE,OAAO;AACd;AACA;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,GAAGA,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD;AACA;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,GAAGJ,GAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAACG,GAAC,GAAG,KAAK,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;AAClF;AACA,EAAE,OAAO;AACT,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;AACpB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE;AACF;AACA;AACA;AACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd;AACA,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAC7B;AACA;AACA,EAAE,IAAI,GAAG,GAAG;AACZ,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIA,GAAC;AACzE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAC;AAC9D,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIA,GAAC;AACzE,GAAG,CAAC;AACJ;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,GAAGC,OAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,SAAS,EAAE;AACb,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;AACxG,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACzEF;AACA;AACA;AACA;AACA;AAKA;AACA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC;AACe,SAAS,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE;AAC1D,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;AACA,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;AACzC,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;AACzC;AACA,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1E;AACA,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpE;AACA,CAAC,OAAO,CAAC,QAAQ,GAAG,GAAG,IAAI,GAAG,GAAG,QAAQ,EAAE;AAC3C;;;;;;;;;;;;ACnBe,SAAS,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE;AAClE,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;AAClB,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AACrB,EAAE;AACF;AACA,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9B;AACA,CAAC,IAAI,CAAC,SAAS,EAAE;AACjB,EAAE,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnG,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC,uEAAuE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9G,EAAE;AACF;AACA,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;AACnC,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;AACnC;AACA,CAAC,KAAK,IAAI,CAAC,IAAI,kBAAkB,EAAE;AACnC,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;AAChE,GAAG,OAAO,kBAAkB,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE;AACF;AACA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AACjE;;ACxBA;AACO,SAAS,EAAE,EAAE,KAAK,EAAE;AAC3B;AACA,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AACvC,CAAC;AACD;AACO,SAAS,EAAE,EAAE,KAAK,EAAE;AAC3B;AACA,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtB,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC3B;;ACZe,SAAS,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;AAChD,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;AAClB,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAClB,EAAE;AACF;AACA,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C;AACA,CAAC,KAAK,IAAI,CAAC,IAAI,aAAa,EAAE;AAC9B,EAAE,IAAI,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;AAC3D,GAAG,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AACzC,GAAG;AACH,EAAE;AACF;AACA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACzD;;ACfO,SAAS,OAAO,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE;AAC9C,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC5C,CAAC,IAAI,SAAS,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC9B,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACrD,CAAC;AACD;AACO,SAAS,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE;AAC7C,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC5C,CAAC,IAAI,SAAS,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC9B,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACrD;;ACbA;AACA;AACA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;AAC7C,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC;AACA,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC3B,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACnB,EAAE;AACF;AACA,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC1B,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE;AAC7C,CAAC,IAAI,UAAU,CAAC;AAChB;AACA,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE;AAClB;AACA,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC;AACzC,EAAE;AACF;AACA,CAAC,IAAI;AACL,EAAE,SAAS,EAAE,YAAY;AACzB,EAAE,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI;AAC5B,EAAE,GAAG,YAAY;AACjB,EAAE,GAAG,OAAO,CAAC;AACb;AACA,CAAC,IAAI,CAAC,UAAU,EAAE;AAClB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1C,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;AAC3C,EAAE;AACF;AACA,CAAC,IAAI,UAAU,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACjC,CAAC,IAAI,WAAW,GAAG,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAClG,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;AACd;AACA,CAAC,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC7B,EAAE,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAChD,EAAE;AACF;AACA,CAAC,IAAI,WAAW,KAAK,CAAC,EAAE;AACxB,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzC,EAAE;AACF,MAAM;AACN,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;AACnC,EAAE,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AACpD,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACpB,GAAG,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAE;AACF;AACA,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE;AACpB;AACA,EAAE,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK;AAC7C,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;AAChB,IAAI,OAAO,CAAC,CAAC;AACb,IAAI;AACJ;AACA,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC9D,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAC5B,GAAG,EAAE,CAAC,CAAC,CAAC;AACR;AACA,EAAE,OAAO,QAAQ,GAAG,SAAS,EAAE;AAC/B;AACA;AACA,GAAG,QAAQ,GAAG,CAAC,CAAC;AAChB;AACA,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,MAAM,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE;AACrE,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1B,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACjC,IAAI,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACvF,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,IAAI,CAAC,EAAE,CAAC;AACR,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7B;AACA,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;AACrD,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;AACtB;AACA,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACtC;AACA,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;AAC5E,EAAE;AACF;AACA,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,GAAG,OAAO,CAAC;AAChE;AACA,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B;AACA;AACA,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AACxB,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AACxB;AACA,CAAC,IAAI,SAAS,GAAG,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;AACrD;AACA,CAAC,IAAI,KAAK,EAAE;AACZ,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC,EAAE;AACF,MAAM;AACN,EAAE,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC;AAC3E,EAAE;AACF;AACA,CAAC,WAAW,GAAG,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;AACjE;AACA,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5B,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5B;AACA;AACA,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1B,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1B;AACA;AACA;AACA,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;AACxD,EAAE,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,SAAS,CAAC;AACnD;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACzB,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AACtD;AACA;AACA;AACA,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AAC/B,GAAG,EAAE,GAAG,EAAE,CAAC;AACX,GAAG;AACH,OAAO,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AACpC,GAAG,EAAE,GAAG,EAAE,CAAC;AACX,GAAG;AACH,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG2B,MAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC1C,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;AACvB,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;AACvB,EAAE;AACF;AACA,CAAC,IAAI,aAAa,EAAE;AACpB;AACA,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3D,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3D,EAAE;AACF;AACA,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI;AAC3B,EAAE,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACvC,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK;AAC/C,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9B,GAAG,OAAO,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACrC,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACzD,EAAE,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC;AACA,EAAE,IAAI,aAAa,EAAE;AACrB;AACA,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/C,GAAG;AACH;AACA,EAAE,IAAI,WAAW,KAAK,KAAK,EAAE;AAC7B,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,EAAE,EAAE;AACJ,EAAE,SAAS;AACX,EAAE,CAAC,CAAC;AACJ,CAAC;AACD;AACO,SAAS,OAAO,EAAE,GAAG,EAAE;AAC9B,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC;AACpD,CAAC;AACD;AACA,QAAQ,CAAC,kBAAkB,GAAG,KAAK;;ACpNnC,UAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,KAAK;AACV,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,YAAY;AACrB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,IAAI;AACX;AACA;AACA,CAAC,QAAQ,EAAE,GAAG,IAAI;AAClB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AAC5C,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACpB;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACf,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACjE;AACA,GAAG,QAAQ,GAAG;AACd,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;AACrD,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACvC,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,IAAI;AACJ;AACA,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,GAAG,CAAC,IAAI,GAAG,CAAC;AACZ,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACnB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE;AAChB,GAAG,CAAC,IAAI,GAAG,CAAC;AACZ,GAAG;AACH;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC/B,EAAE;AACF;AACA;AACA,CAAC,MAAM,EAAE,GAAG,IAAI;AAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACd;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,GAAG,CAAC,IAAI,GAAG,CAAC;AACZ,GAAG;AACH;AACA,EAAE,CAAC,IAAI,GAAG,CAAC;AACX,EAAE,CAAC,IAAI,GAAG,CAAC;AACX;AACA,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE;AACjB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AAC7B,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1D,GAAG;AACH;AACA,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,cAAc,CAAC;AACjE,GAAG;AACH,EAAE,MAAM,EAAE;AACV,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,cAAc,CAAC;AACjE,GAAG,MAAM,EAAE,IAAI;AACf,GAAG,SAAS,EAAE,IAAI;AAClB,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACvFF;AACA;AACA;AACA;AACA;AACA,UAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,KAAK;AACV,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,YAAY;AACrB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,GAAG;AACV;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,EAAE,CAAC,IAAI,GAAG,CAAC;AACX,EAAE,CAAC,IAAI,GAAG,CAAC;AACX;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC;AACA,EAAE,OAAO;AACT,GAAG,CAAC;AACJ,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClC,GAAG,GAAG,GAAG,CAAC;AACV,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA,EAAE,CAAC,IAAI,GAAG,CAAC;AACX,EAAE,CAAC,IAAI,GAAG,CAAC;AACX;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1B;AACA,EAAE,OAAO;AACT,GAAG,CAAC;AACJ,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG;AAClE,GAAG,CAAC,GAAG,GAAG;AACV,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,EAAE,EAAE,OAAO;AACd,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;AACvF,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;AC7DF;AACA;AACA;AACA;AACA;AACA,UAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,KAAK;AACV,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,GAAG;AACV,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC3C,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA;AACA,EAAE,CAAC,IAAI,GAAG,CAAC;AACX,EAAE,CAAC,IAAI,GAAG,CAAC;AACX;AACA;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE;AAChB,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACtB,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC/B,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;AACvF,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACvDF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMF,SAAO,GAAG;AAChB,CAAC,EAAE,kBAAkB,IAAI,kBAAkB,IAAI,kBAAkB,GAAG;AACpE,CAAC,EAAE,mBAAmB,GAAG,kBAAkB,IAAI,mBAAmB,EAAE;AACpE,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,GAAG,kBAAkB,GAAG;AACpE,CAAC,CAAC;AACF;AACA,MAAMC,WAAS,GAAG;AAClB,CAAC,GAAG,kBAAkB,KAAK,CAAC,kBAAkB,IAAI,CAAC,mBAAmB,EAAE;AACxE,CAAC,EAAE,CAAC,kBAAkB,MAAM,kBAAkB,KAAK,mBAAmB,EAAE;AACxE,CAAC,GAAG,oBAAoB,GAAG,CAAC,mBAAmB,IAAI,kBAAkB,GAAG;AACxE,CAAC,CAAC;AACF;AACA,gBAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,eAAe;AACpB,CAAC,KAAK,EAAE,kBAAkB;AAC1B,CAAC,IAAI,EAAE,iCAAiC;AACxC,CAAC,KAAK,EAAE,KAAK;AACb,UAACD,SAAO;AACR,YAACC,WAAS;AACV,CAAC,CAAC;;ACxBF,aAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,QAAQ;AACb,CAAC,KAAK,EAAE,SAAS;AACjB,CAAC,IAAI,EAAE,0BAA0B;AACjC,CAAC,IAAI,EAAE,SAAS;AAChB,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnF,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrF,CAAC,CAAC;;ACPF;AACA;AACA;AACA;AACA,MAAMD,SAAO,GAAG;AAChB,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;AACpE,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;AACpE,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;AACpE,CAAC,CAAC;AACF;AACA,MAAMC,WAAS,GAAG;AAClB,CAAC,GAAG,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,EAAE;AACrE,CAAC,EAAE,CAAC,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;AACrE,CAAC,GAAG,mBAAmB,GAAG,mBAAmB,GAAG,mBAAmB,EAAE;AACrE,CAAC,CAAC;AACF;AACA,qBAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,iBAAiB;AACtB,CAAC,KAAK,EAAE,uBAAuB;AAC/B,CAAC,IAAI,EAAE,iBAAiB;AACxB,CAAC,KAAK,EAAE,KAAK;AACb,CAAC,IAAI,EAAE,OAAO;AACd,UAACD,SAAO;AACR,YAACC,WAAS;AACV,CAAC,CAAC;;ACxBF,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;AACnB,MAAM,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;AACrB;AACA,eAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,UAAU;AACf,CAAC,KAAK,EAAE,cAAc;AACtB,CAAC,IAAI,EAAE,UAAU;AACjB,CAAC,IAAI,EAAE,cAAc;AACrB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AACnD,EAAE;AACF,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AACzD,EAAE;AACF,CAAC,CAAC;;ACdF,YAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,OAAO;AACZ,CAAC,IAAI,EAAE,OAAO;AACd,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,QAAQ;AACjB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE;AACF,CAAC,KAAK,EAAE,KAAK;AACb;AACA,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE;AAClB;AACA,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;AACxB,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC;AACnB;AACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC1C,GAAG,CAAC,GAAG,GAAG,CAAC;AACX,GAAG;AACH,OAAO;AACP,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;AACxC,GAAG;AACH;AACA,EAAE,OAAO;AACT,GAAG,CAAC;AACJ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,GAAGvB,SAAc,CAAC,CAAC,CAAC;AACpB,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;AAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;AACxB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AACX;AACA;AACA,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG;AACH,OAAO;AACP,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AACvC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AACvC,GAAG;AACH;AACA,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACrB,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,OAAO,EAAE;AACX,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,8BAA8B,EAAE,oBAAoB,CAAC;AAC5F,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;AC7DF,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC;AACvB;AACA,MAAMP,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACtB,MAAMG,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3E;AACA,UAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,KAAK;AACV,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH;AACA,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,GAAG;AACH,EAAE;AACF;AACA,CAAC,KAAK,EAAE,KAAK;AACb,CAAC,IAAI,EAAE,OAAO;AACd;AACA;AACA;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACjB;AACA,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AACnD;AACA;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;AACpD,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAIH,GAAC,GAAGG,GAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACnD,EAAE,OAAO;AACT,GAAG,CAAC;AACJ,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,aAAa,CAAC;AAChC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,aAAa,CAAC;AAChC,IAAI,CAAC;AACL,EAAE;AACF;AACA;AACA;AACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB;AACA;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;AAC5B,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,GAAG;AACH;AACA,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClB,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClB;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,aAAa,CAAC;AAC1C,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,aAAa,CAAC;AAC1C;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,GAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;AACvD;AACA,EAAE,OAAO;AACT,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,GAAG,CAAC;AACJ,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,EAAE,EAAE,OAAO;AACd,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;AACxG,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;AChFF,YAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,OAAO;AACZ,CAAC,IAAI,EAAE,OAAO;AACd,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,QAAQ;AACjB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,GAAG;AACV,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;AACjB;AACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC1C,GAAG,GAAG,GAAG,GAAG,CAAC;AACb,GAAG;AACH,OAAO;AACP,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,OAAO;AACT,GAAG,CAAC;AACJ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,GAAGI,SAAc,CAAC,GAAG,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE;AACF,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd;AACA,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;AACrC;AACA,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;AAClB,GAAG,MAAM,GAAG,CAAC,CAAC;AACd,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;AAClB,GAAG,GAAG,GAAG,CAAC,CAAC;AACX,GAAG;AACH,EAAE,OAAO;AACT,GAAG,SAAS;AACZ,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AACzC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AACzC,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,EAAE,EAAE,SAAS;AAChB,GAAG,MAAM,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,oBAAoB,CAAC;AACvF,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACnEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA,MAAMP,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACtB,MAAM,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrB;AACA,MAAM,IAAI,GAAG8B,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;AACA,SAAS,uBAAuB,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE;AAC3D,CAAC,MAAM,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACnE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;AAC7B,CAAC;AACD;AACO,SAAS,sBAAsB,EAAE,CAAC,EAAE;AAC3C,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC;AAC5C,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG9B,GAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;AACnD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AACpE,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AACpD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;AACnD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AACpE,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AACpD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;AACnD,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AACpE,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AACpD;AACA,CAAC,OAAO;AACR,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG;AAChB,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;AACpB,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC;AAC3B,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC;AAC1C,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG;AAChB,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;AACpB,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC;AAC3B,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC;AAC1C,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG;AAChB,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;AACpB,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC;AAC3B,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC;AAC1C,EAAE,CAAC;AACH,CAAC;AACD;AACA,SAAS,kBAAkB,EAAE,KAAK,EAAE,CAAC,EAAE;AACvC,CAAC,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACtC,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClE,CAAC,MAAM,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClE;AACA,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACzC,CAAC;AACD;AACA,YAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,OAAO;AACZ,CAAC,IAAI,EAAE,OAAO;AACd,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,YAAY;AACrB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,UAAU,EAAE,IAAI;AACjB;AACA;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,EAAE,IAAI,CAAC,CAAC;AACR;AACA,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE;AACtB,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG,CAAC,GAAG,GAAG,CAAC;AACX,GAAG;AACH,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE;AAC3B,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG;AACH,OAAO;AACP,GAAG,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AACzC,GAAG,IAAI,GAAG,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC1C,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,EAAE;AACF;AACA;AACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,EAAE,IAAI,CAAC,CAAC;AACR;AACA,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE;AACtB,GAAG,CAAC,GAAG,GAAG,CAAC;AACX,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG;AACH,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE;AAC3B,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG;AACH,OAAO;AACP,GAAG,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AACzC,GAAG,IAAI,GAAG,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC1C,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,EAAE,EAAE,SAAS;AAChB,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;AACvF,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;ACjKF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACa8B,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChBA,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC7B;AACA,SAAS,kBAAkB,EAAE,KAAK,EAAE,SAAS,EAAE;AAC/C,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAChE,CAAC;AACD;AACA,SAAS,kBAAkB,EAAE,KAAK,EAAE;AACpC,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD,CAAC,IAAI,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD;AACA,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACzC,CAAC;AACD;AACA,YAAe,IAAI,UAAU,CAAC;AAC9B,CAAC,EAAE,EAAE,OAAO;AACZ,CAAC,IAAI,EAAE,OAAO;AACd,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,YAAY;AACrB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAClB,GAAG,IAAI,EAAE,WAAW;AACpB,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,EAAE,KAAK;AACZ,CAAC,UAAU,EAAE,MAAM;AACnB;AACA;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,EAAE,IAAI,CAAC,CAAC;AACR;AACA,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE;AACtB,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG,CAAC,GAAG,GAAG,CAAC;AACX,GAAG;AACH,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE;AAC3B,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG;AACH,OAAO;AACP,GAAG,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AACzC,GAAG,IAAI,GAAG,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACvC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACrB,GAAG;AACH,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,EAAE;AACF;AACA;AACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,EAAE,IAAI,CAAC,CAAC;AACR;AACA,EAAE,IAAI,CAAC,GAAG,UAAU,EAAE;AACtB,GAAG,CAAC,GAAG,GAAG,CAAC;AACX,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG;AACH,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE;AAC3B,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG,CAAC,GAAG,CAAC,CAAC;AACT,GAAG;AACH,OAAO;AACP,GAAG,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AACzC,GAAG,IAAI,GAAG,GAAG,kBAAkB,CAAC,KAAQ,CAAC,CAAC;AAC1C,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,EAAE;AACF;AACA,CAAC,OAAO,EAAE;AACV,EAAE,KAAK,EAAE;AACT,GAAG,EAAE,EAAE,SAAS;AAChB,GAAG,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAC;AACvF,GAAG;AACH,EAAE;AACF,CAAC,CAAC;;AC9HF,MAAM,EAAE,GAAG,GAAG,CAAC;AACf,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3B,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC;AAC9B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1B,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC7B,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5B,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3B,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3B;AACA,gBAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,WAAW;AAChB,CAAC,KAAK,EAAE,YAAY;AACpB,CAAC,IAAI,EAAE,aAAa;AACpB,CAAC,IAAI,EAAE,aAAa;AACpB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AAChC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;AACvF,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE;AAC3B,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AAChC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;AACzC,GAAG,IAAI,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,GAAG,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC;AACA,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,EAAE;AAChC,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,CAAC;;ACjCF;AACA;AACA,MAAM,CAAC,GAAG,UAAU,CAAC;AACrB,MAAM,CAAC,GAAG,UAAU,CAAC;AACrB,MAAM,CAAC,GAAG,UAAU,CAAC;AACrB;AACA,MAAM,KAAK,GAAG,MAAM,CAAC;AACrB;AACA,iBAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,YAAY;AACjB,CAAC,KAAK,EAAE,aAAa;AACrB,CAAC,IAAI,EAAE,cAAc;AACrB,CAAC,QAAQ,EAAE,OAAO;AAClB;AACA,CAAC,IAAI,EAAE,aAAa;AACpB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AAChC;AACA;AACA;AACA;AACA,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;AACnB,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAClC,IAAI;AACJ,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC;AACvD,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB;AACA;AACA;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AAChC;AACA,GAAG,GAAG,IAAI,KAAK,CAAC;AAChB;AACA;AACA;AACA,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,IAAI;AACJ,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACzC,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,CAAC;;AC5CK,MAAM,IAAI,GAAG,EAAE,CAAC;AACvB;AACA,KAAK,CAAC,GAAG,CAAC,4BAA4B,EAAE,GAAG,IAAI;AAC/C,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;AACzB,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACpD,EAAE;AACF,CAAC,CAAC,CAAC;AACH;AACA,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,GAAG,IAAI;AAC7C,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AACb,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACpD,EAAE;AACF,CAAC,CAAC,CAAC;AACH;AACO,SAAS,SAAS,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAE;AACvD;AACA,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AACD;AACO,SAAS,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE;AAChD;AACA;AACA;AACA;AACA,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;AACvB;AACA,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAC1D,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAC1D;AACA;AACA,CAAC,IAAI,KAAK,GAAG;AACb,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO;AAC/B,EAAE,CAAC,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,OAAO;AAC/B,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC;AAC/B,EAAE,CAAC;AACH;AACA;AACA,CAAC,IAAI,aAAa,GAAG,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC9D,CAAC,IAAI,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;AAClE;AACA,CAAC,OAAO,OAAO,CAAC;AAChB,CAAC;AACD;AACA,SAAS,CAAC;AACV,CAAC,EAAE,EAAE,WAAW;AAChB,CAAC,QAAQ,EAAE;AACX,EAAE,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC,SAAS,EAAE;AACxC,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;AACxC,EAAE,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;AACxC,EAAE;AACF,CAAC,UAAU,EAAE;AACb,EAAE,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,IAAI,mBAAmB,MAAM;AACxE,EAAE,EAAE,kBAAkB,GAAG,kBAAkB,GAAG,CAAC,uBAAuB,EAAE;AACxE,EAAE,EAAE,CAAC,oBAAoB,CAAC,qBAAqB,kBAAkB,OAAO;AACxE,EAAE;AACF,CAAC,CAAC,CAAC;AACH;AACA,SAAS,CAAC;AACV,CAAC,EAAE,EAAE,UAAU;AACf;AACA;AACA,CAAC,QAAQ,EAAE;AACX,EAAE,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC,SAAS,EAAE;AACxC,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;AACxC,EAAE,GAAG,SAAS,EAAE,CAAC,SAAS,GAAG,SAAS,EAAE;AACxC,EAAE;AACF;AACA,CAAC,UAAU,EAAE;AACb,EAAE,GAAG,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,GAAG;AACrE,EAAE,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,oBAAoB,EAAE;AACrE,EAAE,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,gBAAgB,MAAM;AACrE,EAAE;AACF,CAAC,CAAC,CAAC;AACH;AACA,SAAS,CAAC;AACV,CAAC,EAAE,EAAE,OAAO;AACZ;AACA,CAAC,QAAQ,EAAE;AACX,EAAE,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC,SAAS,EAAE;AACxC,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;AACxC,EAAE,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;AACxC,EAAE;AACF,CAAC,UAAU,EAAE;AACb,EAAE,GAAG,kBAAkB,IAAI,CAAC,mBAAmB,EAAE,mBAAmB,EAAE;AACtE,EAAE,GAAG,kBAAkB,KAAK,kBAAkB,GAAG,mBAAmB,EAAE;AACtE,EAAE,EAAE,CAAC,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,GAAG;AACtE,EAAE;AACF,CAAC,CAAC,CAAC;AACH;AACA,SAAS,CAAC;AACV,CAAC,EAAE,EAAE,OAAO;AACZ,CAAC,QAAQ,EAAE;AACX,EAAE,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC,QAAQ,EAAE;AACrC,EAAE,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE;AACrC,EAAE,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE;AACrC,EAAE;AACF;AACA,CAAC,UAAU,EAAE;AACb,EAAE,GAAG,iBAAiB,IAAI,CAAC,kBAAkB,GAAG,mBAAmB,GAAG;AACtE,EAAE,GAAG,kBAAkB,IAAI,kBAAkB,EAAE,CAAC,oBAAoB,EAAE;AACtE,EAAE,EAAE,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,IAAI;AACtE,EAAE;AACF,CAAC,CAAC,CAAC;AACH;AACA,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;AACtB;AACA;AACA;AACA,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC;AACA;AACA,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;AAClC;AACA;AACA;AACA;AACA,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC;AACA;AACA,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC;AACA;AACA,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACjC,CAAC,CAAC;;AC9HF;AACA;AACA;AACA;AACA,MAAM,CAAC,IAAI,GAAG,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,GAAG,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,CAAC;AACpF;AACA;AACA,MAAM,OAAO,GAAG;AAChB,CAAC,GAAG,kBAAkB,IAAI,mBAAmB,GAAG,kBAAkB,GAAG;AACrE,CAAC,GAAG,mBAAmB,GAAG,kBAAkB,IAAI,mBAAmB,EAAE;AACrE,CAAC,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,kBAAkB,GAAG;AACrE,CAAC,CAAC;AACF,MAAM,SAAS,GAAG;AAClB,CAAC,GAAG,kBAAkB,IAAI,CAAC,gBAAgB,KAAK,CAAC,mBAAmB,GAAG;AACvE,CAAC,EAAE,CAAC,kBAAkB,KAAK,kBAAkB,IAAI,oBAAoB,EAAE;AACvE,CAAC,GAAG,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,IAAI;AACvE,CAAC,CAAC;AACF;AACA,aAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,QAAQ;AACb,CAAC,KAAK,EAAE,UAAU;AAClB,CAAC,IAAI,EAAE,QAAQ;AACf;AACA;AACA;AACA;AACA,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AACpB,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AACpB,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AACpB,GAAG,IAAI,EAAE,MAAM;AACf,GAAG;AACH,EAAE;AACF;AACA,CAAC,QAAQ,EAAE,OAAO;AAClB;AACA,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI;AACnB;AACA,CAAC,OAAO;AACR,CAAC,SAAS;AACV,CAAC,CAAC,CAAC;AACH;AACA;;ACjDA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;AACnB;AACA;AACA;AACA,MAAM,gBAAgB,GAAG,CAAC,UAAU,CAAC;AACrC;AACA;AACA,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AACtD;AACA,aAAe,IAAI,aAAa,CAAC;AACjC,CAAC,EAAE,EAAE,QAAQ;AACb,CAAC,KAAK,EAAE,UAAU;AAClB,CAAC,IAAI,EAAE,QAAQ;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,MAAM,EAAE;AACT,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC;AACzC,GAAG,IAAI,EAAE,KAAK;AACd,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC;AACzC,GAAG,IAAI,EAAE,OAAO;AAChB,GAAG;AACH,EAAE,CAAC,EAAE;AACL,GAAG,KAAK,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC;AACzC,GAAG,IAAI,EAAE,MAAM;AACf,GAAG;AACH,EAAE;AACF,CAAC,QAAQ,EAAE,OAAO;AAClB;AACA,CAAC,IAAI,EAAE,MAAM;AACb;AACA,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AACd,EAAE,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,EAAE,IAAI,KAAK,CAAC;AAClC;AACA,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AAChC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;AACnB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD,IAAI;AACJ,QAAQ,IAAI,GAAG,GAAG,WAAW,EAAE;AAC/B,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC;AACvC,IAAI;AACJ,QAAQ;AACR,IAAI,OAAO,KAAK,CAAC;AACjB,IAAI;AACJ,GAAG,CAAC,CAAC;AACL,EAAE;AACF;AACA;AACA,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;AAChB,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AAChC,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE;AACjB,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AACzC,IAAI;AACJ,QAAQ,IAAI,GAAG,GAAG,CAAC,EAAE;AACrB,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AACtD,IAAI;AACJ,QAAQ;AACR,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAC5C,IAAI;AACJ,GAAG,CAAC,CAAC;AACL,EAAE;AACF;AACA;AACA,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}