{"name": "colorjs.io", "version": "0.5.2", "description": "Let’s get serious about color", "files": ["dist/", "src/", "types/src/", "types/index.d.ts", "types/index.d.cts"], "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/color.js"}, "require": {"types": "./types/index.d.cts", "default": "./dist/color.cjs"}}, "./fn": {"import": {"types": "./types/src/index-fn.d.ts", "default": "./src/index-fn.js"}, "require": {"types": "./types/src/index-fn.d.cts", "default": "./dist/color-fn.cjs"}}, "./dist/*": "./dist/*"}, "typesVersions": {"*": {"fn": ["./types/src/index-fn.d.ts"]}}, "type": "module", "main": "./dist/color.cjs", "module": "./dist/color.js", "types": "./types", "directories": {"test": "tests"}, "scripts": {"test": "npx htest ./test", "dtslint": "dtslint --expectOnly types", "eslint": "eslint . --ext .js --ext .ts --ext .cjs", "lint": "run-s build:space-accessors \"eslint -- --fix\" dtslint", "lint:ci": "run-s build:space-accessors \"eslint -- --max-warnings 0\" dtslint", "build:css": "npx postcss \"**/*.postcss\" --base . --dir . --ext .css --config postcss.config.cjs", "build:html": "npx @11ty/eleventy --config=.eleventy.cjs", "build:js": "rollup -c", "build:js:legacy": "rollup -c rollup.legacy.config.js", "build:space-accessors": "node ./scripts/generate-space-accessor-types.js", "build": "run-s build:html build:css build:js build:js:legacy build:space-accessors", "watch:css": "npx postcss \"**/*.postcss\" --base . --dir . --ext .css --config postcss.config.cjs --watch", "watch:html": "npx @11ty/eleventy --config=.eleventy.cjs --watch", "watch:js": "rollup -c --watch", "watch": "run-p watch:*", "prepack": "npm run build", "release": "release-it"}, "repository": {"type": "git", "url": "git+https://github.com/LeaVerou/color.js.git"}, "keywords": ["color"], "contributors": ["<PERSON>", "<PERSON>"], "license": "MIT", "bugs": {"url": "https://github.com/LeaVerou/color.js/issues"}, "homepage": "https://colorjs.io", "devDependencies": {"@11ty/eleventy": "^2.0", "@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@definitelytyped/dtslint": "0.0.182", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@stylistic/eslint-plugin": "latest", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "acorn": "latest", "core-js": "^3.36.0", "eslint": "latest", "htest.dev": "^0.0.9", "mathjs": "^12.4.0", "npm-run-all": "^4.1.5", "postcss": "latest", "postcss-cli": "latest", "postcss-nesting": "latest", "release-it": "^17.1.1", "rollup": "^4.12.0", "typescript": "^5.3.3"}, "sideEffects": ["./src/index.js", "./src/spaces/index.js"]}