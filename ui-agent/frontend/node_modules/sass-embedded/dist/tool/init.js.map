{"version": 3, "file": "init.js", "sourceRoot": "", "sources": ["../../tool/init.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;AAEvC,iCAA0B;AAE1B,yDAAmD;AACnD,mEAA4D;AAC5D,2DAAoD;AAEpD,MAAM,IAAI,GAAG,IAAA,eAAK,EAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACtC,MAAM,CAAC,eAAe,EAAE;IACvB,IAAI,EAAE,QAAQ;IACd,WAAW,EACT,mEAAmE;CACtE,CAAC;KACD,MAAM,CAAC,cAAc,EAAE;IACtB,IAAI,EAAE,QAAQ;IACd,WAAW,EAAE,wDAAwD;CACtE,CAAC;KACD,MAAM,CAAC,eAAe,EAAE;IACvB,IAAI,EAAE,SAAS;IACf,WAAW,EAAE,kCAAkC;CAChD,CAAC;KACD,MAAM,CAAC,eAAe,EAAE;IACvB,IAAI,EAAE,QAAQ;IACd,WAAW,EAAE,0DAA0D;CACxE,CAAC;KACD,MAAM,CAAC,cAAc,EAAE;IACtB,IAAI,EAAE,QAAQ;IACd,WAAW,EAAE,+CAA+C;CAC7D,CAAC;KACD,SAAS,CAAC;IACT,eAAe,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;IAClD,cAAc,EAAE,CAAC,eAAe,CAAC;IACjC,eAAe,EAAE,CAAC,cAAc,CAAC;CAClC,CAAC;KACD,SAAS,EAAE,CAAC;AAEf,KAAK,CAAC,KAAK,IAAI,EAAE;IACf,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,gBAAgB,CAAC;QAEjC,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YACzB,MAAM,IAAA,mCAAe,EAAC,OAAO,EAAE;gBAC7B,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC;aAC1B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YACjC,MAAM,IAAA,mCAAe,EAAC,OAAO,EAAE;gBAC7B,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC;aAC5B,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,IAAA,mCAAe,EAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAA,2CAAmB,EAAC,OAAO,EAAE;oBACjC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC;iBAC1B,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAA,2CAAmB,EAAC,OAAO,EAAE;oBACjC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC;iBAC5B,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAA,2CAAmB,EAAC,OAAO,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,MAAM,IAAA,kCAAe,EAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;IACvB,CAAC;AACH,CAAC,CAAC,EAAE,CAAC"}