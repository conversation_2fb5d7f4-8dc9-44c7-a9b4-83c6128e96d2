{"version": 3, "file": "get-embedded-compiler.js", "sourceRoot": "", "sources": ["../../tool/get-embedded-compiler.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;AAavC,kDA8BC;AAzCD,0BAA0B;AAC1B,iCAAiC;AAEjC,iCAAiC;AAEjC;;;;;GAKG;AACI,KAAK,UAAU,mBAAmB,CACvC,OAAe,EACf,OAAwC;IAExC,MAAM,IAAI,GAAG,WAAW,CAAC;IAEzB,IAAI,MAAc,CAAC;IACnB,IAAI,CAAC,OAAO,IAAI,KAAK,IAAI,OAAO,EAAE,CAAC;QACjC,KAAK,CAAC,SAAS,CAAC;YACd,IAAI;YACJ,OAAO,EAAE,OAAO;YAChB,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,MAAM;SAC5B,CAAC,CAAC;QACH,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACjC,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACxB,CAAC;IAED,6EAA6E;IAC7E,2EAA2E;IAC3E,qDAAqD;IACrD,MAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC/C,MAAM,kBAAkB,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC;IACvE,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,UAAU,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC;QAClE,MAAM,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QACzC,MAAM,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;IACvD,CAAC;IAED,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAC9B,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AACnE,CAAC;AAED,0EAA0E;AAC1E,SAAS,qBAAqB,CAAC,QAAgB;IAC7C,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE;QAC7B,GAAG,EAAE,QAAQ;QACb,MAAM,EAAE,IAAI;KACb,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,KAAK,CAAC,IAAI,CAAC,8CAA8C,EAAE;QACzD,GAAG,EAAE,QAAQ;QACb,GAAG,EAAE,EAAC,GAAG,OAAO,CAAC,GAAG,EAAE,oBAAoB,EAAE,OAAO,EAAC;KACrD,CAAC,CAAC;AACL,CAAC"}