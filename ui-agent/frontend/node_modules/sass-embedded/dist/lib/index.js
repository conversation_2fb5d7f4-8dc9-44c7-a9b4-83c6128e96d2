"use strict";
// Copyright 2021 Google Inc. Use of this source code is governed by an
// MIT-style license that can be found in the LICENSE file or at
// https://opensource.org/licenses/MIT.
Object.defineProperty(exports, "__esModule", { value: true });
exports.NULL = exports.FALSE = exports.TRUE = exports.Logger = exports.info = exports.renderSync = exports.render = exports.Version = exports.deprecations = exports.Compiler = exports.initCompiler = exports.AsyncCompiler = exports.initAsyncCompiler = exports.NodePackageImporter = exports.compileStringAsync = exports.compileAsync = exports.compileString = exports.compile = exports.Exception = exports.types = exports.SassCalculation = exports.CalculationInterpolation = exports.CalculationOperation = exports.sassNull = exports.Value = exports.SassString = exports.SassNumber = exports.SassMixin = exports.SassMap = exports.SassFunction = exports.SassColor = exports.sassTrue = exports.sassFalse = exports.SassBoolean = exports.SassArgumentList = exports.SassList = void 0;
const pkg = require("../package.json");
const boolean_1 = require("./src/value/boolean");
const null_1 = require("./src/value/null");
var list_1 = require("./src/value/list");
Object.defineProperty(exports, "SassList", { enumerable: true, get: function () { return list_1.SassList; } });
var argument_list_1 = require("./src/value/argument-list");
Object.defineProperty(exports, "SassArgumentList", { enumerable: true, get: function () { return argument_list_1.SassArgumentList; } });
var boolean_2 = require("./src/value/boolean");
Object.defineProperty(exports, "SassBoolean", { enumerable: true, get: function () { return boolean_2.SassBoolean; } });
Object.defineProperty(exports, "sassFalse", { enumerable: true, get: function () { return boolean_2.sassFalse; } });
Object.defineProperty(exports, "sassTrue", { enumerable: true, get: function () { return boolean_2.sassTrue; } });
var color_1 = require("./src/value/color");
Object.defineProperty(exports, "SassColor", { enumerable: true, get: function () { return color_1.SassColor; } });
var function_1 = require("./src/value/function");
Object.defineProperty(exports, "SassFunction", { enumerable: true, get: function () { return function_1.SassFunction; } });
var map_1 = require("./src/value/map");
Object.defineProperty(exports, "SassMap", { enumerable: true, get: function () { return map_1.SassMap; } });
var mixin_1 = require("./src/value/mixin");
Object.defineProperty(exports, "SassMixin", { enumerable: true, get: function () { return mixin_1.SassMixin; } });
var number_1 = require("./src/value/number");
Object.defineProperty(exports, "SassNumber", { enumerable: true, get: function () { return number_1.SassNumber; } });
var string_1 = require("./src/value/string");
Object.defineProperty(exports, "SassString", { enumerable: true, get: function () { return string_1.SassString; } });
var value_1 = require("./src/value");
Object.defineProperty(exports, "Value", { enumerable: true, get: function () { return value_1.Value; } });
var null_2 = require("./src/value/null");
Object.defineProperty(exports, "sassNull", { enumerable: true, get: function () { return null_2.sassNull; } });
var calculations_1 = require("./src/value/calculations");
Object.defineProperty(exports, "CalculationOperation", { enumerable: true, get: function () { return calculations_1.CalculationOperation; } });
Object.defineProperty(exports, "CalculationInterpolation", { enumerable: true, get: function () { return calculations_1.CalculationInterpolation; } });
Object.defineProperty(exports, "SassCalculation", { enumerable: true, get: function () { return calculations_1.SassCalculation; } });
exports.types = require("./src/legacy/value");
var exception_1 = require("./src/exception");
Object.defineProperty(exports, "Exception", { enumerable: true, get: function () { return exception_1.Exception; } });
var compile_1 = require("./src/compile");
Object.defineProperty(exports, "compile", { enumerable: true, get: function () { return compile_1.compile; } });
Object.defineProperty(exports, "compileString", { enumerable: true, get: function () { return compile_1.compileString; } });
Object.defineProperty(exports, "compileAsync", { enumerable: true, get: function () { return compile_1.compileAsync; } });
Object.defineProperty(exports, "compileStringAsync", { enumerable: true, get: function () { return compile_1.compileStringAsync; } });
Object.defineProperty(exports, "NodePackageImporter", { enumerable: true, get: function () { return compile_1.NodePackageImporter; } });
var async_1 = require("./src/compiler/async");
Object.defineProperty(exports, "initAsyncCompiler", { enumerable: true, get: function () { return async_1.initAsyncCompiler; } });
Object.defineProperty(exports, "AsyncCompiler", { enumerable: true, get: function () { return async_1.AsyncCompiler; } });
var sync_1 = require("./src/compiler/sync");
Object.defineProperty(exports, "initCompiler", { enumerable: true, get: function () { return sync_1.initCompiler; } });
Object.defineProperty(exports, "Compiler", { enumerable: true, get: function () { return sync_1.Compiler; } });
var deprecations_1 = require("./src/deprecations");
Object.defineProperty(exports, "deprecations", { enumerable: true, get: function () { return deprecations_1.deprecations; } });
var version_1 = require("./src/version");
Object.defineProperty(exports, "Version", { enumerable: true, get: function () { return version_1.Version; } });
var legacy_1 = require("./src/legacy");
Object.defineProperty(exports, "render", { enumerable: true, get: function () { return legacy_1.render; } });
Object.defineProperty(exports, "renderSync", { enumerable: true, get: function () { return legacy_1.renderSync; } });
exports.info = `sass-embedded\t${pkg.version}`;
var logger_1 = require("./src/logger");
Object.defineProperty(exports, "Logger", { enumerable: true, get: function () { return logger_1.Logger; } });
// Legacy JS API
exports.TRUE = boolean_1.sassTrue;
exports.FALSE = boolean_1.sassFalse;
exports.NULL = null_1.sassNull;
//# sourceMappingURL=index.js.map