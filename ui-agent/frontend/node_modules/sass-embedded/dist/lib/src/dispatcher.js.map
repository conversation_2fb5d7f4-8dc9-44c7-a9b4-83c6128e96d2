{"version": 3, "file": "dispatcher.js", "sourceRoot": "", "sources": ["../../../lib/src/dispatcher.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;;AAEvC,+BAAyC;AACzC,8CAAgE;AAChE,iDAA0C;AAG1C,mDAAmD;AACnD,uDAAiD;AACjD,mCAAoE;AAQpE;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAa,UAAU;IAmCF;IACA;IAGA;IAGA;IAzCnB,6EAA6E;IAC7E,kCAAkC;IACjB,uBAAuB,GAAG,IAAI,gCAAc,EAAE,CAAC;IAEhE,4EAA4E;IAC5E,wCAAwC;IACvB,SAAS,GAAG,IAAI,cAAO,EAAyB,CAAC;IAElE,oEAAoE;IACpE,qEAAqE;IACrE,sDAAsD;IACrC,YAAY,GAAG,IAAI,cAAO,EAAQ,CAAC;IAEpD,yEAAyE;IACzE,oCAAoC;IACnB,cAAc,GAAG,IAAI,cAAO,EAAQ,CAAC;IAEtD;;;;OAIG;IACM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;IAE7C;;;OAGG;IACM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CACvC,IAAA,kBAAM,EAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,EACtD,IAAA,eAAG,EAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAuC,CAAC,CACxE,CAAC;IAEF,YACmB,aAAqB,EACrB,iBAEhB,EACgB,mBAER,EACQ,uBAAiD;QAPjD,kBAAa,GAAb,aAAa,CAAQ;QACrB,sBAAiB,GAAjB,iBAAiB,CAEjC;QACgB,wBAAmB,GAAnB,mBAAmB,CAE3B;QACQ,4BAAuB,GAAvB,uBAAuB,CAA0B;QAElE,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,KAAK,CAAC,0BAA0B,aAAa,GAAG,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,iBAAiB;aACnB,IAAI,CACH,IAAA,kBAAM,EAAC,CAAC,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,EACjE,IAAA,eAAG,EAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAC7B,IAAA,oBAAQ,EAAC,OAAO,CAAC,EAAE;YACjB,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACnD,OAAO,MAAM,YAAY,OAAO;gBAC9B,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC;gBAC5B,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAChB,CAAC,CAAC,EACF,IAAA,qBAAS,EAAC,IAAI,CAAC,YAAY,CAAC,CAC7B;aACA,SAAS,CAAC;YACT,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;YACzC,QAAQ,EAAE,GAAG,EAAE;gBACb,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC1B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YACjC,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACH,kBAAkB,CAChB,OAA4C,EAC5C,QAA0B;QAE1B,0CAA0C;QAC1C,MAAM,SAAS,GAAqB,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YACpD,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACjC,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YAC7B,SAAS,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,EAAE,SAAS,CAAC,CAAC;YACtE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS;aACX,IAAI,CACH,IAAA,kBAAM,EAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,iBAAiB,CAAC,EAC7D,IAAA,eAAG,EAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAyB,CAAC,CAC1D;aACA,SAAS,CAAC,EAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAC,CAAC,CAAC;QAE5D,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACpB,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC;SAC5C,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,IAAI,CAAC,mBAAmB,CAAC;gBACvB,IAAI,CAAC,aAAa;gBAClB,IAAA,iBAAM,EAAC,KAAK,CAAC,oBAAoB,EAAE;oBACjC,OAAO,EAAE,EAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,gBAAgB,EAAC;iBAClD,CAAC;aACH,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,0CAA0C;IAClC,WAAW;QACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;IAC/B,CAAC;IAED,uEAAuE;IACvE,8DAA8D;IACtD,aAAa,CAAC,KAAc;QAClC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,6EAA6E;IAC7E,wEAAwE;IACxE,2EAA2E;IAC3E,uCAAuC;IAC/B,qBAAqB,CAC3B,OAA8B;QAE9B,QAAQ,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC7B,KAAK,UAAU;gBACb,sCAAsC;gBACtC,OAAO,SAAS,CAAC;YAEnB,KAAK,iBAAiB;gBACpB,8CAA8C;gBAC9C,OAAO,SAAS,CAAC;YAEnB,KAAK,eAAe,CAAC,CAAC,CAAC;gBACrB,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;gBACtC,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,gBAAgB,CAAC;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBAE3C,OAAO,IAAA,cAAM,EACX,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,OAAO,CAAC,EACzD,QAAQ,CAAC,EAAE;oBACT,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC;gBAC7D,CAAC,CACF,CAAC;YACJ,CAAC;YAED,KAAK,mBAAmB,CAAC,CAAC,CAAC;gBACzB,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;gBACtC,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,oBAAoB,CAAC;gBAClC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBAC3C,OAAO,IAAA,cAAM,EACX,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAC7D,QAAQ,CAAC,EAAE;oBACT,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC;gBAC7D,CAAC,CACF,CAAC;YACJ,CAAC;YAED,KAAK,qBAAqB,CAAC,CAAC,CAAC;gBAC3B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;gBACtC,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,sBAAsB,CAAC;gBACpC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBAC3C,OAAO,IAAA,cAAM,EACX,IAAI,CAAC,uBAAuB,CAAC,yBAAyB,CAAC,OAAO,CAAC,EAC/D,QAAQ,CAAC,EAAE;oBACT,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC;gBAC7D,CAAC,CACF,CAAC;YACJ,CAAC;YAED,KAAK,qBAAqB,CAAC,CAAC,CAAC;gBAC3B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;gBACtC,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,sBAAsB,CAAC;gBACpC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBAC3C,OAAO,IAAA,cAAM,EACX,IAAI,CAAC,uBAAuB,CAAC,yBAAyB,CAAC,OAAO,CAAC,EAC/D,QAAQ,CAAC,EAAE;oBACT,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC;gBAC7D,CAAC,CACF,CAAC;YACJ,CAAC;YAED,KAAK,OAAO;gBACV,MAAM,IAAA,iBAAS,EAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEjD;gBACE,MAAM,IAAA,qBAAa,EAAC,wBAAwB,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,wEAAwE;IAChE,kBAAkB,CACxB,SAAiB,EACjB,OAGC;QAED,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC;QAE7B,IACE,OAAO,CAAC,IAAI,KAAK,gBAAgB;YACjC,OAAO,CAAC,IAAI,KAAK,oBAAoB;YACrC,OAAO,CAAC,IAAI,KAAK,sBAAsB;YACvC,OAAO,CAAC,IAAI,KAAK,sBAAsB,EACvC,CAAC;YACD,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAC,wBAAwB,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC;YACvB,IAAI,CAAC,aAAa;YAClB,IAAA,iBAAM,EAAC,KAAK,CAAC,oBAAoB,EAAE,EAAC,OAAO,EAAC,CAAC;SAC9C,CAAC,CAAC;IACL,CAAC;CACF;AA1OD,gCA0OC"}