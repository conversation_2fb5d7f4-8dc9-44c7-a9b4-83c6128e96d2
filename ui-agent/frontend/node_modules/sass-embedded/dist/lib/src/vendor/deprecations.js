"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deprecations = void 0;
const version_1 = require("../version");
exports.deprecations = {
    'call-string': {
        id: 'call-string',
        description: 'Passing a string directly to meta.call().',
        status: 'active',
        deprecatedIn: new version_1.Version(0, 0, 0),
        obsoleteIn: null,
    },
    elseif: {
        id: 'elseif',
        description: '@elseif.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 3, 2),
        obsoleteIn: null,
    },
    'moz-document': {
        id: 'moz-document',
        description: '@-moz-document.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 7, 2),
        obsoleteIn: null,
    },
    'relative-canonical': {
        id: 'relative-canonical',
        description: 'Imports using relative canonical URLs.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 14, 2),
        obsoleteIn: null,
    },
    'new-global': {
        id: 'new-global',
        description: 'Declaring new variables with !global.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 17, 2),
        obsoleteIn: null,
    },
    'color-module-compat': {
        id: 'color-module-compat',
        description: 'Using color module functions in place of plain CSS functions.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 23, 0),
        obsoleteIn: null,
    },
    'slash-div': {
        id: 'slash-div',
        description: '/ operator for division.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 33, 0),
        obsoleteIn: null,
    },
    'bogus-combinators': {
        id: 'bogus-combinators',
        description: 'Leading, trailing, and repeated combinators.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 54, 0),
        obsoleteIn: null,
    },
    'strict-unary': {
        id: 'strict-unary',
        description: 'Ambiguous + and - operators.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 55, 0),
        obsoleteIn: null,
    },
    'function-units': {
        id: 'function-units',
        description: 'Passing invalid units to built-in functions.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 56, 0),
        obsoleteIn: null,
    },
    'duplicate-var-flags': {
        id: 'duplicate-var-flags',
        description: 'Using !default or !global multiple times for one variable.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 62, 0),
        obsoleteIn: null,
    },
    'null-alpha': {
        id: 'null-alpha',
        description: 'Passing null as alpha in the $PLATFORM API.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 62, 3),
        obsoleteIn: null,
    },
    'abs-percent': {
        id: 'abs-percent',
        description: 'Passing percentages to the Sass abs() function.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 65, 0),
        obsoleteIn: null,
    },
    'fs-importer-cwd': {
        id: 'fs-importer-cwd',
        description: 'Using the current working directory as an implicit load path.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 73, 0),
        obsoleteIn: null,
    },
    'css-function-mixin': {
        id: 'css-function-mixin',
        description: 'Function and mixin names beginning with --.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 76, 0),
        obsoleteIn: null,
    },
    'mixed-decls': {
        id: 'mixed-decls',
        description: 'Declarations after or between nested rules.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 77, 7),
        obsoleteIn: null,
    },
    'feature-exists': {
        id: 'feature-exists',
        description: 'meta.feature-exists',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 78, 0),
        obsoleteIn: null,
    },
    'color-4-api': {
        id: 'color-4-api',
        description: 'Certain uses of built-in sass:color functions.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 79, 0),
        obsoleteIn: null,
    },
    'color-functions': {
        id: 'color-functions',
        description: 'Using global color functions instead of sass:color.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 79, 0),
        obsoleteIn: null,
    },
    'legacy-js-api': {
        id: 'legacy-js-api',
        description: 'Legacy JS API.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 79, 0),
        obsoleteIn: null,
    },
    import: {
        id: 'import',
        description: '@import rules.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 80, 0),
        obsoleteIn: null,
    },
    'global-builtin': {
        id: 'global-builtin',
        description: 'Global built-in functions that are available in sass: modules.',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 80, 0),
        obsoleteIn: null,
    },
    'type-function': {
        id: 'type-function',
        description: 'Functions named "type".',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 86, 0),
        obsoleteIn: null,
    },
    'compile-string-relative-url': {
        id: 'compile-string-relative-url',
        description: 'Passing a relative url to compileString().',
        status: 'active',
        deprecatedIn: new version_1.Version(1, 88, 0),
        obsoleteIn: null,
    },
    'user-authored': {
        id: 'user-authored',
        status: 'user',
        deprecatedIn: null,
        obsoleteIn: null,
    },
};
//# sourceMappingURL=deprecations.js.map