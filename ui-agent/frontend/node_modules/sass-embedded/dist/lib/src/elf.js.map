{"version": 3, "file": "elf.js", "sourceRoot": "", "sources": ["../../../lib/src/elf.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;AA2BvC,8CAwEC;AAjGD,yBAAyB;AAEzB,qEAAqE;AACrE,SAAS,kBAAkB,CACzB,EAAU,EACV,QAAgB,EAChB,MAAc;IAEd,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACpC,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,OAAO,MAAM,GAAG,MAAM,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE;YACxC,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,QAAQ,GAAG,MAAM;SAC5B,CAAC,CAAC;QACH,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,SAAS,CAAC;IACtB,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,oDAAoD;AACpD,SAAgB,iBAAiB,CAAC,IAAY;IAC5C,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAClC,IAAI,CAAC;QACH,MAAM,iBAAiB,GAAG,IAAI,QAAQ,CACpC,kBAAkB,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,CACrC,CAAC;QAEF,IACE,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI;YACtC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI;YACtC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI;YACtC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,EACtC,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,sBAAsB,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7D,IAAI,sBAAsB,KAAK,CAAC,IAAI,sBAAsB,KAAK,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,4BAA4B,CAAC,CAAC;QACvD,CAAC;QACD,MAAM,UAAU,GAAG,sBAAsB,KAAK,CAAC,CAAC;QAEhD,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,qBAAqB,KAAK,CAAC,IAAI,qBAAqB,KAAK,CAAC,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,6BAA6B,CAAC,CAAC;QACxD,CAAC;QACD,MAAM,YAAY,GAAG,qBAAqB,KAAK,CAAC,CAAC;QAEjD,sEAAsE;QACtE,wEAAwE;QACxE,yCAAyC;QACzC,MAAM,oBAAoB,GAAG,UAAU;YACrC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,YAAY,CAAC;YAC/C,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC;QAC7D,MAAM,uBAAuB,GAAG,UAAU;YACxC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,YAAY,CAAC;YAC/C,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;QAClD,MAAM,wBAAwB,GAAG,UAAU;YACzC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,YAAY,CAAC;YAC/C,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;QAElD,MAAM,cAAc,GAAG,IAAI,QAAQ,CACjC,kBAAkB,CAChB,EAAE,EACF,oBAAoB,EACpB,uBAAuB,GAAG,wBAAwB,CACnD,CAAC,MAAM,CACT,CAAC;QACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,wBAAwB,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,UAAU,GAAG,CAAC,GAAG,uBAAuB,CAAC;YAC/C,MAAM,WAAW,GAAG,cAAc,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YACvE,IAAI,WAAW,KAAK,CAAC;gBAAE,SAAS,CAAC,kCAAkC;YAEnE,MAAM,aAAa,GAAG,UAAU;gBAC9B,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,YAAY,CAAC;gBACxD,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;YACtE,MAAM,eAAe,GAAG,UAAU;gBAChC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,EAAE,YAAY,CAAC;gBACzD,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,GAAG,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,kBAAkB,CAAC,EAAE,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;YACtE,IAAI,MAAM,CAAC,eAAe,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,gBAAgB,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,yCAAyC,CAAC,CAAC;IACpE,CAAC;YAAS,CAAC;QACT,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC;AACH,CAAC"}