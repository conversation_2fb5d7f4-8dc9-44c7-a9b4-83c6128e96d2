{"version": 3, "file": "deprecations.js", "sourceRoot": "", "sources": ["../../../lib/src/deprecations.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;;AAYvC,8CAWC;AA0BD,gEAkBC;AAhED,uCAAkC;AAElC,sDAAmD;AAA3C,4GAAA,YAAY,OAAA;AAGpB;;;GAGG;AACH,SAAgB,iBAAiB,CAC/B,GAAkC;IAElC,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACpB,IAAI,IAAI,YAAY,iBAAO,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,CAAC;aAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACU,QAAA,wBAAwB,GACnC,IAAI,GAAG,EAAE,CAAC;AAUZ;;;;;;GAMG;AACH,SAAgB,0BAA0B,CACxC,OAAe,EACf,WAAwB,EACxB,OAA4B;IAE5B,IACE,WAAW,CAAC,MAAM,KAAK,QAAQ;QAC/B,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,EACtC,CAAC;QACD,OAAO;IACT,CAAC;IACD,MAAM,WAAW,GAAG,gBAAgB,WAAW,CAAC,EAAE,MAAM,OAAO,EAAE,CAAC;IAClE,IAAI,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC;QAClC,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC;IAC3B,CAAC;IACD,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5B,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CACf,WAAwB,EACxB,OAA4B;IAE5B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,KAAK,MAAM,gBAAgB,IAAI,gCAAwB,CAAC,MAAM,EAAE,EAAE,CAAC;YACjE,IAAI,QAAQ,CAAC,WAAW,EAAE,gBAAgB,CAAC;gBAAE,OAAO,IAAI,CAAC;QAC3D,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,IAAI,EAAE,CAAC,CAAC,QAAQ,CAClE,WAAW,CAAC,EAAE,CACf,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,eAAe,CACtB,WAAwB,EACxB,OAA4B;IAE5B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,KAAK,MAAM,gBAAgB,IAAI,gCAAwB,CAAC,MAAM,EAAE,EAAE,CAAC;YACjE,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,gBAAgB,CAAC;gBAAE,OAAO,KAAK,CAAC;QACpE,CAAC;QACD,OAAO,gCAAwB,CAAC,IAAI,GAAG,CAAC,CAAC;IAC3C,CAAC;IACD,OAAO,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,QAAQ,CACjE,WAAW,CAAC,EAAE,CACf,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,OAAO,CACd,WAAwB,EACxB,OAA4B;IAE5B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,KAAK,MAAM,gBAAgB,IAAI,gCAAwB,CAAC,MAAM,EAAE,EAAE,CAAC;YACjE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,gBAAgB,CAAC;gBAAE,OAAO,KAAK,CAAC;QAC5D,CAAC;QACD,OAAO,gCAAwB,CAAC,IAAI,GAAG,CAAC,CAAC;IAC3C,CAAC;IACD,MAAM,aAAa,GACjB,WAAW,CAAC,YAAY,KAAK,IAAI;QAC/B,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,GAAG,OAAO;YACxC,WAAW,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI;YACrC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC;IACrC,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,iBAAiB,IAAI,EAAE,EAAE,CAAC;QACpD,IAAI,KAAK,YAAY,iBAAO,EAAE,CAAC;YAC7B,IAAI,aAAa,KAAK,IAAI;gBAAE,SAAS;YACrC,IACE,aAAa;gBACb,KAAK,CAAC,KAAK,GAAG,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,KAAK,EACxD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACrC,IAAI,KAAK,KAAK,WAAW,CAAC,EAAE;gBAAE,OAAO,IAAI,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,IAAK,KAAqB,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE;gBAAE,OAAO,IAAI,CAAC;QAChE,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}