{"version": 3, "file": "importer-registry.js", "sourceRoot": "", "sources": ["../../../lib/src/importer-registry.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;;AAEvC,mCAAqC;AACrC,0BAA0B;AAC1B,6BAAwB;AACxB,+BAA6B;AAC7B,iDAA0C;AAE1C,iEAA2D;AAC3D,iCAAiC;AAEjC,mDAAmD;AACnD,mCAAmD;AAEnD,MAAM,sBAAsB,GAAG,MAAM,EAAE,CAAC;AAExC,MAAa,mBAAmB;IACrB,CAAC,sBAAsB,CAAC,CAAS;IAE1C,YAAY,mBAA4B;QACtC,mBAAmB,GAAG,mBAAmB;YACvC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAChC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ;gBACtB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAClC,CAAC,CAAC,kDAAkD;oBAClD,uDAAuD;oBACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;wBACf,CAAC,CAAC,IAAA,sBAAa,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACzD,CAAC,CAAC,SAAS,CAAC;QAClB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CACb,4DAA4D;gBAC1D,kDAAkD;gBAClD,uEAAuE,CAC1E,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,sBAAsB,CAAC,GAAG,mBAAmB,CAAC;IACrD,CAAC;CACF;AAtBD,kDAsBC;AAED;;;GAGG;AACH,MAAa,gBAAgB;IAC3B,mEAAmE;IAC1D,SAAS,CAAiD;IAEnE,gEAAgE;IAC/C,aAAa,GAAG,IAAI,GAAG,EAA0B,CAAC;IAEnE,qEAAqE;IACpD,iBAAiB,GAAG,IAAI,GAAG,EAA8B,CAAC;IAE3E,0CAA0C;IAClC,EAAE,GAAG,CAAC,CAAC;IAEf,YAAY,OAAuB;QACjC,IAAI,CAAC,SAAS,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC;aACxC,GAAG,CAAC,QAAQ,CAAC,EAAE,CACd,IAAI,CAAC,QAAQ,CACX,QAAqE,CACtE,CACF;aACA,MAAM,CACL,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACpC,IAAA,iBAAM,EAAC,KAAK,CAAC,4CAA4C,EAAE;YACzD,QAAQ,EAAE,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC;SACjD,CAAC,CACH,CACF,CAAC;IACN,CAAC;IAED,6EAA6E;IAC7E,QAAQ,CACN,QAAmE;QAEnE,MAAM,OAAO,GAAG,IAAA,iBAAM,EACpB,KAAK,CAAC,4CAA4C,EAClD,EAAE,CACH,CAAC;QACF,IAAI,QAAQ,YAAY,mBAAmB,EAAE,CAAC;YAC5C,MAAM,eAAe,GAAG,IAAA,iBAAM,EAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC9D,mBAAmB,EAAE,QAAQ,CAAC,sBAAsB,CAAC;aACtD,CAAC,CAAC;YACH,OAAO,CAAC,QAAQ,GAAG;gBACjB,IAAI,EAAE,qBAAqB;gBAC3B,KAAK,EAAE,eAAe;aACvB,CAAC;QACJ,CAAC;aAAM,IAAI,cAAc,IAAI,QAAQ,EAAE,CAAC;YACtC,IAAI,aAAa,IAAI,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CACb,kEAAkE;oBAChE,IAAA,cAAO,EAAC,QAAQ,CAAC,CACpB,CAAC;YACJ,CAAC;YAED,OAAO,CAAC,QAAQ,GAAG,EAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAC,CAAC;YACxD,OAAO,CAAC,kBAAkB;gBACxB,OAAO,QAAQ,CAAC,kBAAkB,KAAK,QAAQ;oBAC7C,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC;oBAC/B,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;YAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,QAAQ,GAAG,EAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAC,CAAC;YAC5D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC;QACD,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QACb,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,0CAA0C;IAC1C,YAAY,CACV,OAAkD;QAElD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,KAAK,CAAC,aAAa,CAAC,yCAAyC,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAI,0CAAmB,CACjD,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,SAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,EAC7D,OAAO,CAAC,UAAU,CACnB,CAAC;QAEF,OAAO,IAAA,eAAO,EACZ,GAAG,EAAE;YACH,OAAO,IAAA,cAAM,EACX,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,mBAAmB,CAAC,EACvD,GAAG,CAAC,EAAE,CACJ,IAAA,iBAAM,EAAC,KAAK,CAAC,yCAAyC,EAAE;gBACtD,MAAM,EACJ,GAAG,KAAK,IAAI;oBACV,CAAC,CAAC,EAAC,IAAI,EAAE,SAAS,EAAC;oBACnB,CAAC,CAAC,EAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAC;gBAC1C,mBAAmB,EAAE,CAAC,mBAAmB,CAAC,qBAAqB;aAChE,CAAC,CACL,CAAC;QACJ,CAAC,EACD,KAAK,CAAC,EAAE,CACN,IAAA,iBAAM,EAAC,KAAK,CAAC,yCAAyC,EAAE;YACtD,MAAM,EAAE,EAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,EAAC;SAC3C,CAAC,CACL,CAAC;IACJ,CAAC;IAED,iCAAiC;IACjC,MAAM,CACJ,OAA4C;QAE5C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,KAAK,CAAC,aAAa,CAAC,mCAAmC,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,IAAA,eAAO,EACZ,GAAG,EAAE;YACH,OAAO,IAAA,cAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,SAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;gBAC1D,IAAI,CAAC,MAAM;oBACT,OAAO,IAAA,iBAAM,EAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAC;gBAE/D,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBACxC,MAAM,KAAK,CACT,0DACG,MAAM,CAAC,QAAe,CAAC,WAAW,CAAC,IACtC,EAAE,CACH,CAAC;gBACJ,CAAC;gBAED,IAAI,MAAM,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;oBACzD,MAAM,KAAK,CACT,6DAA6D;wBAC3D,MAAM,CAAC,YAAY,CACtB,CAAC;gBACJ,CAAC;gBAED,OAAO,IAAA,iBAAM,EAAC,KAAK,CAAC,mCAAmC,EAAE;oBACvD,MAAM,EAAE;wBACN,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE;4BACL,QAAQ,EAAE,MAAM,CAAC,QAAQ;4BACzB,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC;4BAC1C,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE;yBACpD;qBACF;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,EACD,KAAK,CAAC,EAAE,CACN,IAAA,iBAAM,EAAC,KAAK,CAAC,mCAAmC,EAAE;YAChD,MAAM,EAAE,EAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,EAAC;SAC3C,CAAC,CACL,CAAC;IACJ,CAAC;IAED,qCAAqC;IACrC,UAAU,CACR,OAAgD;QAEhD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,KAAK,CAAC,aAAa,CAAC,uCAAuC,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAI,0CAAmB,CACjD,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,SAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,EAC7D,OAAO,CAAC,UAAU,CACnB,CAAC;QAEF,OAAO,IAAA,eAAO,EACZ,GAAG,EAAE;YACH,OAAO,IAAA,cAAM,EACX,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,mBAAmB,CAAC,EACtD,GAAG,CAAC,EAAE;gBACJ,IAAI,CAAC,GAAG,EAAE,CAAC;oBACT,OAAO,IAAA,iBAAM,EAAC,KAAK,CAAC,uCAAuC,EAAE;wBAC3D,mBAAmB,EAAE,CAAC,mBAAmB,CAAC,qBAAqB;qBAChE,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;oBAC7B,MAAM,CACJ,gBAAgB,IAAA,cAAO,EAAC,QAAQ,CAAC,0BAA0B;wBAC3D,CAAC,IAAI,GAAG,cAAc,OAAO,CAAC,GAAG,IAAI,CACtC,CAAC;gBACJ,CAAC;gBACD,OAAO,IAAA,iBAAM,EAAC,KAAK,CAAC,uCAAuC,EAAE;oBAC3D,MAAM,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAC;oBAChD,mBAAmB,EAAE,CAAC,mBAAmB,CAAC,qBAAqB;iBAChE,CAAC,CAAC;YACL,CAAC,CACF,CAAC;QACJ,CAAC,EACD,KAAK,CAAC,EAAE,CACN,IAAA,iBAAM,EAAC,KAAK,CAAC,uCAAuC,EAAE;YACpD,MAAM,EAAE,EAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,EAAC;SAC3C,CAAC,CACL,CAAC;IACJ,CAAC;CACF;AAlMD,4CAkMC"}