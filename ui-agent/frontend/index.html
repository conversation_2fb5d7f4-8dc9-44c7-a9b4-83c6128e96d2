<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>UI Agent - 智能自动化测试平台</title>
    <meta name="description" content="基于AI的智能自动化测试平台，支持Android和Web端自动化操作" />
    <meta name="keywords" content="自动化测试,AI,Android,Web,测试平台" />
    <style>
      /* 加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }
      
      .loading-content {
        text-align: center;
        color: white;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
      }
    </style>
  </head>
  <body>
    <div id="loading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">🤖 UI Agent</div>
        <div class="loading-subtitle">智能自动化测试平台正在启动...</div>
      </div>
    </div>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      // 页面加载完成后隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(() => {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
