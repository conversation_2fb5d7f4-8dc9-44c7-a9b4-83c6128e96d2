/**
 * 连接相关类型定义
 */

export interface ConnectionInfo {
  host: string
  port: number
}

export interface ServerInfo {
  app_name: string
  version: string
  supported_platforms: string[]
  features: string[]
}

export interface ConnectionRequest {
  host: string
  port: number
}

export interface ConnectionResponse {
  success: boolean
  message: string
  server_info: ServerInfo
}

export interface ConnectionStatus {
  status: string
  timestamp: string
  uptime: string
  active_sessions: number
}
