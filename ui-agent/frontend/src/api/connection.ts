/**
 * 连接管理API
 */

import { request } from '@/utils/request'
import type { 
  ConnectionRequest, 
  ConnectionResponse, 
  ConnectionStatus 
} from '@/types/connection'

export const connectionApi = {
  /**
   * 连接到后端服务
   */
  connect: (data: ConnectionRequest): Promise<ConnectionResponse> => {
    return request.post('/api/connection/connect', data)
  },

  /**
   * 获取连接状态
   */
  getStatus: (): Promise<ConnectionStatus> => {
    return request.get('/api/connection/status')
  },

  /**
   * 断开连接
   */
  disconnect: (): Promise<{ success: boolean; message: string }> => {
    return request.post('/api/connection/disconnect')
  }
}
