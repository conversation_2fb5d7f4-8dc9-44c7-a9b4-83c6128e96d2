/**
 * 报告管理API
 */

import { request } from '@/utils/request'

export interface GenerateReportRequest {
  task_id: number
  report_name?: string
}

export interface ReportResponse {
  id: number
  task_id: number
  report_name: string
  total_steps: number
  success_steps: number
  failed_steps: number
  execution_time?: number
  html_file_path: string
  created_at: string
}

export interface ReportDetailResponse extends ReportResponse {
  summary?: any
  steps_data?: any[]
  screenshots?: string[]
  json_file_path?: string
}

export interface Screenshot {
  id: number
  task_id: number
  step_id?: number
  file_path: string
  file_name: string
  screenshot_type: string
  automation_type: string
  has_annotations: boolean
  created_at: string
}

export interface DomSnapshot {
  id: number
  task_id: number
  step_id?: number
  screenshot_id?: number
  dom_tree: any
  viewport_info: any
  selected_element?: any
  automation_type: string
  page_url?: string
  app_package?: string
  created_at: string
}

export const reportApi = {
  /**
   * 生成任务执行报告
   */
  generateReport: (data: GenerateReportRequest): Promise<ReportResponse> => {
    return request.post('/api/report/generate', data)
  },

  /**
   * 获取报告列表
   */
  getReports: (params?: {
    skip?: number
    limit?: number
    task_id?: number
  }): Promise<ReportResponse[]> => {
    return request.get('/api/report/list', { params })
  },

  /**
   * 获取报告详情
   */
  getReport: (reportId: number): Promise<ReportDetailResponse> => {
    return request.get(`/api/report/${reportId}`)
  },

  /**
   * 下载HTML报告文件
   */
  downloadReport: (reportId: number): Promise<Blob> => {
    return request.download(`/api/report/${reportId}/download`)
  },

  /**
   * 获取报告详细数据
   */
  getReportData: (reportId: number): Promise<any> => {
    return request.get(`/api/report/${reportId}/data`)
  },

  /**
   * 删除报告
   */
  deleteReport: (reportId: number): Promise<{
    success: boolean
    message: string
  }> => {
    return request.delete(`/api/report/${reportId}`)
  },

  /**
   * 获取任务的所有截图
   */
  getTaskScreenshots: (taskId: number): Promise<Screenshot[]> => {
    return request.get(`/api/report/task/${taskId}/screenshots`)
  },

  /**
   * 获取任务的DOM快照
   */
  getTaskDomSnapshots: (taskId: number): Promise<DomSnapshot[]> => {
    return request.get(`/api/report/task/${taskId}/dom_snapshots`)
  }
}
