/**
 * 实时任务执行API
 */

import { request } from '@/utils/request'

export interface DirectExecuteRequest {
  automation_type: 'android' | 'web'
  input_type: 'text' | 'voice'
  input_content: string
  ai_model?: string
  api_base?: string
  temperature?: number
  max_scroll_times?: number
}

export interface TaskHistoryResponse {
  id: number
  automation_type: string
  input_content: string
  status: string
  result?: any
  created_at: string
  execution_time?: number
}

export interface ExecutionStepResponse {
  step_index: number
  action_type: string
  description: string
  screenshot_path?: string
  element_info?: any
  ai_response?: any
  success: boolean
  error_message?: string
  timestamp: string
}

export interface ExecutionStatusResponse {
  is_running: boolean
  history_id?: number
  running_time?: number
  completed_steps?: number
  status?: string
  message?: string
  error?: string
}

export interface DirectExecuteResponse {
  success: boolean
  message: string
  history_id?: number
  execution_time?: number
  result?: any
  error?: string
}

export const taskApi = {
  /**
   * 创建新任务
   */
  createTask: (data: CreateTaskRequest): Promise<TaskResponse> => {
    return request.post('/api/task/create', data)
  },

  /**
   * 执行任务
   */
  executeTask: (data: ExecuteTaskRequest): Promise<{
    success: boolean
    message: string
    task_id: number
    status?: string
    progress?: number
    result?: any
  }> => {
    return request.post('/api/task/execute', data)
  },

  /**
   * 获取任务列表
   */
  getTasks: (params?: {
    skip?: number
    limit?: number
    status?: string
    automation_type?: string
  }): Promise<TaskResponse[]> => {
    return request.get('/api/task/list', { params })
  },

  /**
   * 获取任务详情
   */
  getTask: (taskId: number): Promise<TaskDetailResponse> => {
    return request.get(`/api/task/${taskId}`)
  },

  /**
   * 删除任务
   */
  deleteTask: (taskId: number): Promise<{
    success: boolean
    message: string
  }> => {
    return request.delete(`/api/task/${taskId}`)
  },

  /**
   * 获取任务步骤
   */
  getTaskSteps: (taskId: number): Promise<TaskStep[]> => {
    return request.get(`/api/task/${taskId}/steps`)
  },

  /**
   * 获取任务日志
   */
  getTaskLogs: (taskId: number): Promise<TaskLog[]> => {
    return request.get(`/api/task/${taskId}/logs`)
  },

  /**
   * 停止任务执行
   */
  stopTask: (taskId: number): Promise<{
    success: boolean
    message: string
  }> => {
    return request.post(`/api/task/${taskId}/stop`)
  }
}
