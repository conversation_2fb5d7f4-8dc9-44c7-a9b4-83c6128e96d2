/**
 * 实时任务执行API
 */

import { request } from '@/utils/request'

export interface DirectExecuteRequest {
  automation_type: 'android' | 'web'
  input_type: 'text' | 'voice'
  input_content: string
  ai_model?: string
  api_base?: string
  temperature?: number
  max_scroll_times?: number
}

export interface TaskHistoryResponse {
  id: number
  automation_type: string
  input_content: string
  status: string
  result?: any
  created_at: string
  execution_time?: number
}

export interface ExecutionStepResponse {
  step_index: number
  action_type: string
  description: string
  screenshot_path?: string
  element_info?: any
  ai_response?: any
  success: boolean
  error_message?: string
  timestamp: string
}

export interface ExecutionStatusResponse {
  is_running: boolean
  history_id?: number
  running_time?: number
  completed_steps?: number
  status?: string
  message?: string
  error?: string
}

export interface DirectExecuteResponse {
  success: boolean
  message: string
  history_id?: number
  execution_time?: number
  result?: any
  error?: string
}

export const taskApi = {
  /**
   * 直接执行任务（实时执行模式）
   */
  executeDirectly: (data: DirectExecuteRequest): Promise<DirectExecuteResponse> => {
    return request.post('/api/task/execute', data)
  },

  /**
   * 获取任务执行历史
   */
  getHistory: (params?: {
    skip?: number
    limit?: number
    automation_type?: string
  }): Promise<TaskHistoryResponse[]> => {
    return request.get('/api/task/history', { params })
  },

  /**
   * 获取执行步骤详情
   */
  getExecutionSteps: (historyId: number): Promise<ExecutionStepResponse[]> => {
    return request.get(`/api/task/history/${historyId}/steps`)
  },

  /**
   * 删除任务历史记录
   */
  deleteHistory: (historyId: number): Promise<{
    success: boolean
    message: string
  }> => {
    return request.delete(`/api/task/history/${historyId}`)
  },

  /**
   * 停止当前执行的任务
   */
  stopExecution: (): Promise<{
    success: boolean
    message: string
  }> => {
    return request.post('/api/task/stop')
  },

  /**
   * 获取当前执行状态
   */
  getExecutionStatus: (): Promise<ExecutionStatusResponse> => {
    return request.get('/api/task/status')
  }
}
