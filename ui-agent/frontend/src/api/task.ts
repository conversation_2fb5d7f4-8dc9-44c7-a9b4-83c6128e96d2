/**
 * 任务管理API
 */

import { request } from '@/utils/request'

export interface CreateTaskRequest {
  title: string
  description: string
  automation_type: 'android' | 'web'
  input_type: 'text' | 'voice'
  input_content: string
  ai_model?: string
  api_base?: string
  temperature?: number
}

export interface TaskResponse {
  id: number
  title: string
  description: string
  automation_type: string
  status: string
  progress: number
  created_at: string
  updated_at?: string
}

export interface TaskDetailResponse extends TaskResponse {
  input_type: string
  input_content: string
  ai_model: string
  api_base: string
  temperature: number
  result?: any
  error_message?: string
  started_at?: string
  completed_at?: string
}

export interface ExecuteTaskRequest {
  task_id: number
  max_scroll_times?: number
}

export interface TaskStep {
  id: number
  task_id: number
  step_index: number
  action: string
  element_info?: any
  code_info?: any
  status: string
  result?: any
  observations?: string
  screenshot_path?: string
  created_at: string
  executed_at?: string
}

export interface TaskLog {
  id: number
  task_id: number
  step_id?: number
  level: string
  message: string
  details?: any
  created_at: string
}

export const taskApi = {
  /**
   * 创建新任务
   */
  createTask: (data: CreateTaskRequest): Promise<TaskResponse> => {
    return request.post('/api/task/create', data)
  },

  /**
   * 执行任务
   */
  executeTask: (data: ExecuteTaskRequest): Promise<{
    success: boolean
    message: string
    task_id: number
    status?: string
    progress?: number
    result?: any
  }> => {
    return request.post('/api/task/execute', data)
  },

  /**
   * 获取任务列表
   */
  getTasks: (params?: {
    skip?: number
    limit?: number
    status?: string
    automation_type?: string
  }): Promise<TaskResponse[]> => {
    return request.get('/api/task/list', { params })
  },

  /**
   * 获取任务详情
   */
  getTask: (taskId: number): Promise<TaskDetailResponse> => {
    return request.get(`/api/task/${taskId}`)
  },

  /**
   * 删除任务
   */
  deleteTask: (taskId: number): Promise<{
    success: boolean
    message: string
  }> => {
    return request.delete(`/api/task/${taskId}`)
  },

  /**
   * 获取任务步骤
   */
  getTaskSteps: (taskId: number): Promise<TaskStep[]> => {
    return request.get(`/api/task/${taskId}/steps`)
  },

  /**
   * 获取任务日志
   */
  getTaskLogs: (taskId: number): Promise<TaskLog[]> => {
    return request.get(`/api/task/${taskId}/logs`)
  },

  /**
   * 停止任务执行
   */
  stopTask: (taskId: number): Promise<{
    success: boolean
    message: string
  }> => {
    return request.post(`/api/task/${taskId}/stop`)
  }
}
