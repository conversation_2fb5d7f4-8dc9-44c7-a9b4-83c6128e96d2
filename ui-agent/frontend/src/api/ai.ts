/**
 * AI执行API
 */

import { request } from '@/utils/request'

export interface ProcessComplexAIExecRequest {
  automation_type: 'android' | 'web'
  bdd_script: string
  last_result?: string
  last_step_result?: string
  max_scroll_times?: number
}

export interface AIConfigRequest {
  model_name: string
  api_base: string
  temperature?: number
}

export interface AIModel {
  name: string
  description: string
  provider: string
}

export const aiApi = {
  /**
   * 执行AI智能规划任务
   */
  processComplexAIExec: (data: ProcessComplexAIExecRequest): Promise<string> => {
    return request.post('/api/ai/process_complex_ai_exec', data)
  },

  /**
   * 设置AI配置
   */
  setConfig: (data: AIConfigRequest): Promise<{
    success: boolean
    message: string
    config?: any
  }> => {
    return request.post('/api/ai/config', data)
  },

  /**
   * 获取AI配置
   */
  getConfig: (): Promise<{
    success: boolean
    config: {
      model_name: string
      api_base: string
      temperature: number
    }
  }> => {
    return request.get('/api/ai/config')
  },

  /**
   * 测试AI连接
   */
  testConnection: (data: AIConfigRequest): Promise<{
    success: boolean
    message: string
    model_name?: string
    api_base?: string
    response_time?: string
  }> => {
    return request.post('/api/ai/test_connection', data)
  },

  /**
   * 获取可用的AI模型列表
   */
  getAvailableModels: (): Promise<{
    success: boolean
    models: AIModel[]
  }> => {
    return request.get('/api/ai/models')
  }
}
