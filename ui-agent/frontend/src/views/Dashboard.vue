<template>
  <div class="dashboard-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <el-icon size="32" color="#409EFF">
            <Robot />
          </el-icon>
          <span v-show="!sidebarCollapsed" class="logo-text">UI Agent</span>
        </div>
        <el-button
          text
          @click="toggleSidebar"
          class="collapse-btn"
        >
          <el-icon>
            <Fold v-if="!sidebarCollapsed" />
            <Expand v-else />
          </el-icon>
        </el-button>
      </div>

      <nav class="sidebar-nav">
        <el-menu
          :default-active="$route.name"
          :collapse="sidebarCollapsed"
          router
          class="nav-menu"
        >
          <el-menu-item index="Android">
            <el-icon><Mobile /></el-icon>
            <span>Android端</span>
          </el-menu-item>
          <el-menu-item index="Web">
            <el-icon><Monitor /></el-icon>
            <span>Web端</span>
          </el-menu-item>
          <el-menu-item index="Task">
            <el-icon><List /></el-icon>
            <span>任务管理</span>
          </el-menu-item>
          <el-menu-item index="Report">
            <el-icon><Document /></el-icon>
            <span>报告中心</span>
          </el-menu-item>
          <el-menu-item index="Settings">
            <el-icon><Setting /></el-icon>
            <span>设置</span>
          </el-menu-item>
        </el-menu>
      </nav>

      <div class="sidebar-footer">
        <el-dropdown trigger="click" placement="top-start">
          <div class="user-info">
            <el-avatar size="small">
              <el-icon><User /></el-icon>
            </el-avatar>
            <span v-show="!sidebarCollapsed" class="username">管理员</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleDisconnect">
                <el-icon><SwitchButton /></el-icon>
                断开连接
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部状态栏 -->
      <header class="top-bar">
        <div class="status-info">
          <div class="connection-status">
            <el-icon color="#67C23A"><Connection /></el-icon>
            <span>已连接到 {{ connectionStore.serverUrl }}</span>
          </div>
          <div class="server-info" v-if="connectionStore.serverInfo">
            <el-tag size="small" type="success">
              {{ connectionStore.serverInfo.app_name }} v{{ connectionStore.serverInfo.version }}
            </el-tag>
          </div>
        </div>
        <div class="actions">
          <el-button size="small" @click="refreshStatus">
            <el-icon><Refresh /></el-icon>
            刷新状态
          </el-button>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="page-content">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useConnectionStore } from '@/stores/connection'
import {
  Robot,
  Mobile,
  Monitor,
  List,
  Document,
  Setting,
  User,
  Connection,
  SwitchButton,
  Fold,
  Expand,
  Refresh
} from '@element-plus/icons-vue'

const router = useRouter()
const connectionStore = useConnectionStore()

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 断开连接
const handleDisconnect = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要断开与服务器的连接吗？',
      '确认断开',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await connectionStore.disconnect()
    router.push('/')
  } catch (error) {
    // 用户取消操作
  }
}

// 刷新状态
const refreshStatus = async () => {
  try {
    const isConnected = await connectionStore.checkStatus()
    if (isConnected) {
      ElMessage.success('连接状态正常')
    } else {
      ElMessage.warning('连接已断开')
      router.push('/')
    }
  } catch (error) {
    ElMessage.error('检查连接状态失败')
  }
}

// 组件挂载
onMounted(() => {
  // 检查连接状态
  if (!connectionStore.isConnected) {
    router.push('/')
  }
})
</script>

<style lang="scss" scoped>
.dashboard-layout {
  display: flex;
  height: 100vh;
  background: var(--el-bg-color-page);
}

.sidebar {
  width: 240px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .logo-text {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .collapse-btn {
      padding: 8px;
    }
  }
  
  .sidebar-nav {
    flex: 1;
    padding: 16px 0;
    
    .nav-menu {
      border: none;
      
      .el-menu-item {
        margin: 4px 12px;
        border-radius: 6px;
        
        &:hover {
          background: var(--el-color-primary-light-9);
        }
        
        &.is-active {
          background: var(--el-color-primary);
          color: white;
          
          .el-icon {
            color: white;
          }
        }
      }
    }
  }
  
  .sidebar-footer {
    padding: 16px;
    border-top: 1px solid var(--el-border-color-light);
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      transition: background-color 0.3s;
      
      &:hover {
        background: var(--el-bg-color-page);
      }
      
      .username {
        font-size: 14px;
        color: var(--el-text-color-primary);
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .top-bar {
    height: 60px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    
    .status-info {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .connection-status {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
    
    .actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .page-content {
    flex: 1;
    overflow: auto;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    
    &.collapsed {
      transform: translateX(0);
      width: 64px;
    }
  }
  
  .main-content {
    margin-left: 0;
  }
}
</style>
