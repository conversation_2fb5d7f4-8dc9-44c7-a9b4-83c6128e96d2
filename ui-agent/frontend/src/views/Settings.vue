<template>
  <div class="settings-page">
    <div class="page-header">
      <h1 class="page-title">系统设置</h1>
      <p class="page-description">配置AI模型、系统参数和其他选项</p>
    </div>

    <div class="settings-content">
      <!-- AI配置 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">AI模型配置</span>
            <el-button size="small" @click="testConnection" :loading="loading.test">
              <el-icon><Connection /></el-icon>
              测试连接
            </el-button>
          </div>
        </template>

        <el-form :model="aiConfig" label-width="120px" size="large">
          <el-form-item label="模型名称">
            <el-input
              v-model="aiConfig.model_name"
              placeholder="请输入AI模型名称，如：gpt-4-vision-preview"
            />
            <div class="form-tip">
              支持OpenAI、Azure OpenAI等兼容模型
            </div>
          </el-form-item>

          <el-form-item label="API地址">
            <el-input
              v-model="aiConfig.api_base"
              placeholder="请输入API基础地址，如：https://api.openai.com/v1"
            />
            <div class="form-tip">
              OpenAI官方地址或自定义代理地址
            </div>
          </el-form-item>

          <el-form-item label="API密钥">
            <el-input
              v-model="aiConfig.api_key"
              type="password"
              placeholder="请输入API密钥"
              show-password
            />
            <div class="form-tip">
              您的API密钥将被安全存储
            </div>
          </el-form-item>

          <el-form-item label="温度参数">
            <el-slider
              v-model="aiConfig.temperature"
              :min="0"
              :max="1"
              :step="0.1"
              show-input
              :input-size="'small'"
            />
            <div class="form-tip">
              控制AI响应的随机性，0为最确定，1为最随机
            </div>
          </el-form-item>

          <el-form-item label="最大重试次数">
            <el-input-number
              v-model="aiConfig.max_retries"
              :min="1"
              :max="10"
              style="width: 200px"
            />
            <div class="form-tip">
              AI执行失败时的最大重试次数
            </div>
          </el-form-item>

          <el-form-item label="超时时间(秒)">
            <el-input-number
              v-model="aiConfig.timeout"
              :min="10"
              :max="300"
              style="width: 200px"
            />
            <div class="form-tip">
              单次AI请求的超时时间
            </div>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="saveAiConfig" :loading="loading.save">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button @click="resetAiConfig">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 系统配置 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">系统配置</span>
          </div>
        </template>

        <el-form :model="systemConfig" label-width="120px" size="large">
          <el-form-item label="截图质量">
            <el-radio-group v-model="systemConfig.screenshot_quality">
              <el-radio label="high">高质量</el-radio>
              <el-radio label="medium">中等质量</el-radio>
              <el-radio label="low">低质量</el-radio>
            </el-radio-group>
            <div class="form-tip">
              影响截图文件大小和AI识别精度
            </div>
          </el-form-item>

          <el-form-item label="日志级别">
            <el-select v-model="systemConfig.log_level" style="width: 200px">
              <el-option label="DEBUG" value="debug" />
              <el-option label="INFO" value="info" />
              <el-option label="WARNING" value="warning" />
              <el-option label="ERROR" value="error" />
            </el-select>
            <div class="form-tip">
              控制系统日志的详细程度
            </div>
          </el-form-item>

          <el-form-item label="自动清理">
            <el-switch
              v-model="systemConfig.auto_cleanup"
              active-text="启用"
              inactive-text="禁用"
            />
            <div class="form-tip">
              自动清理过期的截图和日志文件
            </div>
          </el-form-item>

          <el-form-item label="保留天数" v-if="systemConfig.auto_cleanup">
            <el-input-number
              v-model="systemConfig.cleanup_days"
              :min="1"
              :max="365"
              style="width: 200px"
            />
            <div class="form-tip">
              文件保留的天数，超过将被自动删除
            </div>
          </el-form-item>

          <el-form-item label="并发执行">
            <el-switch
              v-model="systemConfig.concurrent_execution"
              active-text="启用"
              inactive-text="禁用"
            />
            <div class="form-tip">
              是否允许同时执行多个任务
            </div>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="saveSystemConfig" :loading="loading.saveSystem">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 语音配置 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">语音配置</span>
            <el-button size="small" @click="testVoice" :loading="loading.voice">
              <el-icon><Microphone /></el-icon>
              测试语音
            </el-button>
          </div>
        </template>

        <el-form :model="voiceConfig" label-width="120px" size="large">
          <el-form-item label="语音识别">
            <el-switch
              v-model="voiceConfig.enabled"
              active-text="启用"
              inactive-text="禁用"
            />
            <div class="form-tip">
              启用语音输入功能
            </div>
          </el-form-item>

          <el-form-item label="识别语言" v-if="voiceConfig.enabled">
            <el-select v-model="voiceConfig.language" style="width: 200px">
              <el-option label="中文(简体)" value="zh-CN" />
              <el-option label="中文(繁体)" value="zh-TW" />
              <el-option label="英语" value="en-US" />
              <el-option label="日语" value="ja-JP" />
              <el-option label="韩语" value="ko-KR" />
            </el-select>
            <div class="form-tip">
              语音识别的目标语言
            </div>
          </el-form-item>

          <el-form-item label="自动执行" v-if="voiceConfig.enabled">
            <el-switch
              v-model="voiceConfig.auto_execute"
              active-text="启用"
              inactive-text="禁用"
            />
            <div class="form-tip">
              语音输入完成后自动开始执行任务
            </div>
          </el-form-item>

          <el-form-item label="静音检测" v-if="voiceConfig.enabled">
            <el-input-number
              v-model="voiceConfig.silence_duration"
              :min="1"
              :max="10"
              style="width: 200px"
            />
            <span style="margin-left: 8px;">秒</span>
            <div class="form-tip">
              检测到静音多长时间后停止录音
            </div>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="saveVoiceConfig" :loading="loading.saveVoice">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 系统信息 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">系统信息</span>
            <el-button size="small" @click="loadSystemInfo">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>

        <div v-if="systemInfo" class="system-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="操作系统">{{ systemInfo.os }}</el-descriptions-item>
            <el-descriptions-item label="Python版本">{{ systemInfo.python_version }}</el-descriptions-item>
            <el-descriptions-item label="应用版本">{{ systemInfo.app_version }}</el-descriptions-item>
            <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
            <el-descriptions-item label="CPU使用率">{{ systemInfo.cpu_usage }}%</el-descriptions-item>
            <el-descriptions-item label="内存使用率">{{ systemInfo.memory_usage }}%</el-descriptions-item>
            <el-descriptions-item label="磁盘使用率">{{ systemInfo.disk_usage }}%</el-descriptions-item>
            <el-descriptions-item label="网络状态">
              <el-tag :type="systemInfo.network_status === 'connected' ? 'success' : 'danger'">
                {{ systemInfo.network_status === 'connected' ? '已连接' : '未连接' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { aiApi } from '@/api/ai'
import {
  Connection,
  Check,
  RefreshLeft,
  Microphone,
  Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const loading = reactive({
  save: false,
  saveSystem: false,
  saveVoice: false,
  test: false,
  voice: false
})

const aiConfig = reactive({
  model_name: 'gpt-4-vision-preview',
  api_base: 'https://api.openai.com/v1',
  api_key: '',
  temperature: 0.7,
  max_retries: 3,
  timeout: 60
})

const systemConfig = reactive({
  screenshot_quality: 'high',
  log_level: 'info',
  auto_cleanup: true,
  cleanup_days: 7,
  concurrent_execution: false
})

const voiceConfig = reactive({
  enabled: false,
  language: 'zh-CN',
  auto_execute: true,
  silence_duration: 2
})

const systemInfo = ref(null)

// 方法
const loadAiConfig = async () => {
  try {
    const response = await aiApi.getConfig()
    if (response.success) {
      Object.assign(aiConfig, response.config)
    }
  } catch (error) {
    console.error('加载AI配置失败:', error)
  }
}

const saveAiConfig = async () => {
  loading.save = true
  try {
    const response = await aiApi.setConfig({
      model_name: aiConfig.model_name,
      api_base: aiConfig.api_base,
      temperature: aiConfig.temperature
    })
    
    if (response.success) {
      ElMessage.success('AI配置保存成功')
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('保存AI配置失败')
  } finally {
    loading.save = false
  }
}

const resetAiConfig = () => {
  Object.assign(aiConfig, {
    model_name: 'gpt-4-vision-preview',
    api_base: 'https://api.openai.com/v1',
    api_key: '',
    temperature: 0.7,
    max_retries: 3,
    timeout: 60
  })
}

const testConnection = async () => {
  loading.test = true
  try {
    const response = await aiApi.testConnection({
      model_name: aiConfig.model_name,
      api_base: aiConfig.api_base,
      temperature: aiConfig.temperature
    })
    
    if (response.success) {
      ElMessage.success(`连接测试成功！响应时间: ${response.response_time}`)
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  } finally {
    loading.test = false
  }
}

const saveSystemConfig = async () => {
  loading.saveSystem = true
  try {
    // 这里应该调用系统配置保存API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    ElMessage.success('系统配置保存成功')
  } catch (error) {
    ElMessage.error('保存系统配置失败')
  } finally {
    loading.saveSystem = false
  }
}

const saveVoiceConfig = async () => {
  loading.saveVoice = true
  try {
    // 这里应该调用语音配置保存API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    ElMessage.success('语音配置保存成功')
  } catch (error) {
    ElMessage.error('保存语音配置失败')
  } finally {
    loading.saveVoice = false
  }
}

const testVoice = async () => {
  loading.voice = true
  try {
    // 这里应该调用语音测试API
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟语音测试
    ElMessage.success('语音测试成功')
  } catch (error) {
    ElMessage.error('语音测试失败')
  } finally {
    loading.voice = false
  }
}

const loadSystemInfo = async () => {
  try {
    // 这里应该调用系统信息API
    // 模拟系统信息
    systemInfo.value = {
      os: 'macOS 14.0',
      python_version: '3.11.5',
      app_version: '1.0.0',
      uptime: '2小时30分钟',
      cpu_usage: 15.6,
      memory_usage: 42.3,
      disk_usage: 68.9,
      network_status: 'connected'
    }
  } catch (error) {
    ElMessage.error('获取系统信息失败')
  }
}

// 组件挂载
onMounted(() => {
  loadAiConfig()
  loadSystemInfo()
})
</script>

<style lang="scss" scoped>
.settings-page {
  padding: 24px;
  height: 100%;
  overflow: auto;
}

.settings-content {
  max-width: 800px;
}

.settings-card {
  margin-bottom: 24px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

.system-info {
  .el-descriptions {
    margin-top: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-content {
    max-width: 100%;
  }
  
  .card-header {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 12px;
  }
}
</style>
