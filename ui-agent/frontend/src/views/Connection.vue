<template>
  <div class="connection-page">
    <div class="connection-container">
      <!-- 头部标题 -->
      <div class="header">
        <div class="logo">
          <el-icon size="48" color="#409EFF">
            <Setting />
          </el-icon>
        </div>
        <h1 class="title">UI Agent</h1>
        <p class="subtitle">智能自动化测试平台</p>
      </div>

      <!-- 连接表单 -->
      <div class="connection-form">
        <el-card shadow="hover" class="form-card">
          <template #header>
            <div class="card-header">
              <el-icon><Connection /></el-icon>
              <span>连接到后端服务</span>
            </div>
          </template>

          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="80px"
            size="large"
          >
            <el-form-item label="服务器" prop="host">
              <el-input
                v-model="form.host"
                placeholder="请输入服务器IP地址"
                :prefix-icon="Monitor"
                clearable
              />
            </el-form-item>

            <el-form-item label="端口" prop="port">
              <el-input-number
                v-model="form.port"
                :min="1"
                :max="65535"
                placeholder="请输入端口号"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="connectionStore.isConnecting"
                @click="handleConnect"
                style="width: 100%"
              >
                <el-icon v-if="!connectionStore.isConnecting"><Connection /></el-icon>
                {{ connectionStore.isConnecting ? '连接中...' : '连接' }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 快速连接 -->
          <div class="quick-connect">
            <el-divider>快速连接</el-divider>
            <div class="quick-buttons">
              <el-button
                v-for="preset in presets"
                :key="preset.name"
                size="small"
                @click="usePreset(preset)"
              >
                {{ preset.name }}
              </el-button>
            </div>
          </div>

          <!-- 上次连接信息 -->
          <div v-if="lastConnection" class="last-connection">
            <el-divider>上次连接</el-divider>
            <div class="last-info">
              <el-text type="info" size="small">
                {{ lastConnection.host }}:{{ lastConnection.port }}
              </el-text>
              <el-button
                size="small"
                text
                @click="useLastConnection"
              >
                使用
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 功能特性 -->
      <div class="features">
        <h3>平台特性</h3>
        <div class="feature-grid">
          <div class="feature-item">
            <el-icon size="24" color="#67C23A"><Cpu /></el-icon>
            <h4>AI智能规划</h4>
            <p>基于大模型的任务智能分解和执行</p>
          </div>
          <div class="feature-item">
            <el-icon size="24" color="#E6A23C"><Iphone /></el-icon>
            <h4>多端支持</h4>
            <p>支持Android设备和Web浏览器自动化</p>
          </div>
          <div class="feature-item">
            <el-icon size="24" color="#F56C6C"><Microphone /></el-icon>
            <h4>语音输入</h4>
            <p>支持语音和文本两种任务输入方式</p>
          </div>
          <div class="feature-item">
            <el-icon size="24" color="#909399"><Document /></el-icon>
            <h4>实时报告</h4>
            <p>实时展示执行过程，生成详细报告</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useConnectionStore } from '@/stores/connection'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Setting,
  Connection,
  Monitor,
  Cpu,
  Iphone,
  Microphone,
  Document
} from '@element-plus/icons-vue'

const router = useRouter()
const connectionStore = useConnectionStore()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  host: 'localhost',
  port: 8000
})

// 表单验证规则
const rules: FormRules = {
  host: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { 
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^localhost$|^[\w.-]+$/,
      message: '请输入有效的IP地址或域名',
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号范围为1-65535', trigger: 'blur' }
  ]
}

// 预设连接
const presets = [
  { name: '本地开发', host: 'localhost', port: 8000 },
  { name: '本地IP', host: '127.0.0.1', port: 8000 },
  { name: '局域网', host: '*************', port: 8000 }
]

// 上次连接信息
const lastConnection = ref<any>(null)

// 连接处理
const handleConnect = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    const success = await connectionStore.connect(form.host, form.port)
    if (success) {
      router.push('/dashboard')
    }
  } catch (error) {
    console.error('连接失败:', error)
  }
}

// 使用预设
const usePreset = (preset: any) => {
  form.host = preset.host
  form.port = preset.port
}

// 使用上次连接
const useLastConnection = () => {
  if (lastConnection.value) {
    form.host = lastConnection.value.host
    form.port = lastConnection.value.port
  }
}

// 组件挂载
onMounted(() => {
  // 加载上次连接信息
  lastConnection.value = connectionStore.loadSavedConnection()
  
  // 如果已经连接，直接跳转到控制台
  if (connectionStore.isConnected) {
    router.push('/dashboard')
  }
})
</script>

<style lang="scss" scoped>
.connection-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.connection-container {
  width: 100%;
  max-width: 500px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;

  .logo {
    margin-bottom: 20px;
  }

  .title {
    font-size: 48px;
    font-weight: 700;
    margin: 0 0 10px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .subtitle {
    font-size: 18px;
    margin: 0;
    opacity: 0.9;
  }
}

.connection-form {
  margin-bottom: 40px;

  .form-card {
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 16px;
  }

  .quick-connect {
    margin-top: 20px;

    .quick-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .last-connection {
    margin-top: 20px;

    .last-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.features {
  color: white;
  text-align: center;

  h3 {
    font-size: 24px;
    margin-bottom: 30px;
    font-weight: 600;
  }

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .feature-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    h4 {
      margin: 10px 0 8px 0;
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
      line-height: 1.4;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .connection-container {
    max-width: 100%;
  }

  .header .title {
    font-size: 36px;
  }

  .features .feature-grid {
    grid-template-columns: 1fr;
  }
}
</style>
