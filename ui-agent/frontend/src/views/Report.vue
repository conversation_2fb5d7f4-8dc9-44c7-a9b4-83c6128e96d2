<template>
  <div class="report-page">
    <div class="page-header">
      <h1 class="page-title">报告中心</h1>
      <p class="page-description">查看任务执行报告，分析执行结果和性能数据</p>
    </div>

    <!-- 报告列表 -->
    <el-card class="report-list-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">执行报告</span>
          <div class="header-controls">
            <el-select v-model="filters.taskId" placeholder="任务筛选" clearable size="small" style="width: 150px">
              <el-option
                v-for="task in tasks"
                :key="task.id"
                :label="task.title"
                :value="task.id"
              />
            </el-select>
            <el-button size="small" @click="loadReports">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="loading.reports" class="loading-container">
        <el-loading-spinner />
        <div class="loading-text">正在加载报告列表...</div>
      </div>

      <div v-else-if="reports.length === 0" class="empty-container">
        <el-icon class="empty-icon"><Document /></el-icon>
        <div class="empty-text">暂无报告</div>
        <div class="empty-description">执行任务后会自动生成报告</div>
      </div>

      <div v-else class="report-list">
        <div
          v-for="report in reports"
          :key="report.id"
          class="report-item"
          @click="viewReportDetail(report)"
        >
          <div class="report-info">
            <div class="report-header">
              <h3 class="report-title">{{ report.report_name }}</h3>
              <div class="report-stats">
                <el-tag size="small" type="success">
                  成功: {{ report.success_steps }}
                </el-tag>
                <el-tag size="small" type="danger" v-if="report.failed_steps > 0">
                  失败: {{ report.failed_steps }}
                </el-tag>
                <el-tag size="small" type="info">
                  总计: {{ report.total_steps }}
                </el-tag>
              </div>
            </div>
            
            <div class="report-summary">
              <div class="summary-item">
                <span class="label">成功率:</span>
                <span class="value">{{ getSuccessRate(report) }}%</span>
              </div>
              <div class="summary-item" v-if="report.execution_time">
                <span class="label">执行时间:</span>
                <span class="value">{{ formatDuration(report.execution_time) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">生成时间:</span>
                <span class="value">{{ formatTime(report.created_at) }}</span>
              </div>
            </div>
            
            <div class="report-actions" @click.stop>
              <el-button size="small" @click="viewReportDetail(report)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button size="small" @click="downloadReport(report.id)">
                <el-icon><Download /></el-icon>
                下载
              </el-button>
              <el-button size="small" type="danger" @click="deleteReport(report.id)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="reports.length > 0" class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadReports"
          @current-change="loadReports"
        />
      </div>
    </el-card>

    <!-- 报告详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="报告详情"
      width="90%"
      :close-on-click-modal="false"
      class="report-detail-dialog"
    >
      <div v-if="selectedReport" class="report-detail">
        <!-- 报告概览 -->
        <div class="report-overview">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总步骤数" :value="selectedReport.total_steps" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="成功步骤" :value="selectedReport.success_steps" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="失败步骤" :value="selectedReport.failed_steps" />
            </el-col>
            <el-col :span="6">
              <el-statistic 
                title="成功率" 
                :value="getSuccessRate(selectedReport)" 
                suffix="%" 
              />
            </el-col>
          </el-row>
        </div>

        <!-- 执行步骤 -->
        <div class="execution-steps">
          <h3>执行步骤</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(step, index) in reportSteps"
              :key="step.id"
              :type="getStepType(step.status)"
              :timestamp="formatTime(step.executed_at || step.created_at)"
            >
              <div class="step-content">
                <div class="step-header">
                  <span class="step-title">步骤 {{ step.step_index }}: {{ step.action }}</span>
                  <el-tag :type="getStepType(step.status)" size="small">
                    {{ getStepStatusText(step.status) }}
                  </el-tag>
                </div>
                
                <div v-if="step.observations" class="step-observations">
                  <strong>观察结果:</strong> {{ step.observations }}
                </div>
                
                <div v-if="step.element_info" class="step-element">
                  <strong>元素信息:</strong>
                  <pre class="element-info">{{ JSON.stringify(step.element_info, null, 2) }}</pre>
                </div>
                
                <div v-if="step.screenshot_path" class="step-screenshot">
                  <strong>截图:</strong>
                  <img :src="step.screenshot_path" alt="步骤截图" class="screenshot-thumb" />
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 执行日志 -->
        <div class="execution-logs">
          <h3>执行日志</h3>
          <div class="log-container">
            <div
              v-for="log in reportLogs"
              :key="log.id"
              class="log-item"
              :class="log.level"
            >
              <span class="log-time">{{ formatTime(log.created_at) }}</span>
              <span class="log-level">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>

        <!-- 截图画廊 -->
        <div class="screenshot-gallery" v-if="reportScreenshots.length > 0">
          <h3>截图画廊</h3>
          <div class="screenshot-grid">
            <div
              v-for="screenshot in reportScreenshots"
              :key="screenshot.id"
              class="screenshot-item"
              @click="previewImage(screenshot.file_path)"
            >
              <img :src="screenshot.file_path" :alt="screenshot.file_name" />
              <div class="screenshot-info">
                <div class="screenshot-name">{{ screenshot.file_name }}</div>
                <div class="screenshot-time">{{ formatTime(screenshot.created_at) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="downloadReport(selectedReport?.id)">
          <el-icon><Download /></el-icon>
          下载HTML报告
        </el-button>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="showImagePreview" title="图片预览" width="80%">
      <div class="image-preview">
        <img :src="previewImageUrl" alt="预览图片" style="width: 100%; height: auto;" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { reportApi, type ReportResponse } from '@/api/report'
import { taskApi, type TaskResponse } from '@/api/task'
import { 
  Document, 
  Refresh, 
  View, 
  Download, 
  Delete 
} from '@element-plus/icons-vue'

// 响应式数据
const loading = reactive({
  reports: false
})

const reports = ref<ReportResponse[]>([])
const tasks = ref<TaskResponse[]>([])
const selectedReport = ref(null)
const reportSteps = ref([])
const reportLogs = ref([])
const reportScreenshots = ref([])

const showDetailDialog = ref(false)
const showImagePreview = ref(false)
const previewImageUrl = ref('')

const filters = reactive({
  taskId: null
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 方法
const loadReports = async () => {
  loading.reports = true
  try {
    const params = {
      skip: (pagination.current - 1) * pagination.size,
      limit: pagination.size,
      task_id: filters.taskId || undefined
    }
    
    const response = await reportApi.getReports(params)
    reports.value = response
    pagination.total = response.length
  } catch (error) {
    ElMessage.error('获取报告列表失败')
  } finally {
    loading.reports = false
  }
}

const loadTasks = async () => {
  try {
    const response = await taskApi.getTasks({ limit: 1000 })
    tasks.value = response
  } catch (error) {
    console.error('获取任务列表失败:', error)
  }
}

const viewReportDetail = async (report: ReportResponse) => {
  try {
    // 获取报告详情
    const response = await reportApi.getReport(report.id)
    selectedReport.value = response
    
    // 获取任务步骤
    const steps = await taskApi.getTaskSteps(report.task_id)
    reportSteps.value = steps
    
    // 获取任务日志
    const logs = await taskApi.getTaskLogs(report.task_id)
    reportLogs.value = logs
    
    // 获取截图
    const screenshots = await reportApi.getTaskScreenshots(report.task_id)
    reportScreenshots.value = screenshots
    
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取报告详情失败')
  }
}

const downloadReport = async (reportId: number) => {
  try {
    const response = await reportApi.downloadReport(reportId)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.download = `report_${reportId}.html`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('报告下载成功')
  } catch (error) {
    ElMessage.error('下载报告失败')
  }
}

const deleteReport = async (reportId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个报告吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await reportApi.deleteReport(reportId)
    if (response.success) {
      ElMessage.success(response.message)
      await loadReports()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    // 用户取消操作
  }
}

const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl
  showImagePreview.value = true
}

const getSuccessRate = (report: ReportResponse) => {
  if (report.total_steps === 0) return 0
  return Math.round((report.success_steps / report.total_steps) * 100)
}

const getStepType = (status: string) => {
  const typeMap = {
    success: 'success',
    failed: 'danger',
    pending: 'info',
    running: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStepStatusText = (status: string) => {
  const statusMap = {
    success: '成功',
    failed: '失败',
    pending: '待执行',
    running: '执行中'
  }
  return statusMap[status] || status
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString()
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分${(seconds % 60).toFixed(0)}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// 组件挂载
onMounted(() => {
  loadReports()
  loadTasks()
})
</script>

<style lang="scss" scoped>
.report-page {
  padding: 24px;
  height: 100%;
  overflow: auto;
}

.report-list {
  .report-item {
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }
    
    .report-info {
      .report-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .report-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
        
        .report-stats {
          display: flex;
          gap: 8px;
        }
      }
      
      .report-summary {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;
        
        .summary-item {
          display: flex;
          align-items: center;
          gap: 4px;
          
          .label {
            font-size: 14px;
            color: var(--el-text-color-regular);
          }
          
          .value {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }
      }
      
      .report-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.pagination {
  margin-top: 24px;
  text-align: center;
}

.report-detail {
  .report-overview {
    margin-bottom: 32px;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
  }
  
  .execution-steps,
  .execution-logs,
  .screenshot-gallery {
    margin-bottom: 32px;
    
    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  .step-content {
    .step-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .step-title {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }
    
    .step-observations,
    .step-element {
      margin-bottom: 8px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    .element-info {
      background: var(--el-bg-color-page);
      padding: 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      margin: 4px 0;
      max-height: 150px;
      overflow-y: auto;
    }
    
    .step-screenshot {
      .screenshot-thumb {
        max-width: 200px;
        max-height: 150px;
        border: 1px solid var(--el-border-color-light);
        border-radius: 4px;
        cursor: pointer;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
  
  .log-container {
    max-height: 400px;
    overflow-y: auto;
    background: var(--el-bg-color-page);
    border-radius: 8px;
    padding: 16px;
    
    .log-item {
      display: flex;
      gap: 12px;
      margin-bottom: 8px;
      font-family: monospace;
      font-size: 12px;
      
      .log-time {
        color: var(--el-text-color-secondary);
        min-width: 150px;
      }
      
      .log-level {
        min-width: 60px;
        font-weight: 600;
      }
      
      .log-message {
        flex: 1;
        color: var(--el-text-color-primary);
      }
      
      &.info .log-level {
        color: var(--el-color-info);
      }
      
      &.warning .log-level {
        color: var(--el-color-warning);
      }
      
      &.error .log-level {
        color: var(--el-color-danger);
      }
      
      &.success .log-level {
        color: var(--el-color-success);
      }
    }
  }
  
  .screenshot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    
    .screenshot-item {
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: var(--el-color-primary);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      img {
        width: 100%;
        height: 150px;
        object-fit: cover;
      }
      
      .screenshot-info {
        padding: 12px;
        
        .screenshot-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }
        
        .screenshot-time {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
}

.image-preview {
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .report-summary {
    flex-direction: column !important;
    gap: 8px !important;
  }
  
  .screenshot-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
  }
}
</style>
