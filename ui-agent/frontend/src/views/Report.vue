<template>
  <div class="history-page">
    <div class="page-header">
      <h1 class="page-title">执行历史</h1>
      <p class="page-description">查看任务执行历史记录，分析执行结果和性能数据</p>
    </div>

    <!-- 执行历史列表 -->
    <el-card class="history-list-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">执行历史</span>
          <div class="header-controls">
            <el-select v-model="filters.automationType" placeholder="类型筛选" clearable size="small" style="width: 120px">
              <el-option label="Android" value="android" />
              <el-option label="Web" value="web" />
            </el-select>
            <el-button size="small" @click="loadHistory">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="loading.history" class="loading-container">
        <el-loading-spinner />
        <div class="loading-text">正在加载执行历史...</div>
      </div>

      <div v-else-if="historyList.length === 0" class="empty-container">
        <el-icon class="empty-icon"><Document /></el-icon>
        <div class="empty-text">暂无执行历史</div>
        <div class="empty-description">执行任务后会自动记录历史</div>
      </div>

      <div v-else class="history-list">
        <div
          v-for="history in historyList"
          :key="history.id"
          class="history-item"
          @click="viewHistoryDetail(history)"
        >
          <div class="history-info">
            <div class="history-header">
              <div class="history-title">
                <el-tag :type="history.automation_type === 'android' ? 'success' : 'primary'" size="small">
                  {{ history.automation_type === 'android' ? 'Android' : 'Web' }}
                </el-tag>
                <span class="task-content">{{ truncateText(history.input_content, 50) }}</span>
              </div>
              <div class="history-status">
                <el-tag :type="getStatusType(history.status)" size="small">
                  {{ getStatusText(history.status) }}
                </el-tag>
              </div>
            </div>

            <div class="history-summary">
              <div class="summary-item" v-if="history.execution_time">
                <span class="label">执行时间:</span>
                <span class="value">{{ formatDuration(history.execution_time) }}</span>
              </div>
              <div class="summary-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatTime(history.created_at) }}</span>
              </div>
            </div>

            <div class="history-actions" @click.stop>
              <el-button size="small" @click="viewHistoryDetail(history)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
              <el-button size="small" type="danger" @click="deleteHistory(history.id)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="historyList.length > 0" class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadHistory"
          @current-change="loadHistory"
        />
      </div>
    </el-card>

    <!-- 执行详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="执行详情"
      width="90%"
      :close-on-click-modal="false"
      class="history-detail-dialog"
    >
      <div v-if="selectedHistory" class="history-detail">
        <!-- 执行概览 -->
        <div class="history-overview">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="自动化类型" :value="selectedHistory.automation_type === 'android' ? 'Android' : 'Web'" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="执行状态" :value="getStatusText(selectedHistory.status)" />
            </el-col>
            <el-col :span="8">
              <el-statistic
                title="执行时间"
                :value="selectedHistory.execution_time ? formatDuration(selectedHistory.execution_time) : '未知'"
              />
            </el-col>
          </el-row>

          <div class="task-description">
            <h4>任务描述:</h4>
            <p>{{ selectedHistory.input_content }}</p>
          </div>
        </div>

        <!-- 执行步骤 -->
        <div class="execution-steps" v-if="executionSteps.length > 0">
          <h3>执行步骤</h3>
          <el-timeline>
            <el-timeline-item
              v-for="step in executionSteps"
              :key="step.step_index"
              :type="step.success ? 'success' : 'danger'"
              :timestamp="formatTime(step.timestamp)"
            >
              <div class="step-content">
                <div class="step-header">
                  <span class="step-title">步骤 {{ step.step_index }}: {{ step.action_type }}</span>
                  <el-tag :type="step.success ? 'success' : 'danger'" size="small">
                    {{ step.success ? '成功' : '失败' }}
                  </el-tag>
                </div>

                <div v-if="step.description" class="step-description">
                  <strong>描述:</strong> {{ step.description }}
                </div>

                <div v-if="step.element_info" class="step-element">
                  <strong>元素信息:</strong>
                  <pre class="element-info">{{ JSON.stringify(step.element_info, null, 2) }}</pre>
                </div>

                <div v-if="step.error_message" class="step-error">
                  <strong>错误信息:</strong> {{ step.error_message }}
                </div>

                <div v-if="step.screenshot_path" class="step-screenshot">
                  <strong>截图:</strong>
                  <img :src="step.screenshot_path" alt="步骤截图" class="screenshot-thumb" @click="previewImage(step.screenshot_path)" />
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 执行结果 -->
        <div class="execution-result" v-if="selectedHistory.result">
          <h3>执行结果</h3>
          <div class="result-container">
            <pre class="result-content">{{ JSON.stringify(selectedHistory.result, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="showImagePreview" title="图片预览" width="80%">
      <div class="image-preview">
        <img :src="previewImageUrl" alt="预览图片" style="width: 100%; height: auto;" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { taskApi, type TaskHistoryResponse, type ExecutionStepResponse } from '@/api/task'
import {
  Document,
  Refresh,
  View,
  Delete
} from '@element-plus/icons-vue'

// 响应式数据
const loading = reactive({
  history: false
})

const historyList = ref<TaskHistoryResponse[]>([])
const selectedHistory = ref<TaskHistoryResponse | null>(null)
const executionSteps = ref<ExecutionStepResponse[]>([])

const showDetailDialog = ref(false)
const showImagePreview = ref(false)
const previewImageUrl = ref('')

const filters = reactive({
  automationType: null as string | null
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 方法
const loadHistory = async () => {
  loading.history = true
  try {
    const params = {
      skip: (pagination.current - 1) * pagination.size,
      limit: pagination.size,
      automation_type: filters.automationType || undefined
    }

    const response = await taskApi.getHistory(params)
    historyList.value = response
    pagination.total = response.length
  } catch (error) {
    ElMessage.error('获取执行历史失败')
  } finally {
    loading.history = false
  }
}

const viewHistoryDetail = async (history: TaskHistoryResponse) => {
  try {
    selectedHistory.value = history

    // 获取执行步骤
    const steps = await taskApi.getExecutionSteps(history.id)
    executionSteps.value = steps

    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取执行详情失败')
  }
}

const deleteHistory = async (historyId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个执行历史吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await taskApi.deleteHistory(historyId)
    if (response.success) {
      ElMessage.success(response.message)
      await loadHistory()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    // 用户取消操作
  }
}

const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl
  showImagePreview.value = true
}

const getStatusType = (status: string) => {
  const typeMap = {
    completed: 'success',
    failed: 'danger',
    running: 'warning',
    stopped: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    completed: '已完成',
    failed: '失败',
    running: '执行中',
    stopped: '已停止'
  }
  return statusMap[status] || status
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString()
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分${(seconds % 60).toFixed(0)}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// 组件挂载
onMounted(() => {
  loadHistory()
})
</script>

<style lang="scss" scoped>
.report-page {
  padding: 24px;
  height: 100%;
  overflow: auto;
}

.report-list {
  .report-item {
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }
    
    .report-info {
      .report-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .report-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
        
        .report-stats {
          display: flex;
          gap: 8px;
        }
      }
      
      .report-summary {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;
        
        .summary-item {
          display: flex;
          align-items: center;
          gap: 4px;
          
          .label {
            font-size: 14px;
            color: var(--el-text-color-regular);
          }
          
          .value {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }
      }
      
      .report-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.pagination {
  margin-top: 24px;
  text-align: center;
}

.report-detail {
  .report-overview {
    margin-bottom: 32px;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
  }
  
  .execution-steps,
  .execution-logs,
  .screenshot-gallery {
    margin-bottom: 32px;
    
    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  .step-content {
    .step-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .step-title {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }
    
    .step-observations,
    .step-element {
      margin-bottom: 8px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    .element-info {
      background: var(--el-bg-color-page);
      padding: 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      margin: 4px 0;
      max-height: 150px;
      overflow-y: auto;
    }
    
    .step-screenshot {
      .screenshot-thumb {
        max-width: 200px;
        max-height: 150px;
        border: 1px solid var(--el-border-color-light);
        border-radius: 4px;
        cursor: pointer;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
  
  .log-container {
    max-height: 400px;
    overflow-y: auto;
    background: var(--el-bg-color-page);
    border-radius: 8px;
    padding: 16px;
    
    .log-item {
      display: flex;
      gap: 12px;
      margin-bottom: 8px;
      font-family: monospace;
      font-size: 12px;
      
      .log-time {
        color: var(--el-text-color-secondary);
        min-width: 150px;
      }
      
      .log-level {
        min-width: 60px;
        font-weight: 600;
      }
      
      .log-message {
        flex: 1;
        color: var(--el-text-color-primary);
      }
      
      &.info .log-level {
        color: var(--el-color-info);
      }
      
      &.warning .log-level {
        color: var(--el-color-warning);
      }
      
      &.error .log-level {
        color: var(--el-color-danger);
      }
      
      &.success .log-level {
        color: var(--el-color-success);
      }
    }
  }
  
  .screenshot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    
    .screenshot-item {
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: var(--el-color-primary);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      img {
        width: 100%;
        height: 150px;
        object-fit: cover;
      }
      
      .screenshot-info {
        padding: 12px;
        
        .screenshot-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }
        
        .screenshot-time {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
}

.image-preview {
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .report-summary {
    flex-direction: column !important;
    gap: 8px !important;
  }
  
  .screenshot-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
  }
}
</style>
