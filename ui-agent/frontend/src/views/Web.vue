<template>
  <div class="web-page">
    <div class="page-header">
      <h1 class="page-title">Web端管理</h1>
      <p class="page-description">管理Web浏览器，导航页面，执行自动化操作</p>
    </div>

    <div class="content-grid">
      <!-- 浏览器控制 -->
      <el-card class="browser-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">浏览器控制</span>
            <el-button size="small" @click="getBrowserStatus">
              <el-icon><Refresh /></el-icon>
              状态
            </el-button>
          </div>
        </template>

        <el-form :model="browserForm" label-width="80px">
          <el-form-item label="URL">
            <el-input
              v-model="browserForm.url"
              placeholder="请输入要访问的网址"
              clearable
            >
              <template #prepend>
                <el-select v-model="browserForm.protocol" style="width: 80px">
                  <el-option label="http://" value="http://" />
                  <el-option label="https://" value="https://" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="模式">
            <el-radio-group v-model="browserForm.isH5Mode">
              <el-radio :label="false">桌面模式</el-radio>
              <el-radio :label="true">H5移动模式</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              :loading="loading.navigate"
              @click="navigateToUrl"
              :disabled="!browserForm.url"
            >
              <el-icon v-if="!loading.navigate"><Link /></el-icon>
              {{ loading.navigate ? '导航中...' : '访问页面' }}
            </el-button>
            <el-button @click="resetBrowser" :loading="loading.reset">
              <el-icon><RefreshRight /></el-icon>
              重置浏览器
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 浏览器状态 -->
        <div v-if="browserStatus" class="browser-status">
          <el-divider>浏览器状态</el-divider>
          <div class="status-info">
            <div class="status-item">
              <span class="label">状态:</span>
              <el-tag :type="browserStatus.status === 'active' ? 'success' : 'warning'">
                {{ browserStatus.status === 'active' ? '活跃' : '未初始化' }}
              </el-tag>
            </div>
            <div v-if="browserStatus.current_url" class="status-item">
              <span class="label">当前URL:</span>
              <span class="value">{{ browserStatus.current_url }}</span>
            </div>
            <div v-if="browserStatus.title" class="status-item">
              <span class="label">页面标题:</span>
              <span class="value">{{ browserStatus.title }}</span>
            </div>
            <div v-if="browserStatus.viewport" class="status-item">
              <span class="label">视口大小:</span>
              <span class="value">{{ browserStatus.viewport.width }}x{{ browserStatus.viewport.height }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- Cookie和Header设置 -->
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">Cookie & Header</span>
          </div>
        </template>

        <el-tabs v-model="activeConfigTab">
          <el-tab-pane label="Cookie设置" name="cookies">
            <div class="config-section">
              <el-button size="small" @click="addCookie" type="primary">
                <el-icon><Plus /></el-icon>
                添加Cookie
              </el-button>
              
              <div v-if="cookies.length === 0" class="empty-tip">
                <el-text type="info">暂无Cookie配置</el-text>
              </div>
              
              <div v-else class="cookie-list">
                <div
                  v-for="(cookie, index) in cookies"
                  :key="index"
                  class="cookie-item"
                >
                  <el-input
                    v-model="cookie.name"
                    placeholder="Cookie名称"
                    size="small"
                    style="width: 120px"
                  />
                  <el-input
                    v-model="cookie.value"
                    placeholder="Cookie值"
                    size="small"
                    style="width: 200px"
                  />
                  <el-input
                    v-model="cookie.domain"
                    placeholder="域名(可选)"
                    size="small"
                    style="width: 120px"
                  />
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeCookie(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              
              <div class="config-actions">
                <el-button
                  type="primary"
                  size="small"
                  :loading="loading.cookies"
                  @click="setCookies"
                  :disabled="cookies.length === 0"
                >
                  应用Cookie
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="Header设置" name="headers">
            <div class="config-section">
              <el-button size="small" @click="addHeader" type="primary">
                <el-icon><Plus /></el-icon>
                添加Header
              </el-button>
              
              <div v-if="headers.length === 0" class="empty-tip">
                <el-text type="info">暂无Header配置</el-text>
              </div>
              
              <div v-else class="header-list">
                <div
                  v-for="(header, index) in headers"
                  :key="index"
                  class="header-item"
                >
                  <el-input
                    v-model="header.name"
                    placeholder="Header名称"
                    size="small"
                    style="width: 150px"
                  />
                  <el-input
                    v-model="header.value"
                    placeholder="Header值"
                    size="small"
                    style="width: 250px"
                  />
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeHeader(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              
              <div class="config-actions">
                <el-button
                  type="primary"
                  size="small"
                  :loading="loading.headers"
                  @click="setHeaders"
                  :disabled="headers.length === 0"
                >
                  应用Header
                </el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- JavaScript执行 -->
      <el-card class="js-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">JavaScript执行</span>
          </div>
        </template>

        <el-form :model="jsForm" label-width="80px">
          <el-form-item label="代码">
            <el-input
              v-model="jsForm.code"
              type="textarea"
              :rows="6"
              placeholder="请输入JavaScript代码"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :loading="loading.js"
              @click="executeJs"
              :disabled="!jsForm.code"
            >
              <el-icon v-if="!loading.js"><CaretRight /></el-icon>
              {{ loading.js ? '执行中...' : '执行代码' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 执行结果 -->
        <div v-if="jsResult" class="js-result">
          <el-divider>执行结果</el-divider>
          <div class="result-header">
            <el-tag :type="jsResult.success ? 'success' : 'danger'" size="small">
              {{ jsResult.success ? '成功' : '失败' }}
            </el-tag>
          </div>
          <div class="result-content">
            <pre class="result-text">{{ JSON.stringify(jsResult.result, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 页面截图 -->
      <el-card class="screenshot-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">页面截图</span>
            <el-button
              size="small"
              @click="takeScreenshot"
              :loading="loading.screenshot"
            >
              <el-icon v-if="!loading.screenshot"><Camera /></el-icon>
              {{ loading.screenshot ? '截图中...' : '截图' }}
            </el-button>
          </div>
        </template>

        <div v-if="screenshot" class="screenshot-container">
          <img :src="screenshot.url" alt="页面截图" class="screenshot-image" />
          <div class="screenshot-info">
            <span>截图时间: {{ formatTime(screenshot.timestamp) }}</span>
          </div>
        </div>

        <div v-else class="empty-container">
          <el-icon class="empty-icon"><Picture /></el-icon>
          <div class="empty-text">暂无截图</div>
          <div class="empty-description">点击截图按钮获取页面截图</div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { webApi } from '@/api/web'
import {
  Link,
  Refresh,
  RefreshRight,
  Plus,
  Delete,
  CaretRight,
  Camera,
  Picture
} from '@element-plus/icons-vue'

// 响应式数据
const loading = reactive({
  navigate: false,
  reset: false,
  cookies: false,
  headers: false,
  js: false,
  screenshot: false
})

const browserForm = reactive({
  protocol: 'https://',
  url: '',
  isH5Mode: false
})

const jsForm = reactive({
  code: ''
})

const activeConfigTab = ref('cookies')
const browserStatus = ref(null)
const jsResult = ref(null)
const screenshot = ref(null)

// Cookie和Header配置
const cookies = ref([])
const headers = ref([])

// 方法
const navigateToUrl = async () => {
  loading.navigate = true
  try {
    const fullUrl = browserForm.protocol + browserForm.url
    const response = await webApi.navigate({
      url: fullUrl,
      is_h5_mode: browserForm.isH5Mode,
      cookies: cookies.value.filter(c => c.name && c.value),
      headers: headers.value.reduce((acc, h) => {
        if (h.name && h.value) {
          acc[h.name] = h.value
        }
        return acc
      }, {})
    })
    
    if (response.success) {
      ElMessage.success(response.message)
      await getBrowserStatus()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('导航失败')
  } finally {
    loading.navigate = false
  }
}

const resetBrowser = async () => {
  loading.reset = true
  try {
    const response = await webApi.resetBrowser()
    if (response.success) {
      ElMessage.success(response.message)
      browserStatus.value = null
      screenshot.value = null
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('重置浏览器失败')
  } finally {
    loading.reset = false
  }
}

const getBrowserStatus = async () => {
  try {
    const response = await webApi.getBrowserStatus()
    browserStatus.value = response
  } catch (error) {
    console.error('获取浏览器状态失败:', error)
  }
}

const addCookie = () => {
  cookies.value.push({ name: '', value: '', domain: '' })
}

const removeCookie = (index: number) => {
  cookies.value.splice(index, 1)
}

const setCookies = async () => {
  loading.cookies = true
  try {
    const validCookies = cookies.value.filter(c => c.name && c.value)
    const response = await webApi.setCookies({ cookies: validCookies })
    
    if (response.success) {
      ElMessage.success(response.message)
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('设置Cookie失败')
  } finally {
    loading.cookies = false
  }
}

const addHeader = () => {
  headers.value.push({ name: '', value: '' })
}

const removeHeader = (index: number) => {
  headers.value.splice(index, 1)
}

const setHeaders = async () => {
  loading.headers = true
  try {
    const validHeaders = headers.value.reduce((acc, h) => {
      if (h.name && h.value) {
        acc[h.name] = h.value
      }
      return acc
    }, {})
    
    const response = await webApi.setHeaders({ headers: validHeaders })
    
    if (response.success) {
      ElMessage.success(response.message)
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('设置Header失败')
  } finally {
    loading.headers = false
  }
}

const executeJs = async () => {
  loading.js = true
  try {
    const response = await webApi.executeJs({ code: jsForm.code })
    jsResult.value = response
    
    if (response.success) {
      ElMessage.success(response.message)
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('执行JavaScript失败')
  } finally {
    loading.js = false
  }
}

const takeScreenshot = async () => {
  loading.screenshot = true
  try {
    const response = await webApi.takeScreenshot()
    
    if (response.success) {
      screenshot.value = {
        url: response.url,
        timestamp: response.timestamp || Date.now()
      }
      ElMessage.success('截图成功')
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('截图失败')
  } finally {
    loading.screenshot = false
  }
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString()
}

// 组件挂载
onMounted(() => {
  getBrowserStatus()
})
</script>

<style lang="scss" scoped>
.web-page {
  padding: 24px;
  height: 100%;
  overflow: auto;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.browser-status {
  margin-top: 16px;
  
  .status-info {
    .status-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .label {
        font-weight: 500;
        margin-right: 8px;
        min-width: 80px;
      }
      
      .value {
        color: var(--el-text-color-regular);
        word-break: break-all;
      }
    }
  }
}

.config-section {
  .empty-tip {
    text-align: center;
    padding: 20px;
    color: var(--el-text-color-secondary);
  }
  
  .cookie-list, .header-list {
    margin: 16px 0;
    
    .cookie-item, .header-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }
  }
  
  .config-actions {
    margin-top: 16px;
    text-align: right;
  }
}

.js-result {
  margin-top: 16px;
  
  .result-header {
    margin-bottom: 12px;
  }
  
  .result-content {
    .result-text {
      background: var(--el-bg-color-page);
      padding: 12px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      line-height: 1.4;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}

.screenshot-container {
  text-align: center;
  
  .screenshot-image {
    max-width: 100%;
    max-height: 400px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .screenshot-info {
    margin-top: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .cookie-item, .header-item {
    flex-direction: column;
    align-items: stretch !important;
    
    .el-input {
      width: 100% !important;
    }
  }
}
</style>
