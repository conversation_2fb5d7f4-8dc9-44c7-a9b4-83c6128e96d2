<template>
  <div class="task-page">
    <div class="page-header">
      <h1 class="page-title">任务管理</h1>
      <p class="page-description">创建和管理自动化任务，查看执行状态和结果</p>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
      </div>
    </div>

    <!-- 任务列表 -->
    <el-card class="task-list-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">任务列表</span>
          <div class="header-controls">
            <el-select v-model="filters.status" placeholder="状态筛选" clearable size="small" style="width: 120px">
              <el-option label="待执行" value="pending" />
              <el-option label="执行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
              <el-option label="已停止" value="stopped" />
            </el-select>
            <el-select v-model="filters.automationType" placeholder="类型筛选" clearable size="small" style="width: 120px">
              <el-option label="Android" value="android" />
              <el-option label="Web" value="web" />
            </el-select>
            <el-button size="small" @click="loadTasks">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="loading.tasks" class="loading-container">
        <el-loading-spinner />
        <div class="loading-text">正在加载任务列表...</div>
      </div>

      <div v-else-if="tasks.length === 0" class="empty-container">
        <el-icon class="empty-icon"><Document /></el-icon>
        <div class="empty-text">暂无任务</div>
        <div class="empty-description">点击"创建任务"开始您的第一个自动化任务</div>
      </div>

      <div v-else class="task-list">
        <div
          v-for="task in tasks"
          :key="task.id"
          class="task-item"
          @click="viewTaskDetail(task)"
        >
          <div class="task-info">
            <div class="task-header">
              <h3 class="task-title">{{ task.title }}</h3>
              <div class="task-meta">
                <el-tag :type="getStatusType(task.status)" size="small">
                  {{ getStatusText(task.status) }}
                </el-tag>
                <el-tag size="small" :type="task.automation_type === 'android' ? 'success' : 'primary'">
                  {{ task.automation_type.toUpperCase() }}
                </el-tag>
              </div>
            </div>
            <div class="task-description">{{ task.description }}</div>
            <div class="task-progress" v-if="task.status === 'running'">
              <el-progress :percentage="task.progress" :show-text="false" />
              <span class="progress-text">{{ task.progress }}%</span>
            </div>
            <div class="task-footer">
              <span class="task-time">创建时间: {{ formatTime(task.created_at) }}</span>
              <div class="task-actions" @click.stop>
                <el-button
                  v-if="task.status === 'running'"
                  size="small"
                  type="warning"
                  @click="stopTask(task.id)"
                >
                  停止
                </el-button>
                <el-button
                  v-if="task.status === 'pending'"
                  size="small"
                  type="primary"
                  @click="executeTask(task.id)"
                >
                  执行
                </el-button>
                <el-button size="small" @click="viewTaskDetail(task)">
                  详情
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="deleteTask(task.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="tasks.length > 0" class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadTasks"
          @current-change="loadTasks"
        />
      </div>
    </el-card>

    <!-- 创建任务对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建任务"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="createForm.title" placeholder="请输入任务标题" />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        
        <el-form-item label="自动化类型" prop="automation_type">
          <el-radio-group v-model="createForm.automation_type">
            <el-radio label="android">Android端</el-radio>
            <el-radio label="web">Web端</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="输入方式" prop="input_type">
          <el-radio-group v-model="createForm.input_type">
            <el-radio label="text">文本输入</el-radio>
            <el-radio label="voice">语音输入</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="任务内容" prop="input_content">
          <el-input
            v-model="createForm.input_content"
            type="textarea"
            :rows="4"
            placeholder="请输入具体的任务指令，例如：打开淘宝，搜索iPhone 15，查看第一个商品详情"
          />
        </el-form-item>
        
        <el-form-item label="AI模型">
          <el-input v-model="createForm.ai_model" placeholder="AI模型名称(可选)" />
        </el-form-item>
        
        <el-form-item label="API地址">
          <el-input v-model="createForm.api_base" placeholder="API基础地址(可选)" />
        </el-form-item>
        
        <el-form-item label="温度参数">
          <el-slider
            v-model="createForm.temperature"
            :min="0"
            :max="1"
            :step="0.1"
            show-input
            :input-size="'small'"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" :loading="loading.create" @click="createTask">
          创建任务
        </el-button>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="任务详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTask" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务标题">{{ selectedTask.title }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="自动化类型">
            <el-tag :type="selectedTask.automation_type === 'android' ? 'success' : 'primary'">
              {{ selectedTask.automation_type.toUpperCase() }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="输入方式">{{ selectedTask.input_type === 'text' ? '文本' : '语音' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ formatTime(selectedTask.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">{{ selectedTask.description }}</el-descriptions-item>
          <el-descriptions-item label="任务内容" :span="2">{{ selectedTask.input_content }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedTask.status === 'running'" class="progress-section">
          <h4>执行进度</h4>
          <el-progress :percentage="selectedTask.progress" />
        </div>
        
        <div v-if="selectedTask.result" class="result-section">
          <h4>执行结果</h4>
          <pre class="result-content">{{ JSON.stringify(selectedTask.result, null, 2) }}</pre>
        </div>
        
        <div v-if="selectedTask.error_message" class="error-section">
          <h4>错误信息</h4>
          <el-alert :title="selectedTask.error_message" type="error" :closable="false" />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button v-if="selectedTask?.status === 'pending'" type="primary" @click="executeTask(selectedTask.id)">
          执行任务
        </el-button>
        <el-button v-if="selectedTask?.status === 'running'" type="warning" @click="stopTask(selectedTask.id)">
          停止任务
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { taskApi, type CreateTaskRequest, type TaskResponse } from '@/api/task'
import { Plus, Refresh, Document } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 响应式数据
const loading = reactive({
  tasks: false,
  create: false
})

const tasks = ref<TaskResponse[]>([])
const selectedTask = ref(null)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)

const filters = reactive({
  status: '',
  automationType: ''
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const createFormRef = ref<FormInstance>()
const createForm = reactive<CreateTaskRequest>({
  title: '',
  description: '',
  automation_type: 'web',
  input_type: 'text',
  input_content: '',
  ai_model: '',
  api_base: '',
  temperature: 0.7
})

const createRules: FormRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入任务描述', trigger: 'blur' }
  ],
  automation_type: [
    { required: true, message: '请选择自动化类型', trigger: 'change' }
  ],
  input_type: [
    { required: true, message: '请选择输入方式', trigger: 'change' }
  ],
  input_content: [
    { required: true, message: '请输入任务内容', trigger: 'blur' }
  ]
}

// 方法
const loadTasks = async () => {
  loading.tasks = true
  try {
    const params = {
      skip: (pagination.current - 1) * pagination.size,
      limit: pagination.size,
      status: filters.status || undefined,
      automation_type: filters.automationType || undefined
    }
    
    const response = await taskApi.getTasks(params)
    tasks.value = response
    // 这里应该从响应头或其他方式获取总数，暂时使用任务数量
    pagination.total = response.length
  } catch (error) {
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.tasks = false
  }
}

const createTask = async () => {
  if (!createFormRef.value) return
  
  try {
    const valid = await createFormRef.value.validate()
    if (!valid) return
    
    loading.create = true
    
    const response = await taskApi.createTask(createForm)
    ElMessage.success('任务创建成功')
    showCreateDialog.value = false
    
    // 重置表单
    createFormRef.value.resetFields()
    
    // 刷新任务列表
    await loadTasks()
    
  } catch (error) {
    ElMessage.error('创建任务失败')
  } finally {
    loading.create = false
  }
}

const executeTask = async (taskId: number) => {
  try {
    const response = await taskApi.executeTask({ task_id: taskId })
    if (response.success) {
      ElMessage.success(response.message)
      await loadTasks()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('执行任务失败')
  }
}

const stopTask = async (taskId: number) => {
  try {
    const response = await taskApi.stopTask(taskId)
    if (response.success) {
      ElMessage.success(response.message)
      await loadTasks()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('停止任务失败')
  }
}

const deleteTask = async (taskId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await taskApi.deleteTask(taskId)
    if (response.success) {
      ElMessage.success(response.message)
      await loadTasks()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    // 用户取消操作
  }
}

const viewTaskDetail = async (task: TaskResponse) => {
  try {
    const response = await taskApi.getTask(task.id)
    selectedTask.value = response
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取任务详情失败')
  }
}

const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    stopped: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待执行',
    running: '执行中',
    completed: '已完成',
    failed: '失败',
    stopped: '已停止'
  }
  return statusMap[status] || status
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString()
}

// 组件挂载
onMounted(() => {
  loadTasks()
})
</script>

<style lang="scss" scoped>
.task-page {
  padding: 24px;
  height: 100%;
  overflow: auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  
  .header-actions {
    margin-top: 8px;
  }
}

.task-list {
  .task-item {
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }
    
    .task-info {
      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .task-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
        
        .task-meta {
          display: flex;
          gap: 8px;
        }
      }
      
      .task-description {
        color: var(--el-text-color-regular);
        margin-bottom: 12px;
        line-height: 1.5;
      }
      
      .task-progress {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        
        .el-progress {
          flex: 1;
        }
        
        .progress-text {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
      
      .task-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .task-time {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
        
        .task-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }
}

.pagination {
  margin-top: 24px;
  text-align: center;
}

.task-detail {
  .progress-section,
  .result-section,
  .error-section {
    margin-top: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .result-content {
    background: var(--el-bg-color-page);
    padding: 12px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 300px;
    overflow-y: auto;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    
    .header-actions {
      margin-top: 16px;
    }
  }
  
  .task-item .task-info .task-header {
    flex-direction: column;
    align-items: stretch;
    
    .task-meta {
      margin-top: 8px;
    }
  }
  
  .task-footer {
    flex-direction: column;
    align-items: stretch !important;
    gap: 12px;
    
    .task-actions {
      justify-content: center;
    }
  }
}
</style>
