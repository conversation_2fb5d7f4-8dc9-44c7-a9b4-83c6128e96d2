/**
 * 全局样式文件
 */

// 导入变量
@import './variables.scss';

// 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

// 间距工具类
@for $i from 0 through 10 {
  .m-#{$i} {
    margin: #{$i * 4}px;
  }
  .mt-#{$i} {
    margin-top: #{$i * 4}px;
  }
  .mr-#{$i} {
    margin-right: #{$i * 4}px;
  }
  .mb-#{$i} {
    margin-bottom: #{$i * 4}px;
  }
  .ml-#{$i} {
    margin-left: #{$i * 4}px;
  }
  .mx-#{$i} {
    margin-left: #{$i * 4}px;
    margin-right: #{$i * 4}px;
  }
  .my-#{$i} {
    margin-top: #{$i * 4}px;
    margin-bottom: #{$i * 4}px;
  }
  
  .p-#{$i} {
    padding: #{$i * 4}px;
  }
  .pt-#{$i} {
    padding-top: #{$i * 4}px;
  }
  .pr-#{$i} {
    padding-right: #{$i * 4}px;
  }
  .pb-#{$i} {
    padding-bottom: #{$i * 4}px;
  }
  .pl-#{$i} {
    padding-left: #{$i * 4}px;
  }
  .px-#{$i} {
    padding-left: #{$i * 4}px;
    padding-right: #{$i * 4}px;
  }
  .py-#{$i} {
    padding-top: #{$i * 4}px;
    padding-bottom: #{$i * 4}px;
  }
}

// 自定义组件样式
.page-container {
  padding: 20px;
  height: 100%;
  overflow: auto;
}

.page-header {
  margin-bottom: 20px;
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
  }
  
  .page-description {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }
}

// 状态指示器
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  
  &.success {
    background: var(--el-color-success-light-9);
    color: var(--el-color-success);
  }
  
  &.warning {
    background: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
  }
  
  &.danger {
    background: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
  }
  
  &.info {
    background: var(--el-color-info-light-9);
    color: var(--el-color-info);
  }
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--el-text-color-regular);
  
  .loading-text {
    margin-top: 16px;
    font-size: 14px;
  }
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--el-text-color-regular);
  
  .empty-icon {
    font-size: 64px;
    color: var(--el-text-color-placeholder);
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .empty-description {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

// 响应式断点
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-header .page-title {
    font-size: 20px;
  }
}

// 自定义滚动条
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-darker);
    border-radius: 3px;
    
    &:hover {
      background: var(--el-border-color-dark);
    }
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
}

.slide-up-leave-to {
  transform: translateY(-100%);
}

.scale-enter-active,
.scale-leave-active {
  transition: transform 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.8);
}
