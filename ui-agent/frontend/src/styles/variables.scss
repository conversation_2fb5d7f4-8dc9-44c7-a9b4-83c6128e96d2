/**
 * SCSS变量定义
 */

// 颜色变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 渐变色
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-success: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
$gradient-warning: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);
$gradient-danger: linear-gradient(135deg, #F56C6C 0%, #f78989 100%);

// 文字颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #C0C4CC;

// 背景颜色
$bg-white: #FFFFFF;
$bg-light: #F5F7FA;
$bg-lighter: #FAFAFA;
$bg-dark: #2C3E50;

// 边框颜色
$border-light: #EBEEF5;
$border-lighter: #F2F6FC;
$border-dark: #DCDFE6;
$border-darker: #CDD0D6;

// 阴影
$shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);

// 圆角
$border-radius-small: 2px;
$border-radius-base: 4px;
$border-radius-large: 6px;
$border-radius-round: 20px;
$border-radius-circle: 50%;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-base: 1.5;
$line-height-small: 1.2;
$line-height-large: 1.8;

// 层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// 动画时间
$transition-fast: 0.15s;
$transition-base: 0.3s;
$transition-slow: 0.5s;

// 动画函数
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out: cubic-bezier(0, 0, 0.2, 1);
$ease-in: cubic-bezier(0.4, 0, 1, 1);

// 组件特定变量
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$footer-height: 50px;

// 卡片样式
$card-padding: 20px;
$card-border-radius: 8px;
$card-shadow: $shadow-base;

// 按钮样式
$button-padding-vertical: 8px;
$button-padding-horizontal: 16px;
$button-border-radius: $border-radius-base;

// 输入框样式
$input-height: 40px;
$input-padding: 12px;
$input-border-radius: $border-radius-base;
