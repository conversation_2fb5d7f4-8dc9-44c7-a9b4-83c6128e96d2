import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { connectionApi } from '@/api/connection'
import type { ConnectionInfo, ServerInfo } from '@/types/connection'

export const useConnectionStore = defineStore('connection', () => {
  // 状态
  const connectionInfo = ref<ConnectionInfo | null>(null)
  const serverInfo = ref<ServerInfo | null>(null)
  const isConnecting = ref(false)
  const lastConnectedAt = ref<string | null>(null)

  // 计算属性
  const isConnected = computed(() => !!connectionInfo.value)
  const serverUrl = computed(() => {
    if (!connectionInfo.value) return ''
    return `http://${connectionInfo.value.host}:${connectionInfo.value.port}`
  })

  // 方法
  const connect = async (host: string, port: number) => {
    try {
      isConnecting.value = true
      
      const response = await connectionApi.connect({ host, port })
      
      if (response.success) {
        connectionInfo.value = { host, port }
        serverInfo.value = response.server_info
        lastConnectedAt.value = new Date().toISOString()
        
        // 保存连接信息到本地存储
        saveConnectionToLocal()
        
        ElMessage.success('连接成功')
        return true
      } else {
        ElMessage.error(response.message || '连接失败')
        return false
      }
    } catch (error: any) {
      console.error('连接失败:', error)
      ElMessage.error(error.message || '连接失败，请检查网络和服务器状态')
      return false
    } finally {
      isConnecting.value = false
    }
  }

  const disconnect = async () => {
    try {
      if (connectionInfo.value) {
        await connectionApi.disconnect()
      }
      
      connectionInfo.value = null
      serverInfo.value = null
      lastConnectedAt.value = null
      
      // 清除本地存储
      clearLocalConnection()
      
      ElMessage.success('已断开连接')
    } catch (error: any) {
      console.error('断开连接失败:', error)
      ElMessage.error('断开连接失败')
    }
  }

  const checkStatus = async () => {
    try {
      if (!isConnected.value) return false
      
      const status = await connectionApi.getStatus()
      return status.status === 'connected'
    } catch (error) {
      console.error('检查连接状态失败:', error)
      return false
    }
  }

  const saveConnectionToLocal = () => {
    if (connectionInfo.value) {
      localStorage.setItem('ui-agent-connection', JSON.stringify({
        ...connectionInfo.value,
        lastConnectedAt: lastConnectedAt.value
      }))
    }
  }

  const loadSavedConnection = () => {
    try {
      const saved = localStorage.getItem('ui-agent-connection')
      if (saved) {
        const data = JSON.parse(saved)
        // 不自动连接，只是显示上次的连接信息
        return {
          host: data.host,
          port: data.port,
          lastConnectedAt: data.lastConnectedAt
        }
      }
    } catch (error) {
      console.error('加载保存的连接信息失败:', error)
    }
    return null
  }

  const clearLocalConnection = () => {
    localStorage.removeItem('ui-agent-connection')
  }

  // 自动重连
  const autoReconnect = async () => {
    const saved = loadSavedConnection()
    if (saved) {
      const success = await connect(saved.host, saved.port)
      return success
    }
    return false
  }

  return {
    // 状态
    connectionInfo: readonly(connectionInfo),
    serverInfo: readonly(serverInfo),
    isConnecting: readonly(isConnecting),
    lastConnectedAt: readonly(lastConnectedAt),
    
    // 计算属性
    isConnected,
    serverUrl,
    
    // 方法
    connect,
    disconnect,
    checkStatus,
    loadSavedConnection,
    autoReconnect
  }
})
