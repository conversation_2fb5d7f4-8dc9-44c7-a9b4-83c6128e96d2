import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useConnectionStore } from '@/stores/connection'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Connection',
    component: () => import('@/views/Connection.vue'),
    meta: {
      title: '连接服务器',
      requiresConnection: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '控制台',
      requiresConnection: true
    },
    children: [
      {
        path: '',
        redirect: '/dashboard/android'
      },
      {
        path: 'android',
        name: 'Android',
        component: () => import('@/views/Android.vue'),
        meta: {
          title: 'Android端',
          requiresConnection: true
        }
      },
      {
        path: 'web',
        name: 'Web',
        component: () => import('@/views/Web.vue'),
        meta: {
          title: 'Web端',
          requiresConnection: true
        }
      },
      {
        path: 'task',
        name: 'Task',
        component: () => import('@/views/Task.vue'),
        meta: {
          title: '任务管理',
          requiresConnection: true
        }
      },
      {
        path: 'report',
        name: 'Report',
        component: () => import('@/views/Report.vue'),
        meta: {
          title: '报告中心',
          requiresConnection: true
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue'),
        meta: {
          title: '设置',
          requiresConnection: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const connectionStore = useConnectionStore()
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - UI Agent`
  }
  
  // 检查连接状态
  if (to.meta?.requiresConnection && !connectionStore.isConnected) {
    ElMessage.warning('请先连接到后端服务')
    next({ name: 'Connection' })
    return
  }
  
  next()
})

export default router
