# UI Agent - 智能自动化测试平台

## 项目简介

UI Agent 是一个基于AI的智能自动化测试平台，支持Android和Web端的自动化操作。通过自然语言或语音输入任务，系统会调用大模型进行智能规划，动态执行测试步骤直到任务完成。

## 技术栈

- **前端**: Vue.js 3 + TypeScript + Element Plus
- **后端**: Python + FastAPI + SQLite
- **Android端**: airtest + poco + python
- **Web端**: playwright + python

## 功能特性

### 核心功能
- 🔗 **连接管理**: 前端通过IP+端口连接后端服务
- 📱 **多端支持**: 支持Android设备和Web浏览器自动化
- 🤖 **AI智能规划**: 基于大模型的任务智能分解和执行
- 🎤 **语音输入**: 支持语音和文本两种任务输入方式
- 📊 **实时报告**: 实时展示执行过程，生成详细HTML报告
- 📝 **历史记录**: 完整的任务执行历史和日志记录

### Android端特性
- 📲 设备切换功能
- 📦 APK下载安装
- 🔧 ADB命令执行
- 📸 实时截图和元素标记

### Web端特性
- 🌐 URL导航
- 🍪 Cookie配置
- 📋 Header设置
- 📱 H5模式支持
- 💻 JavaScript代码执行
- 🔄 浏览器实例重置

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 安装依赖

#### 后端依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 前端依赖
```bash
cd frontend
npm install
```

### 启动服务

#### 开发模式
```bash
# 启动后端服务
cd backend
python main.py

# 启动前端服务
cd frontend
npm run dev
```

#### 生产模式
```bash
# 一键启动脚本
./scripts/start.sh  # Mac/Linux
./scripts/start.bat # Windows
```

### 使用说明

1. **连接服务**: 打开前端页面，输入后端IP和端口，点击连接
2. **选择平台**: 选择Android端或Web端
3. **配置环境**: 
   - Android: 选择设备、安装APK、执行ADB命令
   - Web: 设置URL、Cookie、Header，打开页面
4. **配置AI**: 设置大模型参数（模型名称、API Base、温度）
5. **执行任务**: 通过文本或语音输入任务，点击开始执行
6. **查看报告**: 实时查看执行过程和结果报告

## 项目结构

```
ui-agent/
├── frontend/                 # Vue前端
│   ├── src/
│   │   ├── components/      # 组件
│   │   ├── views/          # 页面
│   │   ├── stores/         # 状态管理
│   │   ├── utils/          # 工具函数
│   │   └── types/          # TypeScript类型
│   ├── package.json
│   └── vite.config.ts
├── backend/                  # Python后端
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心功能
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── requirements.txt
│   └── main.py
├── docs/                     # 文档
├── scripts/                  # 启动脚本
├── output/                   # 截图输出目录
├── prompt/                   # AI提示词
└── README.md
```

## API文档

### 核心接口

#### 任务执行
- `POST /api/process_complex_ai_exec` - 执行AI智能规划任务

#### Android端
- `GET /api/android/devices` - 获取设备列表
- `POST /api/android/install_apk` - 安装APK
- `POST /api/android/adb_command` - 执行ADB命令

#### Web端
- `POST /api/web/navigate` - 导航到URL
- `POST /api/web/set_cookies` - 设置Cookie
- `POST /api/web/execute_js` - 执行JavaScript
- `POST /api/web/reset` - 重置浏览器

## 开发指南

### 添加新功能
1. 后端: 在 `backend/app/api/` 添加新的路由
2. 前端: 在 `frontend/src/components/` 添加新组件
3. 更新类型定义和文档

### 调试模式
设置环境变量 `DEBUG=true` 启用详细日志输出

## 部署说明

### 打包为可执行文件
```bash
# 使用PyInstaller打包
./scripts/build.sh
```

### Docker部署
```bash
docker build -t ui-agent .
docker run -p 8000:8000 -p 3000:3000 ui-agent
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
