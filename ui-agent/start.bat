@echo off
REM UI自动化助手启动脚本 (Windows版本)
REM 使用方法: start.bat [backend|frontend|all]

setlocal enabledelayedexpansion

REM 项目根目录
set "PROJECT_ROOT=%~dp0"
set "BACKEND_DIR=%PROJECT_ROOT%backend"
set "FRONTEND_DIR=%PROJECT_ROOT%frontend"

REM 颜色定义 (Windows CMD限制)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM 检查依赖
:check_dependencies
echo %INFO% 检查系统依赖...

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Python 未安装或未添加到PATH
    exit /b 1
)

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Node.js 未安装或未添加到PATH
    exit /b 1
)

echo %SUCCESS% 系统依赖检查完成
goto :eof

REM 安装Python依赖
:install_python_deps
echo %INFO% 安装Python依赖...
cd /d "%BACKEND_DIR%"

if not exist "requirements.txt" (
    echo %ERROR% requirements.txt 文件不存在
    exit /b 1
)

pip install -r requirements.txt
if errorlevel 1 (
    echo %WARNING% 部分依赖安装失败，尝试安装核心依赖...
    pip install fastapi uvicorn sqlalchemy alembic playwright
)

echo %SUCCESS% Python依赖安装完成
goto :eof

REM 安装Node.js依赖
:install_node_deps
echo %INFO% 安装Node.js依赖...
cd /d "%FRONTEND_DIR%"

if not exist "package.json" (
    echo %ERROR% package.json 文件不存在
    exit /b 1
)

call npm install
call npm install -D sass-embedded

echo %SUCCESS% Node.js依赖安装完成
goto :eof

REM 初始化数据库
:init_database
echo %INFO% 初始化数据库...
cd /d "%BACKEND_DIR%"

if not exist "init_db.py" (
    echo %ERROR% init_db.py 文件不存在
    exit /b 1
)

python init_db.py
echo %SUCCESS% 数据库初始化完成
goto :eof

REM 启动后端服务
:start_backend
echo %INFO% 启动后端服务...
cd /d "%BACKEND_DIR%"

set "PYTHONPATH=%BACKEND_DIR%"

echo %INFO% 后端服务将在 http://localhost:8000 启动
echo %INFO% API文档: http://localhost:8000/docs

python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
goto :eof

REM 启动前端服务
:start_frontend
echo %INFO% 启动前端服务...
cd /d "%FRONTEND_DIR%"

echo %INFO% 前端服务将在 http://localhost:3000 启动

call npm run dev
goto :eof

REM 完整启动流程
:start_all
echo %INFO% 开始完整启动流程...

call :check_dependencies
call :install_python_deps
call :install_node_deps
call :init_database

echo %SUCCESS% 环境准备完成！
echo %INFO% 请在两个命令提示符窗口中分别运行：
echo %INFO% 1. 后端: start.bat backend
echo %INFO% 2. 前端: start.bat frontend
echo.
echo 或者使用以下命令：
echo # 启动后端
echo cd "%BACKEND_DIR%" ^&^& set "PYTHONPATH=%BACKEND_DIR%" ^&^& python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
echo.
echo # 启动前端
echo cd "%FRONTEND_DIR%" ^&^& npm run dev
goto :eof

REM 显示帮助信息
:show_help
echo UI自动化助手启动脚本 (Windows版本)
echo.
echo 使用方法:
echo   start.bat [命令]
echo.
echo 命令:
echo   all       - 完整安装和配置环境
echo   backend   - 启动后端服务
echo   frontend  - 启动前端服务
echo   deps      - 仅安装依赖
echo   init      - 仅初始化数据库
echo   help      - 显示此帮助信息
echo.
echo 示例:
echo   start.bat all       # 完整环境配置
echo   start.bat backend   # 启动后端
echo   start.bat frontend  # 启动前端
goto :eof

REM 主函数
:main
set "command=%~1"
if "%command%"=="" set "command=help"

if "%command%"=="all" (
    call :start_all
) else if "%command%"=="backend" (
    call :start_backend
) else if "%command%"=="frontend" (
    call :start_frontend
) else if "%command%"=="deps" (
    call :check_dependencies
    call :install_python_deps
    call :install_node_deps
) else if "%command%"=="init" (
    call :init_database
) else (
    call :show_help
)

goto :eof

REM 执行主函数
call :main %*
