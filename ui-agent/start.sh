#!/bin/bash

# UI自动化助手启动脚本
# 使用方法: ./start.sh [backend|frontend|all]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查Node.js (通过nvm)
    if [ -s "$HOME/.nvm/nvm.sh" ]; then
        source "$HOME/.nvm/nvm.sh"
        nvm use node &> /dev/null || {
            log_error "Node.js 未通过nvm安装"
            exit 1
        }
    else
        log_error "nvm 未安装"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 安装Python依赖
install_python_deps() {
    log_info "安装Python依赖..."
    cd "$BACKEND_DIR"
    
    if [ ! -f "requirements.txt" ]; then
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    pip3 install -r requirements.txt || {
        log_warning "部分依赖安装失败，尝试安装核心依赖..."
        pip3 install fastapi uvicorn sqlalchemy alembic playwright
    }
    
    log_success "Python依赖安装完成"
}

# 安装Node.js依赖
install_node_deps() {
    log_info "安装Node.js依赖..."
    cd "$FRONTEND_DIR"
    
    # 设置Node.js环境
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    nvm use node
    
    if [ ! -f "package.json" ]; then
        log_error "package.json 文件不存在"
        exit 1
    fi
    
    npm install
    
    # 安装sass-embedded
    npm install -D sass-embedded
    
    log_success "Node.js依赖安装完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    cd "$BACKEND_DIR"
    
    if [ ! -f "init_db.py" ]; then
        log_error "init_db.py 文件不存在"
        exit 1
    fi
    
    python3 init_db.py
    log_success "数据库初始化完成"
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    cd "$BACKEND_DIR"
    
    export PYTHONPATH="$BACKEND_DIR"
    
    log_info "后端服务将在 http://localhost:8000 启动"
    log_info "API文档: http://localhost:8000/docs"
    
    python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    cd "$FRONTEND_DIR"
    
    # 设置Node.js环境
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    nvm use node
    
    log_info "前端服务将在 http://localhost:3000 启动"
    
    npm run dev
}

# 完整启动流程
start_all() {
    log_info "开始完整启动流程..."
    
    check_dependencies
    install_python_deps
    install_node_deps
    init_database
    
    log_success "环境准备完成！"
    log_info "请在两个终端窗口中分别运行："
    log_info "1. 后端: ./start.sh backend"
    log_info "2. 前端: ./start.sh frontend"
    log_info ""
    log_info "或者使用以下命令："
    echo -e "${YELLOW}# 启动后端${NC}"
    echo "cd $BACKEND_DIR && PYTHONPATH=$BACKEND_DIR python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"
    echo ""
    echo -e "${YELLOW}# 启动前端${NC}"
    echo "cd $FRONTEND_DIR && export NVM_DIR=\"\$HOME/.nvm\" && [ -s \"\$NVM_DIR/nvm.sh\" ] && \\. \"\$NVM_DIR/nvm.sh\" && nvm use node && npm run dev"
}

# 显示帮助信息
show_help() {
    echo "UI自动化助手启动脚本"
    echo ""
    echo "使用方法:"
    echo "  ./start.sh [命令]"
    echo ""
    echo "命令:"
    echo "  all       - 完整安装和配置环境"
    echo "  backend   - 启动后端服务"
    echo "  frontend  - 启动前端服务"
    echo "  deps      - 仅安装依赖"
    echo "  init      - 仅初始化数据库"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./start.sh all       # 完整环境配置"
    echo "  ./start.sh backend   # 启动后端"
    echo "  ./start.sh frontend  # 启动前端"
}

# 主函数
main() {
    case "${1:-help}" in
        "all")
            start_all
            ;;
        "backend")
            start_backend
            ;;
        "frontend")
            start_frontend
            ;;
        "deps")
            check_dependencies
            install_python_deps
            install_node_deps
            ;;
        "init")
            init_database
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
